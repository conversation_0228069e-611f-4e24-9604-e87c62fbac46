ALTER TABLE proc_workflow_formdata ADD COLUMN `step_id` varchar(32) DEFAULT NULL COMMENT '步骤id' AFTER update_time;


CREATE TABLE `proc_workflow_formdata_files` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `business_id` varchar(64) NOT NULL COMMENT '流程id',
  `step_id` varchar(32) NOT NULL COMMENT '步骤id',
  `file_name` varchar(255) DEFAULT '' COMMENT '源文件名（页面展示和下载命名用）',
  `url` varchar(255) DEFAULT '' COMMENT '文件地址',
  `upload_user_id` bigint(20) DEFAULT NULL COMMENT '上传人id',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='审批记录文件表';
