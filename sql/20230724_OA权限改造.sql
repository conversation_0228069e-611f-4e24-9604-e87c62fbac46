ALTER TABLE sys_role ADD COLUMN `role_type` char(1) DEFAULT '0' COMMENT '角色类型 0-菜单角色 1-数据角色 2-oa角色' AFTER remark;
ALTER TABLE sys_role MODIFY COLUMN `data_scope` char(1) DEFAULT '2' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）';

INSERT INTO `sys_menu` (`menu_name`,`parent_id`,`order_num`,`path`,`component`,`query`,`is_frame`,`is_cache`,`menu_type`,`visible`,`status`,`perms`,`icon`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) 
VALUES('角色管理-菜单',1,2,'rolemenu','system/rolemenu/index','','1','0','C','0','0','system:rolemenu:list','peoples','admin',now(),'admin',now(),'') ;

INSERT INTO `sys_menu` (`menu_name`,`parent_id`,`order_num`,`path`,`component`,`query`,`is_frame`,`is_cache`,`menu_type`,`visible`,`status`,`perms`,`icon`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) 
VALUES('角色管理-数据',1,2,'roledata','system/roledata/index','','1','0','C','0','0','system:roledata:list','peoples','admin',now(),'admin',now(),'') ;

INSERT INTO `sys_menu` (`menu_name`,`parent_id`,`order_num`,`path`,`component`,`query`,`is_frame`,`is_cache`,`menu_type`,`visible`,`status`,`perms`,`icon`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) 
VALUES('角色管理-OA',1,2,'roleoa','system/roleoa/index','','1','0','C','0','0','system:roleoa:list','peoples','admin',now(),'admin',now(),'') ;





SELECT @mdId1 := (SELECT menu_id FROM sys_menu WHERE menu_name='角色管理-菜单');
INSERT INTO `sys_menu` (`menu_name`,`parent_id`,`order_num`,`path`,`component`,`query`,`is_frame`,`is_cache`,`menu_type`,`visible`,`status`,`perms`,`icon`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) 
VALUES('角色查询',@mdId1,1,'','','','1','0','F','0','0','system:rolemenu:query','#','admin',now(),'admin',now(),'') ;
INSERT INTO `sys_menu` (`menu_name`,`parent_id`,`order_num`,`path`,`component`,`query`,`is_frame`,`is_cache`,`menu_type`,`visible`,`status`,`perms`,`icon`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) 
VALUES('角色查看',@mdId1,2,'','','','1','0','F','0','0','system:rolemenu:view','#','admin',now(),'admin',now(),'') ;
INSERT INTO `sys_menu` (`menu_name`,`parent_id`,`order_num`,`path`,`component`,`query`,`is_frame`,`is_cache`,`menu_type`,`visible`,`status`,`perms`,`icon`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) 
VALUES('角色新增',@mdId1,3,'','','','1','0','F','0','0','system:rolemenu:add','#','admin',now(),'admin',now(),'') ;
INSERT INTO `sys_menu` (`menu_name`,`parent_id`,`order_num`,`path`,`component`,`query`,`is_frame`,`is_cache`,`menu_type`,`visible`,`status`,`perms`,`icon`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) 
VALUES('角色修改',@mdId1,4,'','','','1','0','F','0','0','system:rolemenu:edit','#','admin',now(),'admin',now(),'') ;
INSERT INTO `sys_menu` (`menu_name`,`parent_id`,`order_num`,`path`,`component`,`query`,`is_frame`,`is_cache`,`menu_type`,`visible`,`status`,`perms`,`icon`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) 
VALUES('角色删除',@mdId1,5,'','','','1','0','F','0','0','system:rolemenu:remove','#','admin',now(),'admin',now(),'') ;
INSERT INTO `sys_menu` (`menu_name`,`parent_id`,`order_num`,`path`,`component`,`query`,`is_frame`,`is_cache`,`menu_type`,`visible`,`status`,`perms`,`icon`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) 
VALUES('角色导出',@mdId1,6,'','','','1','0','F','0','0','system:rolemenu:export','#','admin',now(),'admin',now(),'') ;





SELECT @mdId2 := (SELECT menu_id FROM sys_menu WHERE menu_name='角色管理-数据');
INSERT INTO `sys_menu` (`menu_name`,`parent_id`,`order_num`,`path`,`component`,`query`,`is_frame`,`is_cache`,`menu_type`,`visible`,`status`,`perms`,`icon`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) 
VALUES('角色查询',@mdId2,1,'','','','1','0','F','0','0','system:roledata:query','#','admin',now(),'admin',now(),'') ;
INSERT INTO `sys_menu` (`menu_name`,`parent_id`,`order_num`,`path`,`component`,`query`,`is_frame`,`is_cache`,`menu_type`,`visible`,`status`,`perms`,`icon`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) 
VALUES('角色查看',@mdId2,2,'','','','1','0','F','0','0','system:roledata:view','#','admin',now(),'admin',now(),'') ;
INSERT INTO `sys_menu` (`menu_name`,`parent_id`,`order_num`,`path`,`component`,`query`,`is_frame`,`is_cache`,`menu_type`,`visible`,`status`,`perms`,`icon`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) 
VALUES('角色新增',@mdId2,3,'','','','1','0','F','0','0','system:roledata:add','#','admin',now(),'admin',now(),'') ;
INSERT INTO `sys_menu` (`menu_name`,`parent_id`,`order_num`,`path`,`component`,`query`,`is_frame`,`is_cache`,`menu_type`,`visible`,`status`,`perms`,`icon`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) 
VALUES('角色修改',@mdId2,4,'','','','1','0','F','0','0','system:roledata:edit','#','admin',now(),'admin',now(),'') ;
INSERT INTO `sys_menu` (`menu_name`,`parent_id`,`order_num`,`path`,`component`,`query`,`is_frame`,`is_cache`,`menu_type`,`visible`,`status`,`perms`,`icon`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) 
VALUES('角色删除',@mdId2,5,'','','','1','0','F','0','0','system:roledata:remove','#','admin',now(),'admin',now(),'') ;
INSERT INTO `sys_menu` (`menu_name`,`parent_id`,`order_num`,`path`,`component`,`query`,`is_frame`,`is_cache`,`menu_type`,`visible`,`status`,`perms`,`icon`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) 
VALUES('角色导出',@mdId2,6,'','','','1','0','F','0','0','system:roledata:export','#','admin',now(),'admin',now(),'') ;

SELECT @mdId3 := (SELECT menu_id FROM sys_menu WHERE menu_name='角色管理-OA');
INSERT INTO `sys_menu` (`menu_name`,`parent_id`,`order_num`,`path`,`component`,`query`,`is_frame`,`is_cache`,`menu_type`,`visible`,`status`,`perms`,`icon`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) 
VALUES('角色查询',@mdId3,1,'','','','1','0','F','0','0','system:roleoa:query','#','admin',now(),'admin',now(),'') ;
INSERT INTO `sys_menu` (`menu_name`,`parent_id`,`order_num`,`path`,`component`,`query`,`is_frame`,`is_cache`,`menu_type`,`visible`,`status`,`perms`,`icon`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) 
VALUES('角色查看',@mdId3,2,'','','','1','0','F','0','0','system:roleoa:view','#','admin',now(),'admin',now(),'') ;
INSERT INTO `sys_menu` (`menu_name`,`parent_id`,`order_num`,`path`,`component`,`query`,`is_frame`,`is_cache`,`menu_type`,`visible`,`status`,`perms`,`icon`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) 
VALUES('角色新增',@mdId3,3,'','','','1','0','F','0','0','system:roleoa:add','#','admin',now(),'admin',now(),'') ;
INSERT INTO `sys_menu` (`menu_name`,`parent_id`,`order_num`,`path`,`component`,`query`,`is_frame`,`is_cache`,`menu_type`,`visible`,`status`,`perms`,`icon`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) 
VALUES('角色修改',@mdId3,4,'','','','1','0','F','0','0','system:roleoa:edit','#','admin',now(),'admin',now(),'') ;
INSERT INTO `sys_menu` (`menu_name`,`parent_id`,`order_num`,`path`,`component`,`query`,`is_frame`,`is_cache`,`menu_type`,`visible`,`status`,`perms`,`icon`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) 
VALUES('角色删除',@mdId3,5,'','','','1','0','F','0','0','system:roleoa:remove','#','admin',now(),'admin',now(),'') ;
INSERT INTO `sys_menu` (`menu_name`,`parent_id`,`order_num`,`path`,`component`,`query`,`is_frame`,`is_cache`,`menu_type`,`visible`,`status`,`perms`,`icon`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) 
VALUES('角色导出',@mdId3,6,'','','','1','0','F','0','0','system:roleoa:export','#','admin',now(),'admin',now(),'') ;




ALTER TABLE sys_post ADD COLUMN `leader` bigint(20)  null COMMENT '岗位负责人' AFTER remark;
ALTER TABLE sys_post ADD COLUMN `dept_id` bigint(20) not null COMMENT '所属部门' AFTER remark;


INSERT INTO sys_dict_type (dict_name,dict_type,`status`,create_by,create_time,remark) VALUES ('删除标识','sys_del_flag','0','admin',NOW(),'删除标识列表');
INSERT INTO `sys_dict_data` (`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`create_time`,`remark`) 
VALUES( 1, '存在', '0', 'sys_del_flag', '', 'primary', 'Y', '0', 'admin', NOW(), '存在标识'),
( 2, '删除', '2', 'sys_del_flag', '', 'danger', 'N', '0', 'admin', NOW(), '删除标识') ;



CREATE TABLE `sys_unit` (
  `unit_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `unit_code` VARCHAR(64) DEFAULT '' COMMENT '公司编码',
  `unit_name` VARCHAR(200) DEFAULT '' COMMENT '公司名称',
  `unit_short_name` VARCHAR(64) DEFAULT '' COMMENT '公司简称',
  `unit_no` VARCHAR(64) DEFAULT '' COMMENT '统一社会信用代码',
  `linkman` VARCHAR(64) DEFAULT '' COMMENT '联系人',
  `phone` VARCHAR(30) DEFAULT '' COMMENT '联系电话',
  `email` VARCHAR(200) DEFAULT '' COMMENT '联系邮箱',
  `postcode` VARCHAR(20) DEFAULT '' COMMENT '邮政编码',
  `business_address` VARCHAR(300) DEFAULT '' COMMENT '办公地址',
  `website` VARCHAR(100) DEFAULT '' COMMENT '网站',
  `registered_address` VARCHAR(300) DEFAULT '' COMMENT '注册地址',
  `status` char(1) DEFAULT '0' COMMENT '状态，0正常 1禁用',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`unit_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='全量公司信息表';


-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('全量公司信息', '3', '1', 'units', 'system/unit/index', 1, 0, 'C', '0', '0', 'system:unit:list', '#', 'admin', sysdate(), '', null, '全量公司信息菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('全量公司信息查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'system:unit:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('全量公司信息新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'system:unit:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('全量公司信息修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'system:unit:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('全量公司信息删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'system:unit:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('全量公司信息导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'system:unit:export',       '#', 'admin', sysdate(), '', null, '');



ALTER TABLE sys_dept ADD COLUMN `node_type` char(1)  not null DEFAULT '1' COMMENT '节点类型 0公司 1部门' AFTER update_time;
ALTER TABLE sys_dept ADD COLUMN `unit_id` bigint(20) null COMMENT '部门所属公司ID' AFTER update_time;
ALTER TABLE sys_dept ADD COLUMN `leader_id` bigint(20) null COMMENT '部门负责人ID' AFTER update_time;

ALTER TABLE sys_user_post ADD COLUMN `home_post` char(1) not null DEFAULT '1' COMMENT '是否主岗位 0是 1否' AFTER post_id;



CREATE TABLE `sys_role_oa` (
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `oa_id` bigint(20) NOT NULL COMMENT 'OaID',
  `oa_type` char(3) NOT NULL COMMENT 'OaID类型 opc流程分类 opt流程模板',
  PRIMARY KEY (`role_id`,`oa_id`,`oa_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色和OA关联表';

ALTER TABLE sys_role ADD COLUMN `oa_check_strictly`  tinyint(1) null DEFAULT '1' COMMENT 'OA流程树选择项是否关联显示' AFTER dept_check_strictly;


