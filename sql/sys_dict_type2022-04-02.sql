/*
 Navicat Premium Data Transfer

 Source Server         : 数据平台-测试环境
 Source Server Type    : MySQL
 Source Server Version : 50735
 Source Host           : *************:6307
 Source Schema         : mgrdb_dev

 Target Server Type    : MySQL
 Target Server Version : 50735
 File Encoding         : 65001

 Date: 02/04/2022 14:07:36
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_type`;
CREATE TABLE `sys_dict_type`  (
  `dict_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  `dict_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典类型',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_id`) USING BTREE,
  UNIQUE INDEX `dict_type`(`dict_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 117 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '字典类型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dict_type
-- ----------------------------
INSERT INTO `sys_dict_type` VALUES (1, '用户性别', 'sys_user_sex', '0', 'admin', '2022-01-28 13:37:36', '', NULL, '用户性别列表');
INSERT INTO `sys_dict_type` VALUES (2, '菜单状态', 'sys_show_hide', '0', 'admin', '2022-01-28 13:37:36', '', NULL, '菜单状态列表');
INSERT INTO `sys_dict_type` VALUES (3, '系统开关', 'sys_normal_disable', '0', 'admin', '2022-01-28 13:37:36', '', NULL, '系统开关列表');
INSERT INTO `sys_dict_type` VALUES (4, '任务状态', 'sys_job_status', '0', 'admin', '2022-01-28 13:37:36', '', NULL, '任务状态列表');
INSERT INTO `sys_dict_type` VALUES (5, '任务分组', 'sys_job_group', '0', 'admin', '2022-01-28 13:37:36', '', NULL, '任务分组列表');
INSERT INTO `sys_dict_type` VALUES (6, '系统是否', 'sys_yes_no', '0', 'admin', '2022-01-28 13:37:36', '', NULL, '系统是否列表');
INSERT INTO `sys_dict_type` VALUES (7, '通知类型', 'sys_notice_type', '0', 'admin', '2022-01-28 13:37:36', '', NULL, '通知类型列表');
INSERT INTO `sys_dict_type` VALUES (8, '通知状态', 'sys_notice_status', '0', 'admin', '2022-01-28 13:37:36', '', NULL, '通知状态列表');
INSERT INTO `sys_dict_type` VALUES (9, '操作类型', 'sys_oper_type', '0', 'admin', '2022-01-28 13:37:36', '', NULL, '操作类型列表');
INSERT INTO `sys_dict_type` VALUES (10, '系统状态', 'sys_common_status', '0', 'admin', '2022-01-28 13:37:36', '', NULL, '登录状态列表');
INSERT INTO `sys_dict_type` VALUES (11, '担保公司', 'sys_company_code', '0', 'admin', '2022-04-01 14:48:28', '', NULL, NULL);
INSERT INTO `sys_dict_type` VALUES (12, '外部系统', 'sys_external_system', '0', 'admin', '2022-04-01 14:48:42', '', NULL, NULL);
INSERT INTO `sys_dict_type` VALUES (13, '合作方', 'sys_partner_type', '0', 'admin', '2022-04-01 14:48:54', '', NULL, NULL);
INSERT INTO `sys_dict_type` VALUES (14, '资金方', 'sys_capital_type', '0', 'admin', '2022-04-01 14:49:07', '', NULL, NULL);
INSERT INTO `sys_dict_type` VALUES (15, '产品方', 'sys_product_type', '0', 'admin', '2022-04-01 14:49:18', '', NULL, NULL);
INSERT INTO `sys_dict_type` VALUES (100, '系统平台编码', 'sys_platform_code', '0', 'admin', '2022-03-28 11:13:22', '', NULL, '系统平台编码');
INSERT INTO `sys_dict_type` VALUES (101, '私钥使用类型(本部平台/外部平台)', 'private_use_type', '0', 'admin', '2022-03-28 11:58:29', 'admin', '2022-03-31 11:19:12', '秘钥使用类型');
INSERT INTO `sys_dict_type` VALUES (102, '秘钥类型（对称加密、非对称加密）', 'secret_key_type', '0', 'admin', '2022-03-28 12:00:28', 'admin', '2022-03-31 11:19:35', '秘钥类型');
INSERT INTO `sys_dict_type` VALUES (103, '秘钥全部算法', 'secret_key_algorithm', '0', 'admin', '2022-03-28 13:39:06', 'admin', '2022-03-31 11:19:58', '秘钥算法');
INSERT INTO `sys_dict_type` VALUES (104, '秘钥启用状态', 'secret_status', '0', 'admin', '2022-03-28 16:54:10', '', NULL, '秘钥启用状态');
INSERT INTO `sys_dict_type` VALUES (105, '字符编码', 'character_encoding', '0', 'admin', '2022-03-29 11:00:05', '', NULL, '字符编码\n');
INSERT INTO `sys_dict_type` VALUES (106, '秘钥算法全部工作模式', 'work_status', '0', 'admin', '2022-03-29 11:26:42', 'admin', '2022-03-31 10:21:29', '秘钥算法全部工作模式');
INSERT INTO `sys_dict_type` VALUES (107, '秘钥长度', 'secret_length', '0', 'admin', '2022-03-29 11:34:08', '', NULL, '秘钥长度');
INSERT INTO `sys_dict_type` VALUES (108, '私钥算法工作模式_RSA', 'secret_work_rsa', '0', 'admin', '2022-03-29 16:29:09', 'admin', '2022-03-31 11:09:07', '私钥算法工作模式_RSA');
INSERT INTO `sys_dict_type` VALUES (109, '私钥算法工作模式_SM2', 'secret_work_sm2', '0', 'admin', '2022-03-29 16:29:52', '', NULL, 'secret_work_rsa');
INSERT INTO `sys_dict_type` VALUES (110, '私钥算法工作模式_AES', 'secret_work_aes', '0', 'admin', '2022-03-31 11:03:24', 'admin', '2022-03-31 11:03:49', '私钥算法工作模式_AES');
INSERT INTO `sys_dict_type` VALUES (111, '对称加密的秘钥算法', 'symmetrical_encryption', '0', 'admin', '2022-03-31 10:20:38', 'admin', '2022-03-31 11:21:03', '对称加密的秘钥算法');
INSERT INTO `sys_dict_type` VALUES (112, '非对称加密的秘钥算法', 'symmetrical_noencryption', '0', 'admin', '2022-03-31 10:19:04', 'admin', '2022-03-31 11:20:51', '非对称加密的秘钥算法');
INSERT INTO `sys_dict_type` VALUES (114, '秘钥长度-AES', 'secret_length_aes', '0', 'admin', '2022-04-01 15:54:49', 'admin', '2022-04-01 16:26:31', '秘钥长度-AES');
INSERT INTO `sys_dict_type` VALUES (115, '秘钥长度-RSA', 'secret_length_rsa', '0', 'admin', '2022-04-01 15:57:00', 'admin', '2022-04-01 16:26:37', 'secret_lengrh_rsa');
INSERT INTO `sys_dict_type` VALUES (116, '秘钥长度-SM2', 'secret_length_sm2', '0', 'admin', '2022-04-01 16:19:31', 'admin', '2022-04-01 16:26:42', 'secret_lengrh_sm2');

SET FOREIGN_KEY_CHECKS = 1;
