/*
运营情况月报表/年报表 菜单初始化 - start
*/
-- 数据报表下运营情况月报表和运营情况年报表菜单初始化语句
SELECT @sjbbParendId := (SELECT menu_id FROM sys_menu WHERE menu_name='数据报表');
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('运营情况月报表', @sjbbParendId, 8, 'operationm', 'data/operationm/index', NULL, 1, 0, 'C', '0', '0', '', 'example', 'admin', '2023-02-02 10:40:09', 'admin', '2023-02-13 09:28:17', '');
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('运营情况年报表', @sjbbParendId, 9, 'operationy', 'data/operationy/index', NULL, 1, 0, 'C', '0', '0', '', 'example', 'admin', '2023-02-02 14:03:04', 'admin', '2023-02-13 09:28:21', '');
/*
运营情况月报表/年报表 菜单初始化 - end
*/


/*
运营情况月报表/年报表 权限初始化 - start
*/
-- 运营情况月报表菜单初始化
SELECT @yyqkybbParendId := (SELECT menu_id FROM sys_menu WHERE menu_name='运营情况月报表');
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('运营情况月报表查询', @yyqkybbParendId, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'data:external:list3', '#', 'admin', '2023-02-13 09:13:24', '', NULL, '');
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('运营情况月报表导出', @yyqkybbParendId, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'data:external:export3', '#', 'admin', '2023-02-13 09:13:48', '', NULL, '');
-- 运营情况年报表菜单初始化
SELECT @yyqknbbParendId := (SELECT menu_id FROM sys_menu WHERE menu_name='运营情况年报表');
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('运营情况年报表查询', @yyqknbbParendId, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'data:external:list4', '#', 'admin', '2023-02-13 09:14:12', '', NULL, '');
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('运营情况年报表导出', @yyqknbbParendId, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'data:external:export4', '#', 'admin', '2023-02-13 09:14:39', '', NULL, '');
/*
运营情况月报表/年报表 权限初始化 - end
*/