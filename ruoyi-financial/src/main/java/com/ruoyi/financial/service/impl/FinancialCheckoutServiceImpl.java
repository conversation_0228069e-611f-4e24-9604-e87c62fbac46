package com.ruoyi.financial.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.financial.domain.FinancialAccountSets;
import com.ruoyi.financial.domain.FinancialCheckout;
import com.ruoyi.financial.domain.FinancialVoucherDetails;
import com.ruoyi.financial.mapper.FinancialAccountSetsMapper;
import com.ruoyi.financial.mapper.FinancialCheckoutMapper;
import com.ruoyi.financial.mapper.FinancialVoucherDetailsMapper;
import com.ruoyi.financial.mapper.FinancialVoucherMapper;
import com.ruoyi.financial.service.IFinancialCheckoutService;
import com.ruoyi.financial.utils.DoubleComparer;
import com.ruoyi.financial.utils.DoubleValueUtil;
import com.ruoyi.financial.vo.FinancialUserVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : ${PACKAGE_NAME}</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年08月23日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
@Slf4j
@Service
@AllArgsConstructor
public class FinancialCheckoutServiceImpl extends ServiceImpl<FinancialCheckoutMapper, FinancialCheckout> implements IFinancialCheckoutService {

    private FinancialVoucherDetailsMapper voucherDetailsMapper;
    private FinancialVoucherMapper voucherMapper;
    private FinancialAccountSetsMapper accountSetsMapper;

    @Override
    public int batchInsert(List<FinancialCheckout> list) {
        return baseMapper.batchInsert(list);
    }

    /**
     * 期初检查
     *
     * @param accountSetsId
     * @return
     */
    @Override
    public boolean initialCheck(Integer accountSetsId) {
        Map<String, Double> maps = voucherDetailsMapper.selectListInitialCheckData(accountSetsId);
        if (maps == null) {
            return true;
        }
        Double jie = maps.getOrDefault("debit_amount", 0d);
        Double dai = maps.getOrDefault("credit_amount", 0d);
        return DoubleComparer.considerEqual(jie, dai);
    }

    /**
     * 期末检查
     *
     * @param accountSetsId
     * @param year
     * @param month
     * @return
     */
    @Override
    public boolean finalCheck(Integer accountSetsId, Integer year, Integer month) {
        FinancialVoucherDetails details = voucherDetailsMapper.selectFinalCheckData(accountSetsId, year, month);
        if (details == null) {
            return true;
        }
        if (details.getDebitAmount() != null && details.getCreditAmount() != null) {
            return DoubleComparer.considerEqual(details.getDebitAmount(), details.getCreditAmount());
        } else return details.getDebitAmount() == null && details.getCreditAmount() == null;
    }

    /**
     * 报表检查
     *
     * @param accountSetsId
     * @param year
     * @param month
     * @return
     */
    @Override
    public Map<String, Object> reportCheck(Integer accountSetsId, Integer year, Integer month) {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR, year);
        cal.set(Calendar.MONTH, month - 1);
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMinimum(Calendar.DATE));

        List<FinancialVoucherDetails> details = voucherDetailsMapper.assetStatistics(accountSetsId, DateUtils.getMonthEnd(cal.getTime()));
        Map<String, Object> result = new HashMap<>(4);
        result.put("result", true);
        if (details != null) {
            Optional<FinancialVoucherDetails> assets = details.stream().filter(vd -> vd.getSubjectName().equals("资产")).findFirst();
            Optional<FinancialVoucherDetails> liabilities = details.stream().filter(vd -> vd.getSubjectName().equals("负债")).findFirst();
            Optional<FinancialVoucherDetails> rightsAndInterests = details.stream().filter(vd -> vd.getSubjectName().equals("权益")).findFirst();

            double assetsNum = 0, liabilitiesNum = 0, rightsAndInterestsNum = 0;

            if (assets.isPresent()) {
                assetsNum = DoubleValueUtil.getNotNullVal(assets.get().getDebitAmount()) - DoubleValueUtil.getNotNullVal(assets.get().getCreditAmount());
            }

            if (liabilities.isPresent()) {
                liabilitiesNum = DoubleValueUtil.getNotNullVal(liabilities.get().getCreditAmount()) - DoubleValueUtil.getNotNullVal(liabilities.get().getDebitAmount());
            }

            if (rightsAndInterests.isPresent()) {
                rightsAndInterestsNum = DoubleValueUtil.getNotNullVal(rightsAndInterests.get().getCreditAmount()) - DoubleValueUtil.getNotNullVal(rightsAndInterests.get().getDebitAmount());
            }

            //资产类 - 负债类 = 权益类
            result.put("result", DoubleComparer.considerEqual(Math.abs(assetsNum - liabilitiesNum), Math.abs(rightsAndInterestsNum)));
            result.put("资产类", BigDecimal.valueOf(assetsNum).setScale(2, BigDecimal.ROUND_HALF_UP));
            result.put("负债类", BigDecimal.valueOf(liabilitiesNum).setScale(2, BigDecimal.ROUND_HALF_UP));
            result.put("权益类", BigDecimal.valueOf(rightsAndInterestsNum).setScale(2, BigDecimal.ROUND_HALF_UP));
        }
        return result;
    }

    /**
     * 断号检查
     *
     * @param accountSetsId
     * @param year
     * @param month
     * @return
     */
    @Override
    public boolean brokenCheck(Integer accountSetsId, Integer year, Integer month) {
        String startTime = DateUtils.getFirstday(year.toString(), month.toString());
        String endTime = DateUtils.getLastday(year.toString(), month.toString());
        List<Map<String, Object>> data = this.voucherMapper.selectBrokenData(accountSetsId, startTime, endTime);
        return data.stream().allMatch(map -> map.get("total").equals(((Integer) map.get("code")).longValue()));
    }

    /**
     * 结账
     *
     * @param user
     * @param year
     * @param month
     * @return
     */
    @Override
    public boolean invoicing(FinancialUserVo user, Integer year, Integer month) {
        Calendar instance = Calendar.getInstance();
        instance.set(Calendar.YEAR, year);
        instance.set(Calendar.MONTH, month - 1);

        LambdaQueryWrapper<FinancialCheckout> qw = Wrappers.lambdaQuery();
        qw.eq(FinancialCheckout::getAccountSetsId, user.getAccountSetsId());
        qw.eq(FinancialCheckout::getCheckYear, year);
        qw.eq(FinancialCheckout::getCheckMonth, month);
        FinancialCheckout checkout = new FinancialCheckout();
        checkout.setStatus(2);
        checkout.setCheckDate(DateUtils.getMonthBegin(instance.getTime()));

        this.baseMapper.update(checkout, qw);

        DateFormat df = new SimpleDateFormat("yyyyMM");
        if (df.format(user.getAccountSets().getCurrentAccountDate()).equals(df.format(instance.getTime()))) {
            checkout = new FinancialCheckout();
            checkout.setAccountSetsId(user.getAccountSetsId());
            Date nextMonth = DateUtils.getMonthEnd(DateUtils.addMonths(user.getAccountSets().getCurrentAccountDate(), 1));
            instance = Calendar.getInstance();
            instance.setTime(nextMonth);
            checkout.setCheckYear(instance.get(Calendar.YEAR));
            checkout.setCheckMonth(instance.get(Calendar.MONTH) + 1);

            this.baseMapper.insert(checkout);
            //更新账套当前期间
            FinancialAccountSets accountSets = user.getAccountSets();
            accountSets.setCurrentAccountDate(nextMonth);
            this.accountSetsMapper.updateById(accountSets);
        }
        return true;
    }

    /**
     * 反结账
     *
     * @param currentUser
     * @param year
     * @param month
     * @return
     */
    @Override
    public boolean unCheck(FinancialUserVo currentUser, Integer year, Integer month) {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR, year);
        cal.set(Calendar.MONTH, month - 2);
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMinimum(Calendar.DATE));

        LambdaQueryWrapper<FinancialCheckout> cqw = Wrappers.lambdaQuery();
        cqw.eq(FinancialCheckout::getAccountSetsId, currentUser.getAccountSetsId());
        cqw.eq(FinancialCheckout::getStatus, 2);
        cqw.gt(FinancialCheckout::getCheckDate, DateUtils.getMonthEnd(cal.getTime()));
        FinancialCheckout checkout = new FinancialCheckout();
        checkout.setStatus(0);
        checkout.setCheckDate(null);
        baseMapper.update(checkout, cqw);
        return true;
    }

    @Override
    public List<FinancialCheckout> list(Wrapper<FinancialCheckout> queryWrapper) {
        QueryWrapper qw = (QueryWrapper) queryWrapper;
        qw.orderByDesc("check_year", "check_month");
        return super.list(qw);
    }
}

