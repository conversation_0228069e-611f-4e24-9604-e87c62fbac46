package com.ruoyi.financial.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.enums.AuthRoleEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.financial.domain.*;
import com.ruoyi.financial.enums.Roles;
import com.ruoyi.financial.mapper.*;
import com.ruoyi.financial.domain.FinancialUser;
import com.ruoyi.financial.mapper.FinancialUserMapper;
import com.ruoyi.financial.service.IFinancialAccountSetsService;
import com.ruoyi.financial.service.IFinancialSubjectService;
import com.ruoyi.financial.service.IFinancialVoucherDetailsService;
import com.ruoyi.financial.service.IFinancialVoucherTemplateDetailsService;
import com.ruoyi.financial.vo.SubjectVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : ${PACKAGE_NAME}</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年07月30日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
@Slf4j
@Primary
@Service
@RequiredArgsConstructor
public class FinancialAccountSetsServiceImpl extends ServiceImpl<FinancialAccountSetsMapper, FinancialAccountSets> implements IFinancialAccountSetsService {

    private final FinancialUserAccountSetsMapper userAccountSetsMapper;

    private final FinancialUserMapper userMapper;

    private final FinancialSubjectMapper subjectMapper;

    private final IFinancialSubjectService subjectService;

    private final IFinancialVoucherDetailsService voucherDetailsService;

    private final IFinancialVoucherTemplateDetailsService voucherTemplateDetailsService;

    private final FinancialAccountingCategoryMapper accountingCategoryMapper;

    private final FinancialCurrencyMapper currencyMapper;

    private final FinancialCheckoutMapper checkoutMapper;

    private final FinancialVoucherWordMapper voucherWordMapper;

    private final FinancialReportTemplateMapper reportTemplateMapper;

    private final FinancialReportTemplateItemsMapper reportTemplateItemsMapper;

    private final FinancialReportTemplateItemsFormulaMapper reportTemplateItemsFormulaMapper;

    private final FinancialAppMapper financialAppMapper;


    @Override
    public int batchInsert(List<FinancialAccountSets> list) {
        return baseMapper.batchInsert(list);
    }

    @Override
    public List<FinancialAccountSets> myAccountSets(Long uid) {
        return baseMapper.selectMyAccountSets(uid);
    }

    public FinancialUser addNewUser(Integer accountSetsId, Long userId, String role) {
        FinancialUser financialUser = new FinancialUser();
        financialUser.setUserId(userId);
        financialUser.setAccountSetsId(accountSetsId);
        this.userMapper.insert(financialUser);

        FinancialUserAccountSets userAccountSets = new FinancialUserAccountSets();
        userAccountSets.setAccountSetsId(accountSetsId);
        userAccountSets.setUserId(userId);
        userAccountSets.setRoleType(Roles.valueOf(role).name());
        this.userAccountSetsMapper.insert(userAccountSets);

        return financialUser;
    }

    @Override
    @Transactional
    public void removeUser(Integer accountSetsId, Integer uid) {
        /*LambdaQueryWrapper<UserAccountSets> uaqw = Wrappers.lambdaQuery();
        uaqw.eq(UserAccountSets::getAccountSetsId, accountSetsId);
        uaqw.eq(UserAccountSets::getUserId, uid);
        this.userAccountSetsMapper.delete(uaqw);

        //重置用户默认账套
        User user = this.userMapper.selectById(uid);
        if (user.getAccountSetsId().equals(accountSetsId)) {
            uaqw = Wrappers.lambdaQuery();
            uaqw.eq(UserAccountSets::getUserId, uid);
            List<UserAccountSets> accountSets = this.userAccountSetsMapper.selectList(uaqw);
            if (accountSets.size() > 0) {
                user.setAccountSetsId(accountSets.get(0).getAccountSetsId());
            } else {
                user.setAccountSetsId(null);
            }
            this.userMapper.updateById(user);
        }*/
    }

    @Override
    public void updateUserRole(Integer accountSetsId, Integer uid, String role) {
        /*LambdaQueryWrapper<UserAccountSets> uaqw = Wrappers.lambdaQuery();
        uaqw.eq(UserAccountSets::getAccountSetsId, accountSetsId);
        uaqw.eq(UserAccountSets::getUserId, uid);
        UserAccountSets userAccountSets = this.userAccountSetsMapper.selectOne(uaqw);
        userAccountSets.setRoleType(Roles.valueOf(role).name());
        this.userAccountSetsMapper.update(userAccountSets, uaqw);*/
    }

    @Override
    @Transactional
    public void handOver(Integer accountSetsId, Long currentUserId, Long userId) {
        FinancialAccountSets financialAccountSets = this.baseMapper.selectById(accountSetsId);

        //获取手机号的用户信息
        LambdaQueryWrapper<FinancialUser> uqw = Wrappers.lambdaQuery();
        uqw.eq(FinancialUser::getUserId, userId);
        FinancialUser financialUser = this.userMapper.selectOne(uqw);

        boolean relate = true;
        if (financialUser == null) {
            //用户不存在则创建用户并关联
            financialUser = this.addNewUser(accountSetsId, userId, Roles.Manager.name());
        } else {
            LambdaQueryWrapper<FinancialUserAccountSets> uaqw = Wrappers.lambdaQuery();
            uaqw.eq(FinancialUserAccountSets::getAccountSetsId, accountSetsId);
            uaqw.eq(FinancialUserAccountSets::getUserId, financialUser.getUserId());

            relate = this.userAccountSetsMapper.selectCount(uaqw) > 0;

            //如果用户当前账套，则需要修改
            if (financialUser.getAccountSetsId().equals(accountSetsId)) {
                uaqw = Wrappers.lambdaQuery();
                uaqw.eq(FinancialUserAccountSets::getUserId, financialUser.getUserId());
                uaqw.ne(FinancialUserAccountSets::getAccountSetsId, accountSetsId);
                //当前用户拥有的所有账套
                List<FinancialUserAccountSets> userAccountSets = this.userAccountSetsMapper.selectList(uaqw);

                if (userAccountSets.size() > 0) {
                    financialUser.setAccountSetsId(userAccountSets.get(0).getAccountSetsId());
                    this.userMapper.updateByUserId(financialUser);
                }
            }
        }

        //用户未关联是，进行关联
        if (!relate) {
            FinancialUserAccountSets userAccountSets = new FinancialUserAccountSets();
            userAccountSets.setAccountSetsId(accountSetsId);
            userAccountSets.setUserId(financialUser.getUserId());
            userAccountSets.setRoleType(Roles.Manager.name());
            this.userAccountSetsMapper.insert(userAccountSets);
        }

        //修改账套拥有者
        financialAccountSets.setCreatorId(financialUser.getUserId());
        this.baseMapper.updateById(financialAccountSets);

        //解除用户和账套的关联
        LambdaQueryWrapper<FinancialUserAccountSets> uaqw = Wrappers.lambdaQuery();
        uaqw.eq(FinancialUserAccountSets::getAccountSetsId, accountSetsId);
        uaqw.eq(FinancialUserAccountSets::getUserId, currentUserId);
        this.userAccountSetsMapper.delete(uaqw);
    }

    /**
     * 更新科目编码设置
     *
     * @param accountSetsId
     * @param encoding
     * @param newEncoding
     */
    @Override
    @Transactional
    public void updateEncode(Integer accountSetsId, String encoding, String newEncoding) {
        FinancialAccountSets accountSets = this.getById(accountSetsId);
        accountSets.setEncoding(newEncoding);
        this.baseMapper.updateById(accountSets);

        //更新所有科目编码
        LambdaQueryWrapper<FinancialSubject> qw = Wrappers.lambdaQuery();
        qw.eq(FinancialSubject::getAccountSetsId, accountSetsId);
        List<FinancialSubject> subjects = this.subjectMapper.selectList(qw);
        List<Integer> newModel = Arrays.stream(newEncoding.split("-")).map(s -> Integer.parseInt(s)).collect(Collectors.toList());
        List<Integer> model = Arrays.stream(encoding.split("-")).map(s -> Integer.parseInt(s)).collect(Collectors.toList());
        //变更后的code和原始code字典
        Map<String, String> codeMap = new HashMap<>(subjects.size());

        subjects.forEach(subject -> {
            String code = this.updateCode(subject.getCode(), model, newModel);
            codeMap.put(subject.getCode(), code);
            subject.setCode(code);
        });

        this.subjectService.updateBatchById(subjects);

        //更新凭证里面所有code
        this.updateVoucherDetailCode(accountSetsId, codeMap);
        //更新模板凭证里面的所有code
        this.updateVoucherTempDetailCode(accountSetsId, codeMap);

    }

    @Override
    public List<Map<String, Object>> queryAccountListByComId(Long companyId) {
        return baseMapper.getAccountList(companyId);
    }

    @Override
    public List<Map<String, Object>> getAllList() {

        return baseMapper. getAllAccountList();

    }

    @Override
    public List<Map<String, Object>> queryRoleTypeUser(List<Long> companyIds) {
        List<FinancialRoleTypeUserInfo> RoleTypeUserList = financialAppMapper.queryRoleTypeUser(companyIds);
        List<Map<String, Object>> resultList = new LinkedList<>();
        for (long companyId:companyIds) {
         Map<String, Object> resultMap = new LinkedHashMap<>();
         resultMap.put("companyId",companyId);
        //普通会计userID集合
        List<Long> accUserIds = new LinkedList<>();
        List<String> accUserName = new LinkedList<>();
        //会计主管授权用户信息
        List<Long> accManagerUserIds = new LinkedList<>();
        List<String> accManagerUserName = new LinkedList<>();
        //查看权限
        List<Long> viewUserIds = new LinkedList<>();
        List<String> viewUserName = new LinkedList<>();
        for (FinancialRoleTypeUserInfo row:RoleTypeUserList) {
            if(AuthRoleEnum.ZHCWXT1.getCode().equals(row.getRoleType()) && (null == row.getCompanyId() || companyId == row.getCompanyId()) ){
                if(!accUserIds.contains(row.getUserId())){
                    accUserIds.add(row.getUserId());
                    accUserName.add(row.getNickName());
                }
            }else if(AuthRoleEnum.ZHCWXT2.getCode().equals(row.getRoleType()) && (null == row.getCompanyId() || companyId == row.getCompanyId())){
                if (!accManagerUserIds.contains(row.getUserId())) {
                    accManagerUserIds.add(row.getUserId());
                    accManagerUserName.add(row.getNickName());
                }
            }else if(AuthRoleEnum.ZHCWXT4.getCode().equals(row.getRoleType()) && (null == row.getCompanyId() ||companyId == row.getCompanyId())){
                if(!viewUserIds.contains(row.getUserId())){
                    viewUserIds.add(row.getUserId());
                    viewUserName.add(row.getNickName());
                }
            }
        }
        List<Map<String, Object>> roleList = new LinkedList<>();
        Map<String, Object> map1 = new LinkedHashMap<>();
        map1.put("roleType","accountingManagers");
        map1.put("roleTypeName","会计主管");
        map1.put("desc","可查看所有信息，新增凭证，审核凭证，结账，设置账套");
        map1.put("accUserIds",accManagerUserIds);
        map1.put("userName",String.join(",",accManagerUserName));
        roleList.add(map1);
        Map<String, Object> map2 = new LinkedHashMap<>();
        map2.put("roleType","accountings");
        map2.put("roleTypeName","普通会计");
        map2.put("desc","可查看报表、账簿、凭证");
        map2.put("accUserIds",accUserIds);
        map2.put("userName",String.join(",",accUserName));
        roleList.add(map2);

        Map<String, Object> map3 = new LinkedHashMap<>();
        map3.put("roleType","onlyViews");
        map3.put("roleTypeName","查看权限");
        map3.put("desc","可查看报表、账簿");
        map3.put("accUserIds",viewUserIds);
        map3.put("userName",String.join(",",viewUserName));
        roleList.add(map3);
        resultMap.put("roleList",roleList);
        resultList.add(resultMap);
        }
        return resultList;
    }


    @Override
    @Transactional
    public boolean save(FinancialAccountSets entity) {
        LambdaQueryWrapper<FinancialAccountSets> qw = Wrappers.lambdaQuery();
        qw.eq(FinancialAccountSets::getCompanyName, entity.getCompanyName());
        qw.eq(FinancialAccountSets::getCreatorId, entity.getCreatorId());

        if (this.count(qw) > 0) {
            throw new ServiceException("账套名称已经存在！");
        }

        LambdaQueryWrapper<FinancialAccountSets> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(FinancialAccountSets::getPlatformCode, entity.getPlatformCode());
        if (this.count(queryWrapper) > 0) {
            throw new ServiceException("该公司已有绑定账套！");
        }

        boolean rs = super.save(entity);
        if (rs) {
            List<Map<String,Object>> userIds = new ArrayList<>();
            Map<String,Object> mapAdmin = new LinkedHashMap<>();
            mapAdmin.put("userId",1l);
            mapAdmin.put("roleKey","admin");
            userIds.add(mapAdmin);//admin 默认新增

            addUserIds(entity,userIds);

            //初始化数据
            //this.initAccountingCategory(entity);
            this.initCurrency(entity);
            this.initVoucherWord(entity);
            this.initSubject(entity);
            try {
                this.initReport(entity);
            } catch (IOException e) {
                throw new ServiceException("初始化报表失败！", e);
            }

            //初始化结转状态
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(entity.getEnableDate());
            FinancialCheckout checkout = new FinancialCheckout();
            checkout.setAccountSetsId(entity.getId());
            checkout.setCheckYear(calendar.get(Calendar.YEAR));
            checkout.setCheckMonth(calendar.get(Calendar.MONDAY) + 1);
            checkoutMapper.insert(checkout);
        }
        return rs;
    }

    @Override
    public boolean update(FinancialAccountSets entity, Wrapper<FinancialAccountSets> updateWrapper) {
        FinancialAccountSets old = this.getById(entity.getId());
        //如果修改了初始账套日期，需要同步更新
        if (!DateFormatUtils.format(old.getEnableDate(), "yyyyMM").equals(DateFormatUtils.format(entity.getEnableDate(), "yyyyMM"))) {
            LambdaQueryWrapper<FinancialCheckout> cqw = Wrappers.lambdaQuery();
            cqw.eq(FinancialCheckout::getAccountSetsId, entity.getId());
            cqw.eq(FinancialCheckout::getCheckYear, DateFormatUtils.format(old.getEnableDate(), "yyyy"));
            cqw.eq(FinancialCheckout::getCheckMonth, DateFormatUtils.format(old.getEnableDate(), "M"));

            FinancialCheckout checkout = this.checkoutMapper.selectOne(cqw);
            checkout.setCheckYear(Integer.parseInt(DateFormatUtils.format(entity.getEnableDate(), "yyyy")));
            checkout.setCheckMonth(Integer.parseInt(DateFormatUtils.format(entity.getEnableDate(), "M")));
            this.checkoutMapper.updateById(checkout);
            entity.setCurrentAccountDate(entity.getEnableDate());
        }
        addUserIds(entity,new ArrayList<>());
        return super.update(entity, updateWrapper);
    }

    @Override
    @Transactional
    public boolean remove(Wrapper<FinancialAccountSets> wrapper) {
        FinancialAccountSets accountSets = this.getOne(wrapper);
        LambdaQueryWrapper<FinancialUserAccountSets> uasQw = Wrappers.lambdaQuery();
        uasQw.eq(FinancialUserAccountSets::getAccountSetsId, accountSets.getId());
        userAccountSetsMapper.delete(uasQw);

        LambdaQueryWrapper<FinancialUser> uQw = Wrappers.lambdaQuery();
        uQw.eq(FinancialUser::getAccountSetsId, accountSets.getId());

        List<FinancialUser> userList = userMapper.selectList(uQw);
        for (FinancialUser user : userList) {
            uasQw = Wrappers.lambdaQuery();
            uasQw.eq(FinancialUserAccountSets::getUserId, user.getId());
            uasQw.last("limit 1");
            FinancialUserAccountSets one = userAccountSetsMapper.selectOne(uasQw);
            if (one != null) {
                user.setAccountSetsId(one.getAccountSetsId());
            } else {
                one.setAccountSetsId(null);
            }

            userMapper.updateById(user);
        }

        return this.baseMapper.deleteById(accountSets.getId()) > 0;
    }

    /**
     * 初始化核算类别
     *
     * @param entity
     */
    private void initAccountingCategory(FinancialAccountSets entity) {
        String[] categories = new String[]{"客户", "供应商", "职员", "部门", "项目", "存货", "现金流"};
        String[] categoriesCols = new String[]{"助记码,客户类别,经营地址,联系人,手机,税号", "助记码,供应商类别,经营地址,联系人,手机,税号", "助记码,性别,部门编码,部门名称,职务,岗位,手机,出生日期,入职日期,离职日期", "助记码,负责人,手机,成立日期,撤销日期", "助记码,负责部门,负责人,手机,开始日期,验收日期", "助记码,规格型号,存货类别,计量单位,启用日期,停用日期", "现金流类别,助记码"};
        List<FinancialAccountingCategory> categoryList = new ArrayList<>(categories.length);
        for (int i = 0; i < categories.length; i++) {
            FinancialAccountingCategory ac = new FinancialAccountingCategory();
            ac.setCanEdit(false);
            ac.setSystemDefault(true);
            ac.setAccountSetsId(entity.getId());
            ac.setName(categories[i]);
            ac.setCustomColumns(categoriesCols[i]);

            categoryList.add(ac);
        }

        accountingCategoryMapper.batchInsert(categoryList);
    }

    /**
     * 初始化科目
     *
     * @param entity
     */
    private void initSubject(FinancialAccountSets entity) {
        try {
            if (entity.getAccountingStandards() == 0) {
                List<SubjectVo> smallEnterpriseSubject = JSONArray.parseArray(IOUtils.toString(FinancialAccountSetsServiceImpl.class.getResourceAsStream("/subject/small_enterprise.json")), SubjectVo.class);
                this.recursiveSubject(smallEnterpriseSubject, entity);
            } else if(entity.getAccountingStandards() == 1){
                List<SubjectVo> enterpriseSubject = JSONArray.parseArray(IOUtils.toString(FinancialAccountSetsServiceImpl.class.getResourceAsStream("/subject/enterprise.json")), SubjectVo.class);
                this.recursiveSubject(enterpriseSubject, entity);
            } else if(entity.getAccountingStandards() == 2){
                List<SubjectVo> enterpriseSubject = JSONArray.parseArray(IOUtils.toString(FinancialAccountSetsServiceImpl.class.getResourceAsStream("/subject/tech_enterprise.json")), SubjectVo.class);
                this.recursiveSubject(enterpriseSubject, entity);
            } else{
                List<SubjectVo> enterpriseSubject = JSONArray.parseArray(IOUtils.toString(FinancialAccountSetsServiceImpl.class.getResourceAsStream("/subject/guarantee_enterprise.json")), SubjectVo.class);
                this.recursiveSubject(enterpriseSubject, entity);
            }
        } catch (Exception e) {
            log.error("加载默认科目失败....", e);
            throw new ServiceException("初始化默认科目失败");
        }
    }

    private void recursiveSubject(List<SubjectVo> subjects, FinancialAccountSets entity) {
        subjects.forEach(subject -> {
            subject.setAccountSetsId(entity.getId());

            if(entity.getAccountingStandards()==1 && subject.getLevel()==1 && null == subject.getParentId()){
                subject.setParentId(0);
            }
        });
        subjects.forEach(subject -> {
            subjectMapper.insert(subject);

            if (subject.getChildren() != null && subject.getChildren().size() > 0) {
                subject.getChildren().forEach(subject1 -> subject1.setParentId(subject.getId()));
                this.recursiveSubject(subject.getChildren(), entity);
            }
        });
    }

    /**
     * 初始化凭证字
     *
     * @param entity
     */
    private void initVoucherWord(FinancialAccountSets entity) {
        FinancialVoucherWord word = new FinancialVoucherWord();
        word.setWord("记");
        word.setPrintTitle("记账凭证");
        word.setIsDefault(true);
        word.setAccountSetsId(entity.getId());
        voucherWordMapper.insert(word);

        word.setWord("收");
        word.setPrintTitle("收款凭证");
        word.setIsDefault(false);
        voucherWordMapper.insert(word);

        word.setWord("付");
        word.setPrintTitle("付款凭证");
        voucherWordMapper.insert(word);

        word.setWord("转");
        word.setPrintTitle("转账凭证");
        voucherWordMapper.insert(word);
    }

    /**
     * 初始化本币
     *
     * @param entity
     */
    private void initCurrency(FinancialAccountSets entity) {
        FinancialCurrency currency = new FinancialCurrency();
        currency.setCode("RMB");
        currency.setName("人民币");
        currency.setExchangeRate(1d);
        currency.setLocalCurrency(true);
        currency.setAccountSetsId(entity.getId());
        currencyMapper.insert(currency);
    }

    /**
     * 初始报表模板
     *
     * @param entity
     */
    private void initReport(FinancialAccountSets entity) throws IOException {
        String[] fileNames = {"lrb.json", "xjllb.json", "zcfzb.json"};
        for (String fileName : fileNames) {
            JSONObject lrb = JSONObject.parseObject(IOUtils.toString(FinancialAccountSetsServiceImpl.class.getResourceAsStream("/report/standard" + entity.getAccountingStandards() + "/" + fileName)));
            FinancialReportTemplate rt = new FinancialReportTemplate();
            rt.setName(lrb.getString("name"));
            rt.setAccountSetsId(entity.getId());
            rt.setTemplateKey(lrb.getString("templateKey"));
            rt.setType(lrb.getInteger("type"));
            //新增报表
            reportTemplateMapper.insert(rt);

            JSONArray items = lrb.getJSONArray("items");
            if (items != null && !items.isEmpty()) {
                Map<Integer, JSONObject> objectMap = items.stream().collect(Collectors.toMap(map -> ((JSONObject) map).getInteger("id"), map -> (JSONObject) map));
                for (int i = 0, size = items.size(); i < size; i++) {
                    JSONObject item = items.getJSONObject(i);
                    FinancialReportTemplateItems rti = new FinancialReportTemplateItems();
                    rti.setTemplateId(rt.getId());
                    rti.setTitle(item.getString("title"));
                    Integer parentId = item.getInteger("parentId");
                    if (parentId != null) {
                        rti.setParentId(objectMap.get(parentId).getInteger("pid"));
                    }
                    rti.setLineNum(item.getInteger("lineNum"));
                    rti.setType(item.getInteger("type"));
                    rti.setSources(item.getInteger("sources"));
                    rti.setLevel(item.getInteger("level"));
                    rti.setIsBolder(item.getBoolean("isBolder"));
                    rti.setIsFolding(item.getBoolean("isFolding"));
                    rti.setIsClassified(item.getBoolean("isClassified"));
                    rti.setPos(item.getInteger("pos"));

                    reportTemplateItemsMapper.insert(rti);
                    item.put("pid", rti.getId());
                    //公式
                    JSONArray formulas = item.getJSONArray("formulas");
                    if (formulas != null && formulas.size() > 0) {
                        for (int j = 0, size2 = formulas.size(); j < size2; j++) {
                            FinancialReportTemplateItemsFormula formula = formulas.getObject(j, FinancialReportTemplateItemsFormula.class);
                            formula.setTemplateId(rt.getId());
                            formula.setTemplateItemsId(rti.getId());
                            formula.setAccountSetsId(entity.getId());
                            if (item.getInteger("sources") == 1) {
                                formula.setFromTag(objectMap.get(Integer.parseInt(formula.getFromTag())).getString("pid"));
                            } else {
                                boolean hasCode = formulas.getJSONObject(j).containsKey("subjectCode");

                                LambdaQueryWrapper<FinancialSubject> qw = Wrappers.lambdaQuery();
                                qw.eq(FinancialSubject::getAccountSetsId, entity.getId());
                                if (hasCode) {
                                    qw.eq(FinancialSubject::getCode, formulas.getJSONObject(j).getString("subjectCode"));
                                } else {
                                    FinancialSubject subject = subjectMapper.selectById(Integer.parseInt(formula.getFromTag()));
                                    qw.eq(FinancialSubject::getCode, subject.getCode());
                                }
                                qw.select(FinancialSubject::getId, FinancialSubject::getCode);
                                FinancialSubject one = subjectMapper.selectOne(qw);

                                formula.setFromTag(one.getId().toString());
                                if (!hasCode) {
                                    formulas.getJSONObject(j).put("subjectCode", one.getCode());
                                }
                            }

                            reportTemplateItemsFormulaMapper.insert(formula);
                        }
                    }
                }
            }
            log.info("最终输出{}:{}", fileName, lrb.toJSONString());
        }
    }


    private void updateVoucherDetailCode(Integer accountSetsId, Map<String, String> codeMap) {
        LambdaQueryWrapper<FinancialVoucherDetails> dqw = Wrappers.lambdaQuery();
        dqw.eq(FinancialVoucherDetails::getAccountSetsId, accountSetsId);
        List<FinancialVoucherDetails> dList = voucherDetailsService.list(dqw);
        dList.forEach(d -> {
            String name = StringUtils.trim(d.getSubjectName());
            String scode = StringUtils.trim(d.getSubjectCode());

            //获取code部分
            String[] codeSp = name.split("-", 2);
            String code = codeSp[0];
            if (code.contains("_")) {
                String[] codeAc = code.split("_", 2);
                code = (codeMap.containsKey(codeAc[0]) ? codeMap.get(codeAc[0]) : codeAc[0]) + "_" + codeAc[1];
            } else if (codeMap.containsKey(code)) {
                code = codeMap.get(code);
            }

            d.setSubjectName(code + "-" + codeSp[1]);

            if (scode.contains("_")) {
                String[] codeAc = code.split("_", 2);
                scode = (codeMap.containsKey(codeAc[0]) ? codeMap.get(codeAc[0]) : codeAc[0]) + "_" + codeAc[1];
            } else if (codeMap.containsKey(scode)) {
                scode = codeMap.get(scode);
            }
            d.setSubjectCode(scode);
        });
        if (!dList.isEmpty()) {
            voucherDetailsService.updateBatchById(dList);
        }
    }

    private void updateVoucherTempDetailCode(Integer accountSetsId, Map<String, String> codeMap) {
        LambdaQueryWrapper<FinancialVoucherTemplateDetails> dqw = Wrappers.lambdaQuery();
        dqw.eq(FinancialVoucherTemplateDetails::getAccountSetsId, accountSetsId);
        List<FinancialVoucherTemplateDetails> dList = voucherTemplateDetailsService.list(dqw);
        dList.forEach(d -> {
            String name = StringUtils.trim(d.getSubjectName());
            String scode = StringUtils.trim(d.getSubjectCode());

            //获取code部分
            String[] codeSp = name.split("-", 2);
            String code = codeSp[0];
            if (code.contains("_")) {
                String[] codeAc = code.split("_", 2);
                code = (codeMap.containsKey(codeAc[0]) ? codeMap.get(codeAc[0]) : codeAc[0]) + "_" + codeAc[1];
            } else if (codeMap.containsKey(code)) {
                code = codeMap.get(code);
            }
            d.setSubjectName(code + "-" + codeSp[1]);

            if (scode.contains("_")) {
                String[] codeAc = code.split("_", 2);
                scode = (codeMap.containsKey(codeAc[0]) ? codeMap.get(codeAc[0]) : codeAc[0]) + "_" + codeAc[1];
            } else if (codeMap.containsKey(scode)) {
                scode = codeMap.get(scode);
            }
            d.setSubjectCode(scode);
        });
        if (!dList.isEmpty()) {
            voucherTemplateDetailsService.updateBatchById(dList);
        }
    }

    private String updateCode(String code, List<Integer> model, List<Integer> newModel) {
        List<String> codeSp = new ArrayList<>();
        int start = 0;
        for (Integer len : model) {
            if (code.length() <= start) {
                break;
            }
            codeSp.add(code.substring(start, Math.min(code.length(), start + len)));
            start += len;
        }

        for (int i = 0; i < codeSp.size(); i++) {
            String item = codeSp.get(i);
            int len = newModel.get(i);
            if (item != null) {
                if (item.length() < len) {
                    item = StringUtils.leftPad(Integer.parseInt(item) + "", newModel.get(i), "0");
                } else if (item.length() > len) {
                    item = item.substring(item.length() - len);
                }
                codeSp.set(i, item);
            }
        }

        return StringUtils.join(codeSp, "");
    }

    /**
     * 账套添加、修改。新增账套角色权限
     */
    private void addUserIds(FinancialAccountSets entity,List<Map<String,Object>> userIds){
        if(null != entity.getAccountings()){
            entity.getAccountings().forEach(row ->{
                Map<String,Object> map = new LinkedHashMap<>();
                map.put("userId",row);
                map.put("roleKey","accounting");
                userIds.add(map);
            });
        }
        if(null != entity.getAccountingManagers()){
            entity.getAccountingManagers().forEach(row ->{
                Map<String,Object> map = new LinkedHashMap<>();
                map.put("userId",row);
                map.put("roleKey","accountingManager");
                userIds.add(map);
            });
        }
        if(null != entity.getOnlyViews()){
            entity.getOnlyViews().forEach(row ->{
                Map<String,Object> map = new LinkedHashMap<>();
                map.put("userId",row);
                map.put("roleKey","onlyView");
                userIds.add(map);
            });
        }
        //删除账套关联信息
        userAccountSetsMapper.deleteByAccountSetsId(entity.getId());
        if(userIds.size()>0){
            //加入关联表
            for (Map<String,Object> map : userIds) {
                FinancialUserAccountSets userAccountSets = new FinancialUserAccountSets();
                userAccountSets.setAccountSetsId(entity.getId());
                userAccountSets.setUserId((Long) map.get("userId"));
                userAccountSets.setRoleType(map.get("roleKey").toString());
                userAccountSets.setCreateUser(entity.getCreatorId());//创建人
                userAccountSetsMapper.insert(userAccountSets);
            }
        }
    }
}




