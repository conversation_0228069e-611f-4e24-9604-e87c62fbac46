package com.ruoyi.financial.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.redis.RedisConstants;
import com.ruoyi.common.core.redis.RedisPublisher;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.financial.controller.JsonResult;
import com.ruoyi.financial.domain.*;
import com.ruoyi.financial.excel.DetailAccountsExcel;
import com.ruoyi.financial.excel.GeneralLedgerExcel;
import com.ruoyi.financial.excel.JournalAccountsExcel;
import com.ruoyi.financial.excel.VoucherExcel;
import com.ruoyi.financial.mapper.*;
import com.ruoyi.financial.service.IFinancialSubjectService;
import com.ruoyi.financial.service.IFinancialVoucherService;
import com.ruoyi.financial.utils.DoubleComparer;
import com.ruoyi.financial.utils.DoubleValueUtil;
import com.ruoyi.financial.vo.BalanceVo;
import com.ruoyi.financial.vo.FinancialUserVo;
import com.ruoyi.financial.vo.VoucherDetailVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : ${PACKAGE_NAME}</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年07月30日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
@Slf4j
@Service
@AllArgsConstructor
public class FinancialVoucherServiceImpl extends ServiceImpl<FinancialVoucherMapper, FinancialVoucher> implements IFinancialVoucherService {

    private FinancialVoucherDetailsMapper detailsMapper;

    private FinancialSubjectMapper subjectMapper;

    private FinancialVoucherDetailsAuxiliaryMapper voucherDetailsAuxiliaryMapper;

    private IFinancialSubjectService subjectService;

    private FinancialCheckoutMapper checkoutMapper;

    private FinancialAccountSetsMapper accountSetsMapper;

    @Autowired
    private RedisPublisher redisPublisher;

    @Override
    public int batchInsert(List<FinancialVoucher> list) {
        list.forEach(e->{
            e.setSource("S");
        });
        return baseMapper.batchInsert(list);
    }

    @Override
    public IPage<FinancialVoucher> listVoucher(Map<String, String> params) {
        QueryWrapper qw = new QueryWrapper<>();
        qw.eq("account_sets_id", params.get("account_sets_id"));
        if(StringUtils.isNotBlank(params.get("start_time")) && StringUtils.isNotBlank(params.get("end_time"))){
            qw.ge("voucher_date", params.get("start_time"));
            qw.le("voucher_date", params.get("end_time"));
        }
        if("1".equals(params.get("voucher_state"))){
            qw.eq("valid", 1);
            qw.isNull("audit_date");
        }else if("2".equals(params.get("voucher_state"))){
            qw.eq("valid", 1);
            qw.isNotNull("audit_date");
        }else if("3".equals(params.get("voucher_state"))){
            qw.eq("valid", 0);
        }
        Page<FinancialVoucher> pageable = new Page<>(Long.parseLong(params.get("page")), Long.parseLong(params.getOrDefault("pageSize", "20")));
        return this.baseMapper.listVoucher(pageable, qw);
    }

    @Override
    public List<FinancialVoucher> list(Wrapper<FinancialVoucher> queryWrapper) {
        return baseMapper.selectVoucher(queryWrapper);
    }

    @Override
    public IPage<FinancialVoucher> page(Page<FinancialVoucher> page, Wrapper<FinancialVoucher> queryWrapper) {
        return baseMapper.selectVoucher(page, queryWrapper);
    }

    @Override
    public int loadCode(Integer accountSetsId, String word, Date currentAccountDate) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentAccountDate);
        Integer code = baseMapper.selectMaxCode(accountSetsId, word, calendar.get(Calendar.YEAR), calendar.get(Calendar.MONDAY) + 1);
        return code == null ? 1 : code;
    }

    @Override
    public List<VoucherDetailVo> accountBookDetails(Integer accountSetsId, Integer subjectId, Date startTime, Date endTime, String subjectCode, Boolean showNumPrice) {
        //跨月查询展示问题
        List<String> dataList = DateUtils.getTimeRange(DateUtils.format(startTime, DateUtils.DateFormat.YYYY_MM_DD), DateUtils.format(endTime, DateUtils.DateFormat.YYYY_MM_DD), DateUtils.DateFormat.YYYY_MM_DD);
        Map<String, List<String>> monthMap = dataList.stream().map(e -> {
            return e.substring(0, 7);
        }).collect(Collectors.groupingBy(String::valueOf));

        List<VoucherDetailVo> list = new ArrayList<>();
        TreeSet<String> yearMonthSet = new TreeSet<>(monthMap.keySet());

        int i=0;
        for (String yearMonth:yearMonthSet) {
            Date startDate = DateUtils.parse(yearMonth+"-01", DateUtils.DateFormat.YYYY_MM_DD);
            int startIntervalDaysActual = DateUtils.getIntervalDaysActual(startTime, startDate);
            if(startIntervalDaysActual>0){
                startDate = startTime;
            }
            String monthEndDate = DateUtils.getMonthEndDate(yearMonth + "-01");
            Date endDate = DateUtils.parseDate(monthEndDate);
            int endIntervalDaysActual = DateUtils.getIntervalDaysActual(endTime, endDate);
            if(endIntervalDaysActual<0){
                endDate = endTime;
            }

            //System.out.println("开始时间："+DateUtils.format(startDate, DateUtils.DateFormat.YYYY_MM_DD)+"结束时间："+DateUtils.format(endDate, DateUtils.DateFormat.YYYY_MM_DD));
            List<VoucherDetailVo> detailVoList = this.summary(accountSetsId, subjectId, startDate, endDate, subjectCode, showNumPrice, true);
            if(i>0){
                detailVoList.remove(0);
            }
            list.addAll(detailVoList);
            i++;
        }
        return list;
    }

    @Override
    public List accountGeneralLedger(Integer accountSetsId, Date startTime, Date endTime, Boolean showNumPrice) {
        List<FinancialSubject> subjectList = subjectService.accountBookList(accountSetsId, startTime, endTime, showNumPrice);
        List<Map<String, Object>> data = new ArrayList<>(subjectList.size());
        subjectList.forEach(subject -> {
            List<VoucherDetailVo> summary = this.summary(accountSetsId, subject.getId(), startTime, endTime, subject.getCode(), showNumPrice, false);
            if(null != summary){
                Map<String, Object> item = new HashMap<>(2);
                item.put("subject", subject);
                item.put("summary", summary);
                data.add(item);
            }
        });
        return data;
    }

    /**
     * 获取期间结转科目总和
     *
     * @param accountSetsId
     * @param years
     * @param month
     * @param code
     * @return
     */
    @Override
    public Map<String, FinancialVoucherDetails> carryForwardMoney(Integer accountSetsId, Integer years, Integer month, String[] code) {
        Map<String, FinancialVoucherDetails> msv = new HashMap<>(code.length);
        for (String s : code) {
            FinancialVoucherDetails details = this.detailsMapper.selectCarryForwardMoney(accountSetsId, years, month, s + "%");
            msv.put(s, details);
        }
        return msv;
    }

    /**
     * 核算项目明细账
     *
     * @param accountSetsId
     * @param auxiliaryId
     * @param startTime
     * @param endTime
     * @param auxiliaryItemId
     * @param showNumPrice
     * @return
     */
    @Override
    public List<VoucherDetailVo> auxiliaryDetails(Integer accountSetsId, Integer auxiliaryId, Date startTime, Date endTime, Integer auxiliaryItemId, Boolean showNumPrice) {
        List<VoucherDetailVo> data = new ArrayList<>();
        //期初
        VoucherDetailVo startVo = getAuxiliaryStart(accountSetsId, auxiliaryId, startTime, endTime, auxiliaryItemId);
        data.add(startVo);

        //明细
        List<VoucherDetailVo> detailVos = voucherDetailsAuxiliaryMapper.selectAccountBookDetails(accountSetsId, auxiliaryId, startTime, endTime, auxiliaryItemId);
        //计算每次余额
        for (VoucherDetailVo vo : detailVos) {
            double b = 0d;
            switch (vo.getBalanceDirection()) {
                case "借":
                    b = DoubleValueUtil.getNotNullVal(vo.getDebitAmount()) - DoubleValueUtil.getNotNullVal(vo.getCreditAmount());
                    break;
                case "贷":
                    b = DoubleValueUtil.getNotNullVal(vo.getCreditAmount()) - DoubleValueUtil.getNotNullVal(vo.getDebitAmount());
                    break;
            }
            vo.setBalance(startVo.getBalance() + b);
            startVo = vo;
        }

        data.addAll(detailVos);
        //本期统计
        VoucherDetailVo currentVo = getAuxiliaryCurrent(accountSetsId, auxiliaryId, startTime, endTime, auxiliaryItemId, data.get(0));
        //年度统计
        VoucherDetailVo yearVo = getAuxiliaryYear(accountSetsId, auxiliaryId, startTime, endTime, auxiliaryItemId);
        data.add(currentVo);
        data.add(yearVo);
        return data;
    }

    /**
     * 获取期初统计
     *
     * @param accountSetsId
     * @param auxiliaryId
     * @param startTime
     * @param endTime
     * @param auxiliaryItemId
     * @return
     */
    private VoucherDetailVo getAuxiliaryStart(Integer accountSetsId, Integer auxiliaryId, Date startTime, Date endTime, Integer auxiliaryItemId) {
        List<VoucherDetailVo> startVos = voucherDetailsAuxiliaryMapper.selectAccountBookStatistical(accountSetsId, auxiliaryId, null, startTime, auxiliaryItemId);
        VoucherDetailVo startVo = new VoucherDetailVo();
        startVo.setSummary("期初余额");
        startVo.setVoucherDate(endTime);
        startVo.setBalance(0d);
        startVo.setBalanceDirection("平");

        if (startVos != null && !startVos.isEmpty()) {
            if (startVos.get(0) != null) {
                VoucherDetailVo vo = startVos.get(0);
                startVo.setBalanceDirection(vo.getBalanceDirection());
                switch (vo.getBalanceDirection()) {
                    case "借":
                        startVo.setBalance(DoubleValueUtil.getNotNullVal(vo.getDebitAmount()) - DoubleValueUtil.getNotNullVal(vo.getCreditAmount()));
                        break;
                    case "贷":
                        startVo.setBalance(DoubleValueUtil.getNotNullVal(vo.getCreditAmount()) - DoubleValueUtil.getNotNullVal(vo.getDebitAmount()));
                        break;
                }
                startVo.setCreditAmount(vo.getCreditAmount());
                startVo.setDebitAmount(vo.getDebitAmount());
            }
        }
        return startVo;
    }

    /**
     * 获取本期统计
     *
     * @param accountSetsId
     * @param auxiliaryId
     * @param startTime
     * @param endTime
     * @param auxiliaryItemId
     * @return
     */
    private VoucherDetailVo getAuxiliaryCurrent(Integer accountSetsId, Integer auxiliaryId, Date startTime, Date endTime, Integer auxiliaryItemId, VoucherDetailVo startVo) {
        List<VoucherDetailVo> vos = voucherDetailsAuxiliaryMapper.selectAccountBookStatistical(accountSetsId, auxiliaryId, startTime, endTime, auxiliaryItemId);
        VoucherDetailVo currentVo = new VoucherDetailVo();
        currentVo.setSummary("本期合计");
        currentVo.setVoucherDate(endTime);
        currentVo.setBalance(0d);
        currentVo.setBalanceDirection("平");

        if (vos != null && !vos.isEmpty() && vos.get(0) != null) {
            VoucherDetailVo vo = vos.get(0);
            //加上期初余额
            switch (vo.getBalanceDirection()) {
                case "借":
                    currentVo.setBalance(DoubleValueUtil.getNotNullVal(vo.getDebitAmount()) - DoubleValueUtil.getNotNullVal(vo.getCreditAmount()) + startVo.getBalance());
                    break;
                case "贷":
                    currentVo.setBalance(DoubleValueUtil.getNotNullVal(vo.getCreditAmount()) - DoubleValueUtil.getNotNullVal(vo.getDebitAmount()) + startVo.getBalance());
                    break;
            }
            currentVo.setCreditAmount(vo.getCreditAmount());
            currentVo.setDebitAmount(vo.getDebitAmount());
            if (!DoubleComparer.considerEqual(currentVo.getBalance(), 0d)) {
                currentVo.setBalanceDirection(vo.getBalanceDirection());
            }
        }
        return currentVo;
    }

    /**
     * 获取年度统计
     *
     * @param accountSetsId
     * @param auxiliaryId
     * @param startTime
     * @param endTime
     * @param auxiliaryItemId
     * @return
     */
    private VoucherDetailVo getAuxiliaryYear(Integer accountSetsId, Integer auxiliaryId, Date startTime, Date endTime, Integer auxiliaryItemId) {
        List<VoucherDetailVo> startVos = voucherDetailsAuxiliaryMapper.selectAccountBookStatistical(accountSetsId, auxiliaryId, null, DateUtils.getYearBegin(startTime), auxiliaryItemId);
        VoucherDetailVo startVo = new VoucherDetailVo();
        startVo.setBalance(0d);
        if (startVos != null && !startVos.isEmpty() && startVos.get(0) != null) {
            startVo = startVos.get(0);
            double b = 0d;
            switch (startVo.getBalanceDirection()) {
                case "借":
                    b = DoubleValueUtil.getNotNullVal(startVo.getDebitAmount()) - DoubleValueUtil.getNotNullVal(startVo.getCreditAmount());
                    break;
                case "贷":
                    b = DoubleValueUtil.getNotNullVal(startVo.getCreditAmount()) - DoubleValueUtil.getNotNullVal(startVo.getDebitAmount());
                    break;
            }
            startVo.setBalance(b);
        }
        List<VoucherDetailVo> vos = voucherDetailsAuxiliaryMapper.selectAccountBookStatistical(accountSetsId, auxiliaryId, DateUtils.getYearBegin(startTime), endTime, auxiliaryItemId);
        VoucherDetailVo yearVo = new VoucherDetailVo();
        yearVo.setSummary("本年累计");
        yearVo.setVoucherDate(endTime);
        yearVo.setBalance(0d);
        yearVo.setBalanceDirection("平");

        if (vos != null && !vos.isEmpty() && vos.get(0) != null) {
            VoucherDetailVo vo = vos.get(0);
            //加上期初余额
            double b = 0d;
            switch (vo.getBalanceDirection()) {
                case "借":
                    b = DoubleValueUtil.getNotNullVal(vo.getDebitAmount()) - DoubleValueUtil.getNotNullVal(vo.getCreditAmount());
                    break;
                case "贷":
                    b = DoubleValueUtil.getNotNullVal(vo.getCreditAmount()) - DoubleValueUtil.getNotNullVal(vo.getDebitAmount());
                    break;
            }
            yearVo.setBalance(b + startVo.getBalance());
            yearVo.setCreditAmount(vo.getCreditAmount());
            yearVo.setDebitAmount(vo.getDebitAmount());

            if (!DoubleComparer.considerEqual(yearVo.getBalance(), 0d)) {
                yearVo.setBalanceDirection(vo.getBalanceDirection());
            }
        }
        return yearVo;
    }

    /**
     * 本期核算项目
     *
     * @param accountSetsId
     * @return
     */
    @Override
    public List<FinancialAccountingCategoryDetails> auxiliaryList(Integer accountSetsId, Integer auxiliaryId) {
        return voucherDetailsAuxiliaryMapper.selectByAccountBlock(accountSetsId, auxiliaryId);
    }

    /**
     * 辅助核算项目余额
     *
     * @param accountSetsId
     * @param auxiliaryId
     * @param startTime
     * @param endTime
     * @param showNumPrice
     * @return
     */
    @Override
    public List<BalanceVo> auxiliaryBalance(Integer accountSetsId, Integer auxiliaryId, Date startTime, Date endTime, Boolean showNumPrice) {
        //所有辅助项目
        List<FinancialAccountingCategoryDetails> categoryDetails = voucherDetailsAuxiliaryMapper.selectByAccountBlock(accountSetsId, auxiliaryId);
        //转换成待计算的辅助项目
        Map<Integer, BalanceVo> maps = new HashMap<>(categoryDetails.size());
        categoryDetails.forEach(details -> {
            BalanceVo vo = new BalanceVo();
            vo.setName(details.getName());
            vo.setCode(details.getCode());
            vo.setAuxiliaryId(details.getId());
            maps.put(details.getId(), vo);
        });

        //期初
        List<VoucherDetailVo> startVos = voucherDetailsAuxiliaryMapper.selectAccountBookStatistical(accountSetsId, auxiliaryId, null, startTime, null);
        startVos.forEach(startVo -> {
            if (maps.containsKey(startVo.getDetailsId())) {
                BalanceVo vo = maps.get(startVo.getDetailsId());
                vo.setBalanceDirection(startVo.getBalanceDirection());
                vo.setBeginningBalance(DoubleValueUtil.getNotNullVal(startVo.getDebitAmount(), startVo.getCreditAmount()));
            }
        });

        //本期
        List<VoucherDetailVo> currentVos = voucherDetailsAuxiliaryMapper.selectAccountBookStatistical(accountSetsId, auxiliaryId, startTime, endTime, null);
        currentVos.forEach(currentVo -> {
            if (maps.containsKey(currentVo.getDetailsId())) {
                BalanceVo vo = maps.get(currentVo.getDetailsId());
                vo.setBalanceDirection(currentVo.getBalanceDirection());
                vo.setCurrentDebitAmount(currentVo.getDebitAmount());
                vo.setCurrentCreditAmount(currentVo.getCreditAmount());
            }
        });

        //计算期末余额
        BalanceVo aCombined = new BalanceVo();
        aCombined.setName("合计");

        for (BalanceVo balanceVo : maps.values()) {
            double endingData;
            if (balanceVo.getBalanceDirection() == null) {
                continue;
            }
            switch (balanceVo.getBalanceDirection()) {
                case "借":
                    endingData = DoubleValueUtil.getNotNullVal(balanceVo.getBeginningDebitBalance()) - DoubleValueUtil.getNotNullVal(balanceVo.getBeginningCreditBalance())
                            + DoubleValueUtil.getNotNullVal(balanceVo.getCurrentDebitAmount()) - DoubleValueUtil.getNotNullVal(balanceVo.getCurrentCreditAmount());
                    break;
                default:
                    endingData = DoubleValueUtil.getNotNullVal(balanceVo.getBeginningCreditBalance()) - DoubleValueUtil.getNotNullVal(balanceVo.getBeginningDebitBalance())
                            + DoubleValueUtil.getNotNullVal(balanceVo.getCurrentCreditAmount()) - DoubleValueUtil.getNotNullVal(balanceVo.getCurrentDebitAmount());
                    break;
            }

            balanceVo.setEndingActiveBalance(endingData);

            aCombined.setBeginningCreditBalance(balanceVo.getBeginningCreditBalance());
            aCombined.setBeginningDebitBalance(balanceVo.getBeginningDebitBalance());
            aCombined.setCurrentCreditAmount(balanceVo.getCurrentCreditAmount());
            aCombined.setCurrentDebitAmount(balanceVo.getCurrentDebitAmount());
            aCombined.setEndingDebitBalance(balanceVo.getEndingDebitBalance());
            aCombined.setEndingCreditBalance(balanceVo.getEndingCreditBalance());
        }

        List<BalanceVo> collect = maps.values().stream().sorted(Comparator.comparing(BalanceVo::getCode)).collect(Collectors.toList());

        if (collect.size() > 0) {
            collect = collect.stream().filter(vo ->
                    (vo.getBeginningBalance() != null && vo.getBeginningBalance() != 0) ||
                            (vo.getEndingBalance() != null && vo.getEndingBalance() != 0) ||
                            (vo.getCurrentDebitAmount() != null && vo.getCurrentDebitAmount() != 0) ||
                            (vo.getCurrentCreditAmount() != null && vo.getCurrentCreditAmount() != 0)
            ).collect(Collectors.toList());
            collect.add(aCombined);
        }

        return collect;
    }

    /**
     * 首页收入利润图表数据
     *
     * @param accountSetsId
     * @param year
     * @return
     */
    @Override
    public List<Map<String, Object>> getHomeReport(Integer accountSetsId, Integer year) {
        return baseMapper.selectHomeReport(accountSetsId, year);
    }

    /**
     * 首页费用数据
     *
     * @param accountSetsId
     * @param year
     * @param month
     * @return
     */
    @Override
    public List<Map<String, Object>> getCostReport(Integer accountSetsId, int year, int month) {
        return baseMapper.selectHomeCostReport(accountSetsId, year, month);
    }

    /**
     * 首页现金数据
     *
     * @param accountSetsId
     * @param year
     * @param month
     * @return
     */
    @Override
    public List<Map<String, Object>> getCashReport(Integer accountSetsId, int year, int month) {
        return baseMapper.selectHomeCashReport(accountSetsId, year, month);
    }

    /**
     * 断号整理
     *
     * @param accountSetsId
     * @param startTime
     * @param endTime
     */
    @Override
    @Transactional
    public void finishingOffNo(Integer accountSetsId, String startTime, String endTime) {
        this.checkCheckOut(accountSetsId, startTime, endTime);

        List<Map<String, Object>> data = this.baseMapper.selectBrokenData(accountSetsId, startTime, endTime);
        //过滤出没有连续的凭证字类别
        List<Map<String, Object>> collect = data.stream().filter(map -> !map.get("total").equals(((Integer) map.get("code")).longValue())).collect(Collectors.toList());
        if (!collect.isEmpty()) {
            List<FinancialVoucher> updateVouchers = new ArrayList<>();
            collect.forEach(it -> {
                String word = (String) it.get("word");
                LambdaQueryWrapper<FinancialVoucher> qw = Wrappers.lambdaQuery();
                qw.eq(FinancialVoucher::getWord, word);
                qw.eq(FinancialVoucher::getAccountSetsId, accountSetsId);
                qw.ge(FinancialVoucher::getVoucherDate, startTime);
                qw.le(FinancialVoucher::getVoucherDate, endTime);
                qw.orderByAsc(FinancialVoucher::getCreateDate);
                List<FinancialVoucher> vouchers = this.baseMapper.selectList(qw);

                //重置 code
                int code = 1;
                for (FinancialVoucher voucher : vouchers) {
                    voucher.setCode(code);
                    code++;
                }
                updateVouchers.addAll(vouchers);
            });

            this.updateBatchById(updateVouchers);
        }
    }

    /**
     * 批量删除
     *
     * @param accountSetsId
     * @param checked
     */
    @Override
    public void batchDelete(Integer accountSetsId, Integer[] checked, String startTime, String endTime) {
        this.checkCheckOut(accountSetsId, startTime, endTime);

        LambdaQueryWrapper<FinancialVoucher> qw = Wrappers.lambdaQuery();
        qw.eq(FinancialVoucher::getAccountSetsId, accountSetsId);
        qw.ge(FinancialVoucher::getVoucherDate, startTime);
        qw.le(FinancialVoucher::getVoucherDate, endTime);
        qw.in(FinancialVoucher::getId, Arrays.asList(checked));

        this.baseMapper.delete(qw);
    }

    /**
     * 根据当前 Id 获取上一条 ID
     *
     * @param accountSetsId
     * @param currentId
     * @return
     */
    @Override
    public Integer getBeforeId(Integer accountSetsId, Integer currentId, String voucherDate) {
        LocalDate localDate = LocalDate.parse(voucherDate);
        int year = localDate.getYear();
        int month = localDate.getMonthValue();
        return this.baseMapper.selectBeforeId(accountSetsId, currentId, year, month);
    }

    /**
     * 根据当前 Id 获取下一条 ID
     *
     * @param accountSetsId
     * @param currentId
     * @return
     */
    @Override
    public Integer getNextId(Integer accountSetsId, Integer currentId, String voucherDate) {
        LocalDate localDate = LocalDate.parse(voucherDate);
        int year = localDate.getYear();
        int month = localDate.getMonthValue();

        return this.baseMapper.selectNextId(accountSetsId, currentId, year, month);
    }

    /**
     * 获取最近使用的摘要
     *
     * @param accountSetsId
     * @return
     */
    @Override
    public List<String> getTopSummary(Integer accountSetsId) {
        return this.detailsMapper.selectTopSummary(accountSetsId);
    }

    /**
     * 审核
     *
     * @param accountSetsId
     * @param checked
     * @param year
     * @param month
     */
    @Override
    public void audit(Integer accountSetsId, Integer[] checked, FinancialUserVo userVo, String year, String month) {
        this.checkCheckOut(accountSetsId, year, month);
        LambdaQueryWrapper<FinancialVoucher> qw = Wrappers.lambdaQuery();
        qw.eq(FinancialVoucher::getAccountSetsId, accountSetsId);
        qw.eq(FinancialVoucher::getVoucherYear, year);
        qw.eq(FinancialVoucher::getVoucherMonth, month);
        qw.in(FinancialVoucher::getId, Arrays.asList(checked));
        qw.isNull(FinancialVoucher::getAuditMemberId);
        List<FinancialVoucher> vouchers = baseMapper.selectList(qw);
        if (!vouchers.isEmpty()) {
            vouchers.forEach(voucher -> {
                voucher.setAuditMemberId(userVo.getUserId());
                voucher.setAuditMemberName(userVo.getRealName());
                voucher.setAuditDate(new Date());
            });
            this.updateBatchById(vouchers);
        }
    }

    /**
     * 审核
     *
     * @param accountSetsId
     * @param checked
     * @param startTime
     * @param endTime
     */
    @Override
    public void batchAudit(Integer accountSetsId, Integer[] checked, FinancialUserVo userVo, String startTime, String endTime) {
        List<String> dataList = DateUtils.getTimeRange(startTime, endTime, DateUtils.DateFormat.YYYY_MM_DD);
        Map<String, List<String>> monthMap = dataList.stream().map(e -> {
            return e.substring(0, 7);
        }).collect(Collectors.groupingBy(String::valueOf));

        TreeSet<String> yearMonthSet = new TreeSet<>(monthMap.keySet());
        for (String yearMonth:yearMonthSet) {
            String[] split = yearMonth.split("-");
            this.audit(accountSetsId, checked, userVo, split[0], split[1]);
        }
    }

    /**
     * 反审核
     *
     * @param accountSetsId
     * @param checked
     * @param year
     * @param month
     */
    @Override
    public void cancelAudit(Integer accountSetsId, Integer[] checked, FinancialUserVo currentUser, String year, String month) {
        this.checkCheckOut(accountSetsId, year, month);
        LambdaQueryWrapper<FinancialVoucher> qw = Wrappers.lambdaQuery();
        qw.eq(FinancialVoucher::getAccountSetsId, accountSetsId);
        qw.eq(FinancialVoucher::getVoucherYear, year);
        qw.eq(FinancialVoucher::getVoucherMonth, month);
        qw.in(FinancialVoucher::getId, Arrays.asList(checked));
        qw.isNotNull(FinancialVoucher::getAuditMemberId);
        baseMapper.updateAudit(qw);

    }

    /**
     * 批量导入凭证
     *
     * @param voucherList
     * @return
     */
    @Override
    @Transactional
    public Date importVoucher(List<FinancialVoucher> voucherList, FinancialAccountSets accountSets) {
        List<Date> voucherDateList = new ArrayList<>();
        for (FinancialVoucher voucher : voucherList) {
            this.save(voucher, accountSets, true);
            voucherDateList.add(DateUtils.getMonthEnd(voucher.getVoucherDate()));
        }

        List<Date> collect = voucherDateList.stream().distinct().sorted().collect(Collectors.toList());
        collect.forEach(date -> {
            LambdaQueryWrapper<FinancialCheckout> cqw = Wrappers.lambdaQuery();
            Calendar instance = Calendar.getInstance();
            instance.setTime(date);
            cqw.eq(FinancialCheckout::getAccountSetsId, accountSets.getId());
            cqw.eq(FinancialCheckout::getCheckYear, instance.get(Calendar.YEAR));
            cqw.eq(FinancialCheckout::getCheckMonth, instance.get(Calendar.MONTH) + 1);
            if (this.checkoutMapper.selectCount(cqw) == 0) {
                FinancialCheckout checkout = new FinancialCheckout();
                checkout.setAccountSetsId(accountSets.getId());
                checkout.setCheckYear(instance.get(Calendar.YEAR));
                checkout.setCheckMonth(instance.get(Calendar.MONTH) + 1);
                this.checkoutMapper.insert(checkout);
            }
        });

        Date date = this.baseMapper.selectMaxVoucherDate(accountSets.getId());
        accountSets.setCurrentAccountDate(DateUtils.getMonthEndWithTime(date, false));
        this.accountSetsMapper.updateById(accountSets);
        return date;
    }




    private boolean save(FinancialVoucher entity, FinancialAccountSets accountSets, boolean imports) {
        this.setYearAndMonth(entity);
        this.checkCode(entity);
        if(StringUtils.isBlank(entity.getSource())){
            entity.setSource("S");
        }
        boolean rs = super.save(entity);

        double debitAmount = 0d;
        double creditAmount = 0d;
        if (rs) {
            for (FinancialVoucherDetails vd : entity.getDetails()) {
                vd.setVoucherId(entity.getId());
                vd.setAccountSetsId(entity.getAccountSetsId());
                vd.setCarryForward(entity.getCarryForward());
                if (vd.getDebitAmount() != null) {
                    debitAmount += vd.getDebitAmount();
                }
                if (vd.getCreditAmount() != null) {
                    creditAmount += vd.getCreditAmount();
                }
            }

            detailsMapper.batchInsert(entity.getDetails());

            //存储辅助项目
            for (FinancialVoucherDetails voucherDetails : entity.getDetails()) {
                voucherDetails.setSummary(StringUtils.trim(voucherDetails.getSummary()));
                voucherDetails.setSubjectCode(StringUtils.trim(voucherDetails.getSubjectCode()));
                int size;
                if ((size = voucherDetails.getAuxiliary().size()) > 0) {
                    List<FinancialVoucherDetailsAuxiliary> vdas = new ArrayList<>(size);
                    voucherDetails.getAuxiliary().forEach(acd -> {
                        FinancialVoucherDetailsAuxiliary vda = new FinancialVoucherDetailsAuxiliary();
                        vda.setVoucherDetailsId(voucherDetails.getId());
                        vda.setAccountingCategoryId(acd.getAccountingCategoryId());
                        vda.setAccountingCategoryDetailsId(acd.getId());
                        vdas.add(vda);
                    });

                    voucherDetailsAuxiliaryMapper.batchInsert(vdas);
                }
            }

            //更新总和
            entity.setCreditAmount(creditAmount);
            entity.setDebitAmount(debitAmount);
            baseMapper.updateById(entity);

            if (!imports) {
                // 如果是非当前期间切，当前账套时间在凭证时间之前，需要更改默认期间
                DateFormat df = new SimpleDateFormat("yyyyMM");

                LambdaQueryWrapper<FinancialCheckout> cqw = Wrappers.lambdaQuery();
                cqw.eq(FinancialCheckout::getAccountSetsId, entity.getAccountSetsId());
                cqw.eq(FinancialCheckout::getCheckYear, entity.getVoucherYear());
                cqw.eq(FinancialCheckout::getCheckMonth, entity.getVoucherMonth());
                Integer count = this.checkoutMapper.selectCount(cqw);

                //插入结账月份
                if (count == 0) {
                    FinancialCheckout checkout = new FinancialCheckout();
                    checkout.setAccountSetsId(entity.getAccountSetsId());
                    checkout.setCheckYear(entity.getVoucherYear());
                    checkout.setCheckMonth(entity.getVoucherMonth());
                    checkout.setCheckDate(entity.getVoucherDate());
                    this.checkoutMapper.insert(checkout);
                }

                if (!df.format(accountSets.getCurrentAccountDate()).equals(df.format(entity.getVoucherDate()))
                        && accountSets.getCurrentAccountDate().before(entity.getVoucherDate())
                        && count == 0
                ) {
                    //更新当前账套的当前期间
                    accountSets.setCurrentAccountDate(entity.getVoucherDate());
                    this.accountSetsMapper.updateById(accountSets);
                }
            }

        }

        //发布财务经营分析频道
        publishRedis(entity.getId(), entity.getVoucherDate());
        return rs;
    }

    @Override
    @Transactional
    public boolean save(FinancialVoucher entity) {
        return this.save(entity, this.accountSetsMapper.selectById(entity.getAccountSetsId()), false);
    }

    @Override
    @Transactional
    public boolean update(FinancialVoucher entity, Wrapper<FinancialVoucher> updateWrapper) {
        this.setYearAndMonth(entity);
        this.checkCheckOut(entity.getAccountSetsId(), entity.getVoucherYear().toString(), entity.getVoucherMonth().toString());
        this.checkCode(entity);

        boolean rs = super.update(entity, updateWrapper);

        //删除原有明细
        LambdaQueryWrapper<FinancialVoucherDetails> qw = Wrappers.lambdaQuery();
        qw.eq(FinancialVoucherDetails::getVoucherId, entity.getId());
        detailsMapper.delete(qw);

        double debitAmount = 0d;
        double creditAmount = 0d;
        if (rs) {
            for (FinancialVoucherDetails vd : entity.getDetails()) {
                vd.setVoucherId(entity.getId());
                vd.setAccountSetsId(entity.getAccountSetsId());
                vd.setCarryForward(entity.getCarryForward());
                if (vd.getDebitAmount() != null) {
                    debitAmount += vd.getDebitAmount();
                }
                if (vd.getCreditAmount() != null) {
                    creditAmount += vd.getCreditAmount();
                }
            }

            detailsMapper.batchInsert(entity.getDetails());

            //存储辅助项目
            for (FinancialVoucherDetails voucherDetails : entity.getDetails()) {
                voucherDetails.setSummary(StringUtils.trim(voucherDetails.getSummary()));
                voucherDetails.setSubjectCode(StringUtils.trim(voucherDetails.getSubjectCode()));
                int size;
                if ((size = voucherDetails.getAuxiliary().size()) > 0) {
                    List<FinancialVoucherDetailsAuxiliary> vdas = new ArrayList<>(size);
                    voucherDetails.getAuxiliary().forEach(acd -> {
                        FinancialVoucherDetailsAuxiliary vda = new FinancialVoucherDetailsAuxiliary();
                        vda.setVoucherDetailsId(voucherDetails.getId());
                        vda.setAccountingCategoryId(acd.getAccountingCategoryId());
                        vda.setAccountingCategoryDetailsId(acd.getId());
                        vdas.add(vda);
                    });

                    voucherDetailsAuxiliaryMapper.batchInsert(vdas);
                }
            }

            //更新总和
            entity.setCreditAmount(creditAmount);
            entity.setDebitAmount(debitAmount);
            baseMapper.updateById(entity);
        }
        //发布财务经营分析频道
        publishRedis(entity.getId(), entity.getVoucherDate());
        return rs;
    }

    @Override
    public FinancialVoucher getOne(Wrapper<FinancialVoucher> queryWrapper) {
        List<FinancialVoucher> vouchers = baseMapper.selectVoucher(queryWrapper);
        if (vouchers.size() > 0) {
            return vouchers.get(0);
        }
        return null;
    }

    @Override
    @Transactional
    public boolean remove(Wrapper<FinancialVoucher> wrapper) {
        FinancialVoucher voucher = baseMapper.selectOne(wrapper);
        String voucherDate = DateUtils.dateForStr(voucher.getVoucherDate());
        this.checkCheckOut(voucher.getAccountSetsId(), voucherDate, voucherDate);

        LambdaQueryWrapper<FinancialVoucherDetails> qw = Wrappers.lambdaQuery();
        qw.eq(FinancialVoucherDetails::getVoucherId, voucher.getId());
        detailsMapper.delete(qw);

        boolean result = baseMapper.delete(wrapper) > 0;
        //发布财务经营分析频道
        publishRedis(voucher.getId(), voucher.getVoucherDate());
        return result;
    }

    /**
     * <AUTHOR>
     * @Description 作废凭证
     * @Date 2024/1/17 10:14
     * @Param [wrapper]
     * @return boolean
     **/
    @Override
    @Transactional
    public boolean validVoucher(Wrapper wrapper) {
        FinancialVoucher voucher = baseMapper.selectOne(wrapper);
        String voucherDate = DateUtils.dateForStr(voucher.getVoucherDate());
        this.checkCheckOut(voucher.getAccountSetsId(), voucherDate, voucherDate);

        Boolean result = baseMapper.validVoucher(voucher.getId());
        //发布财务经营分析频道
        publishRedis(voucher.getId(), voucher.getVoucherDate());
        return result;
    }

    /**
     * <AUTHOR>
     * @Description 凭证号重新排序
     * @Date 2024/3/27 17:00
     * @Param [accountSetsId, voucherYearMonth]
     * @return int
     **/
    @Override
    public int reorderCode(Integer accountSetsId, String voucherYearMonth) {
         String[] split = voucherYearMonth.split("-");
         return baseMapper.reorderCode(accountSetsId, split[0], split[1]);
    }

    /**
     * <AUTHOR>
     * @Description 检查是否已结账
     * @Date 2024/9/13 10:53
     * @Param [voucherId]
     * @return boolean
     **/
    @Override
    public boolean checkCheckOut(Integer voucherId) {
        FinancialVoucher financialVoucher = baseMapper.selectById(voucherId);
        try {
            //判断是否已结账
            this.checkCheckOut(financialVoucher.getAccountSetsId(), financialVoucher.getVoucherYear().toString(), financialVoucher.getVoucherMonth().toString());
            return true;
        } catch (ServiceException e) {
            return false;
        }
    }

    /**
     * <AUTHOR>
     * @Description 撤销凭证
     * @Date 2024/9/13 9:33
     * @Param [voucherId]
     * @return java.lang.String
     **/
    @Override
    public JsonResult revokeVoucher(Integer voucherId) {
        //判断是否已结账
        if(this.checkCheckOut(voucherId)){
            //删除凭证
            baseMapper.deleteById(voucherId);
            //删除凭证明细
            detailsMapper.deleteByVoucherId(voucherId);
            return JsonResult.successful();
        }
        return JsonResult.failure();
    }

    /**
     * 导出凭证数据
     * @param params
     * @return
     */
    @Override
    public List<VoucherExcel> exportVoucher(Map<String, String> params) {
        return this.baseMapper.exportVoucher(params);
    }

    private void checkCode(FinancialVoucher entity) {
        LambdaQueryWrapper<FinancialVoucher> qw = Wrappers.lambdaQuery();
        qw.eq(FinancialVoucher::getCode, entity.getCode());
        qw.eq(FinancialVoucher::getWord, entity.getWord());
        qw.eq(FinancialVoucher::getVoucherYear, entity.getVoucherYear());
        qw.eq(FinancialVoucher::getVoucherMonth, entity.getVoucherMonth());
        qw.eq(FinancialVoucher::getAccountSetsId, entity.getAccountSetsId());

        if (entity.getId() != null) {
            qw.ne(FinancialVoucher::getId, entity.getId());
        }

        if (this.count(qw) > 0) {
            throw new ServiceException("亲,凭证字号[" + entity.getWord() + "-" + entity.getCode() + "]已经存在！");
        }
    }

    private void checkCheckOut(Integer accountSetsId, String year, String month) {
        LambdaQueryWrapper<FinancialCheckout> cqw = Wrappers.lambdaQuery();
        cqw.eq(FinancialCheckout::getAccountSetsId, accountSetsId);
        cqw.eq(FinancialCheckout::getCheckYear, year);
        cqw.eq(FinancialCheckout::getCheckMonth, month);
        cqw.eq(FinancialCheckout::getStatus, 2);

        if (checkoutMapper.selectCount(cqw) > 0) {
            throw new ServiceException("亲,期间已结账，不允许操作凭证！");
        }
    }

    private void setYearAndMonth(FinancialVoucher entity) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(entity.getVoucherDate());
        entity.setVoucherYear(calendar.get(Calendar.YEAR));
        entity.setVoucherMonth(calendar.get(Calendar.MONDAY) + 1);

        FinancialAccountSets accountSets = this.accountSetsMapper.selectById(entity.getAccountSetsId());
        Date d = DateUtils.getMonthEnd(accountSets.getEnableDate());
        Date d2 = DateUtils.getMonthEnd(entity.getVoucherDate());
        if (d.after(d2)) {
            throw new ServiceException("亲,日期不能小于账套启用日期：" + DateFormatUtils.format(d, "yyyy-MM-dd"));
        }
    }

    /**
     * 汇总和明细账
     *
     * @param accountSetsId
     * @param subjectId
     * @param startTime
     * @param endTime
     * @param subjectCode
     * @param details
     * @return
     */
    private List<VoucherDetailVo> summary(Integer accountSetsId, Integer subjectId, Date startTime, Date endTime, String subjectCode, Boolean showNumPrice, boolean details) {
        FinancialSubject subject = subjectMapper.selectById(subjectId);
        LambdaQueryWrapper<FinancialSubject> sqw = Wrappers.lambdaQuery();
        sqw.likeRight(FinancialSubject::getCode, subjectCode);
        sqw.eq(FinancialSubject::getAccountSetsId, accountSetsId);
        List<FinancialSubject> subjects = subjectMapper.selectList(sqw);
        List<Integer> sids = subjects.stream().map(FinancialSubject::getId).collect(Collectors.toList());

        if(sids.size() == 0){
            return null;
        }

        List<VoucherDetailVo> initialBalance = baseMapper.selectAccountBookInitialBalance(accountSetsId, sids, startTime);
        List<VoucherDetailVo> statistical = baseMapper.selectAccountBookStatistical(accountSetsId, subjectId, sids, endTime);

        VoucherDetailVo init;
        //没有期初
        if (initialBalance.size() == 0) {
            init = new VoucherDetailVo();
            init.setVoucherDate(startTime);
            init.setSummary("期初余额");
            init.setBalanceDirection("平");
            init.setSubjectName(subject.getCode() + "-" + subject.getName());
            init.setBalance(0d);
            initialBalance.add(init);
        } else {
            init = initialBalance.get(0);
            init.setVoucherDate(startTime);
        }

        //科目明细
        if (details) {
            VoucherDetailVo pre = init;
            //初始数量余额
            pre.setNumBalance(pre.getNum());

            List<VoucherDetailVo> list = baseMapper.selectAccountBookDetails(accountSetsId, sids, startTime, endTime);
            //计算余额
            for (int i = 0; i < list.size(); i++) {
                if (i > 0) {
                    pre = list.get(i - 1);
                }
                VoucherDetailVo vo = list.get(i);
                switch (vo.getBalanceDirection()) {
                    case "借":
                        if (vo.getDebitAmount() != null) {
                            vo.setBalance(DoubleValueUtil.getNotNullVal(pre.getBalance()) + DoubleValueUtil.getNotNullVal(vo.getDebitAmount()));
                        } else if (vo.getCreditAmount() != null) {
                            vo.setBalance(DoubleValueUtil.getNotNullVal(pre.getBalance()) - DoubleValueUtil.getNotNullVal(vo.getCreditAmount()));
                        }

                        if (vo.getNum() != null) {
                            vo.setNumBalance(DoubleValueUtil.getNotNullVal(pre.getNumBalance()) + vo.getNum());
                        } else {
                            vo.setNumBalance(pre.getNumBalance());
                        }
                        break;
                    case "贷":
                        if (vo.getDebitAmount() != null) {
                            vo.setBalance(DoubleValueUtil.getNotNullVal(pre.getBalance()) - DoubleValueUtil.getNotNullVal(vo.getDebitAmount()));
                        } else if (vo.getCreditAmount() != null) {
                            vo.setBalance(DoubleValueUtil.getNotNullVal(pre.getBalance()) + DoubleValueUtil.getNotNullVal(vo.getCreditAmount()));
                        }
                        if (vo.getNum() != null) {
                            vo.setNumBalance(DoubleValueUtil.getNotNullVal(pre.getNumBalance()) - vo.getNum());
                        } else {
                            vo.setNumBalance(pre.getNumBalance());
                        }
                        break;
                }
            }

            initialBalance.addAll(list);
        }

        //设置汇总余额
        Double balance = null;
        for (VoucherDetailVo vo : statistical) {
            double he = DoubleValueUtil.getNotNullVal(vo.getDebitAmount()) - DoubleValueUtil.getNotNullVal(vo.getCreditAmount());
            switch (vo.getBalanceDirection()) {
                case "借":
                    vo.setBalance(DoubleValueUtil.getNotNullVal(init.getBalance()) + he);
                    break;
                case "贷":
                    vo.setBalance(DoubleValueUtil.getNotNullVal(init.getBalance()) - he);
                    break;
            }
            if (vo.getSummary().equals("本期合计")) {
                balance = vo.getBalance();
            } else {
                vo.setBalance(DoubleValueUtil.getNotNullVal(balance, init.getBalance()));
            }
            vo.setNumBalance(vo.getNum());
            vo.setVoucherDate(endTime);
        }

        initialBalance.addAll(statistical);
        return initialBalance;
    }

    /**
     * <AUTHOR>
     * @Description 获取最早凭证日期
     * @Date 2023/6/25 16:40
     * @Param [accountSetsId]
     * @return java.lang.String
     **/
    @Override
    public String getEarliestTime(Integer accountSetsId) {
        Date beginDate = this.baseMapper.selectMinVoucherDate(accountSetsId);
        return DateUtils.dateTime(beginDate);
    }

    /**
     * <AUTHOR>
     * @Description 导出明细账
     * @Date 2023/6/25 16:40
     * @Param [accountSetsId, startTime, endTime, showNumPrice]
     * @return java.util.List<com.ruoyi.financial.excel.DetailAccountsExcel>
     **/
    @Override
    public List<DetailAccountsExcel> exportDetail(Integer accountSetsId, Date startTime, Date endTime, Boolean showNumPrice, Integer subjectId, String subjectCode) {
        List<DetailAccountsExcel> detailAccountsExcels = new ArrayList<>();
        List<VoucherDetailVo> detailList = this.summary(accountSetsId, subjectId, startTime, endTime, subjectCode, showNumPrice, true);

        detailList.forEach(detail->{
            DetailAccountsExcel detailAccountsExcel = new DetailAccountsExcel();
            BeanUtils.copyProperties(detail, detailAccountsExcel);
            if(null != detail.getWord() && null != detail.getCode()){
                detailAccountsExcel.setWordCode(detail.getWord()+"-"+detail.getCode());
            }
            if(null != detail.getDebitAmount()){
                detailAccountsExcel.setDebitAmount(BigDecimal.valueOf(detail.getDebitAmount()));
            }
            if(null != detail.getCreditAmount()){
                detailAccountsExcel.setCreditAmount(BigDecimal.valueOf(detail.getCreditAmount()));
            }
            if(null != detail.getBalance()){
                detailAccountsExcel.setBalance(BigDecimal.valueOf(detail.getBalance()));
            }
            detailAccountsExcel.setVoucherDate(DateUtils.parseDateToStr("yyyy-MM-dd", detail.getVoucherDate()));
            detailAccountsExcels.add(detailAccountsExcel);
        });
        return detailAccountsExcels;
    }

    /**
     * <AUTHOR>
     * @Description 导出总账
     * @Date 2023/6/25 16:41
     * @Param [accountSetsId, startTime, endTime, showNumPrice]
     * @return java.util.List<com.ruoyi.financial.excel.GeneralLedgerExcel>
     **/
    @Override
    public List<GeneralLedgerExcel> exportGeneralLedger(Integer accountSetsId, Date startTime, Date endTime, Boolean showNumPrice){
        List<FinancialSubject> subjectList = subjectService.accountBookList(accountSetsId, startTime, endTime, showNumPrice);

        List<GeneralLedgerExcel> generalLedgerExcels = new ArrayList<>();
        subjectList.forEach(subject -> {
            List<VoucherDetailVo> summary = this.summary(accountSetsId, subject.getId(), startTime, endTime, subject.getCode(), showNumPrice, false);
            summary.forEach(e->{
                GeneralLedgerExcel generalLedgerExcel = new GeneralLedgerExcel();
                BeanUtils.copyProperties(e, generalLedgerExcel);
                generalLedgerExcel.setSubjectCode(subject.getCode());
                generalLedgerExcel.setSubjectName(subject.getName());
                generalLedgerExcel.setVoucherDate(e.getVoucherDate());
                if(null != e.getDebitAmount()){
                    generalLedgerExcel.setDebitAmount(BigDecimal.valueOf(e.getDebitAmount()));
                }
                if(null != e.getCreditAmount()){
                    generalLedgerExcel.setCreditAmount(BigDecimal.valueOf(e.getCreditAmount()));
                }
                if(null != e.getBalance()){
                    generalLedgerExcel.setBalance(BigDecimal.valueOf(e.getBalance()));
                }
                generalLedgerExcels.add(generalLedgerExcel);
            });
        });
        return generalLedgerExcels;
    }

    /**
     * <AUTHOR>
     * @Description 查询序时账
     * @Date 2023/6/25 16:41
     * @Param [accountSetsId, startTime, endTime, showNumPrice]
     * @return java.util.List<com.ruoyi.financial.vo.VoucherDetailVo>
     **/
    @Override
    public List<VoucherDetailVo> journalDetails(Integer accountSetsId, Date startTime, Date endTime, Boolean showNumPrice){
        List<VoucherDetailVo> voucherDetailVos = new ArrayList<>();
        List<FinancialSubject> subjectList = subjectService.accountBookList(accountSetsId, startTime, endTime, showNumPrice);
        subjectList.forEach(subject -> {
            String subjectCode = subject.getCode();
            if(subjectCode.equals("1001") || subjectCode.equals("1002")){
                List<VoucherDetailVo> summary = this.summary(accountSetsId, subject.getId(), startTime, endTime, subjectCode, showNumPrice, true);
                summary.forEach(e->{
                    e.setSubjectName(subject.getName());
                    e.setSubjectCode(subject.getCode());
                });
                voucherDetailVos.addAll(summary);
            }
        });
        return voucherDetailVos;
    }

    /**
     * <AUTHOR>
     * @Description 导出序时账
     * @Date 2023/6/25 16:57
     * @Param [accountSetsId, startTime, endTime, showNumPrice]
     * @return void
     **/
    @Override
    public List<JournalAccountsExcel> exportJournalDetail(Integer accountSetsId, Date startTime, Date endTime, Boolean showNumPrice) {
        List<VoucherDetailVo> voucherDetailVos = journalDetails(accountSetsId, startTime, endTime, showNumPrice);

        List<JournalAccountsExcel> journalAccountsExcels = new ArrayList<>();

        voucherDetailVos.forEach(detail->{
            JournalAccountsExcel journalAccountsExcel = new JournalAccountsExcel();
            BeanUtils.copyProperties(detail, journalAccountsExcel);
            if(null != detail.getWord() && null != detail.getCode()){
                journalAccountsExcel.setWordCode(detail.getWord()+"-"+detail.getCode());
            }
            if(null != detail.getDebitAmount()){
                journalAccountsExcel.setDebitAmount(BigDecimal.valueOf(detail.getDebitAmount()));
            }
            if(null != detail.getCreditAmount()){
                journalAccountsExcel.setCreditAmount(BigDecimal.valueOf(detail.getCreditAmount()));
            }
            journalAccountsExcel.setDebitDirection(detail.getBalanceDirection());
            journalAccountsExcel.setCreditDirection(detail.getBalanceDirection());
            journalAccountsExcels.add(journalAccountsExcel);
        });
        return journalAccountsExcels;
    }

    /**
     * <AUTHOR>
     * @Description 发布redis
     * @Date 2024/12/31 16:44
     * @Param [voucherId, voucherDate]
     * @return void
     **/
    private void publishRedis(Integer voucherId, Date voucherDate){
        JSONObject publishJson = new JSONObject();
        publishJson.put("voucherId", voucherId);
        publishJson.put("dataDay", DateUtils.format(voucherDate, DateUtils.DateFormat.YYYY_MM_DD));
        //发布财务经营分析频道
        redisPublisher.publish(RedisConstants.FINANCIAL_DATAQUERY_ADD_CHANNEL, publishJson.toJSONString());
    }
}





