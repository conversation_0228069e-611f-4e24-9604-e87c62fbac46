package com.ruoyi.financial.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.financial.domain.FinancialVoucherTemplateDetails;

import java.util.List;

/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : ${PACKAGE_NAME}</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年08月03日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
public interface IFinancialVoucherTemplateDetailsService extends IService<FinancialVoucherTemplateDetails> {


    int batchInsert(List<FinancialVoucherTemplateDetails> list);

}



