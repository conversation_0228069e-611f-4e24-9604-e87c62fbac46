package com.ruoyi.financial.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.financial.domain.FinancialU8CBankaccount;
import com.ruoyi.financial.domain.PushU8CLedger;
import com.ruoyi.financial.domain.vo.BankaccbasVO;
import com.ruoyi.financial.domain.vo.GetU8CLedgerVO;
import com.ruoyi.financial.mapper.FinancialU8CBankaccountMapper;
import com.ruoyi.financial.service.IFinancialU8CBankaccountService;
import com.ruoyi.financial.utils.U8CGetData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


/**
 * 智慧财务系统关联用友U8C银行账户Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-21
 */
@Service
public class FinancialU8CBankaccountServiceImpl implements IFinancialU8CBankaccountService {

    @Autowired
    private FinancialU8CBankaccountMapper mapper;

    @Value("${u8c.ip}")
    private String IP;

    @Value("${u8c.userCode}")
    private String userCode;

    @Value("${u8c.passWord}")
    private String passWord;

    @Value("${u8c.system}")
    private String system;

    /**
     * 查询智慧财务系统关联用友U8C银行账户
     *
     * @param accountId 智慧财务系统关联用友U8C银行账户主键
     * @return 智慧财务系统关联用友U8C银行账户
     */
    @Override
    public FinancialU8CBankaccount selectFinancialU8CBankaccountByAccountId(Long accountId)
    {
        return mapper.selectFinancialU8CBankaccountByAccountId(accountId);
    }

    /**
     * 查询智慧财务系统关联用友U8C银行账户列表
     *
     * @param financialU8CBankaccount 智慧财务系统关联用友U8C银行账户
     * @return 智慧财务系统关联用友U8C银行账户
     */
    @Override
    public List<FinancialU8CBankaccount> selectFinancialU8CBankaccountList(FinancialU8CBankaccount financialU8CBankaccount)
    {
        //查询收付款人数据
        List<FinancialU8CBankaccount> witData = mapper.selectAccountList(financialU8CBankaccount);
        return witData;
    }

    /**
     * 保存智慧财务系统关联用友U8C银行账户
     *
     * @param financialU8CBankaccount 智慧财务系统关联用友U8C银行账户
     * @return 结果
     */
    @Override
    public int insertFinancialU8CBankaccount(FinancialU8CBankaccount financialU8CBankaccount)
    {
        financialU8CBankaccount.setCreateTime(DateUtils.getNowDate());
        financialU8CBankaccount.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserName());
        financialU8CBankaccount.setRelevanceType("1");
        //查找之前是否有配置
        List<FinancialU8CBankaccount> lastVersion = mapper.selectListToLastVersion(financialU8CBankaccount);
        //如果没有找到配置，则直接新增版本为1的
        if(lastVersion == null || lastVersion.size() < 1 || lastVersion.get(0) == null){
            financialU8CBankaccount.setVersion(1);
        }else{
            //作废之前的，累计最高的版本
            financialU8CBankaccount.setVersion(lastVersion.get(0).getVersion()+1);
            financialU8CBankaccount.setPkBankaccbasBefor(lastVersion.get(0).getPkBankaccbasBefor());
            financialU8CBankaccount.setU8cAccountBefor(lastVersion.get(0).getU8cAccountBefor());
            financialU8CBankaccount.setU8cAccountCodeBefor(lastVersion.get(0).getU8cAccountCodeBefor());
            financialU8CBankaccount.setU8cAccountNameBefor(lastVersion.get(0).getU8cAccountNameBefor());
            mapper.setState("1",financialU8CBankaccount.getAccountId(),financialU8CBankaccount.getVersion(),financialU8CBankaccount.getTraderId());
        }
        return mapper.insertFinancialU8CBankaccount(financialU8CBankaccount);
    }

    /**
     * 查询修改记录
     * @param financialU8CBankaccount  搜索条件
     * @return
     */
    @Override
    public List<FinancialU8CBankaccount> selectUpdate(FinancialU8CBankaccount financialU8CBankaccount) {
        return mapper.selectUpdate(financialU8CBankaccount);
    }

    /**
     * 获取U8C银行账户
     * @return
     */
    @Override
    public List<BankaccbasVO> getU8CBankaccount(FinancialU8CBankaccount financialU8CBankaccount) {
        U8CGetData getData = new U8CGetData();
        String apiUrl = IP+"/u8cloud/api/pub/sql/query";
        //设置查询json
        GetU8CLedgerVO sendData = new GetU8CLedgerVO();
        List<BankaccbasVO> getBankaccbas = new ArrayList<>();
        String sql = "select bas.accountname,bas.account,bas.pk_bankaccbas,bas.accountcode from bd_glorgbook book left join bd_glorg glo on glo.pk_glorg = book.pk_glorg left join bd_bankaccbas bas on bas.pk_corp = glo.pk_entityorg where book.pk_glorgbook = '"+financialU8CBankaccount.getPkGlorgbook()+"'";
        if(financialU8CBankaccount.getU8cAccountName() != null && financialU8CBankaccount.getU8cAccountName() != ""){
            sql += " and bas.accountname like '%"+financialU8CBankaccount.getU8cAccountName()+"%'";
        }
        if(financialU8CBankaccount.getU8cAccount() != null && financialU8CBankaccount.getU8cAccount() != ""){
            sql += " and bas.account like '%"+financialU8CBankaccount.getU8cAccount()+"%'";
        }
        sendData.setSql(sql);
        sendData.setPage_now(1);
        sendData.setPage_size(999);
        String result = getData.getU8CResponse(apiUrl, sendData,userCode,passWord,system);
        JSONObject jsonObject = JSON.parseObject(result);
        if("success".equals(jsonObject.getString("status"))){
            String data = jsonObject.getString("data");
            JSONObject jsonDatas = JSON.parseObject(data);
            String datas = jsonDatas.getString("datas");
            if(datas != null){
                getBankaccbas = JSON.parseArray(datas, BankaccbasVO.class);
                return getBankaccbas;
            }
        }
        return getBankaccbas;
    }

}
