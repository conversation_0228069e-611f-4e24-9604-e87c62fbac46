package com.ruoyi.financial.excel;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 *@Authoer: huoruidong
 *@Description: 总账导出
 *@Date: 2023/6/21 13:40
 **/
@Data
public class GeneralLedgerExcel {

    @Excel(name = "科目编码")
    private String subjectCode;

    @Excel(name = "科目名称")
    private String subjectName;

    @Excel(name = "期间", dateFormat = "yyyy年第MM期")
    private Date voucherDate;

    @Excel(name = "摘要")
    private String summary;

    @Excel(name = "借方金额", scale = 2)
    private BigDecimal debitAmount;

    @Excel(name = "贷方金额", scale = 2)
    private BigDecimal creditAmount;

    @Excel(name = "方向")
    private String balanceDirection;

    @Excel(name = "余额", scale = 2)
    private BigDecimal balance;
}
