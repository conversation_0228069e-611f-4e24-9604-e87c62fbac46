package com.ruoyi.financial.domain;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 智慧财务系统推送用友U8C账套
 * lichen ********
 */
@Data
public class PushU8CLedger extends BaseEntity {

    /**
     * 智慧财务账套主键
     */
    private Long accountId;

    /**
     * 智慧财务账套名称
     */
    private String accountName;

    /**
     * 用友U8C账簿主键
     */
    private String pkGlorgbook;

    /**
     * 用友U8C账簿名称
     */
    private String glorgbookName;

    /**
     * 用友U8C账簿编码
     */
    private String glorgbookCode;

    /**
     * 修改前用友U8C账套名称
     */
    private String glorgbookNameBefore;

    /**
     * 修改前用友U8C账套ID
     */
    private String pkGlorgbookBefore;

    /**
     * 修改前用友U8C账簿名称
     */
    private String glorgbookCodeBefore;

    /**
     * 修改原因
     */
    private String updateReason;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 最新生效版本号
     */
    private Integer lastVersion;

    private Long id;

    private String pk_entityorg;

    private String pk_entityorgBefor;

    private String pkEntityorg;

    private String pkEntityorgBefor;
}
