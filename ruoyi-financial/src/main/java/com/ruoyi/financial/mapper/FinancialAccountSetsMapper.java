package com.ruoyi.financial.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.financial.domain.FinancialAccountSets;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : cn.gson.financial.kernel.model.mapper</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年10月16日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
public interface FinancialAccountSetsMapper extends BaseMapper<FinancialAccountSets> {
    int batchInsert(@Param("list") List<FinancialAccountSets> list);

    List<FinancialAccountSets> selectMyAccountSets(@Param("uid") Long uid);

    List<Map<String, Object>> getAccountList(@Param("companyId") Long companyId);

    List<Map<String, Object>> getAllAccountList();
}