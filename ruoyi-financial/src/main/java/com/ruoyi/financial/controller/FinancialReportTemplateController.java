package com.ruoyi.financial.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.financial.controller.base.BaseCrudController;
import com.ruoyi.financial.domain.FinancialReportTemplate;
import com.ruoyi.financial.service.IFinancialReportTemplateService;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;

/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : cn.gson.financial.controller</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年09月05日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
@RestController
@RequestMapping("/report/template")
public class FinancialReportTemplateController extends BaseCrudController<IFinancialReportTemplateService, FinancialReportTemplate> {

    @GetMapping("view/{id:\\d+}")
    public JsonResult view(@PathVariable Long id, Integer accountSetsId, Date startTime, Date endTime) {
        return JsonResult.successful(service.view(accountSetsId, id, startTime, endTime));
    }

    /**
     * 报表导出
     */
    @Log(title = "报表导出", businessType = BusinessType.EXPORT)
    @PostMapping("exportDetail")
    public void exportDetail(HttpServletResponse response, Integer accountSetsId, Long reportId, Date startTime, Date endTime) {
        this.service.exportDetail(response, accountSetsId, reportId, startTime, endTime);
    }
}
