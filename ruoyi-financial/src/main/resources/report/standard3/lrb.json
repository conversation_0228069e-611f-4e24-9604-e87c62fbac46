{"name": "利润表", "type": 0, "items": [{"isFolding": false, "sources": 0, "level": 1, "formulas": [{"calculation": "+", "accessRules": 0, "fromTag": "5653", "templateItemsId": 0, "id": 315, "templateId": 99, "accountSetsId": 38, "subjectCode": "6001001"}, {"calculation": "+", "accessRules": 0, "fromTag": "5654", "templateItemsId": 0, "id": 316, "templateId": 99, "accountSetsId": 38, "subjectCode": "6001002"}, {"calculation": "+", "accessRules": 0, "fromTag": "5658", "templateItemsId": 289, "id": 317, "templateId": 99, "accountSetsId": 38, "subjectCode": "6051"}], "isBolder": true, "pid": 0, "templateId": 0, "title": "一、营业收入", "type": 0, "pos": 1, "isClassified": false, "lineNum": 1, "id": 1}, {"isFolding": false, "sources": 0, "level": 2, "formulas": [{"calculation": "+", "accessRules": 0, "fromTag": "5666", "templateItemsId": 0, "id": 317, "templateId": 99, "accountSetsId": 38, "subjectCode": "6401001"}, {"calculation": "+", "accessRules": 0, "fromTag": "5666", "templateItemsId": 0, "id": 317, "templateId": 99, "accountSetsId": 38, "subjectCode": "6401002"}, {"calculation": "+", "accessRules": 0, "fromTag": "5667", "templateItemsId": 0, "id": 318, "templateId": 99, "accountSetsId": 38, "subjectCode": "6402"}], "isBolder": false, "pid": 0, "templateId": 0, "title": "减：营业成本", "type": 0, "parentId": 1, "pos": 2, "isClassified": false, "lineNum": 2, "id": 2}, {"isFolding": false, "sources": 0, "level": 3, "formulas": [{"calculation": "+", "accessRules": 0, "fromTag": "5668", "templateItemsId": 291, "id": 319, "templateId": 99, "accountSetsId": 38, "subjectCode": "6403"}], "isBolder": false, "pid": 1714, "templateId": 99, "title": "税金及附加", "type": 0, "parentId": 2, "pos": 3, "isClassified": false, "lineNum": 3, "id": 3}, {"isFolding": false, "sources": 0, "level": 3, "formulas": [{"calculation": "+", "accessRules": 0, "fromTag": "5678", "templateItemsId": 292, "id": 320, "templateId": 99, "accountSetsId": 38, "subjectCode": "6601"}], "isBolder": false, "pid": 1715, "templateId": 99, "title": "销售费用", "type": 0, "parentId": 2, "pos": 4, "isClassified": false, "lineNum": 4, "id": 4}, {"isFolding": false, "sources": 0, "level": 3, "formulas": [{"calculation": "+", "accessRules": 0, "fromTag": "5679", "templateItemsId": 293, "id": 321, "templateId": 99, "accountSetsId": 38, "subjectCode": "6602"}], "isBolder": false, "pid": 1716, "templateId": 99, "title": "管理费用", "type": 0, "parentId": 2, "pos": 5, "isClassified": false, "lineNum": 5, "id": 5}, {"isFolding": false, "sources": 0, "level": 3, "formulas": [], "isBolder": false, "pid": 1716, "templateId": 99, "title": "研发费用", "type": 0, "parentId": 2, "pos": 6, "isClassified": false, "lineNum": 6, "id": 6}, {"isFolding": false, "sources": 0, "level": 3, "formulas": [{"calculation": "+", "accessRules": 0, "fromTag": "5680", "templateItemsId": 294, "id": 322, "templateId": 99, "accountSetsId": 38, "subjectCode": "6603"}], "isBolder": false, "pid": 1717, "templateId": 99, "title": "财务费用", "type": 0, "parentId": 2, "pos": 7, "isClassified": false, "lineNum": 7, "id": 7}, {"isFolding": false, "sources": 0, "level": 4, "formulas": [{"calculation": "+", "accessRules": 0, "fromTag": "5682", "templateItemsId": 295, "id": 323, "templateId": 99, "accountSetsId": 38, "subjectCode": "6603002"}], "isBolder": false, "pid": 1718, "templateId": 99, "title": "其中：手续费", "type": 0, "parentId": 7, "pos": 8, "isClassified": false, "lineNum": 8, "id": 8}, {"isFolding": false, "sources": 0, "level": 5, "formulas": [{"calculation": "+", "accessRules": 0, "fromTag": "5660", "templateItemsId": 296, "id": 324, "templateId": 99, "accountSetsId": 38, "subjectCode": "6603001"}], "isBolder": false, "pid": 1719, "templateId": 99, "title": "利息费用", "type": 0, "parentId": 8, "pos": 9, "isClassified": false, "lineNum": 9, "id": 9}, {"isFolding": false, "sources": 0, "level": 3, "formulas": [], "isBolder": false, "pid": 1719, "templateId": 99, "title": "资产减值损失", "type": 0, "parentId": 2, "pos": 10, "isClassified": false, "lineNum": 10, "id": 10}, {"isFolding": false, "sources": 0, "level": 3, "formulas": [], "isBolder": false, "pid": 1719, "templateId": 99, "title": "信用减值损失", "type": 0, "parentId": 2, "pos": 11, "isClassified": false, "lineNum": 11, "id": 11}, {"isFolding": false, "sources": 0, "level": 2, "formulas": [], "isBolder": false, "pid": 1719, "templateId": 99, "title": "加：其他收益", "type": 0, "parentId": 1, "pos": 12, "isClassified": false, "lineNum": 12, "id": 12}, {"isFolding": false, "sources": 0, "level": 3, "formulas": [{"calculation": "+", "accessRules": 0, "fromTag": "5661", "templateItemsId": 297, "id": 325, "templateId": 99, "accountSetsId": 38, "subjectCode": "6111"}], "isBolder": false, "pid": 1720, "templateId": 99, "title": "投资收益（损失以“-”填列）", "type": 0, "parentId": 12, "pos": 13, "isClassified": false, "lineNum": 13, "id": 13}, {"isFolding": false, "sources": 0, "level": 4, "isBolder": false, "pid": 1720, "templateId": 99, "title": "其中：对联营企业和合营企业的投资收益", "type": 0, "parentId": 13, "pos": 14, "isClassified": false, "lineNum": 14, "id": 14}, {"isFolding": false, "sources": 0, "level": 3, "isBolder": false, "pid": 1720, "templateId": 99, "title": "净敞口套期收益（损失以“-”号填列）", "type": 0, "parentId": 12, "pos": 15, "isClassified": false, "lineNum": 15, "id": 15}, {"isFolding": false, "sources": 0, "level": 3, "isBolder": false, "pid": 1720, "templateId": 99, "title": "公允价值变动收益（损失以“-”号填列）", "type": 0, "parentId": 12, "pos": 16, "isClassified": false, "lineNum": 16, "id": 16}, {"isFolding": false, "sources": 0, "level": 3, "isBolder": false, "pid": 1720, "templateId": 99, "title": "资产处置收益（损失以“-”号填列）", "type": 0, "parentId": 12, "pos": 17, "isClassified": false, "lineNum": 17, "id": 17}, {"isFolding": false, "sources": 1, "level": 1, "formulas": [{"calculation": "+", "accessRules": 0, "fromTag": "1", "templateItemsId": 299, "id": 334, "templateId": 99, "accountSetsId": 38}, {"calculation": "-", "accessRules": 0, "fromTag": "2", "templateItemsId": 299, "id": 336, "templateId": 99, "accountSetsId": 38}, {"calculation": "-", "accessRules": 0, "fromTag": "3", "templateItemsId": 299, "id": 337, "templateId": 99, "accountSetsId": 38}, {"calculation": "-", "accessRules": 0, "fromTag": "4", "templateItemsId": 299, "id": 338, "templateId": 99, "accountSetsId": 38}, {"calculation": "-", "accessRules": 0, "fromTag": "5", "templateItemsId": 299, "id": 339, "templateId": 99, "accountSetsId": 38}, {"calculation": "-", "accessRules": 0, "fromTag": "6", "templateItemsId": 299, "id": 339, "templateId": 99, "accountSetsId": 38}, {"calculation": "-", "accessRules": 0, "fromTag": "7", "templateItemsId": 299, "id": 340, "templateId": 99, "accountSetsId": 38}, {"calculation": "-", "accessRules": 0, "fromTag": "10", "templateItemsId": 299, "id": 340, "templateId": 99, "accountSetsId": 38}, {"calculation": "-", "accessRules": 0, "fromTag": "11", "templateItemsId": 299, "id": 340, "templateId": 99, "accountSetsId": 38}, {"calculation": "+", "accessRules": 0, "fromTag": "12", "templateItemsId": 299, "id": 340, "templateId": 99, "accountSetsId": 38}, {"calculation": "+", "accessRules": 0, "fromTag": "13", "templateItemsId": 299, "id": 340, "templateId": 99, "accountSetsId": 38}, {"calculation": "+", "accessRules": 0, "fromTag": "15", "templateItemsId": 299, "id": 340, "templateId": 99, "accountSetsId": 38}, {"calculation": "+", "accessRules": 0, "fromTag": "16", "templateItemsId": 299, "id": 340, "templateId": 99, "accountSetsId": 38}, {"calculation": "+", "accessRules": 0, "fromTag": "17", "templateItemsId": 299, "id": 340, "templateId": 99, "accountSetsId": 38}], "isBolder": true, "pid": 1722, "templateId": 99, "title": "二、营业利润（亏损失以“-”号填列）", "type": 0, "pos": 18, "isClassified": false, "lineNum": 18, "id": 18}, {"isFolding": false, "sources": 0, "level": 2, "formulas": [{"calculation": "+", "accessRules": 0, "fromTag": "5665", "templateItemsId": 300, "id": 333, "templateId": 99, "accountSetsId": 38, "subjectCode": "6301"}], "isBolder": false, "pid": 1723, "templateId": 99, "title": "加：营业外收入", "type": 0, "parentId": 18, "pos": 19, "isClassified": false, "lineNum": 19, "id": 19}, {"isFolding": false, "sources": 0, "level": 2, "formulas": [{"calculation": "+", "accessRules": 0, "fromTag": "5683", "templateItemsId": 301, "id": 341, "templateId": 99, "accountSetsId": 38, "subjectCode": "6711"}], "isBolder": false, "pid": 1724, "templateId": 99, "title": "减：营业外支出", "type": 0, "parentId": 18, "pos": 20, "isClassified": false, "lineNum": 20, "id": 20}, {"isFolding": false, "sources": 1, "level": 1, "formulas": [{"calculation": "+", "accessRules": 0, "fromTag": "18", "templateItemsId": 303, "id": 346, "templateId": 99, "accountSetsId": 38}, {"calculation": "+", "accessRules": 0, "fromTag": "19", "templateItemsId": 303, "id": 347, "templateId": 99, "accountSetsId": 38}, {"calculation": "-", "accessRules": 0, "fromTag": "20", "templateItemsId": 303, "id": 348, "templateId": 99, "accountSetsId": 38}], "isBolder": true, "pid": 1726, "templateId": 99, "title": "三、利润总额", "type": 0, "pos": 21, "isClassified": false, "lineNum": 21, "id": 21}, {"isFolding": false, "sources": 0, "level": 2, "formulas": [{"calculation": "+", "accessRules": 0, "fromTag": "5684", "templateItemsId": 304, "id": 345, "templateId": 99, "accountSetsId": 38, "subjectCode": "6801"}], "isBolder": false, "pid": 1727, "templateId": 99, "title": "减：所得税费用（亏损失以“-”号填列）", "type": 0, "parentId": 21, "pos": 22, "isClassified": false, "lineNum": 22, "id": 22}, {"isFolding": false, "sources": 1, "level": 1, "formulas": [{"calculation": "+", "accessRules": 0, "fromTag": "21", "templateItemsId": 305, "id": 349, "templateId": 99, "accountSetsId": 38}, {"calculation": "-", "accessRules": 0, "fromTag": "22", "templateItemsId": 305, "id": 350, "templateId": 99, "accountSetsId": 38}], "isBolder": true, "pid": 1728, "templateId": 99, "title": "四、净利润（亏损失以“-”号填列）", "type": 0, "pos": 23, "isClassified": false, "lineNum": 23, "id": 23}, {"isFolding": false, "sources": 1, "level": 2, "formulas": [], "isBolder": false, "pid": 1728, "templateId": 99, "title": "归属于母公司所有者的净利润", "type": 0, "parentId": 23, "pos": 24, "isClassified": false, "lineNum": 24, "id": 24}, {"isFolding": false, "sources": 1, "level": 2, "formulas": [], "isBolder": false, "pid": 1728, "templateId": 99, "title": "少数股东损益", "type": 0, "parentId": 23, "pos": 25, "isClassified": false, "lineNum": 25, "id": 25}, {"isFolding": false, "sources": 1, "level": 1, "formulas": [], "isBolder": true, "pid": 1728, "templateId": 99, "title": "五、其他综合收益的税后净额", "type": 0, "pos": 26, "isClassified": false, "lineNum": 26, "id": 26}, {"isFolding": false, "sources": 1, "level": 2, "formulas": [], "isBolder": true, "pid": 1728, "templateId": 99, "title": "（一）不能重分类进损益的其他综合收益", "type": 0, "parentId": 26, "pos": 27, "isClassified": false, "lineNum": 27, "id": 27}, {"isFolding": false, "sources": 1, "level": 3, "formulas": [], "isBolder": false, "pid": 1728, "templateId": 99, "title": "1．重新计量设定受益计划变动额", "type": 0, "parentId": 27, "pos": 28, "isClassified": false, "lineNum": 28, "id": 28}, {"isFolding": false, "sources": 1, "level": 3, "formulas": [], "isBolder": false, "pid": 1728, "templateId": 99, "title": "2．权益法下不能转损益的其他综合收益", "type": 0, "parentId": 27, "pos": 29, "isClassified": false, "lineNum": 29, "id": 29}, {"isFolding": false, "sources": 1, "level": 3, "formulas": [], "isBolder": false, "pid": 1728, "templateId": 99, "title": "3．其他权益工具投资公允价值变动", "type": 0, "parentId": 27, "pos": 30, "isClassified": false, "lineNum": 30, "id": 30}, {"isFolding": false, "sources": 1, "level": 3, "formulas": [], "isBolder": false, "pid": 1728, "templateId": 99, "title": "4．企业自身信用风险公允价值变动", "type": 0, "parentId": 27, "pos": 31, "isClassified": false, "lineNum": 31, "id": 31}, {"isFolding": false, "sources": 1, "level": 2, "formulas": [], "isBolder": true, "pid": 1728, "templateId": 99, "title": "（二）将重分类进损益的其他综合收益", "type": 0, "parentId": 26, "pos": 32, "isClassified": false, "lineNum": 32, "id": 32}, {"isFolding": false, "sources": 1, "level": 3, "formulas": [], "isBolder": false, "pid": 1728, "templateId": 99, "title": "1．权益法下可转损益的其他综合收益", "type": 0, "parentId": 32, "pos": 33, "isClassified": false, "lineNum": 33, "id": 33}, {"isFolding": false, "sources": 1, "level": 3, "formulas": [], "isBolder": false, "pid": 1728, "templateId": 99, "title": "2．其他债权投资公允价值变动", "type": 0, "parentId": 32, "pos": 34, "isClassified": false, "lineNum": 34, "id": 34}, {"isFolding": false, "sources": 1, "level": 3, "formulas": [], "isBolder": false, "pid": 1728, "templateId": 99, "title": "3．金融资产重分类计入其他综合收益的金额", "type": 0, "parentId": 32, "pos": 35, "isClassified": false, "lineNum": 35, "id": 35}, {"isFolding": false, "sources": 1, "level": 3, "formulas": [], "isBolder": false, "pid": 1728, "templateId": 99, "title": "4．其他债权投资信用减值准备", "type": 0, "parentId": 32, "pos": 36, "isClassified": false, "lineNum": 36, "id": 36}, {"isFolding": false, "sources": 1, "level": 3, "formulas": [], "isBolder": false, "pid": 1728, "templateId": 99, "title": "5．现金流量套期储备", "type": 0, "parentId": 32, "pos": 37, "isClassified": false, "lineNum": 37, "id": 37}, {"isFolding": false, "sources": 1, "level": 3, "formulas": [], "isBolder": false, "pid": 1728, "templateId": 99, "title": "6．外币财务报表折算差额", "type": 0, "parentId": 32, "pos": 38, "isClassified": false, "lineNum": 38, "id": 38}, {"isFolding": false, "sources": 1, "level": 1, "formulas": [], "isBolder": true, "pid": 1728, "templateId": 99, "title": "六、综合收益总额", "type": 0, "pos": 39, "isClassified": false, "lineNum": 39, "id": 39}, {"isFolding": false, "sources": 1, "level": 1, "formulas": [], "isBolder": true, "pid": 1728, "templateId": 99, "title": "七、每股收益：", "type": 0, "pos": 40, "isClassified": false, "lineNum": 40, "id": 40}, {"isFolding": false, "sources": 1, "level": 2, "formulas": [], "isBolder": false, "pid": 1728, "templateId": 99, "title": "（一）基本每股收益", "type": 0, "parentId": 40, "pos": 41, "isClassified": false, "lineNum": 41, "id": 41}, {"isFolding": false, "sources": 1, "level": 2, "formulas": [], "isBolder": false, "pid": 1728, "templateId": 99, "title": "（二）稀释每股收益", "type": 0, "parentId": 40, "pos": 42, "isClassified": false, "lineNum": 42, "id": 42}], "templateKey": "lrb"}