<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.financial.mapper.FinancialAccountingCategoryMapper">
  <resultMap id="BaseResultMap" type="com.ruoyi.financial.domain.FinancialAccountingCategory">
    <!--@mbg.generated-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="custom_columns" jdbcType="VARCHAR" property="customColumns" />
      <result column="system_default" jdbcType="BIT" property="systemDefault"/>
    <result column="account_sets_id" jdbcType="INTEGER" property="accountSetsId" />
      <result column="can_edit" jdbcType="BIT" property="canEdit"/>
  </resultMap>

  <sql id="Base_Column_List">
    <!--@mbg.generated-->
      id, `name`, custom_columns, system_default, account_sets_id, can_edit
  </sql>

  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into financial_accounting_category
      (`name`, custom_columns, system_default, account_sets_id, can_edit)
    values
    <foreach collection="list" item="item" separator=",">
        (#{item.name,jdbcType=VARCHAR}, #{item.customColumns,jdbcType=VARCHAR}, #{item.systemDefault,jdbcType=BIT},
        #{item.accountSetsId,jdbcType=INTEGER}, #{item.canEdit,jdbcType=BIT})
    </foreach>
  </insert>

</mapper>