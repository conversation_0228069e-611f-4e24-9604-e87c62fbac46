package com.ruoyi.common.utils.pdf;

import com.itextpdf.text.BaseColor;
import com.itextpdf.text.Font;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.tool.xml.XMLWorkerFontProvider;

public class <PERSON>ont<PERSON>rovider extends XMLWorkerFontProvider {
    private String fontPath;//字体路径
	
	public FontProvider() {}
    public FontProvider(String fontPath) {
        this.fontPath=fontPath;
    }
	public String getFontPath() {
		return fontPath;
	}
	public void setFontPath(String fontPath) {
		this.fontPath = fontPath;
	}

	/**
	 * 
	 * 字体
	 *
	 */
	public Font getFont(final String fontname, final String encoding,  
			final boolean embedded, final float size, final int style,  
			final BaseColor color) {  
		BaseFont bf = null;  
		try {  
			bf = BaseFont.createFont(fontPath+"/font/SourceHanSansSC-VF.ttf", BaseFont.IDENTITY_H , BaseFont.EMBEDDED);//思源黑体
			//                bf = BaseFont.createFont(webinfoPath+"/font/SourceHanSerifCN-Regular.ttf", BaseFont.IDENTITY_H , BaseFont.EMBEDDED);//思源宋体
		} catch (Exception e) {  
			e.printStackTrace();  
		}  
		Font font = new Font(bf, size, style, color);  
		font.setColor(color);  
		return font;  
	}
}
