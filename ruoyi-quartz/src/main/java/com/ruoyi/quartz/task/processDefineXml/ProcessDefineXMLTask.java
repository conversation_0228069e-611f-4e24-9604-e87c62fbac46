package com.ruoyi.quartz.task.processDefineXml;

import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.system.service.DefinitionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * Description：更新act_ge_bytearray表中定时任务，只执行一次
 * CreateTime：2024/6/24
 * Author：yu-qiang
 */
@Component
public class ProcessDefineXMLTask {

    private static final Logger log = LoggerFactory.getLogger(ProcessDefineXMLTask.class);

    public void updateBpmnFile(){
        DefinitionService definitionService = SpringUtils.getBean(DefinitionService.class);
        if(definitionService.getUpdateBpmnFileJobNum()==0){
            log.info("====》》》》更新流程定义任务开始执行《《《《====");
            definitionService.updateBpmnFile();
            log.info("====》》》》更新流程定义任务执行结束《《《《====");
        }else{
            log.info("====》》》》更新流程定义任务已经执行，不可重复执行《《《《====");
        }
    }
}
