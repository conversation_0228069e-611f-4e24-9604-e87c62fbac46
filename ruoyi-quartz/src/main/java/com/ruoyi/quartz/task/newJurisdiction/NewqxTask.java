package com.ruoyi.quartz.task.newJurisdiction;

import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.quartz.service.TaskService;
import com.ruoyi.quartz.task.manualStat.ManualTasks;
import org.ruoyi.core.service.impl.SysSelectDataRefServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName NewqxTask.java
 * @Description TODO
 * @createTime 2024年06月18日 09:18:00
 */
public class NewqxTask {
    private static final Logger log = LoggerFactory.getLogger(NewqxTask.class);
    public void initialSelectData(){
        log.info("开始处理产品信息初始化，初始化数据都将存入sys_select_data_ref表中");
        SysSelectDataRefServiceImpl TaskService = SpringUtils.getBean(SysSelectDataRefServiceImpl.class);
        TaskService.InitializationData();
        log.info("处理完成");
    }
}
