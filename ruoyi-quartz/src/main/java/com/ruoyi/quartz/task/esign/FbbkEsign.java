package com.ruoyi.quartz.task.esign;

import java.io.FileInputStream;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.ruoyi.core.esign.apply.domain.EsignApply;
import org.ruoyi.core.esign.apply.domain.FbbkDbmxqdInfo;
import org.ruoyi.core.esign.apply.service.IEsignApplyService;
import org.ruoyi.core.tool.DataRequestTool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.enums.SecretStatus;
import com.ruoyi.common.utils.pdf.PDFUtil;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.framework.web.service.EncryptService;
import com.ruoyi.quartz.service.TaskService;
import com.ruoyi.quartz.util.TaskCommon;
import com.ruoyi.quartz.wukongsdk.exception.DefineException;
import com.ruoyi.quartz.wukongsdk.run.RunProcess;
import com.ruoyi.system.service.IDDataSqlManualTaskService;

@Component
public class FbbkEsign implements InitializingBean{

    private static final Logger log = LoggerFactory.getLogger(FbbkEsign.class);
    
    @Value("${ruoyi.profile}")
    private String initProfilePath;
    private static String profilePath;

	@Override
	public void afterPropertiesSet() throws Exception {
		FbbkEsign.profilePath=this.initProfilePath;
	}
    
	/**
	 * 数据校验
	 */
	public void checkFbbkDataTask(){
		IEsignApplyService esignApplyService=SpringUtils.getBean(IEsignApplyService.class);
		List<EsignApply> list= esignApplyService.selectEsignApplyCheckList();
		if(!CollectionUtils.isEmpty(list)){
			log.info("====》》》》checkFbbkDataTask 查询到需要执行的数据，开始执行《《《《====");
		
			for (EsignApply esignApply : list) {
				log.info("====》》》》checkFbbkDataTask 开始执行  id:"+esignApply.getId()+" 清单年月："+esignApply.getFileMonth()+"《《《《====");
				try {
					//解析文件内容
					ExcelUtil<FbbkDbmxqdInfo> util = new ExcelUtil<FbbkDbmxqdInfo>(FbbkDbmxqdInfo.class);
			        List<FbbkDbmxqdInfo> applyList = util.importExcel(new FileInputStream(profilePath+"/FbbkESign/"+esignApply.getFilePathExcel()));
					//校验文件内容
			        boolean a=checkFbbkData(applyList);
			        if(a) {
			        	//生成待签章pdf
				        String htmlFileName = esignApply.getRealFileName().substring(0,esignApply.getRealFileName().lastIndexOf("."))+"html";
				        String pdfFileName = esignApply.getRealFileName().substring(0,esignApply.getRealFileName().lastIndexOf("."))+"pdf";
				        Map<String, Object> paramMap =new HashMap<String, Object>();
				        paramMap.put("applyList", applyList);
				        PDFUtil pdfUtil = new PDFUtil(profilePath+"/FbbkESign",htmlFileName,pdfFileName,profilePath);//初始化协议pdf生成工具类
				        String templateContent=pdfUtil.readfile(profilePath+"/pdfTemplate/fbdbmxqd.html");
				        pdfUtil.createdHtmlTemplate(templateContent);//创建html模板
				        pdfUtil.fillTemplate(paramMap);//向html模板填充数据
				        esignApply.setFileDataSize(applyList.size());
				        //计算总页数，每页37条数据
				        if((applyList.size()+1)%37!=0) {
					        esignApply.setFilePage((applyList.size()+1)/37+1);
				        }else {
					        esignApply.setFilePage((applyList.size()+1)/37);
				        }
				        esignApply.setFlowStatus("2");
				        esignApply.setFilePathPdf(pdfFileName);
				        esignApplyService.updateEsignApply(esignApply);
				        paramMap.clear();
				        applyList.clear();
			        }else {
						log.error("====》》》》checkFbbkDataTask 数据校验失败，状态转为失败  id:"+esignApply.getId()+" 清单年月："+esignApply.getFileMonth()+"《《《《====");
			        	esignApply.setFlowStatus("9");
						esignApply.setStopUser("系统任务");
						esignApply.setStopTime(new Date());
						esignApply.setStopRemark("校验数据失败，存在同业务数据不匹配的情况");
				        esignApplyService.updateEsignApply(esignApply);
			        }
				} catch (Exception e) {
					log.error("====》》》》checkFbbkDataTask 执行异常，状态转为失败  id:"+esignApply.getId()+" 清单年月："+esignApply.getFileMonth()+"《《《《====",e);
					esignApply.setFlowStatus("9");
					esignApply.setStopUser("系统任务");
					esignApply.setStopTime(new Date());
					esignApply.setStopRemark("校验数据或PDF生成异常");
			        esignApplyService.updateEsignApply(esignApply);
				}
			}
		}else {
			log.info("====》》》》checkFbbkDataTask 没有符合条件的任务需要执行，此次任务结束《《《《====");
		}
		
		
	}
	//校验文件内容
	private boolean checkFbbkData(List<FbbkDbmxqdInfo> applyList) {
		
		IDDataSqlManualTaskService idDataSqlManualTaskService = SpringUtils.getBean(IDDataSqlManualTaskService.class);
		TaskService TaskService = SpringUtils.getBean(TaskService.class);
		EncryptService encryptService = SpringUtils.getBean(EncryptService.class);
		String platformNo="XJ";
		String useType = TaskService.getUseType(platformNo);
		String remark = idDataSqlManualTaskService.getPlatformRemark(platformNo);
		//根据当前系统获取redis存储的key
        String keyCode =  platformNo + ":" + useType;
        String pushCode = encryptService.getPushCodeByRedis("SJPT_PUSHCODE:SYSTEM_"+platformNo).toString();
        //拼接访问地址
        String requestPort = TaskCommon.jointIPAndPort(remark, "S2m/query");
        String sql="SELECT apply_no,`name` client_name,loan_amt,DATE_FORMAT(loan_time,'%Y%m%d') loan_time,DATE_FORMAT(due_date,'%Y%m%d') due_date FROM ln_loan_info where apply_no in (";
        String applyNos="";
        int count = 0;
        List<Map<String, Object>> allXjList=new ArrayList<Map<String,Object>>();
        for (FbbkDbmxqdInfo fbbkDbmxqdInfo : applyList) {
        	applyNos=applyNos+"'JT"+fbbkDbmxqdInfo.getApplyNo()+"',";
        	count++;
        	if(count%200==0) {
            	Map<String, Object> responseDataMap = this.requestSysTool(requestPort, sql+applyNos.substring(0, applyNos.length()-1)+")", "Post", keyCode, pushCode);
            	count=0;
            	applyNos="";
            	if(SecretStatus.STATUS000000.getCode().equals(responseDataMap.get("sysCode").toString())) {
                    log.debug("=========》checkFbbkDataTask 调用获取数据接口成功，请求码为"+responseDataMap.get("sysCode").toString()+"《==========");
                    List<Map<String, Object>> xjList = JSONObject.parseObject(responseDataMap.get("data").toString(), List.class);
                    allXjList.addAll(xjList);
                    xjList.clear();
                }else{
                    log.debug("=========》checkFbbkDataTask 调用获取数据接口出错，请求码为"+responseDataMap.get("sysCode").toString()+"，返回日志为："+responseDataMap.get("sysMsg").toString()+"《==========");
                }
        	}
			
		}
        //处理不满一批的
        if(count>0) {
        	Map<String, Object> responseDataMap = this.requestSysTool(requestPort, sql+applyNos.substring(0, applyNos.length()-1)+")", "Post", keyCode, pushCode);
        	count=0;
        	applyNos="";
        	if(SecretStatus.STATUS000000.getCode().equals(responseDataMap.get("sysCode").toString())) {
                log.debug("=========》checkFbbkDataTask 调用获取数据接口成功，请求码为"+responseDataMap.get("sysCode").toString()+"《==========");
                List<Map<String, Object>> xjList = JSONObject.parseObject(responseDataMap.get("data").toString(), List.class);
                allXjList.addAll(xjList);
                xjList.clear();
            }else{
                log.debug("=========》checkFbbkDataTask 调用获取数据接口出错，请求码为"+responseDataMap.get("sysCode").toString()+"，返回日志为："+responseDataMap.get("sysMsg").toString()+"《==========");
            }
        }
        //加工待处理数据，以便下一步的对比
        Map<String, String> allXjMap = new HashMap<String, String>();
        for (Map<String, Object> data : allXjList) {
        	allXjMap.put(data.get("apply_no")+"_name", data.get("client_name")+"");
        	allXjMap.put(data.get("apply_no")+"_loan_amt", data.get("loan_amt")+"");
        	allXjMap.put(data.get("apply_no")+"_loan_time", data.get("loan_time")+"");
        	allXjMap.put(data.get("apply_no")+"_due_date", data.get("due_date")+"");
        }
        //对比数据
        int rscount = 0;
        DecimalFormat df = new DecimalFormat("#,##0.00");
        for (FbbkDbmxqdInfo fbbkDbmxqdInfo : applyList) {
			if(allXjMap.get("JT"+fbbkDbmxqdInfo.getApplyNo()+"_name")!=null && 
					fbbkDbmxqdInfo.getClientName().equals(allXjMap.get("JT"+fbbkDbmxqdInfo.getApplyNo()+"_name")) 
					&& allXjMap.get("JT"+fbbkDbmxqdInfo.getApplyNo()+"_loan_amt")!=null && 
					new BigDecimal(fbbkDbmxqdInfo.getLoanAmt()).compareTo(new BigDecimal(allXjMap.get("JT"+fbbkDbmxqdInfo.getApplyNo()+"_loan_amt")))==0
					&& allXjMap.get("JT"+fbbkDbmxqdInfo.getApplyNo()+"_loan_time")!=null && 
							fbbkDbmxqdInfo.getBeginDate().equals(allXjMap.get("JT"+fbbkDbmxqdInfo.getApplyNo()+"_loan_time"))
					&& allXjMap.get("JT"+fbbkDbmxqdInfo.getApplyNo()+"_due_date")!=null && 
							fbbkDbmxqdInfo.getEndDate().equals(allXjMap.get("JT"+fbbkDbmxqdInfo.getApplyNo()+"_due_date"))) {
				fbbkDbmxqdInfo.setLoanAmt(df.format(new BigDecimal(fbbkDbmxqdInfo.getLoanAmt())));
				rscount++; 
			}
		}
        System.out.println("rscount："+rscount);
        System.out.println("applyList.size()："+applyList.size());
        allXjList.clear();
        allXjMap.clear();
        if(rscount==applyList.size()) {
        	return true;
        }else {
        	return false;
        }
	}
	
	
	/**
     * 请求系统工具
     * 调用外部平台系统接口方法
     *
     * @param url           url
     * @param sql           sql
     * @param requestMethod 请求方法
     * @param keyCode       关键代码
     * @param pushCode      把代码
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    public Map<String, Object> requestSysTool(String url,String sql,String requestMethod,String keyCode,String pushCode){
        EncryptService encryptService = SpringUtils.getBean(EncryptService.class);
        Map<String, Object> jsonRequest = encryptService.commonToEncrypt(keyCode, pushCode, sql);
        Map<String, Object> map = JSONObject.parseObject(JSONObject.toJSONString(jsonRequest.get("data")), Map.class);
        Map<String, Object> decryptMap = new HashMap<>();
        Map<String, Object> result = null;
        if("Get".equals(requestMethod)){
            result = DataRequestTool.toGetRequest(url, map, null);
        }else if("Post".equals(requestMethod)){
            result = DataRequestTool.toPostRequest(url, map, null);
        }
        log.debug("调用接口返回："+result);

        if(result.containsKey("data")){
            Map<String, Object> resultData = JSONObject.parseObject(JSONObject.toJSONString(result.get("data")), Map.class);
            String receivePushCode = String.valueOf(resultData.get("pushCode"));
            String encryptData = String.valueOf(resultData.get("encryptData"));
            String encryptAESKey = String.valueOf(resultData.get("encryptAESKey"));
            String sign1 = String.valueOf(resultData.get("sign1"));
            String sign2 = String.valueOf(resultData.get("sign2"));
            String sign3 = String.valueOf(resultData.get("sign3"));

            decryptMap = encryptService.commonToDecrypt(keyCode, receivePushCode, encryptData, sign1, sign2, sign3, encryptAESKey);
        }

        if(result.containsKey("msg")){
            decryptMap.put("sysMsg",result.get("msg"));
        }else {
            decryptMap.put("sysMsg","没有得到返回的msg信息");
        }

        if(result.containsKey("code")){
            decryptMap.put("sysCode",result.get("code"));
        }else {
            decryptMap.put("sysCode","没有得到返回的code信息");
        }

        return  decryptMap;
    }
	
	/**
	 * 协议签章
	 */
	public void fbbkPdfSignTask(){
		IEsignApplyService esignApplyService=SpringUtils.getBean(IEsignApplyService.class);
		List<EsignApply> list= esignApplyService.selectEsignApplyEsignList();
		if(!CollectionUtils.isEmpty(list)){
			log.info("====》》》》fbbkPdfSignTask 查询到需要执行的数据，开始执行《《《《====");
			for (EsignApply esignApply : list) {
		        String signPdfFileName = esignApply.getRealFileName().substring(0,esignApply.getRealFileName().lastIndexOf(".")-1)+"_esign.pdf";
			    try {
			    	//计算签章位置
			    	float a4Xscale = 9.5f/140.5f;//A4纸100像素X轴偏移量比例
			    	float a4Yscale = 9.5f/199f;//A4纸100像素Y轴偏移量比例
			    	//A4纸尺寸    21*29.7cm 
			    	float posX=0.45f/a4Xscale*0.6f*100;
			    	float posY=0;
		    		if((esignApply.getFileDataSize()+1f)%37f!=0) {
		    			posY=(1-((esignApply.getFileDataSize()+1f)%37f)/37f)/a4Yscale*0.4f*100;
			        }
		    		System.out.println("posX:"+posX);
		    		System.out.println("posY:"+posY);
					RunProcess.doSign_Platform_Custom(profilePath+"/FbbkESign/"+esignApply.getFilePathPdf(),
							profilePath+"/FbbkESign/"+signPdfFileName,
							esignApply.getFilePage()+"",
							posX,
							posY);
			        esignApply.setFlowStatus("6");
			        esignApply.setFilePathEsign(signPdfFileName);
			        esignApply.setEsignFalg("Y");
			        esignApply.setEsignTime(new Date());
			        esignApplyService.updateEsignApply(esignApply);
				} catch (DefineException e) {
					e.printStackTrace();
					log.error("====》》》》fbbkPdfSignTask 执行异常，状态转为失败  id:"+esignApply.getId()+" 清单年月："+esignApply.getFileMonth()+"《《《《====",e);
					esignApply.setFlowStatus("9");
					esignApply.setStopUser("系统任务");
					esignApply.setStopTime(new Date());
					esignApply.setStopRemark("PDF签章异常");
			        esignApplyService.updateEsignApply(esignApply);
				}
			}

			log.info("====》》》》fbbkPdfSignTask 执行完成《《《《====");
		}else {
			log.info("====》》》》fbbkPdfSignTask 没有符合条件的任务需要执行，此次任务结束《《《《====");
		}
		
	}
}
