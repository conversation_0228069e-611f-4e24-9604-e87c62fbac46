package com.ruoyi.quartz.service.impl;

import com.ruoyi.common.core.DateNumerationUtils;
import com.ruoyi.quartz.service.TotalRepay7Service;
import lombok.extern.slf4j.Slf4j;
import org.ruoyi.core.domain.DData;
import org.ruoyi.core.domain.DProjectParameter;
import org.ruoyi.core.mapper.DDataMapper;
import org.ruoyi.core.mapper.DProjectParameterMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TotalRepay7ServiceImpl.java
 * @Description TODO
 * @createTime 2023年06月19日 09:53:00
 */
@Slf4j
@Service
public class TotalRepay7ServiceImpl implements TotalRepay7Service {

    private static final Logger logger = LoggerFactory.getLogger(TotalRepay7ServiceImpl.class);
    
    @Autowired
    private DDataMapper dDataMapper;
    @Autowired
    private DProjectParameterMapper dProjectParameterMapper;

    /**
     * 计算repay7 五天内的数据
     */
    @Override
    public void computeRepay7DataToday() {
        //每日执行五天内的数据  获取今天三天之前和四天之前的日期
        //获取五天内的日期  包括今天
        //五天
        String fourDayAgo = LocalDateTime.now().plusDays(-4).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
//        //今天
        String threeDayAgo = LocalDateTime.now().plusDays(-0).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
//                 String fourDayAgo = "2023-02-27";
//                String threeDayAgo ="2023-03-03";
        //获取两个日期中间的所有日期
        List<String> betweenDate = DateNumerationUtils.getBetweenDate(fourDayAgo, threeDayAgo);
        for (String s : betweenDate) {
            String preDateByDate = DateNumerationUtils.getPreDateByDate(s);
            this.computeDateRepay7Total(s,preDateByDate);
        }

    }


    /**
     * 计算repay7存量数据
     *
     */
    @Override
    public void computeRepay7Stock(){
        //获取到所有符合条件的 累计代偿金额和累计还款金额大于0.00的数据的日期
        List<Map<String, Object>> repay7Date = dDataMapper.getRepay7Date();
        //循环日期
        for (Map<String, Object> map : repay7Date) {
            String reconDate = map.get("recon_date").toString();
            String preDateByDate = DateNumerationUtils.getPreDateByDate(reconDate);
            this.computeDateRepay7Total(reconDate,preDateByDate);
        }
        //得到当前日期前一天的日期 调用计算方法


    }

    /**
     * 计算日期repay7
     * 查询要计算的数据并计算入库
     * @param today     今天
     * @param yesterday 昨天
     */
    public void computeDateRepay7Total(String today ,String yesterday){
        List<String> reconDate = new ArrayList<>();
        reconDate.add(today);
        reconDate.add(yesterday);
        //获取符合条件的数据
        List<DData> dData = dDataMapper.getcomputeRepay8Data(reconDate);
        if(dData.size()>0){
            //转换 拼接为 系统+公司+合作+资金+产品+日期为key 数据为value
            Map<String, DData> computeData = this.listToMap(dData);
            //查询配置表的数据 循环取值
            DProjectParameter dProjectParameter = new DProjectParameter();
            //2024-05-17 条件修改
            dProjectParameter.setIsProjectTd("Y");
//            dProjectParameter.setIsProjectRepay1("Y");
//            dProjectParameter.setIsProjectRepay4("Y");
//            dProjectParameter.setIsProjectRepay5("Y");
//            dProjectParameter.setIsProjectRepay7("Y");
//            dProjectParameter.setIsProjectRepay8("Y");
            dProjectParameter.setIsProjectTotalRepay("Y");
            dProjectParameter.setIsProjectTotalRepay7("Y");
            dProjectParameter.setIsMapping("Y");
            dProjectParameter.setStatus("0");
            List<DProjectParameter> dProjectParameters = dProjectParameterMapper.selectDProjectParameterList(dProjectParameter);
            List<DData> updateList = new ArrayList<>();
            //如果没查到任何数据停止计算
            if(dProjectParameters.size()>0){
                //循环查到的数据
                for (DProjectParameter projectParameter : dProjectParameters) {
                    String todaykey = projectParameter.getPlatformNo()+"_"+projectParameter.getCustNo()+"_"+projectParameter.getPartnerNo()+"_"+projectParameter.getFundNo()+"_"+projectParameter.getProductNo()+"_"+today;
                    String yesterdaykey = projectParameter.getPlatformNo()+"_"+projectParameter.getCustNo()+"_"+projectParameter.getPartnerNo()+"_"+projectParameter.getFundNo()+"_"+projectParameter.getProductNo()+"_"+yesterday;
                    DData dDataToday = computeData.get(todaykey);
                    DData dDataYester= computeData.get(yesterdaykey);
                    //如果没获取到当前产品+日期的数据则跳过 进行下次循环
                    if(null == dDataToday){
                        logger.info("没有查询到"+projectParameter.getPlatformNo()+"_"+projectParameter.getCustNo()+"_"+projectParameter.getPartnerNo()+"_"+projectParameter.getFundNo()+"_"+projectParameter.getProductNo()+"的时间为："+today+"的数据，跳过"+today+"计算");
                    }else {
                        //如果没有得到当前产品+昨天  的数据也跳过
                        if (null == dDataYester) {
                            logger.info("没有查询到" + projectParameter.getPlatformNo() + "_" + projectParameter.getCustNo() + "_" + projectParameter.getPartnerNo() + "_" + projectParameter.getFundNo() + "_" + projectParameter.getProductNo() + "的时间为：" + yesterday + "的数据，跳过" + today + "计算");
                        } else {
                            //用今天的total_compensate_print_amount-昨天的total_compensate_print_amount 存入今天的add_compensate_amount和add_compensate_print_amount
                            BigDecimal subtract = dDataToday.getTotalCompensatePrintAmount().subtract(dDataYester.getTotalCompensatePrintAmount());
                            dDataToday.setAddCompensateAmount(subtract);
                            dDataToday.setAddCompensatePrintAmount(subtract);
                            //用今天的total_repay_print_amount-昨天的total_repay_print_amount   存入add_repay_print_amount
                            BigDecimal subtract1 = dDataToday.getTotalRepayPrintAmount().subtract(dDataYester.getTotalRepayPrintAmount());
                            dDataToday.setAddRepayPrintAmount(subtract1);
                            updateList.add(dDataToday);
                            int i = dDataMapper.updateBatch(updateList);
                            logger.info("计算通道类业务的每日代偿及还款的发生额，此次入库共" + i + "条");
                        }
                    }
                }
            }else {
                logger.info("查询到的符合条件的配置表为空，请检查数据配置表配置");
            }

        }else {
            logger.info("查询到的需要计算数据为空，请检查数据配置表配置");
        }

    }

    public Map<String,DData> listToMap(List<DData> dDatas){
        SimpleDateFormat sdf =new SimpleDateFormat("yyyy-MM-dd");
        HashMap<String, DData> returnMap = new HashMap<>();
        for (DData dData : dDatas) {
            String format = sdf.format(dData.getReconDate());
            returnMap.put(dData.getPlatformNo()+"_"+dData.getCustNo()+"_"+dData.getPartnerNo()+"_"+dData.getFundNo()+"_"+dData.getProductNo()+"_"+format,dData);
        }
        return returnMap;
    }
}
