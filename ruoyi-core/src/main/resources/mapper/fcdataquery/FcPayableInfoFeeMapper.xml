<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.modules.fcdataquery.mapper.FcPayableInfoFeeMapper">


    <select id="getChannelMapCompany" resultType="org.ruoyi.core.modules.fcdataquery.po.ChannelMapProjectPo">
        select cha.id as projectId, fan.user_name as companyName, cha.nick_name as channelName
        from (
            select opd.id, xpc.nick_name from oa_project_deploy opd
            inner join xmgl_project_deploy xpd on opd.id = xpd.deploy_id
            inner join xmgl_project xp on xpd.project_id = xp.id
            inner join xmgl_project_channel xpc on xp.id = xpc.project_id
            where xpd.`status` = 1 and xp.channel_type = 1) cha
        inner join (
            select opd.id, ot.user_name from oa_project_deploy opd
            inner join cw_project cw on opd.id = cw.oa_project_deploy_id
            inner join cw_project_fee cpf on cw.id = cpf.project_id
            inner join cw_project_cust cpc on cpf.cust_id = cpc.id
            inner join oa_trader ot ON cpc.oa_trader_id=ot.id
            where ot.trader_type = 1 and ot.type = 0
            group by opd.id, ot.user_name
            union
            select opd.id, ot.user_name from oa_project_deploy opd
            inner join cw_project cw on opd.id = cw.oa_project_deploy_id
            inner join cw_project_fee cpf on cw.id = cpf.project_id
            inner join oa_trader ot ON cpf.reveal_fee_company_id=ot.id
            where ot.trader_type = 1 and ot.type = 0
            group by opd.id, ot.user_name
        ) fan on cha.id = fan.id
        where fan.user_name not in (select company_name from sys_company where is_inside = 1)
    </select>

    <select id="getVoucherDetailByProject" parameterType="org.ruoyi.core.modules.fcdataquery.vo.PayableInfoFeeDetailVo"
            resultType="org.ruoyi.core.modules.fcdataquery.po.PayableInfoFeeDetailPo">
        select fpm.project_name projectName, fv.voucher_date dataDay, ROUND(ifnull(fvd.credit_amount, 0.00), 2) addAmt, ROUND(ifnull(fvd.debit_amount, 0.00), 2) subtractAmt from financial_voucher fv
        inner join financial_voucher_details fvd on fv.id = fvd.voucher_id and fv.valid=1
        inner join fc_project_mapping fpm on fvd.subject_id = fpm.subject_id
        <where>
            <if test="subjectIds != null and !subjectIds.isEmpty()">
                AND fvd.subject_id IN
                <foreach collection="subjectIds" item="subjectId" open="(" close=")" separator=",">
                    #{subjectId}
                </foreach>
            </if>
            and fv.voucher_date between #{startTime} and #{endTime}
            and fvd.subject_name like '%应付账款%'
            and fvd.subject_name not like '%应付账款2%'
            and fvd.subject_name not like '%应付账款3%'
        </where>
        group by fpm.project_id
        order by fv.voucher_date
    </select>
</mapper>