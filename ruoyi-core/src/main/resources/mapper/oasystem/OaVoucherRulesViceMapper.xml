<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.oasystem.mapper.OaVoucherRulesViceMapper">

    <resultMap type="OaVoucherRulesVice" id="OaVoucherRulesViceResult">
        <result property="id"    column="id"    />
        <result property="rulesMainId"    column="rules_main_id"    />
        <result property="abstractJson"    column="abstract_json"    />
        <result property="isEnableDynamicForm" column="is_enable_dynamic_form"/>
        <result property="rulerType"    column="ruler_type"    />
        <result property="finanicalWord"    column="finanical_word"    />
        <result property="collAccountingField"    column="coll_accounting_field"    />
        <result property="collAccountingFieldName"    column="coll_accounting_field_name"    />
        <result property="name"    column="name"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectOaVoucherRulesViceVo">
        select id, rules_main_id, abstract_json,is_enable_dynamic_form, ruler_type, finanical_word, coll_accounting_field, coll_accounting_field_name, name, status, create_by, create_time, update_by, update_time from oa_voucher_rules_vice
    </sql>
    <select id="selectOaVoucherRulesViceList" parameterType="OaVoucherRulesVice" resultMap="OaVoucherRulesViceResult">
        <include refid="selectOaVoucherRulesViceVo"/>
        <where>  
            <if test="rulesMainId != null "> and rules_main_id = #{rulesMainId}</if>
            <if test="abstractJson != null  and abstractJson != ''"> and abstract_json = #{abstractJson}</if>
            <if test="isEnableDynamicForm != null and isEnableDynamicForm != ''">and is_enable_dynamic_form = #{isEnableDynamicForm}</if>
            <if test="finanicalWord != null  and finanicalWord != ''"> and finanical_word = #{finanicalWord}</if>
            <if test="rulerType != null  and rulerType != ''"> and ruler_type = #{rulerType}</if>
            <if test="collAccountingField != null  and collAccountingField != ''"> and coll_accounting_field = #{collAccountingField}</if>
            <if test="collAccountingFieldName != null  and collAccountingFieldName != ''"> and coll_accounting_field_name like concat('%', #{collAccountingFieldName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectOaVoucherRulesViceById" parameterType="Long" resultMap="OaVoucherRulesViceResult">
        <include refid="selectOaVoucherRulesViceVo"/>
        where id = #{id}
    </select>
    <select id="selectIdByMainId" resultType="java.lang.Long" parameterType="java.lang.Long">
        select id from oa_voucher_rules_vice where  rules_main_id = #{id}

    </select>

    <insert id="insertOaVoucherRulesVice" parameterType="OaVoucherRulesVice" useGeneratedKeys="true" keyProperty="id">
        insert into oa_voucher_rules_vice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rulesMainId != null">rules_main_id,</if>
            <if test="abstractJson != null">abstract_json,</if>
            <if test="isEnableDynamicForm != null and isEnableDynamicForm != ''">is_enable_dynamic_form,</if>
            <if test="rulerType != null">ruler_type,</if>
            <if test="finanicalWord != null">finanical_word,</if>
            <if test="collAccountingField != null">coll_accounting_field,</if>
            <if test="collAccountingFieldName != null">coll_accounting_field_name,</if>
            <if test="name != null">name,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="rulesMainId != null">#{rulesMainId},</if>
            <if test="abstractJson != null">#{abstractJson},</if>
            <if test="isEnableDynamicForm != null and isEnableDynamicForm != ''">#{isEnableDynamicForm},</if>
            <if test="rulerType != null">#{rulerType},</if>
            <if test="finanicalWord != null">#{finanicalWord},</if>
            <if test="collAccountingField != null">#{collAccountingField},</if>
            <if test="collAccountingFieldName != null">#{collAccountingFieldName},</if>
            <if test="name != null">#{name},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateOaVoucherRulesVice" parameterType="OaVoucherRulesVice">
        update oa_voucher_rules_vice
        <trim prefix="SET" suffixOverrides=",">
            <if test="rulesMainId != null">rules_main_id = #{rulesMainId},</if>
            <if test="abstractJson != null">abstract_json = #{abstractJson},</if>
            <if test="isEnableDynamicForm != null and isEnableDynamicForm != ''">is_enable_dynamic_form = #{isEnableDynamicForm},</if>
            <if test="rulerType != null">ruler_type = #{rulerType},</if>
            <if test="finanicalWord != null">finanical_word = #{finanicalWord},</if>
            <if test="collAccountingField != null">coll_accounting_field = #{collAccountingField},</if>
            <if test="collAccountingFieldName != null">coll_accounting_field_name = #{collAccountingFieldName},</if>
            <if test="name != null">name = #{name},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>
    <delete id="deleteOaVoucherRulesViceById" parameterType="Long">
        delete from oa_voucher_rules_vice where id = #{id}
    </delete>

    <delete id="deleteOaVoucherRulesViceByIds" parameterType="String">
        delete from oa_voucher_rules_vice where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteViceByMainId" parameterType="java.lang.Long">

        delete from oa_voucher_rules_vice where rules_main_id = #{id}
    </delete>
</mapper>