<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.oasystem.mapper.OaProcessTemplateUserMapper">

    <resultMap type="OaProcessTemplateUser" id="OaProcessTemplateUserResult">
        <result property="templateId"    column="template_id"    />
        <result property="userId"    column="user_id"    />
        <result property="status"    column="status"    />
    </resultMap>

    <sql id="selectOaProcessTemplateUserVo">
        select template_id, user_id, `status` from oa_process_template_user
    </sql>

    <select id="selectByTemplateId" resultMap="OaProcessTemplateUserResult">
        <include refid="selectOaProcessTemplateUserVo"/>
        <where>
            <if test="templateId != null"> and template_id = #{templateId}</if>
        </where>
    </select>

    <insert id="batchAddOaProcessTemplateUser" useGeneratedKeys="true" keyProperty="id">
        insert into oa_process_template_user (template_id,user_id,status)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.templateId}, #{item.userId}, #{item.status})
        </foreach>
    </insert>

</mapper>