<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.oasystem.mapper.OaEditApproveGeneralityUserMapper">



    <select id="selectOaEditApproveGeneralityUserByOaApplyTypeAndOaApplyId" resultType="org.ruoyi.core.oasystem.domain.OaEditApproveGeneralityUser">
        SELECT ovru.id AS id, ovru.oa_apply_type AS oaApplyType, ovru.oa_apply_id AS oaApplyId, ovru.user_id AS userId, ovru.user_flag AS userFlag, ovru.status AS status, ovru.create_by AS createBy, ovru.create_time AS createTime, ovru.update_by AS updateBy, ovru.update_time AS updateTime,su.nick_name AS userNickName FROM oa_edit_approve_generality_user ovru LEFT JOIN sys_user su ON ovru.user_id=su.user_id WHERE ovru.oa_apply_type=#{oaApplyType,jdbcType=VARCHAR} AND ovru.oa_apply_id=#{oaApplyId,jdbcType=BIGINT} AND ovru.status='0'
    </select>

    <insert id="insertOaEditApproveGeneralityUser" parameterType="OaEditApproveGeneralityUser" useGeneratedKeys="true" keyProperty="id">
        insert into oa_edit_approve_generality_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="oaApplyType != null">oa_apply_type,</if>
            <if test="oaApplyId != null">oa_apply_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="userFlag != null and userFlag != ''">user_flag,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="oaApplyType != null">#{oaApplyType,jdbcType=VARCHAR},</if>
            <if test="oaApplyId != null">#{oaApplyId,jdbcType=BIGINT},</if>
            <if test="userId != null">#{userId,jdbcType=BIGINT},</if>
            <if test="userFlag != null and userFlag != ''">#{userFlag,jdbcType=VARCHAR},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <delete id="deleteOaEditApproveGeneralityUserByOaApplyTypeAndOaApplyId">
        DELETE FROM oa_edit_approve_generality_user WHERE oa_apply_type=#{oaApplyType,jdbcType=VARCHAR} AND oa_apply_id=#{oaApplyId,jdbcType=BIGINT}
    </delete>

    <select id="selectOaEditApproveGeneralityUserByOaApplyTypeAndOaApplyIdAndUserId" resultType="org.ruoyi.core.oasystem.domain.OaEditApproveGeneralityUser">
        SELECT ovru.id AS id, ovru.oa_apply_type AS oaApplyType, ovru.oa_apply_id AS oaApplyId, ovru.user_id AS userId, ovru.user_flag AS userFlag, ovru.status AS status, ovru.create_by AS createBy, ovru.create_time AS createTime, ovru.update_by AS updateBy, ovru.update_time AS updateTime,su.nick_name AS userNickName FROM oa_edit_approve_generality_user ovru LEFT JOIN sys_user su ON ovru.user_id=su.user_id WHERE ovru.oa_apply_type=#{oaApplyType,jdbcType=VARCHAR} AND ovru.oa_apply_id=#{oaApplyId,jdbcType=BIGINT} AND  ovru.user_id=#{userId,jdbcType=BIGINT} AND ovru.status='0'
    </select>

    <select id="queryDataByOaApplyTypeAndOaApplyId" resultType="map">

        SELECT  su.user_id id, su.nick_name name,su.status    FROM oa_edit_approve_generality_user oeagu LEFT JOIN sys_user su ON oeagu.user_id = su.user_id
        WHERE oeagu.oa_apply_type = #{oaApplyType} AND oeagu.oa_apply_id = #{oaApplyId} AND oeagu.user_flag = #{userFlag} group by id,name,status
    </select>



    <select id="queryDataObjectByOaApplyTypeAndOaApplyId" resultType="org.ruoyi.core.oasystem.domain.OaEditApproveGeneralityUser">
        SELECT ovru.id AS id, ovru.oa_apply_type AS oaApplyType, ovru.oa_apply_id AS oaApplyId, ovru.user_id AS userId, ovru.user_flag AS userFlag, ovru.status AS status, ovru.create_by AS createBy, ovru.create_time AS createTime, ovru.update_by AS updateBy, ovru.update_time AS updateTime,su.nick_name AS userNickName FROM oa_edit_approve_generality_user ovru LEFT JOIN sys_user su ON ovru.user_id=su.user_id
        WHERE ovru.oa_apply_type=#{oaApplyType} AND ovru.oa_apply_id=#{oaApplyId} AND ovru.status='0' AND ovru.user_flag = #{userFlag}
    </select>

    <delete id="deleteDataByAppIdAndUserIdAndUserType">
        DELETE FROM oa_edit_approve_generality_user WHERE oa_apply_type=#{oaApplyType,jdbcType=VARCHAR} AND oa_apply_id=#{id,jdbcType=BIGINT} and user_id = #{userid} and user_flag = #{userFlag}
    </delete>

    <delete id="deleteDataByAppIdAndUserType">
        DELETE FROM oa_edit_approve_generality_user WHERE oa_apply_type=#{oaApplyType,jdbcType=VARCHAR} AND oa_apply_id=#{id,jdbcType=BIGINT}  and user_flag = #{userFlag}
    </delete>


</mapper>