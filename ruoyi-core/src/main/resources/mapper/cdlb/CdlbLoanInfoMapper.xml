<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.cdlb.mapper.CdlbLoanInfoMapper">

    <resultMap type="CdlbLoanInfo" id="CdlbLoanInfoResult">
        <result property="id"    column="id"    />
        <result property="cdlbBinding"    column="cdlb_binding"    />
        <result property="cdlbId"    column="cdlb_id"    />
        <result property="platformNo"    column="platform_no"    />
        <result property="custNo"    column="cust_no"    />
        <result property="partnerNo"    column="partner_no"    />
        <result property="fundNo"    column="fund_no"    />
        <result property="productNo"    column="product_no"    />
        <result property="loanNo"    column="loan_no"    />
        <result property="clientName"    column="client_name"    />
        <result property="clientCardId"    column="client_card_id"    />
        <result property="clientCardAddress"    column="client_card_address"    />
        <result property="loanStatus"    column="loan_status"    />
        <result property="applyTime"    column="apply_time"    />
        <result property="loanAmt"    column="loan_amt"    />
        <result property="balanceAmt"    column="balance_amt"    />
        <result property="totalTerm"    column="total_term"    />
        <result property="loanTime"    column="loan_time"    />
        <result property="dueDate"    column="due_date"    />
        <result property="loanReqNo"    column="loan_req_no"    />
        <result property="repayWay"    column="repay_way"    />
        <result property="loanUse"    column="loan_use"    />
        <result property="carBrandName"    column="car_brand_name"    />
        <result property="carNo"    column="car_no"    />
        <result property="carVin"    column="car_vin"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_Time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_Time"    />
        <result property="cdlbRecord"    column="cdlb_record"    />
        <result property="reconciliationTime"    column="reconciliation_time"    />
        <result property="registerTime"    column="register_time"    />
    </resultMap>

    <sql id="selectCdlbLoanInfoVo">
        select id, cdlb_binding, cdlb_id,reconciliation_time, platform_no, cust_no, partner_no, fund_no, product_no, loan_no, client_name, client_card_id, client_card_address, cdlb_record,loan_status, apply_time, loan_amt, balance_amt, total_term, loan_time, due_date, loan_req_no, repay_way, loan_use, car_brand_name, car_no, car_vin, status, create_by, create_Time, update_by, update_Time from cdlb_loan_info
    </sql>
    <sql id="selectCdlbLoanInfo">
        select id, client_card_id, loan_no, apply_time,  loan_amt,   cust_no from cdlb_loan_info
</sql>
    <select id="selectCdlbLoanInfos" parameterType="CdlbLoanInfo" resultMap="CdlbLoanInfoResult">
        <include refid="selectCdlbLoanInfo"/>
        <where>
            <if test="registerTime != null "> and register_time = #{registerTime}</if>
            <if test="cdlbBinding != null  and cdlbBinding != ''"> and cdlb_binding = #{cdlbBinding}</if>
            <if test="cdlbId != null "> and cdlb_id = #{cdlbId}</if>
            <if test="platformNo != null  and platformNo != ''"> and platform_no = #{platformNo}</if>
            <if test="custNo != null  and custNo != ''"> and cust_no = #{custNo}</if>
            <if test="partnerNo != null  and partnerNo != ''"> and partner_no = #{partnerNo}</if>
            <if test="fundNo != null  and fundNo != ''"> and fund_no = #{fundNo}</if>
            <if test="productNo != null  and productNo != ''"> and product_no = #{productNo}</if>
            <if test="loanNo != null  and loanNo != ''"> and loan_no = #{loanNo}</if>
            <if test="clientName != null  and clientName != ''"> and client_name like concat('%', #{clientName}, '%')</if>
            <if test="clientCardId != null  and clientCardId != ''"> and client_card_id = #{clientCardId}</if>
            <if test="clientCardAddress != null  and clientCardAddress != ''"> and client_card_address = #{clientCardAddress}</if>
            <if test="loanStatus != null  and loanStatus != ''"> and loan_status = #{loanStatus}</if>
            <if test="applyTime != null "> and apply_time = #{applyTime}</if>
            <if test="loanAmt != null "> and loan_amt = #{loanAmt}</if>
            <if test="balanceAmt != null "> and balance_amt = #{balanceAmt}</if>
            <if test="totalTerm != null "> and total_term = #{totalTerm}</if>
            <if test="loanTime != null "> and loan_time = #{loanTime}</if>
            <if test="dueDate != null "> and due_date = #{dueDate}</if>
            <if test="loanReqNo != null  and loanReqNo != ''"> and loan_req_no = #{loanReqNo}</if>
            <if test="repayWay != null  and repayWay != ''"> and repay_way = #{repayWay}</if>
            <if test="loanUse != null  and loanUse != ''"> and loan_use = #{loanUse}</if>
            <if test="carBrandName != null  and carBrandName != ''"> and car_brand_name like concat('%', #{carBrandName}, '%')</if>
            <if test="carNo != null  and carNo != ''"> and car_no = #{carNo}</if>
            <if test="carVin != null  and carVin != ''"> and car_vin = #{carVin}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="createTime != null "> and create_Time = #{createTime}</if>
            <if test="updateTime != null "> and update_Time = #{updateTime}</if>
            <if test="cdlbRecord != null "> and cdlb_record = #{cdlbRecord}</if>
            <if test="reconciliationTime != null "> and reconciliation_time = #{reconciliationTime}</if>
        </where>
    </select>
    <select id="selectCdlbLoanInfoList" parameterType="CdlbLoanInfo" resultMap="CdlbLoanInfoResult">
        <include refid="selectCdlbLoanInfoVo"/>
        <where>
            <if test="registerTime != null "> and register_time = #{registerTime}</if>
            <if test="cdlbBinding != null  and cdlbBinding != ''"> and cdlb_binding = #{cdlbBinding}</if>
            <if test="cdlbId != null "> and cdlb_id = #{cdlbId}</if>
            <if test="platformNo != null  and platformNo != ''"> and platform_no = #{platformNo}</if>
            <if test="custNo != null  and custNo != ''"> and cust_no = #{custNo}</if>
            <if test="partnerNo != null  and partnerNo != ''"> and partner_no = #{partnerNo}</if>
            <if test="fundNo != null  and fundNo != ''"> and fund_no = #{fundNo}</if>
            <if test="productNo != null  and productNo != ''"> and product_no = #{productNo}</if>
            <if test="loanNo != null  and loanNo != ''"> and loan_no = #{loanNo}</if>
            <if test="clientName != null  and clientName != ''"> and client_name like concat('%', #{clientName}, '%')</if>
            <if test="clientCardId != null  and clientCardId != ''"> and client_card_id = #{clientCardId}</if>
            <if test="clientCardAddress != null  and clientCardAddress != ''"> and client_card_address = #{clientCardAddress}</if>
            <if test="loanStatus != null  and loanStatus != ''"> and loan_status = #{loanStatus}</if>
            <if test="applyTime != null "> and apply_time = #{applyTime}</if>
            <if test="loanAmt != null "> and loan_amt = #{loanAmt}</if>
            <if test="balanceAmt != null "> and balance_amt = #{balanceAmt}</if>
            <if test="totalTerm != null "> and total_term = #{totalTerm}</if>
            <if test="loanTime != null "> and loan_time = #{loanTime}</if>
            <if test="dueDate != null "> and due_date = #{dueDate}</if>
            <if test="loanReqNo != null  and loanReqNo != ''"> and loan_req_no = #{loanReqNo}</if>
            <if test="repayWay != null  and repayWay != ''"> and repay_way = #{repayWay}</if>
            <if test="loanUse != null  and loanUse != ''"> and loan_use = #{loanUse}</if>
            <if test="carBrandName != null  and carBrandName != ''"> and car_brand_name like concat('%', #{carBrandName}, '%')</if>
            <if test="carNo != null  and carNo != ''"> and car_no = #{carNo}</if>
            <if test="carVin != null  and carVin != ''"> and car_vin = #{carVin}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="createTime != null "> and create_Time = #{createTime}</if>
            <if test="updateTime != null "> and update_Time = #{updateTime}</if>
            <if test="cdlbRecord != null "> and cdlb_record = #{cdlbRecord}</if>
            <if test="reconciliationTime != null "> and reconciliation_time = #{reconciliationTime}</if>
        </where>
    </select>

    <select id="selectCdlbLoanInfoById" parameterType="Long" resultMap="CdlbLoanInfoResult">
        <include refid="selectCdlbLoanInfoVo"/>
        where id = #{id}
    </select>
    <select id="selectinfoIdcounts" parameterType="string" resultType="java.lang.Integer">
        select count(id) from  cdlb_loan_info where client_card_id = #{clientCardId}
    </select>

    <insert id="insertCdlbLoanInfo" parameterType="CdlbLoanInfo" useGeneratedKeys="true" keyProperty="id">
        insert into cdlb_loan_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cdlbBinding != null and cdlbBinding != ''">cdlb_binding,</if>
            <if test="cdlbId != null">cdlb_id,</if>
            <if test="platformNo != null and platformNo != ''">platform_no,</if>
            <if test="custNo != null and custNo != ''">cust_no,</if>
            <if test="partnerNo != null and partnerNo != ''">partner_no,</if>
            <if test="fundNo != null and fundNo != ''">fund_no,</if>
            <if test="productNo != null and productNo != ''">product_no,</if>
            <if test="loanNo != null and loanNo != ''">loan_no,</if>
            <if test="clientName != null and clientName != ''">client_name,</if>
            <if test="clientCardId != null and clientCardId != ''">client_card_id,</if>
            <if test="clientCardAddress != null and clientCardAddress != ''">client_card_address,</if>
            <if test="loanStatus != null and loanStatus != ''">loan_status,</if>
            <if test="applyTime != null">apply_time,</if>
            <if test="loanAmt != null">loan_amt,</if>
            <if test="balanceAmt != null">balance_amt,</if>
            <if test="totalTerm != null">total_term,</if>
            <if test="loanTime != null">loan_time,</if>
            <if test="dueDate != null">due_date,</if>
            <if test="loanReqNo != null and loanReqNo != ''">loan_req_no,</if>
            <if test="repayWay != null and repayWay != ''">repay_way,</if>
            <if test="loanUse != null and loanUse != ''">loan_use,</if>
            <if test="carBrandName != null and carBrandName != ''">car_brand_name,</if>
            <if test="carNo != null and carNo != ''">car_no,</if>
            <if test="carVin != null and carVin != ''">car_vin,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_Time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_Time,</if>
            <if test="cdlbRecord != null">cdlb_record,</if>
            <if test="reconciliationTime != null">reconciliation_time,</if>
            <if test="registerTime != null">register_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="cdlbBinding != null and cdlbBinding != ''">#{cdlbBinding},</if>
            <if test="cdlbId != null">#{cdlbId},</if>
            <if test="platformNo != null and platformNo != ''">#{platformNo},</if>
            <if test="custNo != null and custNo != ''">#{custNo},</if>
            <if test="partnerNo != null and partnerNo != ''">#{partnerNo},</if>
            <if test="fundNo != null and fundNo != ''">#{fundNo},</if>
            <if test="productNo != null and productNo != ''">#{productNo},</if>
            <if test="loanNo != null and loanNo != ''">#{loanNo},</if>
            <if test="clientName != null and clientName != ''">#{clientName},</if>
            <if test="clientCardId != null and clientCardId != ''">#{clientCardId},</if>
            <if test="clientCardAddress != null and clientCardAddress != ''">#{clientCardAddress},</if>
            <if test="loanStatus != null and loanStatus != ''">#{loanStatus},</if>
            <if test="applyTime != null">#{applyTime},</if>
            <if test="loanAmt != null">#{loanAmt},</if>
            <if test="balanceAmt != null">#{balanceAmt},</if>
            <if test="totalTerm != null">#{totalTerm},</if>
            <if test="loanTime != null">#{loanTime},</if>
            <if test="dueDate != null">#{dueDate},</if>
            <if test="loanReqNo != null and loanReqNo != ''">#{loanReqNo},</if>
            <if test="repayWay != null and repayWay != ''">#{repayWay},</if>
            <if test="loanUse != null and loanUse != ''">#{loanUse},</if>
            <if test="carBrandName != null and carBrandName != ''">#{carBrandName},</if>
            <if test="carNo != null and carNo != ''">#{carNo},</if>
            <if test="carVin != null and carVin != ''">#{carVin},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="cdlbRecord != null">#{cdlbRecord},</if>
            <if test="reconciliationTime != null">#{reconciliationTime},</if>
            <if test="registerTime != null">#{registerTime},</if>
        </trim>
    </insert>

    <update id="updateCdlbLoanInfo" parameterType="CdlbLoanInfo">
        update cdlb_loan_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="registerTime != null ">register_time = #{registerTime},</if>
            <if test="cdlbBinding != null and cdlbBinding != ''">cdlb_binding = #{cdlbBinding},</if>
            <if test="cdlbId != null">cdlb_id = #{cdlbId},</if>
            <if test="platformNo != null and platformNo != ''">platform_no = #{platformNo},</if>
            <if test="custNo != null and custNo != ''">cust_no = #{custNo},</if>
            <if test="partnerNo != null and partnerNo != ''">partner_no = #{partnerNo},</if>
            <if test="fundNo != null and fundNo != ''">fund_no = #{fundNo},</if>
            <if test="productNo != null and productNo != ''">product_no = #{productNo},</if>
            <if test="loanNo != null and loanNo != ''">loan_no = #{loanNo},</if>
            <if test="clientName != null and clientName != ''">client_name = #{clientName},</if>
            <if test="clientCardId != null and clientCardId != ''">client_card_id = #{clientCardId},</if>
            <if test="clientCardAddress != null and clientCardAddress != ''">client_card_address = #{clientCardAddress},</if>
            <if test="loanStatus != null and loanStatus != ''">loan_status = #{loanStatus},</if>
            <if test="applyTime != null">apply_time = #{applyTime},</if>
            <if test="loanAmt != null">loan_amt = #{loanAmt},</if>
            <if test="balanceAmt != null">balance_amt = #{balanceAmt},</if>
            <if test="totalTerm != null">total_term = #{totalTerm},</if>
            <if test="loanTime != null">loan_time = #{loanTime},</if>
            <if test="dueDate != null">due_date = #{dueDate},</if>
            <if test="loanReqNo != null and loanReqNo != ''">loan_req_no = #{loanReqNo},</if>
            <if test="repayWay != null and repayWay != ''">repay_way = #{repayWay},</if>
            <if test="loanUse != null and loanUse != ''">loan_use = #{loanUse},</if>
            <if test="carBrandName != null and carBrandName != ''">car_brand_name = #{carBrandName},</if>
            <if test="carNo != null and carNo != ''">car_no = #{carNo},</if>
            <if test="carVin != null and carVin != ''">car_vin = #{carVin},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_Time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_Time = #{updateTime},</if>
            <if test="cdlbRecord != null">cdlb_record = #{cdlbRecord},</if>
            <if test="reconciliationTime != null">reconciliation_time = #{reconciliationTime},</if>
        </trim>
        where id = #{id}
    </update>
    <delete id="deleteCdlbLoanInfoById" parameterType="Long">
        delete from cdlb_loan_info where id = #{id}
    </delete>

    <delete id="deleteCdlbLoanInfoByIds" parameterType="String">
        delete from cdlb_loan_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>