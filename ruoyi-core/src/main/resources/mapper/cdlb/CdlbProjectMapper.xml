<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.cdlb.mapper.CdlbProjectMapper">

    
    <resultMap type="CdlbProject" id="CdlbProjectResult">
        <result property="id"    column="id"    />
        <result property="projectName"    column="project_name"    />
        <result property="custId"    column="cust_id"    />
        <result property="custName"    column="cust_name"    />
        <result property="inApplyCount"    column="in_apply_count"    />
        <result property="inOkCount"    column="in_ok_count"    />
        <result property="outApplyCount"    column="out_apply_count"    />
        <result property="outAuditCount"    column="out_audit_count"    />
        <result property="outOkCount"    column="out_ok_count"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_Time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_Time"    />
        <result property="counts"    column="counts"    />
    </resultMap>

    <sql id="selectCdlbProjectVo">
        select id, project_name, cust_id, cust_name, in_apply_count, in_ok_count, out_apply_count, out_audit_count, out_ok_count, status, create_by, create_Time, update_by, update_Time from cdlb_project
    </sql>

    <select id="selectCdlbProjectList" parameterType="CdlbProject" resultMap="CdlbProjectResult">
        <include refid="selectCdlbProjectVo"/>
        <where>  
            <if test="projectName != null  and projectName != ''"> and project_name = #{projectName}</if>
            <if test="custId != null  and custId != ''"> and cust_id = #{custId}</if>
            <if test="custName != null  and custName != ''"> and cust_name = #{custName}</if>
            <if test="inApplyCount != null "> and in_apply_count = #{inApplyCount}</if>
            <if test="inOkCount != null "> and in_ok_count = #{inOkCount}</if>
            <if test="outApplyCount != null "> and out_apply_count = #{outApplyCount}</if>
            <if test="outAuditCount != null "> and out_audit_count = #{outAuditCount}</if>
            <if test="outOkCount != null "> and out_ok_count = #{outOkCount}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="createTime != null "> and create_Time = #{createTime}</if>
            <if test="updateTime != null "> and update_Time = #{updateTime}</if>
        </where>
    </select>
    
    <select id="selectCdlbProjectById" parameterType="Long" resultMap="CdlbProjectResult">
        <include refid="selectCdlbProjectVo"/>
        where id = #{id}
    </select>




    <insert id="insertCdlbProject" parameterType="CdlbProject" useGeneratedKeys="true" keyProperty="id">
        insert into cdlb_project
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectName != null and projectName != ''">project_name,</if>
            <if test="custId != null and custId != ''">cust_id,</if>
            <if test="custName != null and custName != ''">cust_name,</if>
            <if test="inApplyCount != null">in_apply_count,</if>
            <if test="inOkCount != null">in_ok_count,</if>
            <if test="outApplyCount != null">out_apply_count,</if>
            <if test="outAuditCount != null">out_audit_count,</if>
            <if test="outOkCount != null">out_ok_count,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_Time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_Time,</if>

         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectName != null and projectName != ''">#{projectName},</if>
            <if test="custId != null and custId != ''">#{custId},</if>
            <if test="custName != null and custName != ''">#{custName},</if>
            <if test="inApplyCount != null">#{inApplyCount},</if>
            <if test="inOkCount != null">#{inOkCount},</if>
            <if test="outApplyCount != null">#{outApplyCount},</if>
            <if test="outAuditCount != null">#{outAuditCount},</if>
            <if test="outOkCount != null">#{outOkCount},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>

         </trim>
    </insert>

    <update id="updateCdlbProject" parameterType="CdlbProject">
        update cdlb_project
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectName != null and projectName != ''">project_name = #{projectName},</if>
            <if test="custId != null and custId != ''">cust_id = #{custId},</if>
            <if test="custName != null and custName != ''">cust_name = #{custName},</if>
            <if test="inApplyCount != null">in_apply_count = #{inApplyCount},</if>
            <if test="inOkCount != null">in_ok_count = #{inOkCount},</if>
            <if test="outApplyCount != null">out_apply_count = #{outApplyCount},</if>
            <if test="outAuditCount != null">out_audit_count = #{outAuditCount},</if>
            <if test="outOkCount != null">out_ok_count = #{outOkCount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_Time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_Time = #{updateTime},</if>

        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCdlbProjectById" parameterType="Long">
        delete from cdlb_project where id = #{id}
    </delete>

    <delete id="deleteCdlbProjectByIds" parameterType="String">
        delete from cdlb_project where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>