<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.license.mapper.ZzCommonNotifyMapper">
    
    <resultMap type="ZzCommonNotify" id="ZzCommonNotifyResult">
        <result property="id"    column="id"    />
        <result property="licenseId"    column="license_id"    />
        <result property="notifyModule"    column="notify_module"    />
        <result property="url"    column="url"    />
        <result property="notifyType"    column="notify_type"    />
        <result property="notifyMsg"    column="notify_msg"    />
        <result property="disposeUser"    column="dispose_user"    />
        <result property="viewFlag"    column="view_flag"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_Time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_Time"    />
    </resultMap>

    <sql id="selectZzCommonNotifyVo">
        select id, license_id, notify_module, url, notify_type, notify_msg, dispose_user, view_flag, status, create_by, create_Time, update_by, update_Time from zz_common_notify
    </sql>

    <select id="selectZzCommonNotifyList" parameterType="ZzCommonNotify" resultMap="ZzCommonNotifyResult">
        <include refid="selectZzCommonNotifyVo"/>
        <where>  
            <if test="notifyModule != null  and notifyModule != ''"> and notify_module = #{notifyModule}</if>
            <if test="licenseId != null  and licenseId != ''"> and license_id = #{licenseId}</if>
            <if test="url != null  and url != ''"> and url = #{url}</if>
            <if test="notifyType != null  and notifyType != ''"> and notify_type = #{notifyType}</if>
            <if test="notifyMsg != null  and notifyMsg != ''"> and notify_msg = #{notifyMsg}</if>
            <if test="disposeUser != null "> and dispose_user = #{disposeUser}</if>
            <if test="viewFlag != null  and viewFlag != ''"> and view_flag = #{viewFlag}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="createTime != null "> and create_Time = #{createTime}</if>
            <if test="updateTime != null "> and update_Time = #{updateTime}</if>
        </where>
    </select>
    
    <select id="selectZzCommonNotifyById" parameterType="Long" resultMap="ZzCommonNotifyResult">
        <include refid="selectZzCommonNotifyVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertZzCommonNotify" parameterType="ZzCommonNotify">
        insert into zz_common_notify
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="licenseId != null">license_id,</if>
            <if test="notifyModule != null">notify_module,</if>
            <if test="url != null">url,</if>
            <if test="notifyType != null">notify_type,</if>
            <if test="notifyMsg != null">notify_msg,</if>
            <if test="disposeUser != null">dispose_user,</if>
            <if test="viewFlag != null">view_flag,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_Time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_Time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="licenseId != null">#{licenseId},</if>
            <if test="notifyModule != null">#{notifyModule},</if>
            <if test="url != null">#{url},</if>
            <if test="notifyType != null">#{notifyType},</if>
            <if test="notifyMsg != null">#{notifyMsg},</if>
            <if test="disposeUser != null">#{disposeUser},</if>
            <if test="viewFlag != null">#{viewFlag},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateZzCommonNotify" parameterType="ZzCommonNotify">
        update zz_common_notify
        <trim prefix="SET" suffixOverrides=",">
            <if test="licenseId != null">license_id = #{licenseId},</if>
            <if test="notifyModule != null">notify_module = #{notifyModule},</if>
            <if test="url != null">url = #{url},</if>
            <if test="notifyType != null">notify_type = #{notifyType},</if>
            <if test="notifyMsg != null">notify_msg = #{notifyMsg},</if>
            <if test="disposeUser != null">dispose_user = #{disposeUser},</if>
            <if test="viewFlag != null">view_flag = #{viewFlag},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_Time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_Time = #{updateTime},</if>
        </trim>
        where id = #{id} and dispose_user = #{disposeUser}
    </update>

    <delete id="deleteZzCommonNotifyById" parameterType="Long">
        delete from zz_common_notify where id = #{id}
    </delete>

    <delete id="deleteZzCommonNotifyByIds" parameterType="String">
        delete from zz_common_notify where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>