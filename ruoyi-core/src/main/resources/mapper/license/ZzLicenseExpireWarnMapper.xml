<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.license.mapper.ZzLicenseExpireWarnMapper">
    
    <resultMap type="ZzLicenseExpireWarn" id="ZzLicenseExpireWarnResult">
        <result property="licenseId"    column="license_id"    />
        <result property="isSwitch"    column="is_switch"    />
        <result property="warnDay"    column="warn_day"    />
        <result property="addWarnLicense"    column="add_warn_license"    />
        <result property="pushAck"    column="push_ack"    />
        <result property="pushQyvx"    column="push_qyvx"    />
    </resultMap>

    <sql id="selectZzLicenseExpireWarnVo">
        select license_id, is_switch, warn_day, add_warn_license, push_ack, push_qyvx from zz_license_annual_audit_expire_warn
    </sql>

    <select id="selectZzLicenseExpireWarnList" parameterType="ZzLicenseExpireWarn" resultMap="ZzLicenseExpireWarnResult">
        <include refid="selectZzLicenseExpireWarnVo"/>
        <where>  
            <if test="isSwitch != null  and isSwitch != ''"> and is_switch = #{isSwitch}</if>
            <if test="warnDay != null "> and warn_day = #{warnDay}</if>
            <if test="addWarnLicense != null  and addWarnLicense != ''"> and add_warn_license = #{addWarnLicense}</if>
            <if test="pushAck != null  and pushAck != ''"> and push_ack = #{pushAck}</if>
            <if test="pushQyvx != null  and pushQyvx != ''"> and push_qyvx = #{pushQyvx}</if>
        </where>
    </select>
    
    <select id="selectZzLicenseExpireWarnByLicenseId" parameterType="String" resultMap="ZzLicenseExpireWarnResult">
        <include refid="selectZzLicenseExpireWarnVo"/>
        where license_id = #{licenseId}
    </select>

    <insert id="insertZzLicenseExpireWarn" parameterType="ZzLicenseExpireWarn">
        insert into zz_license_annual_audit_expire_warn
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="licenseId != null">license_id,</if>
            <if test="isSwitch != null">is_switch,</if>
            <if test="warnDay != null">warn_day,</if>
            <if test="addWarnLicense != null">add_warn_license,</if>
            <if test="pushAck != null">push_ack,</if>
            <if test="pushQyvx != null">push_qyvx,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="licenseId != null">#{licenseId},</if>
            <if test="isSwitch != null">#{isSwitch},</if>
            <if test="warnDay != null">#{warnDay},</if>
            <if test="addWarnLicense != null">#{addWarnLicense},</if>
            <if test="pushAck != null">#{pushAck},</if>
            <if test="pushQyvx != null">#{pushQyvx},</if>
         </trim>
    </insert>

    <update id="updateZzLicenseExpireWarn" parameterType="ZzLicenseExpireWarn">
        update zz_license_annual_audit_expire_warn
        <trim prefix="SET" suffixOverrides=",">
            <if test="isSwitch != null">is_switch = #{isSwitch},</if>
            <if test="warnDay != null">warn_day = #{warnDay},</if>
            <if test="addWarnLicense != null">add_warn_license = #{addWarnLicense},</if>
            <if test="pushAck != null">push_ack = #{pushAck},</if>
            <if test="pushQyvx != null">push_qyvx = #{pushQyvx},</if>
        </trim>
        where license_id = #{licenseId}
    </update>

    <delete id="deleteZzLicenseExpireWarnByLicenseId" parameterType="String">
        delete from zz_license_annual_audit_expire_warn where license_id = #{licenseId}
    </delete>

    <delete id="deleteZzLicenseExpireWarnByLicenseIds" parameterType="String">
        delete from zz_license_annual_audit_expire_warn where license_id in 
        <foreach item="licenseId" collection="array" open="(" separator="," close=")">
            #{licenseId}
        </foreach>
    </delete>

    <insert id="insertZzLicenseExpireWarnList" parameterType="list">
        insert into zz_license_annual_audit_expire_warn (license_id, is_switch, warn_day, add_warn_license, push_ack, push_qyvx)
        values
            <foreach collection="licenseExpireWarnList" item="list" separator="," >
                (#{list.licenseId}, #{list.isSwitch}, #{list.warnDay}, #{list.addWarnLicense}, #{list.pushAck}, #{list.pushQyvx})
            </foreach>
    </insert>

    <select id="queryLicenseWarnStatusEcho"
            resultType="org.ruoyi.core.license.domain.ZzLicenseExpireWarn">
        <include refid="selectZzLicenseExpireWarnVo"/>
        where license_id = #{mainId}
    </select>

    <select id="queryAuditIsSwitchWarnLicenseList"
            resultType="org.ruoyi.core.license.domain.ZzLicenseExpireWarn">
        <include refid="selectZzLicenseExpireWarnVo"/>
        where is_switch = '1' and warn_day is not null and push_ack is not null or push_qyvx is not null
    </select>

</mapper>