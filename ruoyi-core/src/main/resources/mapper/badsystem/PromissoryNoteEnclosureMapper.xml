<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.badsystem.mapper.PromissoryNoteEnclosureMapper">

    <resultMap type="PromissoryNoteEnclosure" id="PromissoryNoteEnclosureResult">
        <result property="id"    column="id"    />
        <result property="promissoryNoteId"    column="promissory_note_id"    />
        <result property="age"    column="age"    />
        <result property="sex"    column="sex"    />
        <result property="hometown"    column="hometown"    />
        <result property="phoneNumber"    column="phone_number"    />
        <result property="currentAddress"    column="current_address"    />
        <result property="companyProductName"    column="company_product_name"    />
        <result property="loanAmount"    column="loan_amount"    />
        <result property="interestRateYear"    column="interest_rate_year"    />
        <result property="loanDisbursementDate"    column="loan_disbursement_date"    />
        <result property="dueDate"    column="due_date"    />
        <result property="remainingPrincipal"    column="remaining_principal"    />
        <result property="cumulativeRepaymentAmount"    column="cumulative_repayment_amount"    />
        <result property="loanInterest"    column="loan_interest"    />
        <result property="loanPenaltyInterest"    column="loan_penalty_interest"    />
        <result property="remainingDue"    column="remaining_due"    />
        <result property="periods"    column="periods"    />
        <result property="overdueDays"    column="overdue_days"    />
        <result property="accumulatedCompensation"    column="accumulated_compensation"    />
    </resultMap>

    <sql id="selectPromissoryNoteEnclosureVo">
        select id, promissory_note_id, age, sex, hometown, phone_number, current_address, company_product_name, loan_amount, interest_rate_year, loan_disbursement_date, due_date, remaining_principal, cumulative_repayment_amount, loan_interest, loan_penalty_interest, remaining_due, periods, overdue_days, accumulated_compensation from bl_promissory_note_enclosure
    </sql>

    <select id="selectPromissoryNoteEnclosureList" parameterType="PromissoryNoteEnclosure" resultMap="PromissoryNoteEnclosureResult">
        <include refid="selectPromissoryNoteEnclosureVo"/>
        <where>
            <if test="promissoryNoteId != null "> and promissory_note_id = #{promissoryNoteId}</if>
            <if test="age != null  and age != ''"> and age = #{age}</if>
            <if test="sex != null  and sex != ''"> and sex = #{sex}</if>
            <if test="hometown != null  and hometown != ''"> and hometown = #{hometown}</if>
            <if test="phoneNumber != null  and phoneNumber != ''"> and phone_number = #{phoneNumber}</if>
            <if test="currentAddress != null  and currentAddress != ''"> and current_address = #{currentAddress}</if>
            <if test="companyProductName != null  and companyProductName != ''"> and company_product_name like concat('%', #{companyProductName}, '%')</if>
            <if test="loanAmount != null "> and loan_amount = #{loanAmount}</if>
            <if test="interestRateYear != null "> and interest_rate_year = #{interestRateYear}</if>
            <if test="loanDisbursementDate != null "> and loan_disbursement_date = #{loanDisbursementDate}</if>
            <if test="dueDate != null "> and due_date = #{dueDate}</if>
            <if test="remainingPrincipal != null "> and remaining_principal = #{remainingPrincipal}</if>
            <if test="cumulativeRepaymentAmount != null "> and cumulative_repayment_amount = #{cumulativeRepaymentAmount}</if>
            <if test="loanInterest != null "> and loan_interest = #{loanInterest}</if>
            <if test="loanPenaltyInterest != null "> and loan_penalty_interest = #{loanPenaltyInterest}</if>
            <if test="remainingDue != null "> and remaining_due = #{remainingDue}</if>
            <if test="periods != null "> and periods = #{periods}</if>
            <if test="overdueDays != null "> and overdue_days = #{overdueDays}</if>
            <if test="accumulatedCompensation != null "> and accumulated_compensation = #{accumulatedCompensation}</if>
        </where>
    </select>

    <select id="selectPromissoryNoteEnclosureById" parameterType="Long" resultMap="PromissoryNoteEnclosureResult">
        <include refid="selectPromissoryNoteEnclosureVo"/>
        where id = #{id}
    </select>

    <insert id="insertPromissoryNoteEnclosure" parameterType="PromissoryNoteEnclosure" useGeneratedKeys="true" keyProperty="id">
        insert into bl_promissory_note_enclosure
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="promissoryNoteId != null">promissory_note_id,</if>
            <if test="age != null">age,</if>
            <if test="sex != null">sex,</if>
            <if test="hometown != null">hometown,</if>
            <if test="phoneNumber != null">phone_number,</if>
            <if test="currentAddress != null">current_address,</if>
            <if test="companyProductName != null">company_product_name,</if>
            <if test="loanAmount != null">loan_amount,</if>
            <if test="interestRateYear != null">interest_rate_year,</if>
            <if test="loanDisbursementDate != null">loan_disbursement_date,</if>
            <if test="dueDate != null">due_date,</if>
            <if test="remainingPrincipal != null">remaining_principal,</if>
            <if test="cumulativeRepaymentAmount != null">cumulative_repayment_amount,</if>
            <if test="loanInterest != null">loan_interest,</if>
            <if test="loanPenaltyInterest != null">loan_penalty_interest,</if>
            <if test="remainingDue != null">remaining_due,</if>
            <if test="periods != null">periods,</if>
            <if test="overdueDays != null">overdue_days,</if>
            <if test="accumulatedCompensation != null">accumulated_compensation,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="promissoryNoteId != null">#{promissoryNoteId},</if>
            <if test="age != null">#{age},</if>
            <if test="sex != null">#{sex},</if>
            <if test="hometown != null">#{hometown},</if>
            <if test="phoneNumber != null">#{phoneNumber},</if>
            <if test="currentAddress != null">#{currentAddress},</if>
            <if test="companyProductName != null">#{companyProductName},</if>
            <if test="loanAmount != null">#{loanAmount},</if>
            <if test="interestRateYear != null">#{interestRateYear},</if>
            <if test="loanDisbursementDate != null">#{loanDisbursementDate},</if>
            <if test="dueDate != null">#{dueDate},</if>
            <if test="remainingPrincipal != null">#{remainingPrincipal},</if>
            <if test="cumulativeRepaymentAmount != null">#{cumulativeRepaymentAmount},</if>
            <if test="loanInterest != null">#{loanInterest},</if>
            <if test="loanPenaltyInterest != null">#{loanPenaltyInterest},</if>
            <if test="remainingDue != null">#{remainingDue},</if>
            <if test="periods != null">#{periods},</if>
            <if test="overdueDays != null">#{overdueDays},</if>
            <if test="accumulatedCompensation != null">#{accumulatedCompensation},</if>
         </trim>
    </insert>

    <update id="updatePromissoryNoteEnclosure" parameterType="PromissoryNoteEnclosure">
        update bl_promissory_note_enclosure
        <trim prefix="SET" suffixOverrides=",">
            <if test="promissoryNoteId != null">promissory_note_id = #{promissoryNoteId},</if>
            <if test="age != null">age = #{age},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="hometown != null">hometown = #{hometown},</if>
            <if test="phoneNumber != null">phone_number = #{phoneNumber},</if>
            <if test="currentAddress != null">current_address = #{currentAddress},</if>
            <if test="companyProductName != null">company_product_name = #{companyProductName},</if>
            <if test="loanAmount != null">loan_amount = #{loanAmount},</if>
            <if test="interestRateYear != null">interest_rate_year = #{interestRateYear},</if>
            <if test="loanDisbursementDate != null">loan_disbursement_date = #{loanDisbursementDate},</if>
            <if test="dueDate != null">due_date = #{dueDate},</if>
            <if test="remainingPrincipal != null">remaining_principal = #{remainingPrincipal},</if>
            <if test="cumulativeRepaymentAmount != null">cumulative_repayment_amount = #{cumulativeRepaymentAmount},</if>
            <if test="loanInterest != null">loan_interest = #{loanInterest},</if>
            <if test="loanPenaltyInterest != null">loan_penalty_interest = #{loanPenaltyInterest},</if>
            <if test="remainingDue != null">remaining_due = #{remainingDue},</if>
            <if test="periods != null">periods = #{periods},</if>
            <if test="overdueDays != null">overdue_days = #{overdueDays},</if>
            <if test="accumulatedCompensation != null">accumulated_compensation = #{accumulatedCompensation},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePromissoryNoteEnclosureById" parameterType="Long">
        delete from bl_promissory_note_enclosure where id = #{id}
    </delete>

    <delete id="deletePromissoryNoteEnclosureByIds" parameterType="String">
        delete from bl_promissory_note_enclosure where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchPromissoryNoteEnclosure" parameterType="java.util.List">
        INSERT INTO bl_promissory_note_enclosure
        (
         promissory_note_id, age, sex, hometown, phone_number, current_address, company_product_name, loan_amount
        , interest_rate_year, loan_disbursement_date, due_date, remaining_principal, cumulative_repayment_amount
        , loan_interest, loan_penalty_interest, remaining_due, periods, overdue_days
        , accumulated_compensation
        )
        VALUES
        <foreach item="item" collection="list" separator=",">
            (
            #{item.promissoryNoteId},
            #{item.age},
            #{item.sex},
            #{item.hometown},
            #{item.phoneNumber},
            #{item.currentAddress},
            #{item.companyProductName},
            #{item.loanAmount},
            #{item.interestRateYear},
            #{item.loanDisbursementDate},
            #{item.dueDate},
            #{item.remainingPrincipal},
            #{item.cumulativeRepaymentAmount},
            #{item.loanInterest},
            #{item.loanPenaltyInterest},
            #{item.remainingDue},
            #{item.periods},
            #{item.overdueDays},
            #{item.accumulatedCompensation}
            )
        </foreach>
    </insert>
</mapper>
