<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.badsystem.mapper.FinancialSettlementReconciliationMapper">

    <resultMap type="FinancialSettlementReconciliation" id="FinancialSettlementReconciliationResult">
        <result property="id"    column="id"    />
        <result property="financialSettlementId"    column="financial_settlement_id"    />
        <result property="reconciliationId"    column="reconciliation_id"    />
        <result property="reconciliationCode"    column="reconciliation_code"    />
        <result property="reconciliationDate"    column="reconciliation_date"    />
        <result property="totalAmountCollected"    column="total_amount_collected"    />
    </resultMap>

    <sql id="selectFinancialSettlementReconciliationVo">
        select id, financial_settlement_id, reconciliation_id, reconciliation_code, reconciliation_date, total_amount_collected from bl_financial_settlement_reconciliation
    </sql>

    <select id="selectFinancialSettlementReconciliationList" parameterType="FinancialSettlementReconciliation" resultMap="FinancialSettlementReconciliationResult">
        <include refid="selectFinancialSettlementReconciliationVo"/>
        <where>
            <if test="reconciliationId != null "> and reconciliation_id = #{reconciliationId}</if>
        </where>
    </select>

    <select id="selectFinancialSettlementReconciliationById" parameterType="Long" resultMap="FinancialSettlementReconciliationResult">
        <include refid="selectFinancialSettlementReconciliationVo"/>
        where id = #{id}
    </select>

    <insert id="insertFinancialSettlementReconciliation" parameterType="FinancialSettlementReconciliation" useGeneratedKeys="true" keyProperty="id">
        insert into bl_financial_settlement_reconciliation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="financialSettlementId != null">financial_settlement_id,</if>
            <if test="reconciliationId != null">reconciliation_id,</if>
            <if test="reconciliationCode != null">reconciliation_code,</if>
            <if test="reconciliationDate != null">reconciliation_date,</if>
            <if test="totalAmountCollected != null">total_amount_collected,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="financialSettlementId != null">#{financialSettlementId},</if>
            <if test="reconciliationId != null">#{reconciliationId},</if>
            <if test="reconciliationCode != null">#{reconciliationCode},</if>
            <if test="reconciliationDate != null">#{reconciliationDate},</if>
            <if test="totalAmountCollected != null">#{totalAmountCollected},</if>
        </trim>
    </insert>

    <update id="updateFinancialSettlementReconciliation" parameterType="FinancialSettlementReconciliation">
        update bl_financial_settlement_reconciliation
        <trim prefix="SET" suffixOverrides=",">
            <if test="financialSettlementId != null">financial_settlement_id = #{financialSettlementId},</if>
            <if test="reconciliationId != null">reconciliation_id = #{reconciliationId},</if>
            <if test="reconciliationCode != null">reconciliation_code = #{reconciliationCode},</if>
            <if test="reconciliationDate != null">reconciliation_date = #{reconciliationDate},</if>
            <if test="totalAmountCollected != null">total_amount_collected = #{totalAmountCollected},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFinancialSettlementReconciliationById" parameterType="Long">
        delete from bl_financial_settlement_reconciliation where id = #{id}
    </delete>

    <delete id="deleteFinancialSettlementReconciliationByIds" parameterType="String">
        delete from bl_financial_settlement_reconciliation where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchFinancialSettlementReconciliation" parameterType="java.util.List">
        INSERT INTO bl_financial_settlement_reconciliation
        (financial_settlement_id, reconciliation_id,reconciliation_code, reconciliation_date, total_amount_collected )
        VALUES
        <foreach item="item" collection="list" separator=",">
            (
            #{item.financialSettlementId},
            #{item.reconciliationId},
            #{item.reconciliationCode},
            #{item.reconciliationDate},
            #{item.totalAmountCollected}
            )
        </foreach>
    </insert>

    <delete id="deleteFinancialSettlementReconciliationByFinancialSettlementId" parameterType="Long">
        delete from bl_financial_settlement_reconciliation where financial_settlement_id = #{financialSettlementId}
    </delete>
</mapper>
