<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.kaoqin.mapper.AskLeaveMapper">

    <resultMap type="AskLeave" id="AskLeaveResult">
        <result property="id"    column="id"    />
        <result property="askLeaveCode"    column="ask_leave_code"    />
        <result property="handover"    column="handover"    />
        <result property="lagReason"    column="lag_reason"    />
        <result property="totalTime"    column="total_time"    />
        <result property="applicationTime"    column="application_time"    />
        <result property="reason"    column="reason"    />
        <result property="processId"    column="process_id"    />
        <result property="state"    column="state"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="effective"    column="effective"    />
        <result property="voidReason"    column="void_reason"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="nickName"    column="nick_name"    />
        <result property="refuseReason" column="refuse_reason"/>
        <result property="handleState" column="handle_state"/>
        <result property="userId"    column="user_id"    />
        <collection property="askLeaveSlaveList" column = "{id = id}" select = "selectAskLeaveSlaveList"/>
    </resultMap>

    <resultMap type="AskLeaveSlave" id="AskLeaveSlaveResult">
        <result property="leaveId"    column="leave_id"    />
        <result property="leaveType"    column="leave_type"    />
        <result property="startTime"    column="start_time"    />
        <result property="startTimePeriod"    column="start_time_period"    />
        <result property="endTime"    column="end_time"    />
        <result property="endTimePeriod"    column="end_time_period"    />
        <result property="times"    column="times"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAskLeaveVo">
        select id, ask_leave_code, reason, process_id, state, is_delete, remark, create_by, create_time, update_by, update_time from kq_ask_leave
    </sql>

    <select id="selectAskLeaveList" parameterType="AskLeaveVo" resultMap="AskLeaveResult">
        select
            kal.id, kal.ask_leave_code,kal.handover, kal.reason,kal.lag_reason,kal.void_reason,
            kal.total_time,kal.application_time,kal.effective,kal.process_id, kal.state, kal.is_delete,
            kal.remark, kal.create_by, kal.create_time, kal.update_by, kal.update_time ,
            user.nick_name,user.user_id
        from kq_ask_leave kal
        left join sys_user user on kal.create_by = user.user_name
        left join sys_user_post sup on sup.user_id = user.user_id and home_post = 0
        left join sys_post post on post.post_id = sup.post_id
        left join sys_dept dept on dept.dept_id = post.dept_id
        left join kq_ask_leave_slave kals on kals.leave_id = kal.id
        <where>
            and kal.is_delete = 1
            <if test="askLeaveCode != null  and askLeaveCode != ''"> and ask_leave_code = #{askLeaveCode}</if>
            <if test="reason != null  and reason != ''"> and reason = #{reason}</if>
            <if test="processId != null  and processId != ''"> and process_id = #{processId}</if>
            <if test="state != null  and state != ''"> and state = #{state}</if>
            <if test="effective != null  and effective != ''"> and effective = #{effective}</if>
            <if test="leaveType != null  and leaveType != ''"> and leave_type = #{leaveType}</if>
            <if test="nickName != null  and nickName != ''"> and user.nick_name like concat('%', #{nickName}, '%')</if>
            <if test="createBy != null and createBy != ''"> and (kal.create_by = #{createBy} or (kal.state = 2 OR kal.state = 3)) </if>
            <if test="startTime != null  ">
                AND date_format(kals.start_time,'%Y-%m-%d') <![CDATA[ >= ]]>  date_format(#{startTime},'%Y-%m-%d')
            </if>
            <if test="endTime != null  ">
                AND date_format(kals.start_time,'%Y-%m-%d') <![CDATA[ <= ]]> date_format(#{endTime},'%Y-%m-%d')
            </if>
            <trim prefix="and (" suffix=")" prefixOverrides="and|or">
                <if test="createBy != null and createBy != ''">
                    or kal.create_by = #{createBy}
                </if>
                <if test="unitIds != null and unitIds.size() > 0">
                    or dept.unit_id in
                    <foreach item="unitId" collection="unitIds" open="(" close=")" separator=",">
                        #{unitId}
                    </foreach>
                </if>
                <if test="deptIds != null and deptIds.size() > 0">
                    or dept.dept_id in
                    <foreach item="deptId" collection="deptIds" open="(" close=")" separator=",">
                        #{deptId}
                    </foreach>
                </if>
            </trim>
        </where>
        group by kal.id
        ORDER BY FIELD(state, 1, 4, 2, 3),
                 FIELD(effective, 0, 2, 1),
                 application_time DESC,
                 start_time
    </select>

    <select id="selectAskLeaveById" parameterType="Long" resultMap="AskLeaveResult">
        select
            kal.id, kal.ask_leave_code,kal.handover, kal.reason,kal.lag_reason,kal.void_reason,
            kal.total_time,kal.application_time,kal.effective,kal.process_id, kal.state, kal.is_delete,
            kal.remark, kal.create_by, kal.create_time, kal.update_by, kal.update_time ,nick_name
        from kq_ask_leave kal
        left join sys_user user on kal.create_by = user.user_name
        left join kq_ask_leave_slave kals on kals.leave_id = kal.id
        where  kal.id = #{id}
        group by kal.id
    </select>

    <select id="selectAskLeaveByHandleId" parameterType="Long" resultMap="AskLeaveResult">
    select
        kal.id, kal.ask_leave_code,kal.handover, kal.reason,kal.lag_reason,kal.void_reason,
        kal.total_time,kal.application_time,kal.effective,kal.process_id, kal.state, kal.is_delete,
        kal.remark, kal.create_by, kal.create_time, kal.update_by, kal.update_time ,nick_name,
        leave_id, leave_type, start_time, start_time_period, end_time, end_time_period, times,
        kvh.refuse_reason ,kvh.handle_state
    from kq_ask_leave kal
    left join sys_user user on kal.create_by = user.user_name
    left join kq_ask_leave_slave kals on kals.leave_id = kal.id
    left join kq_void_handle kvh  on kvh.correlation_id = kal.id
    where kvh.type = 1 and  kvh.id = #{id}
    group by kal.id
    </select>

    <select id="selectAskLeaveByProcessId" parameterType="String" resultMap="AskLeaveResult">
        select
            kal.id, kal.ask_leave_code, kal.handover,kal.reason,kal.lag_reason,kal.void_reason,
            kal.total_time,kal.application_time,kal.effective, kal.process_id, kal.state, kal.is_delete,
            kal.remark, kal.create_by, kal.create_time, kal.update_by, kal.update_time ,
            nick_name,
            leave_id, leave_type, start_time, start_time_period, end_time, end_time_period, times
        from kq_ask_leave kal
        left join sys_user user on kal.create_by = user.user_name
        left join kq_ask_leave_slave kals on kals.leave_id = kal.id
        where process_id = #{process_id}
        group by kal.id
    </select>

    <insert id="insertAskLeave" parameterType="AskLeaveVo" useGeneratedKeys="true" keyProperty="id">
        <selectKey keyProperty="id" resultType="Long" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into kq_ask_leave
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="askLeaveCode != null">ask_leave_code,</if>
            <if test="reason != null">reason,</if>
            <if test="handover != null">handover,</if>
            <if test="lagReason != null">lag_reason,</if>
            <if test="totalTime != null">total_time,</if>
            <if test="applicationTime != null">application_time,</if>
            <if test="processId != null">process_id,</if>
            <if test="state != null">state,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="askLeaveCode != null">#{askLeaveCode},</if>
            <if test="reason != null">#{reason},</if>
            <if test="handover != null">#{handover},</if>
            <if test="lagReason != null">#{lagReason},</if>
            <if test="totalTime != null">#{totalTime},</if>
            <if test="applicationTime != null">#{applicationTime},</if>
            <if test="processId != null">#{processId},</if>
            <if test="state != null">#{state},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateAskLeave" parameterType="AskLeave">
        update kq_ask_leave
        <trim prefix="SET" suffixOverrides=",">
            <if test="askLeaveCode != null">ask_leave_code = #{askLeaveCode},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="handover != null">handover = #{handover},</if>
            <if test="lagReason != null">lag_reason = #{lagReason},</if>
            <if test="totalTime != null">total_time = #{totalTime},</if>
            <if test="applicationTime != null">application_time = #{applicationTime},</if>
            <if test="processId != null">process_id = #{processId},</if>
            <if test="state != null">state = #{state},</if>
            <if test="effective != null">effective = #{effective},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAskLeaveById" parameterType="Long">
        delete from kq_ask_leave where id = #{id}
    </delete>

    <update id="deleteAskLeaveByIds" parameterType="String">
        update kq_ask_leave
        set    is_delete = '0'
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="getCountByCreateTime" resultType="int" parameterType="String">
        SELECT COUNT(id) FROM kq_ask_leave
        <where>
            <if test="createTime != null  and createTime != ''"> and create_time like concat('%', #{createTime}, '%')</if>
        </where>
    </select>

    <update id="passAskLeaveById" parameterType="Long">
        update kq_ask_leave
        set    state = '3'
        where  id = #{id}
    </update>

    <update id="unpassAskLeaveById" parameterType="Long">
        update kq_ask_leave
        set    state = '4'
        where  id = #{id}
    </update>

     <select id="selectAskLeaveSlaveList" resultMap="AskLeaveSlaveResult">
         select kals.id, leave_id, leave_type, start_time, start_time_period, end_time, end_time_period, times
              ,kals.create_by, kals.create_time, kals.update_by, kals.update_time
         from kq_ask_leave_slave kals
         where kals.leave_id = #{id}
     </select>

    <update id="voidAskLeave" parameterType="WorkOvertime">
        update kq_ask_leave
        set    effective = 2,
               void_reason = #{voidReason},
               void_time = #{voidTime},
               update_time = #{updateTime}
        where  id = #{id}
    </update>


    <select id="inspectionTime" parameterType="AskLeaveVo" resultType="int">
        SELECT count(kals.id)
        FROM kq_ask_leave_slave kals
        right join kq_ask_leave kal on kal.id = kals.leave_id
        left join sys_dict_data sd on sd.dict_value = kals.leave_type and sd.dict_type = 'check_work_pseudotype'
        WHERE kals.create_by = #{createBy} and kal.state in (2,3) and kal.effective != 1
        and sd.dict_label not in ('哺乳假')
          and (
        <foreach collection="dates" item="date" separator=" OR ">
            #{date} BETWEEN CONCAT(start_time, ' ', start_time_period) AND CONCAT(end_time, ' ', end_time_period)
        </foreach>
        )
    </select>
</mapper>
