<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.kaoqin.mapper.NotifyMapper">

    <resultMap type="Notify" id="MonthLogNotifyResult">
        <result property="id"    column="id"    />
        <result property="notifyModule"    column="notify_module"    />
        <result property="url"    column="url"    />
        <result property="notifyType"    column="notify_type"    />
        <result property="notifyMsg"    column="notify_msg"    />
        <result property="disposeUser"    column="dispose_user"    />
        <result property="viewFlag"    column="view_flag"    />
        <result property="status"    column="status"    />
        <result property="remindText"    column="remind_text"    />
        <result property="correlationId"    column="correlation_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_Time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_Time"    />
    </resultMap>

    <sql id="selectMonthLogNotifyVo">
        select id, notify_module, url, notify_type, notify_msg, dispose_user, view_flag, status, remind_text, correlation_id, create_by, create_Time, update_by, update_Time from kq_notify
    </sql>

    <select id="selectMonthLogNotifyList" parameterType="Notify" resultMap="MonthLogNotifyResult">
        <include refid="selectMonthLogNotifyVo"/>
        <where>
              <if test="notifyModule != null  and notifyModule != ''"> and notify_module = #{notifyModule}</if>
              <if test="url != null  and url != ''"> and url = #{url}</if>
              <if test="notifyType != null  and notifyType != ''"> and notify_type = #{notifyType}</if>
              <if test="notifyMsg != null  and notifyMsg != ''"> and notify_msg = #{notifyMsg}</if>
              <if test="disposeUser != null "> and dispose_user = #{disposeUser}</if>
              <if test="viewFlag != null  and viewFlag != ''"> and view_flag = #{viewFlag}</if>
              <if test="status != null  and status != ''"> and status = #{status}</if>
              <if test="remindText != null  and remindText != ''"> and remind_text = #{remindText}</if>
              <if test="correlationId != null "> and correlation_id = #{correlationId}</if>
              <if test="createTime != null "> and create_Time = #{createTime}</if>
              <if test="updateTime != null "> and update_Time = #{updateTime}</if>
        </where>
    </select>

    <select id="selectMonthLogNotifyById" parameterType="Long" resultMap="MonthLogNotifyResult">
        <include refid="selectMonthLogNotifyVo"/>
        where id = #{id}
    </select>

    <insert id="insertMonthLogNotify" parameterType="Notify" useGeneratedKeys="true" keyProperty="id">
        insert into kq_notify
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="notifyModule != null">notify_module,</if>
            <if test="url != null">url,</if>
            <if test="notifyType != null">notify_type,</if>
            <if test="notifyMsg != null">notify_msg,</if>
            <if test="disposeUser != null">dispose_user,</if>
            <if test="viewFlag != null">view_flag,</if>
            <if test="status != null">status,</if>
            <if test="remindText != null">remind_text,</if>
            <if test="correlationId != null">correlation_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_Time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_Time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="notifyModule != null">#{notifyModule},</if>
            <if test="url != null">#{url},</if>
            <if test="notifyType != null">#{notifyType},</if>
            <if test="notifyMsg != null">#{notifyMsg},</if>
            <if test="disposeUser != null">#{disposeUser},</if>
            <if test="viewFlag != null">#{viewFlag},</if>
            <if test="status != null">#{status},</if>
            <if test="remindText != null">#{remindText},</if>
            <if test="correlationId != null">#{correlationId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <insert id="insertMonthLogNotifyBatch" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into kq_notify
        <trim prefix="(" suffix=")" suffixOverrides=",">
            notify_module,
            url,
            notify_type,
            notify_msg,
            dispose_user,
            view_flag,
            status,
            remind_text,
            correlation_id,
            create_by,
            create_Time
        </trim>
        values
        <foreach collection="list" item="notify" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="notify.notifyModule != null">#{notify.notifyModule},</if>
                <if test="notify.url != null">#{notify.url},</if>
                <if test="notify.notifyType != null">#{notify.notifyType},</if>
                <if test="notify.notifyMsg != null">#{notify.notifyMsg},</if>
                <if test="notify.disposeUser != null">#{notify.disposeUser},</if>
                <if test="notify.viewFlag != null">#{notify.viewFlag},</if>
                <if test="notify.status != null">#{notify.status},</if>
                <if test="notify.remindText != null">#{notify.remindText},</if>
                <if test="notify.correlationId != null">#{notify.correlationId},</if>
                <if test="notify.createBy != null">#{notify.createBy},</if>
                <if test="notify.createTime != null">#{notify.createTime},</if>
            </trim>
        </foreach>
    </insert>

    <update id="updateMonthLogNotify" parameterType="Notify">
        update kq_notify
        <trim prefix="SET" suffixOverrides=",">
            <if test="notifyModule != null">notify_module = #{notifyModule},</if>
            <if test="url != null">url = #{url},</if>
            <if test="notifyType != null">notify_type = #{notifyType},</if>
            <if test="notifyMsg != null">notify_msg = #{notifyMsg},</if>
            <if test="disposeUser != null">dispose_user = #{disposeUser},</if>
            <if test="viewFlag != null">view_flag = #{viewFlag},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remindText != null">remind_text = #{remindText},</if>
            <if test="correlationId != null">correlation_id = #{correlationId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_Time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_Time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMonthLogNotifyById" parameterType="Long">
        delete from kq_notify where id = #{id}
    </delete>

    <delete id="deleteMonthLogNotifyByIds" parameterType="String">
        delete from kq_notify where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="reviewedNotify" parameterType="Notify">
        update kq_notify
        set
            view_flag = '1',
            update_by = #{updateBy},
            update_Time = #{updateTime}
        where
            dispose_user = #{disposeUser}
        and correlation_id = #{correlationId}
    </update>

    <update id="confirmNotify" parameterType="Notify">
        update kq_notify
        set
            view_flag = '1',
            update_by = #{updateBy},
            update_Time = #{updateTime}
        where
            id = #{id}
    </update>

    <select id="getProcessEndTime" parameterType="String" resultType="ProcessEndTime">
        SELECT ahp.BUSINESS_KEY_ as businessKey,aha.ACT_NAME_ as actName,aha.ASSIGNEE_,aha.END_TIME_ as endTime,user_id
        FROM act_hi_procinst ahp
        LEFT JOIN act_hi_actinst aha ON ahp.PROC_INST_ID_ = aha.PROC_INST_ID_
        LEFT JOIN sys_user user ON user.user_name = aha.ASSIGNEE_
        WHERE BUSINESS_KEY_ = #{businessKey}
    </select>


    <select id="getVoidUserId" resultType="java.lang.Long">
        select user_id
        from sys_menu sm
        left join sys_role_menu srm on sm.menu_id = srm.menu_id
        left join sys_user_role sur on srm.role_id = sur.role_id
        where menu_name = '处理作废申请'
    </select>
</mapper>
