<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.qrReport.mapper.QrCodeAuditReportMapper">
    
    <resultMap type="QrCodeAuditReport" id="QrCodeAuditReportResult">
        <result property="id"    column="id"    />
        <result property="auditedUnitName"    column="audited_unit_name"    />
        <result property="firmName"    column="firm_name"    />
        <result property="entrustedProject"    column="entrusted_project"    />
        <result property="documentNumber"    column="document_number"    />
        <result property="documentDate"    column="document_date"    />
        <result property="signAccountant1"    column="sign_accountant1"    />
        <result property="signAccountant2"    column="sign_accountant2"    />
        <result property="reportStatus"    column="report_status"    />
        <result property="uniqueFlag"    column="unique_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createrNickName"    column="nick_name"    />
        <result property="reportStatusLabel"    column="dict_label"    />
    </resultMap>

    <sql id="selectQrCodeAuditReportVo">
        select id, audited_unit_name, firm_name, entrusted_project, document_number, document_date, sign_accountant1, sign_accountant2, report_status, unique_flag, create_time, create_by from qr_code_audit_report
    </sql>

    <select id="selectQrCodeAuditReportList" parameterType="QrCodeAuditReport" resultMap="QrCodeAuditReportResult">
        select qcar.id, qcar.audited_unit_name, qcar.firm_name, qcar.entrusted_project, qcar.document_number, qcar.document_date,
               qcar.sign_accountant1, qcar.sign_accountant2, qcar.report_status, qcar.create_time, qcar.create_by, su.nick_name, qcar.unique_flag
        from qr_code_audit_report qcar
        left join sys_user su on qcar.create_by = su.user_name
        <where>
            <if test="auditedUnitName != null  and auditedUnitName != ''"> and audited_unit_name like concat('%', #{auditedUnitName}, '%')</if>
            <if test="firmName != null  and firmName != ''"> and firm_name like concat('%', #{firmName}, '%')</if>
            <if test="entrustedProject != null  and entrustedProject != ''"> and entrusted_project like concat('%', #{entrustedProject}, '%')</if>
            <if test="documentNumber != null  and documentNumber != ''"> and document_number like concat('%', #{documentNumber}, '%')</if>
            <if test="beginTime != null"><!-- 开始时间检索 -->
                and date_format(document_date,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null"><!-- 结束时间检索 -->
                and date_format(document_date,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
            <if test="signAccountant1 != null  and signAccountant1 != ''"> and sign_accountant1 = #{signAccountant1}</if>
            <if test="signAccountant2 != null  and signAccountant2 != ''"> and sign_accountant2 = #{signAccountant2}</if>
            <if test="reportStatus != null  and reportStatus != ''"> and report_status = #{reportStatus}</if>
        </where>
        /*keep orderby */
        order by create_time desc
    </select>
    
    <select id="selectQrCodeAuditReportById" resultMap="QrCodeAuditReportResult">
        select qcar.id, qcar.audited_unit_name, qcar.firm_name, qcar.entrusted_project, qcar.document_number, qcar.document_date, qcar.unique_flag,
               qcar.sign_accountant1, qcar.sign_accountant2, qcar.report_status, qcar.create_time, qcar.create_by, su.nick_name, sdd.dict_label
        from qr_code_audit_report qcar
        left join sys_user su on qcar.create_by = su.user_name
        left join sys_dict_data sdd on qcar.report_status = dict_value and dict_type = 'qr_report_status'
        where unique_flag = #{uniqueFlag,jdbcType=VARCHAR}
    </select>

    <insert id="insertQrCodeAuditReport" parameterType="QrCodeAuditReport" useGeneratedKeys="true" keyProperty="id">
        insert into qr_code_audit_report
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="auditedUnitName != null">audited_unit_name,</if>
            <if test="firmName != null">firm_name,</if>
            <if test="entrustedProject != null">entrusted_project,</if>
            <if test="documentNumber != null">document_number,</if>
            <if test="documentDate != null">document_date,</if>
            <if test="signAccountant1 != null">sign_accountant1,</if>
            <if test="signAccountant2 != null">sign_accountant2,</if>
            <if test="reportStatus != null">report_status,</if>
            <if test="uniqueFlag != null">unique_flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="auditedUnitName != null">#{auditedUnitName},</if>
            <if test="firmName != null">#{firmName},</if>
            <if test="entrustedProject != null">#{entrustedProject},</if>
            <if test="documentNumber != null">#{documentNumber},</if>
            <if test="documentDate != null">#{documentDate},</if>
            <if test="signAccountant1 != null">#{signAccountant1},</if>
            <if test="signAccountant2 != null">#{signAccountant2},</if>
            <if test="reportStatus != null">#{reportStatus},</if>
            <if test="uniqueFlag != null">#{uniqueFlag},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
         </trim>
    </insert>

    <update id="updateQrCodeAuditReport" parameterType="QrCodeAuditReport">
        update qr_code_audit_report
        <trim prefix="SET" suffixOverrides=",">
            <if test="auditedUnitName != null">audited_unit_name = #{auditedUnitName},</if>
            <if test="firmName != null">firm_name = #{firmName},</if>
            <if test="entrustedProject != null">entrusted_project = #{entrustedProject},</if>
            <if test="documentNumber != null">document_number = #{documentNumber},</if>
            <if test="documentDate != null">document_date = #{documentDate},</if>
            <if test="signAccountant1 != null">sign_accountant1 = #{signAccountant1},</if>
            <if test="signAccountant2 != null">sign_accountant2 = #{signAccountant2},</if>
            <if test="reportStatus != null">report_status = #{reportStatus},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteQrCodeAuditReportById" parameterType="Long">
        delete from qr_code_audit_report where id = #{id}
    </delete>

    <delete id="deleteQrCodeAuditReportByIds" parameterType="String">
        delete from qr_code_audit_report where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="checkUniqueFlag" resultType="org.ruoyi.core.qrReport.domain.QrCodeAuditReport">
        select id from qr_code_audit_report where unique_flag = #{unique}
    </select>

</mapper>