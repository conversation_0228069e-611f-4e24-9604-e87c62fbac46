<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.qrReport.mapper.QrCodeAuditReportHistoryMapper">
    
    <resultMap type="QrCodeAuditReportHistory" id="QrCodeAuditReportHistoryResult">
        <result property="id"    column="id"    />
        <result property="relationId"    column="relation_id"    />
        <result property="relationType"    column="relation_type"    />
        <result property="oldJsonDate"    column="old_json_date"    />
        <result property="newJsonDate"    column="new_json_date"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectQrCodeAuditReportHistoryVo">
        select id, relation_id, relation_type, old_json_date, new_json_date, create_by, create_time from qr_code_audit_report_history
    </sql>

    <select id="selectQrCodeAuditReportHistoryList" parameterType="QrCodeAuditReportHistory" resultMap="QrCodeAuditReportHistoryResult">
        select qcarh.id, qcarh.relation_id, qcarh.relation_type, qcarh.old_json_date, qcarh.new_json_date, su.nick_name as 'create_by', qcarh.create_time from qr_code_audit_report_history qcarh
        left join sys_user su on qcarh.create_by = su.user_name
        <where>  
            <if test="relationId != null "> and relation_id = #{relationId}</if>
            <if test="relationType != null  and relationType != ''"> and relation_type = #{relationType}</if>
            <if test="oldJsonDate != null  and oldJsonDate != ''"> and old_json_date = #{oldJsonDate}</if>
            <if test="newJsonDate != null  and newJsonDate != ''"> and new_json_date = #{newJsonDate}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
        </where>
        /*keep orderby */
        order by create_time desc
    </select>
    
    <select id="selectQrCodeAuditReportHistoryById" parameterType="Long" resultMap="QrCodeAuditReportHistoryResult">
        <include refid="selectQrCodeAuditReportHistoryVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertQrCodeAuditReportHistory" parameterType="QrCodeAuditReportHistory" useGeneratedKeys="true" keyProperty="id">
        insert into qr_code_audit_report_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="relationId != null">relation_id,</if>
            <if test="relationType != null">relation_type,</if>
            <if test="oldJsonDate != null">old_json_date,</if>
            <if test="newJsonDate != null">new_json_date,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="relationId != null">#{relationId},</if>
            <if test="relationType != null">#{relationType},</if>
            <if test="oldJsonDate != null">#{oldJsonDate},</if>
            <if test="newJsonDate != null">#{newJsonDate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateQrCodeAuditReportHistory" parameterType="QrCodeAuditReportHistory">
        update qr_code_audit_report_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="relationId != null">relation_id = #{relationId},</if>
            <if test="relationType != null">relation_type = #{relationType},</if>
            <if test="oldJsonDate != null">old_json_date = #{oldJsonDate},</if>
            <if test="newJsonDate != null">new_json_date = #{newJsonDate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteQrCodeAuditReportHistoryById" parameterType="Long">
        delete from qr_code_audit_report_history where id = #{id}
    </delete>

    <delete id="deleteQrCodeAuditReportHistoryByIds" parameterType="String">
        delete from qr_code_audit_report_history where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>