<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.mapper.DBalanceDistributionMonthMapper">

    <resultMap type="org.ruoyi.core.domain.DBalanceDistributionMonth" id="DBalanceDistributionMonthResult">
        <result property="id"    column="id"    />
        <result property="platformNo"    column="platform_no"    />
        <result property="custNo"    column="cust_no"    />
        <result property="partnerNo"    column="partner_no"    />
        <result property="fundNo"    column="fund_no"    />
        <result property="productNo"    column="product_no"    />
        <result property="loanMonth"    column="loan_month"    />
        <result property="reconMonth"    column="recon_month"    />
        <result property="loanBalanceAmount"    column="loan_balance_amount"    />
        <result property="loanRemainNumber"    column="loan_remain_number"    />
        <result property="balanceDistributionType"    column="balance_distribution_type"    />
        <result property="mNumber"    column="m_number"    />
        <result property="mBalanceAmount"    column="m_balance_amount"    />
        <result property="isMapping"    column="is_mapping"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDBalanceDistributionMonthVo">
        select id, platform_no, cust_no, partner_no, fund_no, product_no, loan_month, recon_month, loan_balance_amount, loan_remain_number, balance_distribution_type, m_number, m_balance_amount, is_mapping, remark, create_time, update_time from d_balance_distribution_month
    </sql>

    <select id="selectDBalanceDistributionMonthList" parameterType="org.ruoyi.core.domain.DBalanceDistributionMonth" resultMap="DBalanceDistributionMonthResult">
        <include refid="selectDBalanceDistributionMonthVo"/>
        <where>
            <if test="platformNo != null  and platformNo != ''"> and platform_no = #{platformNo}</if>
            <if test="custNo != null  and custNo != ''"> and cust_no = #{custNo}</if>
            <if test="partnerNo != null  and partnerNo != ''"> and partner_no = #{partnerNo}</if>
            <if test="fundNo != null  and fundNo != ''"> and fund_no = #{fundNo}</if>
            <if test="productNo != null  and productNo != ''"> and product_no = #{productNo}</if>
            <if test="loanMonth != null  and loanMonth != ''"> and loan_month = #{loanMonth}</if>
            <if test="reconMonth != null  and reconMonth != ''"> and recon_month = #{reconMonth}</if>
            <if test="loanBalanceAmount != null "> and loan_balance_amount = #{loanBalanceAmount}</if>
            <if test="loanRemainNumber != null "> and loan_remain_number = #{loanRemainNumber}</if>
            <if test="balanceDistributionType != null  and balanceDistributionType != ''"> and balance_distribution_type = #{balanceDistributionType}</if>
            <if test="mNumber != null "> and m_number = #{mNumber}</if>
            <if test="mBalanceAmount != null "> and m_balance_amount = #{mBalanceAmount}</if>
            <if test="isMapping != null  and isMapping != ''"> and is_mapping = #{isMapping}</if>
        </where>

        ORDER BY
	    recon_month DESC,
	    loan_month DESC,
	    FIELD( balance_distribution_type, 'M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'M7', 'M7+', 'MF' )
    </select>

    <select id="selectDBalanceDistributionMonthListByDict" parameterType="org.ruoyi.core.domain.DBalanceDistributionMonth" resultMap="DBalanceDistributionMonthResult">
        <include refid="selectDBalanceDistributionMonthVo"/>
        <where>
            1=1
            <if test="platformNos != null  ">
                and platform_no in
                <foreach collection="platformNos" item="platformNo" open="(" close=")" separator=",">
                    #{platformNo}
                </foreach>
            </if>
            <if test="custNos != null">
                and cust_no in
                <foreach collection="custNos" item="custNo" open="(" close=")" separator=",">
                    #{custNo}
                </foreach>
            </if>
            <if test="partnerNos != null">
                and partner_no in
                <foreach collection="partnerNos" item="partnerNo" open="(" close=")" separator=",">
                    #{partnerNo}
                </foreach>
            </if>
            <if test="fundNos != null">
                and fund_no in
                <foreach collection="fundNos" item="fundNo" open="(" close=")" separator=",">
                    #{fundNo}
                </foreach>
            </if>
            <if test="productNos != null ">
                and product_no in
                <foreach collection="productNos" item="productNo" open="(" close=")" separator=",">
                    #{productNo}
                </foreach>
            </if>
            <if test="isMapping != null  and isMapping != ''">and is_mapping = #{isMapping}</if>

            <if test="loanMonth != null and loanMonth != ''">and loan_month = #{loanMonth}</if>

            <if test="reconMonth != null and reconMonth != ''">and recon_month = #{reconMonth}</if>
            <if test="dBalanceDistributionMonth.moreSearchMap != null and !dBalanceDistributionMonth.moreSearchMap.isEmpty()">
                <foreach collection="dBalanceDistributionMonth.moreSearchMap.entrySet()" index="key" item="values">
                    AND ${key}_no IN
                    <foreach collection="values" item="value" open="(" close=")" separator=",">
                        #{value}
                    </foreach>
                </foreach>
            </if>
            <!-- 数据范围过滤 -->
            ${dBalanceDistributionMonth.params.dataScope}
        </where>


        ORDER BY
	    recon_month DESC,
	    loan_month DESC,
	    FIELD( balance_distribution_type, 'M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'M7', 'M7+', 'MF' )
    </select>

    <select id="selectDBalanceDistributionMonthById" parameterType="Long" resultMap="DBalanceDistributionMonthResult">
        <include refid="selectDBalanceDistributionMonthVo"/>
        where id = #{id}
    </select>

    <insert id="insertDBalanceDistributionMonth" parameterType="org.ruoyi.core.domain.DBalanceDistributionMonth" useGeneratedKeys="true" keyProperty="id">
        insert into d_balance_distribution_month
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="platformNo != null and platformNo != ''">platform_no,</if>
            <if test="custNo != null">cust_no,</if>
            <if test="partnerNo != null">partner_no,</if>
            <if test="fundNo != null">fund_no,</if>
            <if test="productNo != null">product_no,</if>
            <if test="loanMonth != null and loanMonth != ''">loan_month,</if>
            <if test="reconMonth != null and reconMonth != ''">recon_month,</if>
            <if test="loanBalanceAmount != null">loan_balance_amount,</if>
            <if test="loanRemainNumber != null">loan_remain_number,</if>
            <if test="balanceDistributionType != null and balanceDistributionType != ''">balance_distribution_type,</if>
            <if test="mNumber != null">m_number,</if>
            <if test="mBalanceAmount != null">m_balance_amount,</if>
            <if test="isMapping != null">is_mapping,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="platformNo != null and platformNo != ''">#{platformNo},</if>
            <if test="custNo != null">#{custNo},</if>
            <if test="partnerNo != null">#{partnerNo},</if>
            <if test="fundNo != null">#{fundNo},</if>
            <if test="productNo != null">#{productNo},</if>
            <if test="loanMonth != null and loanMonth != ''">#{loanMonth},</if>
            <if test="reconMonth != null and reconMonth != ''">#{reconMonth},</if>
            <if test="loanBalanceAmount != null">#{loanBalanceAmount},</if>
            <if test="loanRemainNumber != null">#{loanRemainNumber},</if>
            <if test="balanceDistributionType != null and balanceDistributionType != ''">#{balanceDistributionType},</if>
            <if test="mNumber != null">#{mNumber},</if>
            <if test="mBalanceAmount != null">#{mBalanceAmount},</if>
            <if test="isMapping != null">#{isMapping},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDBalanceDistributionMonth" parameterType="org.ruoyi.core.domain.DBalanceDistributionMonth">
        update d_balance_distribution_month
        <trim prefix="SET" suffixOverrides=",">
            <if test="platformNo != null and platformNo != ''">platform_no = #{platformNo},</if>
            <if test="custNo != null">cust_no = #{custNo},</if>
            <if test="partnerNo != null">partner_no = #{partnerNo},</if>
            <if test="fundNo != null">fund_no = #{fundNo},</if>
            <if test="productNo != null">product_no = #{productNo},</if>
            <if test="loanMonth != null and loanMonth != ''">loan_month = #{loanMonth},</if>
            <if test="reconMonth != null and reconMonth != ''">recon_month = #{reconMonth},</if>
            <if test="loanBalanceAmount != null">loan_balance_amount = #{loanBalanceAmount},</if>
            <if test="loanRemainNumber != null">loan_remain_number = #{loanRemainNumber},</if>
            <if test="balanceDistributionType != null and balanceDistributionType != ''">balance_distribution_type = #{balanceDistributionType},</if>
            <if test="mNumber != null">m_number = #{mNumber},</if>
            <if test="mBalanceAmount != null">m_balance_amount = #{mBalanceAmount},</if>
            <if test="isMapping != null">is_mapping = #{isMapping},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDBalanceDistributionMonthById" parameterType="Long">
        delete from d_balance_distribution_month where id = #{id}
    </delete>

    <delete id="deleteDBalanceDistributionMonthByIds" parameterType="String">
        delete from d_balance_distribution_month where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <select id="getNotMapping" resultMap="DBalanceDistributionMonthResult">
        <include refid="selectDBalanceDistributionMonthVo"/>
        where is_mapping = 'N'
    </select>

    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update d_balance_distribution_month
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="is_mapping = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.isMapping,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="m_balance_amount = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.mBalanceAmount,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="m_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.mNumber,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="balance_distribution_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.balanceDistributionType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="loan_remain_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.loanRemainNumber,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="loan_balance_amount = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.loanBalanceAmount,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="recon_month = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.reconMonth,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="loan_month = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.loanMonth,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.productNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="fund_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.fundNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="partner_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.partnerNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cust_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.custNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="platform_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.platformNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update d_balance_distribution_month
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.remark != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_mapping = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isMapping != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.isMapping,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="m_balance_amount = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.mBalanceAmount != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.mBalanceAmount,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="m_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.mNumber != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.mNumber,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="balance_distribution_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.balanceDistributionType != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.balanceDistributionType,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="loan_remain_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.loanRemainNumber != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.loanRemainNumber,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="loan_balance_amount = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.loanBalanceAmount != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.loanBalanceAmount,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="recon_month = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.reconMonth != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.reconMonth,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="loan_month = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.loanMonth != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.loanMonth,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="product_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.productNo != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.productNo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="fund_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.fundNo != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.fundNo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="partner_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.partnerNo != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.partnerNo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="cust_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.custNo != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.custNo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="platform_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.platformNo != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.platformNo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="selectDBalanceDistributionMonthListByDict1" resultType="java.util.Map">
        select b.dict_label AS platformNo,
               c.dict_label AS custNo,
               a.dict_label AS partnerNo,
               t.dict_label AS fundNo,
               sdd.dict_label AS productNo,
                d.loan_month AS loanMonth,
                d.recon_month AS reconMonth,
                d.loan_balance_amount AS loanBalanceAmount,
                d.loan_remain_number AS loanRemainNumber,
                d.balance_distribution_type AS balanceDistributionType,
                d.m_number AS mNumber,
                d.m_balance_amount AS mBalanceAmount,
                case when d.is_mapping='Y' then '映射成功'
                  when d.is_mapping='N' then '映射失败'
                  else '' end as isMapping,
                d.remark
                from d_balance_distribution_month d
                LEFT JOIN (
                     SELECT dict_label,dict_value,dict_type FROM sys_dict_data WHERE dict_type='cust_no'
                ) c ON d.cust_no=c.dict_value
            LEFT JOIN (
                     SELECT dict_label,dict_value,dict_type FROM sys_dict_data WHERE dict_type='platform_no'
                ) b ON d.platform_no=b.dict_value
            LEFT JOIN (
                     SELECT dict_label,dict_value,dict_type FROM sys_dict_data WHERE dict_type='partner_no'
                ) a ON d.partner_no=a.dict_value
            LEFT JOIN (
                     SELECT dict_label,dict_value,dict_type FROM sys_dict_data WHERE dict_type='fund_no'
                ) t ON d.fund_no=t.dict_value
            LEFT JOIN sys_dict_data sdd ON d.product_no=sdd.dict_value
            <where>
                1=1
                <if test="platformNos != null  ">
                    and d.platform_no in
                    <foreach collection="platformNos" item="platformNo" open="(" close=")" separator=",">
                        #{platformNo}
                    </foreach>
                </if>
                <if test="custNos != null">
                    and d.cust_no in
                    <foreach collection="custNos" item="custNo" open="(" close=")" separator=",">
                        #{custNo}
                    </foreach>
                </if>
                <if test="partnerNos != null">
                    and d.partner_no in
                    <foreach collection="partnerNos" item="partnerNo" open="(" close=")" separator=",">
                        #{partnerNo}
                    </foreach>
                </if>
                <if test="fundNos != null">
                    and d.fund_no in
                    <foreach collection="fundNos" item="fundNo" open="(" close=")" separator=",">
                        #{fundNo}
                    </foreach>
                </if>
                <if test="productNos != null ">
                    and d.product_no in
                    <foreach collection="productNos" item="productNo" open="(" close=")" separator=",">
                        #{productNo}
                    </foreach>
                </if>
                <if test="isMapping != null  and isMapping != ''">and d.is_mapping = #{isMapping}</if>

                <if test="loanMonth != null and loanMonth != ''">and d.loan_month = #{loanMonth}</if>

                <if test="reconMonth != null and reconMonth != ''">and d.recon_month = #{reconMonth}</if>
                <!-- 数据范围过滤 -->
                ${dBalanceDistributionMonth.params.dataScope}
            </where>


            ORDER BY
            d.recon_month DESC,
            d.loan_month DESC,
            FIELD( d.balance_distribution_type, 'M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'M7', 'M7+', 'MF' )
    </select>
</mapper>
