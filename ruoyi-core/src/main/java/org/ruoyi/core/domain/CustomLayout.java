package org.ruoyi.core.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 custom_layout
 * 
 * <AUTHOR>
 * @date 2023-05-08
 */
public class CustomLayout extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 自定义布局id */
    private Long id;

    /** 用户id */
    @Excel(name = "用户id")
    private Long userId;

    /** 在贷余额是否显示0显示1隐藏 */
    @Excel(name = "在贷余额是否显示0显示1隐藏")
    private String balance;

    /** 每日放还款是否显示0显示1隐藏 */
    @Excel(name = "每日放还款是否显示0显示1隐藏")
    private String repayCount;

    /** 每日新增贷款笔数0显示1隐藏 */
    @Excel(name = "每日新增贷款笔数0显示1隐藏")
    private String addAmount;

    /** 各合作方占比 */
    @Excel(name = "各合作方占比")
    private String partnerPie;

    /** 各合作方在贷余额分布 */
    @Excel(name = "各合作方在贷余额分布")
    private String partnerBar;

    /** 各资金方占比 */
    @Excel(name = "各资金方占比")
    private String fundPie;

    /** 各资金方在贷余额分布 */
    @Excel(name = "各资金方在贷余额分布")
    private String fundBar;

    /** 各担保公司占比 */
    @Excel(name = "各担保公司占比")
    private String custPie;

    /** 各担保公司在贷余额分布 */
    @Excel(name = "各担保公司在贷余额分布")
    private String custBar;

    /** Vintage是否显示 */
    @Excel(name = "Vintage是否显示")
    private String vintage;

    /** 利润测算是否显示 */
    @Excel(name = "利润测算是否显示")
    private String profit;

    /** 坏账率是否显示 */
    @Excel(name = "坏账率是否显示")
    private String badDebt;

    /** 在贷余额合作方堆叠 */
    @Excel(name = "在贷余额合作方堆叠")
    private String balancePartnerStack;

    /** 在贷余额资金方堆叠 */
    @Excel(name = "在贷余额资金方堆叠")
    private String balanceFundStack;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setBalance(String balance) 
    {
        this.balance = balance;
    }

    public String getBalance() 
    {
        return balance;
    }
    public void setRepayCount(String repayCount) 
    {
        this.repayCount = repayCount;
    }

    public String getRepayCount() 
    {
        return repayCount;
    }
    public void setAddAmount(String addAmount) 
    {
        this.addAmount = addAmount;
    }

    public String getAddAmount() 
    {
        return addAmount;
    }
    public void setPartnerPie(String partnerPie) 
    {
        this.partnerPie = partnerPie;
    }

    public String getPartnerPie() 
    {
        return partnerPie;
    }
    public void setPartnerBar(String partnerBar) 
    {
        this.partnerBar = partnerBar;
    }

    public String getPartnerBar() 
    {
        return partnerBar;
    }
    public void setFundPie(String fundPie) 
    {
        this.fundPie = fundPie;
    }

    public String getFundPie() 
    {
        return fundPie;
    }
    public void setFundBar(String fundBar) 
    {
        this.fundBar = fundBar;
    }

    public String getFundBar() 
    {
        return fundBar;
    }
    public void setCustPie(String custPie) 
    {
        this.custPie = custPie;
    }

    public String getCustPie() 
    {
        return custPie;
    }
    public void setCustBar(String custBar) 
    {
        this.custBar = custBar;
    }

    public String getCustBar() 
    {
        return custBar;
    }
    public void setVintage(String vintage) 
    {
        this.vintage = vintage;
    }

    public String getVintage() 
    {
        return vintage;
    }
    public void setProfit(String profit) 
    {
        this.profit = profit;
    }

    public String getProfit() 
    {
        return profit;
    }
    public void setBadDebt(String badDebt) 
    {
        this.badDebt = badDebt;
    }

    public String getBadDebt() 
    {
        return badDebt;
    }
    public void setBalancePartnerStack(String balancePartnerStack) 
    {
        this.balancePartnerStack = balancePartnerStack;
    }

    public String getBalancePartnerStack() 
    {
        return balancePartnerStack;
    }
    public void setBalanceFundStack(String balanceFundStack) 
    {
        this.balanceFundStack = balanceFundStack;
    }

    public String getBalanceFundStack() 
    {
        return balanceFundStack;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userId", getUserId())
            .append("balance", getBalance())
            .append("repayCount", getRepayCount())
            .append("addAmount", getAddAmount())
            .append("partnerPie", getPartnerPie())
            .append("partnerBar", getPartnerBar())
            .append("fundPie", getFundPie())
            .append("fundBar", getFundBar())
            .append("custPie", getCustPie())
            .append("custBar", getCustBar())
            .append("vintage", getVintage())
            .append("profit", getProfit())
            .append("badDebt", getBadDebt())
            .append("balancePartnerStack", getBalancePartnerStack())
            .append("balanceFundStack", getBalanceFundStack())
            .toString();
    }
}
