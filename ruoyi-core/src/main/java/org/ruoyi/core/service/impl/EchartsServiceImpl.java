package org.ruoyi.core.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.google.gson.Gson;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.core.DateNumerationUtils;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.system.util.EchartDataUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.ruoyi.core.domain.*;
import org.ruoyi.core.domain.vo.HomePageDataSystemVo;
import org.ruoyi.core.mapper.DDataMapper;
import org.ruoyi.core.mapper.EChartsMapper;
import org.ruoyi.core.service.EchartsService;
import org.ruoyi.core.tool.DataDisposeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName EChartsServiceImpl.java
 * @Description 统计图service
 * @createTime 2022年04月24日 17:41:00
 */
@Service("echartsService")
@Slf4j
public class EchartsServiceImpl implements EchartsService {

    @Autowired
    private EChartsMapper eChartsMapper;

    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private SysSelectDataRefServiceImpl sysSelectDataRefService;

    @Autowired
    private DDataMapper dDataMapper;
    /**
     * gete图表数据
     * 获取图表数据
     * @param dVintageMonth vintage数据表
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    @Override
    @DataScope()
    public Map<String,Object> getVintageEChartData(DVintageMonth dVintageMonth) {

        int mobsize = 12;
        List<String> xaxis = new ArrayList<>();
        for (int i = 1;i<=mobsize;i++){
            xaxis.add("MOB"+i);
        }
        List<Map> paramsData = new ArrayList<>();

        //获取逻辑修改
        List<String> platforms = null;
        //  修改获取逻辑
//        if (Strings.isNotEmpty(dVintageMonth.getPlatformNo())) {
//            platforms = Arrays.asList(dVintageMonth.getPlatformNo().split(","));
//        }
        List<String> custNos = null;
//        if (Strings.isNotEmpty(dVintageMonth.getCustNo())) {
//            custNos = Arrays.asList(dVintageMonth.getCustNo().split(","));
//        }
        List<String> partnerNos = null;
//        if (Strings.isNotEmpty(dVintageMonth.getPartnerNo())) {
//
//            partnerNos = Arrays.asList(dVintageMonth.getPartnerNo().split(","));
//        }
        List<String> fundNos = null;

//        if (Strings.isNotEmpty(dVintageMonth.getFundNo())) {
//            fundNos = Arrays.asList(dVintageMonth.getFundNo().split(","));
//        }
        List<String> products = null;
        if (Strings.isNotEmpty(dVintageMonth.getProductNo())) {
            products = Arrays.asList(dVintageMonth.getProductNo().split(","));
        }
        String vintageDay = null;
        if (Strings.isNotEmpty(dVintageMonth.getVintageDay().toString())) {
            vintageDay = dVintageMonth.getVintageDay().toString();
        }
        //end
//        if (dVintageMonth.getMoreSearch() != null && dVintageMonth.getMoreSearch().length() > 2){
//            Gson gson = new Gson();
//            // 将字符串转换为 Map<String, List<Long>>
//            Map<String, List<String>> moreSearch = gson.fromJson(dVintageMonth.getMoreSearch(), Map.class);
//            dVintageMonth.setMoreSearchMap(moreSearch);
//        }

        if(StringUtils.isNotEmpty(dVintageMonth.getReconMonth())){
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM");
            Date parse = new Date();
            try {
                parse = simpleDateFormat.parse(dVintageMonth.getReconMonth());
            } catch (ParseException e) {
                e.printStackTrace();
            }
            String startTime = simpleDateFormat.format(DateUtils.addMonths(parse,  - 12));
            String endTime = simpleDateFormat.format(parse);
            HashMap<String, Object> dataHashMap = new HashMap<>();
//            dataHashMap.put("beginTime",startTime);
//            dataHashMap.put("endTime",endTime);
//            dVintageMonth.setParams(dataHashMap);
//            paramsData =eChartsMapper.getParamsData(dVintageMonth);
            //系统，担保公司，资产方，资金方，产品，vintage天数，时间放在了实体里
            paramsData =eChartsMapper.getVintageEChartData(platforms,custNos,partnerNos,fundNos,products,vintageDay,startTime,endTime,dVintageMonth);
        }else {
            String startTime = null;
            String endTime = null;
            paramsData =eChartsMapper.getVintageEChartData(platforms,custNos,partnerNos,fundNos,products,vintageDay,startTime,endTime,dVintageMonth);
        }



        Map<String, String[]> seriesDatas = new TreeMap<>();
        for (Map d : paramsData) {
            String loanMonth = (String) d.get("loan_month");
            String vtgType = (String) d.get("vintage_type");
            String vtgRate = "";
            BigDecimal vintageMoleCule = EchartDataUtils.getBigDecimal(d.get("sum1"));
            BigDecimal vintageDenominator = EchartDataUtils.getBigDecimal(d.get("sum2"));

            if(vintageDenominator.compareTo(BigDecimal.ZERO) == 0 || vintageMoleCule.compareTo(BigDecimal.ZERO) == 0){
                vtgRate = "0.0000";
            }else {
                BigDecimal divide = vintageMoleCule.divide(vintageDenominator, 4,BigDecimal.ROUND_HALF_UP);
                vtgRate =  divide.toString();
            }

            int mob = Integer.parseInt(vtgType.substring(3));
            if (mob < 1 || mob >= mobsize) {
                continue;
            }

            String[] val = seriesDatas.get(loanMonth);

            if (val == null) {
                val = new String[mobsize];
                seriesDatas.put(loanMonth, val);
            }
            val[mob] = new BigDecimal(vtgRate).toString();

        }
        List<Map> seriesList = new ArrayList<>();
        seriesDatas.forEach((k, val) -> {
            Map m = new HashMap();
            Map focus = new HashMap();
            m.put("name", k);
            m.put("type", "line");
            focus.put("focus","series");
            m.put("emphasis",focus);

            m.put("data", val);
            seriesList.add(m);
        });
        Map retMap = new HashMap<>();
        retMap.put("legendData", seriesDatas.keySet());
        retMap.put("xaxis",xaxis);

        retMap.put("data", seriesList);
        return retMap;


    }

    /**
     * 得到x轴数据
     *
     * @return {@link List}<{@link String}>
     */
    @Override
    public List<String> getXaxis() {
        List<String> vitageTypes =  eChartsMapper.getVitages();
        return vitageTypes;
    }


    /**
     * 余额分布
     *
     * @param dBalanceDistributionMonthVO 月vo d平衡分布
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    @Override
    @DataScope()
    public Map<String, Object> BalanceCharts(DBalanceDistributionMonth dBalanceDistributionMonthVO) {
        Map retMap = new HashMap<>();


        //获取逻辑修改
        //  修改获取逻辑
        List<String> platforms = null;
//        if (Strings.isNotEmpty(dVintageMonth.getPlatformNo())) {
//            platforms = Arrays.asList(dVintageMonth.getPlatformNo().split(","));
//        }
        List<String> custNos = null;
//        if (Strings.isNotEmpty(dVintageMonth.getCustNo())) {
//            custNos = Arrays.asList(dVintageMonth.getCustNo().split(","));
//        }
        List<String> partnerNos = null;
//        if (Strings.isNotEmpty(dVintageMonth.getPartnerNo())) {
//
//            partnerNos = Arrays.asList(dVintageMonth.getPartnerNo().split(","));
//        }
        List<String> fundNos = null;

//        if (Strings.isNotEmpty(dVintageMonth.getFundNo())) {
//            fundNos = Arrays.asList(dVintageMonth.getFundNo().split(","));
//        }
        List<String> products = null;
        if (Strings.isNotEmpty(dBalanceDistributionMonthVO.getProductNo())) {
            products = Arrays.asList(dBalanceDistributionMonthVO.getProductNo().split(","));
        }
        //end

        List<Map> seriesList = new ArrayList<Map>();
//        if (dBalanceDistributionMonthVO.getMoreSearch() != null && dBalanceDistributionMonthVO.getMoreSearch().length() > 2){
//            Gson gson = new Gson();
//            // 将字符串转换为 Map<String, List<Long>>
//            Map<String, List<String>> moreSearch = gson.fromJson(dBalanceDistributionMonthVO.getMoreSearch(), Map.class);
//            dBalanceDistributionMonthVO.setMoreSearchMap(moreSearch);
//        }
        if(StringUtils.isNotEmpty(dBalanceDistributionMonthVO.getReconMonth())){
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM");
            Date parse = new Date();
            try {
                parse = simpleDateFormat.parse(dBalanceDistributionMonthVO.getReconMonth());
            } catch (ParseException e) {
                e.printStackTrace();
            }
            String format = simpleDateFormat.format(parse);
            dBalanceDistributionMonthVO.setReconMonth(format);
            seriesList = eChartsMapper.queryAssetLoanBalanceSts(platforms,custNos,partnerNos,fundNos,products,format,dBalanceDistributionMonthVO);
//             seriesList = eChartsMapper.queryAssetLoanBalanceSts(dBalanceDistributionMonthVO);
        }else {
            String format = null;
            seriesList = eChartsMapper.queryAssetLoanBalanceSts(platforms,custNos,partnerNos,fundNos,products,format,dBalanceDistributionMonthVO);
        }

        retMap.put("data", seriesList);
        return retMap;
    }

    /**
     * 运行情况数据 不堆叠
     *
     *
     * @param dDataCope 维数据处理
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    @Override
    @DataScope()
    public Map<String, Object> dDataEChartData(DData dDataCope){
        HashMap<String, Object> returnData = new HashMap<>();


        DData dData = new DData();
        dData.setPlatformNo(dDataCope.getPlatformNo());
        dData.setCustNo(dDataCope.getCustNo());
        dData.setPartnerNo(dDataCope.getPartnerNo());
        dData.setFundNo(dDataCope.getFundNo());
        dData.setProductNo(dDataCope.getProductNo());
        String productCode = sysSelectDataRefService.getProductCode(dData);
        if (productCode != null) {
            dDataCope.setProductNo(productCode);
        }
        if(StringUtils.isNotEmpty(dDataCope.getPortrayalType())){
            returnData = this.DDataEchart(dDataCope);
        } else {
            returnData = this.getDDataEChartData(dDataCope);
        }
        return returnData;

    }


    /**
     * 获取系统运行统计数据  不加维度统计
     * @param dDataCope 系统运行实体
     * @return
     */

    public HashMap<String, Object> getDDataEChartData(DData dDataCope) {

        //获取逻辑修改
        //  修改获取逻辑
        List<String> platforms = null;
        //        if (Strings.isNotEmpty(dData.getPlatformNo())) {
//            platforms = Arrays.asList(dData.getPlatformNo().split(","));
//        }
        List<String> custNos = null;
//        if (Strings.isNotEmpty(dData.getCustNo())) {
//            custNos = Arrays.asList(dData.getCustNo().split(","));
//        }
        List<String> partnerNos = null;
//        if (Strings.isNotEmpty(dData.getPartnerNo())) {
//
//            partnerNos = Arrays.asList(dData.getPartnerNo().split(","));
//        }
        List<String> fundNos = null;
//        if (Strings.isNotEmpty(dData.getFundNo())) {
//
//            fundNos = Arrays.asList(dData.getFundNo().split(","));
//        }
        List<String> products = null;
        if (Strings.isNotEmpty(dDataCope.getProductNo())) {
            products = Arrays.asList(dDataCope.getProductNo().split(","));
        }
        String beginTime  = null;
        if (Strings.isNotEmpty(dDataCope.getPortrayalNo())) {
            beginTime = dDataCope.getPortrayalNo();
        }

        String endTime  = null;
        if (Strings.isNotEmpty(dDataCope.getRemark())) {
            endTime = dDataCope.getRemark();
        }
        //end
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        String format = simpleDateFormat.format(date);
        //echart数据
        List<Map<String, Object>> maps= eChartsMapper.dDataEchart(platforms,custNos,partnerNos,fundNos,products,beginTime,endTime,dDataCope);
        //周数据add
        List<Map<String, Object>> weekMaps= eChartsMapper.dDataWeekEchart(platforms,custNos,partnerNos,fundNos,products,beginTime,endTime,dDataCope);
        //月数据add
        List<Map<String, Object>> monthMaps= eChartsMapper.dDataMonthEchart(platforms,custNos,partnerNos,fundNos,products,beginTime,endTime,dDataCope);
        //年数据add
        List<Map<String, Object>> yearMaps= eChartsMapper.dDataYearEchart(platforms,custNos,partnerNos,fundNos,products,beginTime,endTime,dDataCope);

        //获取累计数据 需要取时间点的数据
        //获取一段时间内周末的数据
        List<String> strings = com.ruoyi.common.utils.DateUtils.getweekDays(beginTime, endTime);
        //获取一段时间内月末的日期
        List<String> monthXaxis= DateNumerationUtils.getMonthBetweenDate(beginTime, endTime);
        List<String> monthEndDate = com.ruoyi.common.utils.DateUtils.getMonthEndDate(monthXaxis);
        //获取时间段内年末的日期
        List<String> yearXaxisData = new ArrayList<>();
        for (Map<String, Object> yearData : yearMaps) {
            if(!yearXaxisData.contains(yearData.get("reconDate").toString())){
                yearXaxisData.add(yearData.get("reconDate").toString()+"-12-31");
            }
        }
        //周数据count
        List<Map<String, Object>> weekCountMaps= eChartsMapper.dDataWeekCountEchart(platforms,custNos,partnerNos,fundNos,products,strings,dDataCope);
        //月数据count
        List<Map<String, Object>> monthCountMaps= eChartsMapper.dDataMonthCountEchart(platforms,custNos,partnerNos,fundNos,products,monthEndDate,dDataCope);
        //年数据count
        List<Map<String, Object>> yearCountMaps= eChartsMapper.dDataYearCountEchart(platforms,custNos,partnerNos,fundNos,products,yearXaxisData,dDataCope);

        String year =  endTime.substring(0,4);
        String month = endTime.substring(0,7);
        String month1 = endTime.substring(5,7);
        //判断当天日期和结束时间日期 如果当天大于结束时间则取最近
        int i = endTime.compareTo(format);
        if(i>0 || i == 0){
            //本周周末
            String weekendTimeSection = com.ruoyi.common.utils.DateUtils.getWeekendTimeSection(0);
            //本月月末
            String lastDayOfMonth = com.ruoyi.common.utils.DateUtils.getLastDayOfMonth(Integer.parseInt(year), Integer.parseInt(month1));
            //年末
            String yeardate = year+"-12-31";
            //获取endtime当天的数据
            //周
            List<Map<String, Object>> totalcount = dDataMapper.getnearTotalCountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> totalAmount = dDataMapper.getnearTotalAmountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> balanceAmount = dDataMapper.getnearDayData(platforms, custNos, partnerNos, fundNos,products,dDataCope);
            List<Map<String, Object>> repayAmount = dDataMapper.getnearTotalRepayAmountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> repayCount =dDataMapper.getnearTotalRepayCountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);

            totalcount.removeAll(Collections.singleton(null));
            totalAmount.removeAll(Collections.singleton(null));
            balanceAmount.removeAll(Collections.singleton(null));
            repayAmount.removeAll(Collections.singleton(null));
            repayCount.removeAll(Collections.singleton(null));
            if(totalcount.size()>0 && totalAmount.size()>0 && balanceAmount.size()>0 && repayAmount.size()>0 && repayCount.size()>0 ){
                strings.add(weekendTimeSection);
            Map<String, Object> map = totalcount.get(0);
            map.put("reconDate",weekendTimeSection);
            map.put("totalAmount",totalAmount.get(0).get("totalAmount"));
            map.put("balanceAmount",balanceAmount.get(0).get("balanceAmount"));
            map.put("repayPrintAmount",repayAmount.get(0).get("totalRepayPrintAmount"));
            map.put("repayCount",repayCount.get(0).get("totalRepayCount"));
            weekCountMaps.add(map);
            }
            //月

            List<Map<String, Object>> totalmountcount = dDataMapper.getnearTotalCountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> totalmountAmount = dDataMapper.getnearTotalAmountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> balancemountAmount = dDataMapper.getnearDayData(platforms, custNos, partnerNos, fundNos,products,dDataCope);
            List<Map<String, Object>> repaymountAmount = dDataMapper.getnearTotalRepayAmountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> repaymountCount =dDataMapper.getnearTotalRepayCountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            totalmountcount.removeAll(Collections.singleton(null));
            totalmountAmount.removeAll(Collections.singleton(null));
            balancemountAmount.removeAll(Collections.singleton(null));
            repaymountAmount.removeAll(Collections.singleton(null));
            repaymountCount.removeAll(Collections.singleton(null));
            if(totalmountcount.size()>0 && totalmountAmount.size()>0 && balancemountAmount.size()>0 && repaymountAmount.size()>0 && repaymountCount.size()>0 ) {
                monthXaxis.add(lastDayOfMonth);
                Map<String, Object> mapmonth = totalmountcount.get(0);
                mapmonth.put("reconDate", lastDayOfMonth);
                mapmonth.put("totalAmount", totalmountAmount.get(0).get("totalAmount"));
                mapmonth.put("balanceAmount", balancemountAmount.get(0).get("balanceAmount"));
                mapmonth.put("repayPrintAmount",repaymountAmount.get(0).get("totalRepayPrintAmount"));
                mapmonth.put("repayCount",repaymountCount.get(0).get("totalRepayCount"));
                monthCountMaps.add(mapmonth);
            }
            //年

            List<Map<String, Object>> totalyearcount = dDataMapper.getnearTotalCountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> totalyearAmount = dDataMapper.getnearTotalAmountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> balanceyearAmount = dDataMapper.getnearDayData(platforms, custNos, partnerNos, fundNos,products,dDataCope);
            List<Map<String, Object>> repayyearAmount = dDataMapper.getnearTotalRepayAmountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> repayyearCount =dDataMapper.getnearTotalRepayCountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);

            totalyearcount.removeAll(Collections.singleton(null));
            totalyearAmount.removeAll(Collections.singleton(null));
            balanceyearAmount.removeAll(Collections.singleton(null));
            repayyearAmount.removeAll(Collections.singleton(null));
            repayyearCount.removeAll(Collections.singleton(null));
            if(totalyearcount.size()>0 && totalyearAmount.size()>0 && balanceyearAmount.size()>0 && repayyearAmount.size()>0 && repaymountCount.size()>0 ) {
                Map<String, Object> mapyear = totalyearcount.get(0);
                mapyear.put("reconDate", yeardate);
                mapyear.put("totalAmount", totalyearAmount.get(0).get("totalAmount"));
                mapyear.put("balanceAmount", balanceyearAmount.get(0).get("balanceAmount"));
                mapyear.put("repayPrintAmount", repayyearAmount.get(0).get("totalRepayPrintAmount"));
                mapyear.put("repayCount", repayyearCount.get(0).get("totalRepayCount"));
                yearCountMaps.add(mapyear);
            }
        }






        //X轴 时间
        List<String> xixas = DateNumerationUtils.getBetweenDate(beginTime,endTime);
        //添加累计贷款余额
        ArrayList<Object> balanceAList = new ArrayList<>();
        //累计贷款笔数
        ArrayList<Object> tCountList = new ArrayList<>();
        //累计贷款本金金额
        ArrayList<Object> tAmountList = new ArrayList<>();
        //新增贷款本金
        ArrayList<Object> addAmountList = new ArrayList<>();
        //新增贷款笔数
        ArrayList<Object> addCountList = new ArrayList<>();
        //累计还款本金
        ArrayList<Object> repayAmountList = new ArrayList<>();
        //累计还款笔数
        ArrayList<Object> repayCountList = new ArrayList<>();
        //新增还款本金
        ArrayList<Object> addRepayAmountList = new ArrayList<>();
        //新增还款笔数
        ArrayList<Object> addRepayCountList = new ArrayList<>();

        String mapKey = "reconDate";
        //将 List<Map<String, Object>> 格式转换为Map<String, Map<String, Object>> 方便用日期获取数据
        Map<String, Map<String, Object>> echartDataMap = DataDisposeUtil.listToMap(maps, mapKey);
        //周
        Map<String, Map<String, Object>> weekDataMap = DataDisposeUtil.listToMap(weekMaps, mapKey);
        //月
        Map<String, Map<String, Object>> monthDataMap = DataDisposeUtil.listToMap(monthMaps, mapKey);
        //年
        Map<String, Map<String, Object>> yearDataMap = DataDisposeUtil.listToMap(yearMaps, mapKey);
        //周count
        Map<String, Map<String, Object>> weekDataMapcount = DataDisposeUtil.listToMap(weekCountMaps, mapKey);
        //月count
        Map<String, Map<String, Object>> monthDataMapcount = DataDisposeUtil.listToMap(monthCountMaps, mapKey);
        //年count
        Map<String, Map<String, Object>> yearDataMapcount = DataDisposeUtil.listToMap(yearCountMaps, mapKey);

        //带维度不堆叠

        Map<String, Object> map = org.ruoyi.core.util.EChartDataUtil.noweiduData(weekDataMap, monthDataMap, yearDataMap, yearMaps,strings,monthEndDate,yearXaxisData,weekDataMapcount,monthDataMapcount,yearDataMapcount, beginTime, endTime, null, "N", "N");
        for (String xixa : xixas) {
            if(null==echartDataMap.get(xixa)){
                //添加累计贷款本金余额
                balanceAList.add("0.00");

                //累计贷款笔数
                tCountList.add("0");

                //累计贷款本金金额
                tAmountList.add("0.00");

                //新增贷款本金
                addAmountList.add("0.00");

                //新增贷款笔数
                addCountList.add("0");

                //累计还款本金
                repayAmountList.add("0.00");

                //累计还款笔数
                repayCountList.add("0");

                //新增还款本金
                addRepayAmountList.add("0.00");

                //新增还款笔数
                addRepayCountList.add("0");
            }else {
                Map<String, Object> dataMap = echartDataMap.get(xixa);
                //添加累计贷款本金余额
                balanceAList.add(dataMap.get("balanceAmount"));

                //累计贷款笔数
                tCountList.add(dataMap.get("totalCount"));

                //累计贷款本金金额
                tAmountList.add(dataMap.get("totalAmount"));

                //新增贷款本金
                addAmountList.add(dataMap.get("addAmount"));

                //新增贷款笔数
                addCountList.add(dataMap.get("addCount"));

                //累计还款本金
                repayAmountList.add(dataMap.get("repayPrintAmount"));

                //累计还款笔数
                repayCountList.add(dataMap.get("repayCount"));

                //新增还款本金
                addRepayAmountList.add(dataMap.get("addRepayPrintAmount"));

                //新增还款笔数
                addRepayCountList.add(dataMap.get("addRepayCount"));
            }
        }
        HashMap<String, Object> balanceData = new HashMap<>();
        balanceData.put("name","贷款余额");
        balanceData.put("type","bar");
        balanceData.put("data",balanceAList);
        //累计贷款笔数
        HashMap<String, Object> totalData = new HashMap<>();
        totalData.put("name","在贷笔数");
        totalData.put("type","bar");
        totalData.put("data",tCountList);
        //累计贷款金额
        HashMap<String, Object> amountData = new HashMap<>();
        amountData.put("name","贷款金额");
        amountData.put("type","bar");
        amountData.put("data",tAmountList);
        //新增贷款本金
        HashMap<String, Object> addMountData = new HashMap<>();
        addMountData.put("name","新增贷款本金");
        addMountData.put("type","bar");
        addMountData.put("data",addAmountList);

        //新增贷款笔数
        HashMap<String, Object> addCountData = new HashMap<>();
        addCountData.put("name","新增贷款笔数");
        addCountData.put("type","bar");
        addCountData.put("data",addCountList);
        //累计还款本金
        HashMap<String, Object> repayAmountData = new HashMap<>();
        repayAmountData.put("name","累计还款本金");
        repayAmountData.put("type","bar");
        repayAmountData.put("data",repayAmountList);
        //累计还款笔数
        HashMap<String, Object> repayCountData = new HashMap<>();
        repayCountData.put("name","累计还款笔数");
        repayCountData.put("type","bar");
        repayCountData.put("data",repayCountList);
        //新增还款金额
        HashMap<String, Object> addRepayAmountData = new HashMap<>();
        addRepayAmountData.put("name","新增还款金额");
        addRepayAmountData.put("type","bar");
        addRepayAmountData.put("data",addRepayAmountList);
        //新增还款笔数
        HashMap<String, Object> addRepayCountData = new HashMap<>();
        addRepayCountData.put("name","新增还款笔数");
        addRepayCountData.put("type","bar");
        addRepayCountData.put("data",addRepayCountList);

        //1、添加累计贷款余额与笔数图标显示，
        ArrayList<Map<String, Object>> balanceAndTotalData = new ArrayList<>();
        balanceAndTotalData.add(balanceData);
        balanceAndTotalData.add(totalData);
//        2、添加累计贷款本金金额与笔数图表显示
        ArrayList<Map<String, Object>> amountAndTotalData = new ArrayList<>();
        amountAndTotalData.add(amountData);
        amountAndTotalData.add(totalData);
//        3、新增贷款本金金额与笔数图表显示
        ArrayList<Map<String, Object>> addAmountAndTotalData = new ArrayList<>();
        addAmountAndTotalData.add(addMountData);
        addAmountAndTotalData.add(addCountData);
//        4、累计还款本金与笔数图表显示
        ArrayList<Map<String, Object>>  totalPrintAmountAndTotalData = new ArrayList<>();
        totalPrintAmountAndTotalData.add(repayAmountData);
        totalPrintAmountAndTotalData.add(repayCountData);
//        5、新增还款本金与笔数图表显示
        ArrayList<Map<String, Object>> addRepayAmountAndTotalData = new ArrayList<>();
        addRepayAmountAndTotalData.add(addRepayAmountData);
        addRepayAmountAndTotalData.add(addRepayCountData);

        HashMap<String, Object> returnData = new HashMap<>();

        returnData.put("balanceAndTotalData",balanceAndTotalData);
        returnData.put("amountAndTotalData",amountAndTotalData);
        returnData.put("addAmountAndTotalData",addAmountAndTotalData);
        returnData.put("totalPrintAmountAndTotalData",totalPrintAmountAndTotalData);
        returnData.put("addRepayAmountAndTotalData",addRepayAmountAndTotalData);
        returnData.put("xiaxis",xixas);
        returnData.putAll(map);
        returnData.put("weekcountxaxis",strings);
        returnData.put("monthcountxaxis",monthEndDate);
        returnData.put("yearcountxaxis",yearXaxisData);
        return returnData;
    }


    /**
     * ddata echart
     * 运行情况统计 加维度
     * @param dDataCope 维数据处理
     * @return {@link HashMap}<{@link String}, {@link Object}>
     */
    public HashMap<String, Object> DDataEchart(DData dDataCope){

        //获取逻辑修改
        //  修改获取逻辑
        List<String> platforms = null;
        //        if (Strings.isNotEmpty(dData.getPlatformNo())) {
//            platforms = Arrays.asList(dData.getPlatformNo().split(","));
//        }
        List<String> custNos = null;
//        if (Strings.isNotEmpty(dData.getCustNo())) {
//            custNos = Arrays.asList(dData.getCustNo().split(","));
//        }
        List<String> partnerNos = null;
//        if (Strings.isNotEmpty(dData.getPartnerNo())) {
//
//            partnerNos = Arrays.asList(dData.getPartnerNo().split(","));
//        }
        List<String> fundNos = null;
//        if (Strings.isNotEmpty(dData.getFundNo())) {
//
//            fundNos = Arrays.asList(dData.getFundNo().split(","));
//        }
        List<String> products = null;
        if (Strings.isNotEmpty(dDataCope.getProductNo())) {
            products = Arrays.asList(dDataCope.getProductNo().split(","));
        }

        String beginTime  = null;
        if (Strings.isNotEmpty(dDataCope.getPortrayalNo())) {
            beginTime = dDataCope.getPortrayalNo();
        }

        String endTime  = null;
        if (Strings.isNotEmpty(dDataCope.getRemark())) {
            endTime = dDataCope.getRemark();
        }
        String portrayalType = null;
        if (Strings.isNotEmpty(dDataCope.getPortrayalType())) {
            portrayalType = dDataCope.getPortrayalType();
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        String format = simpleDateFormat.format(date);
        //X轴 时间
        List<String> xixas = DateNumerationUtils.getBetweenDate(beginTime,endTime);

        if (dDataCope.getMoreSearch() != null && dDataCope.getMoreSearch().length() > 2){
            Gson gson = new Gson();
            // 将字符串转换为 Map<String, List<Long>>
            Map<String, List<String>> moreSearch = gson.fromJson(dDataCope.getMoreSearch(), Map.class);
            dDataCope.setMoreSearchMap(moreSearch);
        }
        //echaert数据
        List<Map<String, Object>> maps = eChartsMapper.dDataDateEchart(platforms,custNos,partnerNos,fundNos,products,beginTime,endTime,portrayalType,dDataCope);
        //周数据
        List<Map<String, Object>> weekMaps = eChartsMapper.dDataWeeKDateEchart(platforms,custNos,partnerNos,fundNos,products,beginTime,endTime,portrayalType,dDataCope);
        //月数据
        List<Map<String, Object>> monthMaps = eChartsMapper.dDataMonthDateEchart(platforms,custNos,partnerNos,fundNos,products,beginTime,endTime,portrayalType,dDataCope);
        //年数据
        List<Map<String, Object>> yearMaps = eChartsMapper.dDataYearDateEchart(platforms,custNos,partnerNos,fundNos,products,beginTime,endTime,portrayalType,dDataCope);
        //获取累计数据 需要取时间点的数据
        //获取一段时间内周末的数据
        List<String> strings = com.ruoyi.common.utils.DateUtils.getweekDays(beginTime, endTime);
        //获取一段时间内月末的日期
        List<String> monthXaxis= DateNumerationUtils.getMonthBetweenDate(beginTime, endTime);
        List<String> monthEndDate = com.ruoyi.common.utils.DateUtils.getMonthEndDate(monthXaxis);
        //获取时间段内年末的日期
        List<String> yearXaxisData = new ArrayList<>();
        for (Map<String, Object> yearData : yearMaps) {
            if(!yearXaxisData.contains(yearData.get("reconDate").toString())){
                yearXaxisData.add(yearData.get("reconDate").toString()+"-12-31");
            }
        }
        //周数据count
        List<Map<String, Object>> weekCountMaps= eChartsMapper.dDataWeeKCountDateEchart(platforms,custNos,partnerNos,fundNos,products,strings,portrayalType,dDataCope);
        //月数据count
        List<Map<String, Object>> monthCountMaps= eChartsMapper.dDataMonthCountDateEchart(platforms,custNos,partnerNos,fundNos,products,monthEndDate,portrayalType,dDataCope);
        //年数据count
        List<Map<String, Object>> yearCountMaps= eChartsMapper.dDataYearCountDateEchart(platforms,custNos,partnerNos,fundNos,products,yearXaxisData,portrayalType,dDataCope);
        String year =  endTime.substring(0,4);
        String month = endTime.substring(0,7);
        String month1 = endTime.substring(5,7);
        //判断当天日期和结束时间日期 如果当天大于结束时间则取最近
        int i = endTime.compareTo(format);
        if(i>0 || i == 0){
            //本周周末
            String weekendTimeSection = com.ruoyi.common.utils.DateUtils.getWeekendTimeSection(0);
            //本月月末
            String lastDayOfMonth = com.ruoyi.common.utils.DateUtils.getLastDayOfMonth(Integer.parseInt(year), Integer.parseInt(month1));
            //年末
            String yeardate = year+"-12-31";
            //获取endtime当天的数据
            //周
            List<Map<String, Object>> totalcount = dDataMapper.getnearTotalCountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> totalAmount = dDataMapper.getnearTotalAmountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> balanceAmount = dDataMapper.getnearDayData(platforms, custNos, partnerNos, fundNos,products,dDataCope);
            List<Map<String, Object>> repayAmount = dDataMapper.getnearTotalRepayAmountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> repayCount =dDataMapper.getnearTotalRepayCountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            totalcount.removeAll(Collections.singleton(null));
            totalAmount.removeAll(Collections.singleton(null));
            balanceAmount.removeAll(Collections.singleton(null));
            repayAmount.removeAll(Collections.singleton(null));
            repayCount.removeAll(Collections.singleton(null));
            String dictValue = "";
            if(dDataCope.getPortrayalType().equals("platform_no")){
                dictValue = "platformNo";
            }else if(dDataCope.getPortrayalType().equals("cust_no")){
                dictValue = "custNo";
            }if(dDataCope.getPortrayalType().equals("partner_no")){
                dictValue = "partnerNo";
            }if(dDataCope.getPortrayalType().equals("fund_no")){
                dictValue = "fundNo";
            }if(dDataCope.getPortrayalType().equals("product_no")){
                dictValue = "productNo";
            }
            if(totalcount.size()>0 && totalAmount.size()>0 && balanceAmount.size()>0 && repayAmount.size()>0 && repayCount.size()>0 ){
                strings.add(weekendTimeSection);
                Map<String, Object> map = totalcount.get(0);
                map.put("reconDate",weekendTimeSection);
                map.put("totalAmount",totalAmount.get(0).get("totalAmount"));
                map.put("balanceAmount",balanceAmount.get(0).get("balanceAmount"));
                map.put("repayPrintAmount",repayAmount.get(0).get("totalRepayPrintAmount"));
                map.put("repayCount",repayCount.get(0).get("totalRepayCount"));
                map.put("dictValue", map.get(dictValue));
                weekCountMaps.add(map);
            }
            //月

            List<Map<String, Object>> totalmountcount = dDataMapper.getnearTotalCountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> totalmountAmount = dDataMapper.getnearTotalAmountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> balancemountAmount = dDataMapper.getnearDayData(platforms, custNos, partnerNos, fundNos,products,dDataCope);
            List<Map<String, Object>> repaymountAmount = dDataMapper.getnearTotalRepayAmountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> repaymountCount =dDataMapper.getnearTotalRepayCountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            totalmountcount.removeAll(Collections.singleton(null));
            totalmountAmount.removeAll(Collections.singleton(null));
            balancemountAmount.removeAll(Collections.singleton(null));
            repaymountAmount.removeAll(Collections.singleton(null));
            repaymountCount.removeAll(Collections.singleton(null));
            if(totalmountcount.size()>0 && totalmountAmount.size()>0 && balancemountAmount.size()>0 && repaymountAmount.size()>0 && repaymountCount.size()>0 ) {
                monthXaxis.add(lastDayOfMonth);
                Map<String, Object> mapmonth = totalmountcount.get(0);
                mapmonth.put("reconDate", lastDayOfMonth);
                mapmonth.put("totalAmount", totalmountAmount.get(0).get("totalAmount"));
                mapmonth.put("balanceAmount", balancemountAmount.get(0).get("balanceAmount"));
                mapmonth.put("repayPrintAmount", repaymountAmount.get(0).get("totalRepayPrintAmount"));
                mapmonth.put("repayCount", repaymountCount.get(0).get("totalRepayCount"));
                mapmonth.put("dictValue", mapmonth.get(dictValue));
                monthCountMaps.add(mapmonth);
            }
            //年

            List<Map<String, Object>> totalyearcount = dDataMapper.getnearTotalCountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> totalyearAmount = dDataMapper.getnearTotalAmountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> balanceyearAmount = dDataMapper.getnearDayData(platforms, custNos, partnerNos, fundNos,products,dDataCope);
            List<Map<String, Object>> repayyearAmount = dDataMapper.getnearTotalRepayAmountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> repayyearCount =dDataMapper.getnearTotalRepayCountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);

            totalyearcount.removeAll(Collections.singleton(null));
            totalyearAmount.removeAll(Collections.singleton(null));
            balanceyearAmount.removeAll(Collections.singleton(null));
            repayyearAmount.removeAll(Collections.singleton(null));
            repayyearCount.removeAll(Collections.singleton(null));
            if(totalyearcount.size()>0 && totalyearAmount.size()>0 && balanceyearAmount.size()>0 && repayyearAmount.size()>0 && repaymountCount.size()>0 ) {
                Map<String, Object> mapyear = totalyearcount.get(0);
                mapyear.put("reconDate", yeardate);
                mapyear.put("totalAmount", totalyearAmount.get(0).get("totalAmount"));
                mapyear.put("balanceAmount", balanceyearAmount.get(0).get("balanceAmount"));
                mapyear.put("repayPrintAmount", repayyearAmount.get(0).get("totalRepayPrintAmount"));
                mapyear.put("repayCount", repayyearCount.get(0).get("totalRepayCount"));
                mapyear.put("dictValue", mapyear.get(dictValue));
                yearCountMaps.add(mapyear);
            }
        }

        //所有类型
        List<Map<String, Object>> dimensions =  eChartsMapper.getDDataDimension(platforms,custNos,partnerNos,fundNos,products,beginTime,endTime,portrayalType,dDataCope);

        //1、添加累计贷款余额与笔数图标显示，
        ArrayList<Map<String, Object>> balanceAndTotalData = new ArrayList<>();
//        2、添加累计贷款本金金额与笔数图表显示
        ArrayList<Map<String, Object>> amountAndTotalData = new ArrayList<>();

//        3、新增贷款本金金额与笔数图表显示
        ArrayList<Map<String, Object>> addAmountAndTotalData = new ArrayList<>();

//        4、累计还款本金与笔数图表显示
        ArrayList<Map<String, Object>>  totalPrintAmountAndTotalData = new ArrayList<>();

//        5、新增还款本金与笔数图表显示
        ArrayList<Map<String, Object>> addRepayAmountAndTotalData = new ArrayList<>();

        String mapKey = "reconDate";
        String otherMapKey = "dictValue";
        //将 List<Map<String, Object>> 格式转换为Map<String, Map<String, Object>> 方便用日期获取数据
        Map<String, Map<String, Object>> echartDataMap = DataDisposeUtil.listToMap(maps, mapKey,otherMapKey);
        //周
        Map<String, Map<String, Object>> weekDataMap = DataDisposeUtil.listToMap(weekMaps, mapKey,otherMapKey);
        //月
        Map<String, Map<String, Object>> monthDataMap = DataDisposeUtil.listToMap(monthMaps, mapKey,otherMapKey);
        //年
        Map<String, Map<String, Object>> yearDataMap = DataDisposeUtil.listToMap(yearMaps, mapKey,otherMapKey);
        //周count
        Map<String, Map<String, Object>> weekcountDataMap = DataDisposeUtil.listToMap(weekCountMaps, mapKey,otherMapKey);
        //月count
        Map<String, Map<String, Object>> monthcountDataMap = DataDisposeUtil.listToMap(monthCountMaps, mapKey,otherMapKey);
        //年count
        Map<String, Map<String, Object>> yearcountDataMap = DataDisposeUtil.listToMap(yearCountMaps, mapKey,otherMapKey);
        //带维度不堆叠

        Map<String, Object> map = org.ruoyi.core.util.EChartDataUtil.noweiduData(weekDataMap, monthDataMap, yearDataMap, yearMaps,strings,monthEndDate,yearXaxisData,weekcountDataMap,monthcountDataMap,yearcountDataMap, beginTime, endTime, dimensions, "Y", "N");
        for (Map<String, Object> dimension : dimensions) {
            //添加累计贷款余额
            ArrayList<Object> balanceAList = new ArrayList<>();
            //累计贷款笔数
            ArrayList<Object> tCountList = new ArrayList<>();
            //累计贷款本金金额
            ArrayList<Object> tAmountList = new ArrayList<>();
            //新增贷款本金
            ArrayList<Object> addAmountList = new ArrayList<>();
            //新增贷款笔数
            ArrayList<Object> addCountList = new ArrayList<>();
            //累计还款本金
            ArrayList<Object> repayAmountList = new ArrayList<>();
            //累计还款笔数
            ArrayList<Object> repayCountList = new ArrayList<>();
            //新增还款本金
            ArrayList<Object> addRepayAmountList = new ArrayList<>();
            //新增还款笔数
            ArrayList<Object> addRepayCountList = new ArrayList<>();

            for (String xixa : xixas) {
                if(null == echartDataMap.get(xixa+dimension.get("dictValue"))){
                    //添加累计贷款本金余额
                    balanceAList.add("0.00");

                    //累计贷款笔数
                    tCountList.add("0");

                    //累计贷款本金金额
                    tAmountList.add("0.00");

                    //新增贷款本金
                    addAmountList.add("0.00");

                    //新增贷款笔数
                    addCountList.add("0");

                    //累计还款本金
                    repayAmountList.add("0.00");

                    //累计还款笔数
                    repayCountList.add("0");

                    //新增还款本金
                    addRepayAmountList.add("0.00");

                    //新增还款笔数
                    addRepayCountList.add("0");

                }else {
                    Map<String, Object> dataMap = echartDataMap.get(xixa+dimension.get("dictValue"));
                    if(dataMap.get("dictValue").equals(dimension.get("dictValue"))){
                        //添加累计贷款本金余额
                        balanceAList.add(dataMap.get("balanceAmount"));

                        //累计贷款笔数
                        tCountList.add(dataMap.get("totalCount"));

                        //累计贷款本金金额
                        tAmountList.add(dataMap.get("totalAmount"));

                        //新增贷款本金
                        addAmountList.add(dataMap.get("addAmount"));

                        //新增贷款笔数
                        addCountList.add(dataMap.get("addCount"));

                        //累计还款本金
                        repayAmountList.add(dataMap.get("repayPrintAmount"));

                        //累计还款笔数
                        repayCountList.add(dataMap.get("repayCount"));

                        //新增还款本金
                        addRepayAmountList.add(dataMap.get("addRepayPrintAmount"));

                        //新增还款笔数
                        addRepayCountList.add(dataMap.get("addRepayCount"));

                    }

                }
            }

            //累计贷款余额
            HashMap<String, Object> balanceData = new HashMap<>();
            balanceData.put("name",dimension.get("name")+"贷款余额");
            balanceData.put("type","bar");
            balanceData.put("data",balanceAList);
            //累计贷款笔数
            HashMap<String, Object> totalData = new HashMap<>();
            totalData.put("name",dimension.get("name")+"在贷笔数");
            totalData.put("type","bar");
            totalData.put("data",tCountList);
            //累计贷款金额
            HashMap<String, Object> amountData = new HashMap<>();
            amountData.put("name",dimension.get("name")+"贷款金额");
            amountData.put("type","bar");
            amountData.put("data",tAmountList);
            //新增贷款本金
            HashMap<String, Object> addMountData = new HashMap<>();
            addMountData.put("name",dimension.get("name")+"新增贷款本金");
            addMountData.put("type","bar");
            addMountData.put("data",addAmountList);

            //新增贷款笔数
            HashMap<String, Object> addCountData = new HashMap<>();
            addCountData.put("name",dimension.get("name")+"新增贷款笔数");
            addCountData.put("type","bar");
            addCountData.put("data",addCountList);
            //累计还款本金
            HashMap<String, Object> repayAmountData = new HashMap<>();
            repayAmountData.put("name",dimension.get("name")+"累计还款本金");
            repayAmountData.put("type","bar");
            repayAmountData.put("data",repayAmountList);
            //累计还款笔数
            HashMap<String, Object> repayCountData = new HashMap<>();
            repayCountData.put("name",dimension.get("name")+"累计还款笔数");
            repayCountData.put("type","bar");
            repayCountData.put("data",repayCountList);
            //新增还款金额
            HashMap<String, Object> addRepayAmountData = new HashMap<>();
            addRepayAmountData.put("name",dimension.get("name")+"新增还款金额");
            addRepayAmountData.put("type","bar");
            addRepayAmountData.put("data",addRepayAmountList);
            //新增还款笔数
            HashMap<String, Object> addRepayCountData = new HashMap<>();
            addRepayCountData.put("name",dimension.get("name")+"新增还款笔数");
            addRepayCountData.put("type","bar");
            addRepayCountData.put("data",addRepayCountList);
//        1、添加累计贷款余额与笔数图标显示，
            balanceAndTotalData.add(balanceData);
            balanceAndTotalData.add(totalData);
//        2、添加累计贷款本金金额与笔数图表显示
            amountAndTotalData.add(amountData);
            amountAndTotalData.add(totalData);
//        3、新增贷款本金金额与笔数图表显示
            addAmountAndTotalData.add(addMountData);
            addAmountAndTotalData.add(addCountData);
//        4、累计还款本金与笔数图表显示
            totalPrintAmountAndTotalData.add(repayAmountData);
            totalPrintAmountAndTotalData.add(repayCountData);
//        5、新增还款本金与笔数图表显示
            addRepayAmountAndTotalData.add(addRepayAmountData);
            addRepayAmountAndTotalData.add(addRepayCountData);
        }

        HashMap<String, Object> returnData = new HashMap<>();

        returnData.put("balanceAndTotalData",balanceAndTotalData);
        returnData.put("amountAndTotalData",amountAndTotalData);
        returnData.put("addAmountAndTotalData",addAmountAndTotalData);
        returnData.put("totalPrintAmountAndTotalData",totalPrintAmountAndTotalData);
        returnData.put("addRepayAmountAndTotalData",addRepayAmountAndTotalData);
        returnData.put("xiaxis",xixas);
        returnData.putAll(map);
        returnData.put("weekcountxaxis",strings);
        returnData.put("monthcountxaxis",monthEndDate);
        returnData.put("yearcountxaxis",yearXaxisData);
        return returnData;

    }


    /**
     * 堆叠的系统运营情况
     */
    @Override
    @DataScope()
    public Map<String, Object> dDataEChartDataStack(DData dDataCope){
        log.debug("进入数据处理");

        String productCode = sysSelectDataRefService.getProductCode(dDataCope);
        if (productCode != null) {
            dDataCope.setProductNo(productCode);
        }
        HashMap<String, Object> returnData = new HashMap<>();
        if(StringUtils.isNotEmpty(dDataCope.getPortrayalType())){

            returnData = this.echartStack(dDataCope);
        } else {
            returnData = this.getdDataEChartDataStack(dDataCope);
        }
        log.debug("数据处理结束");
        return returnData;


    }

    /**
     * 利润计算echart
     *
     * @param dProfitData d利润数据
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    @Override
    @DataScope(moduleName = "profit")
    public Map<String, Object> profitCalculateEChart(DProfitData dProfitData) {
        BigDecimal tenThousand = new BigDecimal("10000");
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy");
        Date parse = new Date();
        //当前日期
        String format = simpleDateFormat.format(parse);
        String startTime = null;
        String endTime = null;
        if(StringUtils.isEmpty(dProfitData.getReconYear()) || null == dProfitData.getReconYear()){
            startTime = format+"-01";
        }else {
            startTime = dProfitData.getReconYear();
        }

        if(StringUtils.isEmpty(dProfitData.getRemark()) || null == dProfitData.getRemark()){
            endTime = format+"-12";
        }else {
            endTime = dProfitData.getRemark();
        }

        //获取X轴数据 也就是两个时间段的所有月份
        List<String> xAxis = DateNumerationUtils.getMonthBetweenDate(startTime, endTime);
        //取出时间参数 截取年份 进数据库查询数据
        String startYear = startTime.substring(0, 4);
        String endYear = endTime.substring(0, 4);
//        ArrayList<String> reconYear = new ArrayList<>();
//        reconYear.add(startYear);
//        reconYear.add(endYear);

        List<String> reconYear = DateNumerationUtils.getYearBetweenDate(startYear, endYear);
        //系统
        List<String> platformNo = null;
//        if (Strings.isNotEmpty(dVintageMonth.getPlatformNo())) {
//            platforms = Arrays.asList(dVintageMonth.getPlatformNo().split(","));
//        }
        List<String> custNo = null;
//        if (Strings.isNotEmpty(dVintageMonth.getCustNo())) {
//            custNos = Arrays.asList(dVintageMonth.getCustNo().split(","));
//        }
        List<String> partnerNo = null;
//        if (Strings.isNotEmpty(dVintageMonth.getPartnerNo())) {
//
//            partnerNos = Arrays.asList(dVintageMonth.getPartnerNo().split(","));
//        }
        List<String> fundNo = null;

//        if (Strings.isNotEmpty(dVintageMonth.getFundNo())) {
//            fundNos = Arrays.asList(dVintageMonth.getFundNo().split(","));
//        }
        List<String> products = null;
        if (Strings.isNotEmpty(dProfitData.getProductNo())) {
            products = Arrays.asList(dProfitData.getProductNo().split(","));
        }

//        if (dProfitData.getMoreSearch() != null && dProfitData.getMoreSearch().length() > 2){
//            Gson gson = new Gson();
//            // 将字符串转换为 Map<String, List<Long>>
//            Map<String, List<String>> moreSearch = gson.fromJson(dProfitData.getMoreSearch(), Map.class);
//            dProfitData.setMoreSearchMap(moreSearch);
//        }
       List<Map<String,Object>> profitCalculateData = eChartsMapper.getProfitCalculateData(platformNo,custNo,partnerNo,fundNo,products,reconYear,dProfitData);
        //数据封装  指标_年份_月为key 对应的月份值为value
        HashMap<String, Object> profitDataMap = this.profitDataListToMap(profitCalculateData);
        //循环X轴从 数据封装的map中获取数据
        //月毛利
        ArrayList<Object> profitList = new ArrayList<>();
        //月收入
        ArrayList<Object> incomeList = new ArrayList<>();
        //月成本
        ArrayList<Object> totalCostList = new ArrayList<>();
        //月坏账
        ArrayList<Object> badCostList = new ArrayList<>();
        //月通道费
        ArrayList<Object> flowCostList = new ArrayList<>();
        //月流量费
        ArrayList<Object> flowCostRateList = new ArrayList<>();
        //月运营成本
        ArrayList<Object> operatorCostList = new ArrayList<>();

        for (String xAxi : xAxis) {
            //月毛利
            if(profitDataMap.containsKey("9_"+xAxi) && null != profitDataMap.get("9_"+xAxi)){
                profitList.add(new BigDecimal(profitDataMap.get("9_"+xAxi).toString()).divide(tenThousand, 2, BigDecimal.ROUND_HALF_UP));
            }else {
                profitList.add("0.00");
            }
            //月收入
            if(profitDataMap.containsKey("1_"+xAxi) && null != profitDataMap.get("1_"+xAxi) ){
                incomeList.add(new BigDecimal(profitDataMap.get("1_"+xAxi).toString()).divide(tenThousand, 2, BigDecimal.ROUND_HALF_UP));
            }else {
                incomeList.add("0.00");
            }
            //月坏账
            if(profitDataMap.containsKey("2_"+xAxi) && null != profitDataMap.get("2_"+xAxi)){
                badCostList.add(new BigDecimal(profitDataMap.get("2_"+xAxi).toString()).divide(tenThousand, 2, BigDecimal.ROUND_HALF_UP));
            }else {
                badCostList.add("0.00");
            }
            //月通道费
            if(profitDataMap.containsKey("4_"+xAxi) && null != profitDataMap.get("4_"+xAxi)){
                flowCostList.add(new BigDecimal(profitDataMap.get("4_"+xAxi).toString()).divide(tenThousand, 2, BigDecimal.ROUND_HALF_UP));
            }else {
                flowCostList.add("0.00");
            }
            //月流量费
            if(profitDataMap.containsKey("5_"+xAxi) && null != profitDataMap.get("5_"+xAxi)){
                flowCostRateList.add(new BigDecimal(profitDataMap.get("5_"+xAxi).toString()).divide(tenThousand, 2, BigDecimal.ROUND_HALF_UP));
            }else {
                flowCostRateList.add("0.00");
            }
            //月运营成本
            if(profitDataMap.containsKey("6_"+xAxi) && null != profitDataMap.get("6_"+xAxi)){
                operatorCostList.add(new BigDecimal(profitDataMap.get("6_"+xAxi).toString()).divide(tenThousand, 2, BigDecimal.ROUND_HALF_UP));
            }else {
                operatorCostList.add("0.00");
            }
            //月成本
            totalCostList.add(new BigDecimal(new BigDecimal(badCostList.get(badCostList.size() - 1).toString()).add(new BigDecimal(flowCostList.get(flowCostList.size() - 1).toString())).add(new BigDecimal(flowCostRateList.get(flowCostRateList.size() - 1).toString()))
                    .add(new BigDecimal(operatorCostList.get(operatorCostList.size() - 1).toString())).toString()).divide(tenThousand, 2, BigDecimal.ROUND_HALF_UP));
//            if(profitDataMap.containsKey("6_"+xAxi) && null != profitDataMap.get("6_"+xAxi)){
//                totalCostList.add(profitDataMap.get("6_"+xAxi));
//            }else {
//                totalCostList.add("0.00");
//            }
        }
        //月毛利
        HashMap<String, Object> profitMap = new HashMap<>();
//        profitMap.put("name","利润");
        profitMap.put("type","bar");
//        profitMap.put("stack","abc");
        profitMap.put("data",profitList);
        //月收入
        HashMap<String, Object> incomeMap = new HashMap<>();
        incomeMap.put("name","收入");
        incomeMap.put("type","bar");
//        incomeMap.put("stack","abc");
        incomeMap.put("data",incomeList);
        //月成本
        HashMap<String, Object> totalCostMap = new HashMap<>();
        totalCostMap.put("name","成本");
        totalCostMap.put("type","bar");
//        totalCostMap.put("stack","abc");
        totalCostMap.put("data",totalCostList);
        //月坏账
        HashMap<String, Object> badCostMap = new HashMap<>();
        badCostMap.put("name","月坏账");
        badCostMap.put("type","bar");
        badCostMap.put("stack","abc");
        badCostMap.put("data",badCostList);
        //月通道费
        HashMap<String, Object> flowCostMap = new HashMap<>();
        flowCostMap.put("name","月通道费");
        flowCostMap.put("type","bar");
        flowCostMap.put("stack","abc");
        flowCostMap.put("data",flowCostList);
        //月流量费
        HashMap<String, Object> flowCostRateMap = new HashMap<>();
        flowCostRateMap.put("name","月流量费");
        flowCostRateMap.put("type","bar");
        flowCostRateMap.put("stack","abc");
        flowCostRateMap.put("data",flowCostRateList);
        //月运营成本
        HashMap<String, Object> operatorCostMap = new HashMap<>();
        operatorCostMap.put("name","月运营成本");
        operatorCostMap.put("type","bar");
        operatorCostMap.put("stack","abc");
        operatorCostMap.put("data",operatorCostList);

        //每月利润
        ArrayList<Map<String,Object>> profitEChartList = new ArrayList<>();
        profitEChartList.add(profitMap);
        //每月收入与成本
        ArrayList<Map<String,Object>> incomeAndCostEChartList = new ArrayList<>();
        incomeAndCostEChartList.add(incomeMap);
        //todo 把成本搞没
//        incomeAndCostEChartList.add(totalCostMap);
        //每月成本构成
        ArrayList<Map<String,Object>> costConstituteEChartList = new ArrayList<>();
        costConstituteEChartList.add(badCostMap);
        costConstituteEChartList.add(flowCostMap);
        costConstituteEChartList.add(flowCostRateMap);
        costConstituteEChartList.add(operatorCostMap);

        HashMap<String, Object> returnMap = new HashMap<>();
        returnMap.put("profitData",profitEChartList);
        returnMap.put("incomeAndCostData",incomeAndCostEChartList);
        returnMap.put("costConstituteData",costConstituteEChartList);
        returnMap.put("xAxis",xAxis);

        return returnMap;
    }

    @Override
    @DataScope()
    public Map<String, Object> partnerEchartData(DData dData) {

        //参数
        List<String> platforms = null;
        //        if (Strings.isNotEmpty(dData.getPlatformNo())) {
//            platforms = Arrays.asList(dData.getPlatformNo().split(","));
//        }
        List<String> custNos = null;
//        if (Strings.isNotEmpty(dData.getCustNo())) {
//            custNos = Arrays.asList(dData.getCustNo().split(","));
//        }
        List<String> partnerNos = null;
//        if (Strings.isNotEmpty(dData.getPartnerNo())) {
//
//            partnerNos = Arrays.asList(dData.getPartnerNo().split(","));
//        }
        List<String> fundNos = null;
//        if (Strings.isNotEmpty(dData.getFundNo())) {
//
//            fundNos = Arrays.asList(dData.getFundNo().split(","));
//        }
        List<String> products = null;
        if (Strings.isNotEmpty(dData.getProductNo())) {
            products = Arrays.asList(dData.getProductNo().split(","));
        }
//        if (dData.getMoreSearch() != null && dData.getMoreSearch().length() > 2){
//            Gson gson = new Gson();
//            // 将字符串转换为 Map<String, List<Long>>
//            Map<String, List<String>> moreSearch = gson.fromJson(dData.getMoreSearch(), Map.class);
//            dData.setMoreSearchMap(moreSearch);
//        }
        Map<String, Object> stackMap = this.partnerEchartLineStack(platforms, custNos, partnerNos, fundNos,products, dData);
        //权限sql语句字段替换
//        HashMap<String, Object> roleSql = new HashMap<>();
//        roleSql.put("dataScope",this.roleStringCost(dData.getParams().get("dataScope").toString()));
//        dData.setParams(roleSql);
        HashMap<String, Object> returnMap = new HashMap<>();
        //echart柱状图表数据
        List<Map<String, Object>> partnerEchartData = eChartsMapper.getPartnerEchartData(platforms, custNos, partnerNos, fundNos,products,dData);
        //得到所有合作方名字
        String dictType = "partner_no";
        List<Map<String, Object>> dictData = eChartsMapper.getDictData(dictType);

        //所有和
        Map<String, Object> allPartnerSum = eChartsMapper.getAllPartnerSum(platforms, custNos, partnerNos, fundNos, dData);

        if(CollectionUtils.isNotEmpty(partnerEchartData) && null != allPartnerSum.get("count")){
            //数据转换
            Map<String, Object> map = this.listToMap(partnerEchartData);
            List<Map<String, Object>> dictAndData = this.dictAddData(map,dictData);

            // 降序
            List<Map<String, Object>> sortedByHeightDescList = dictAndData.stream().sorted((h1, h2) -> (new BigDecimal(h2.get("count").toString())).compareTo(new BigDecimal(h1.get("count").toString()))).collect(Collectors.toList());


            //数据分割
            Map<String, List> countData = this.getXixasData(sortedByHeightDescList,allPartnerSum.get("count").toString());
            //柱状图数据
            HashMap<String, Object> pillarData = new HashMap<>();
            pillarData.put("type","bar");
            pillarData.put("data",countData.get("data"));

            //柱状图数据
            returnMap.put("pillarEchart",pillarData);
            //X轴数据
            returnMap.put("xaxis",countData.get("xaxis"));
            //饼图数据
            returnMap.put("pieData",countData.get("pie"));
            returnMap.put("partnerTableData",countData.get("fundTableData"));
            returnMap.putAll(stackMap);
            return returnMap;
        }
        return returnMap;
    }

    /**
     * 合作方echart堆叠数据
     *
     * @param platforms  平台
     * @param custNos    cust号
     * @param partnerNos 伴侣号
     * @param fundNos    基金号
     * @param dData      维数据
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    public Map<String,Object> partnerEchartLineStack(List<String> platforms,List<String> custNos,List<String> partnerNos,List<String> fundNos,List<String> products,DData dData){
        HashMap<String, Object> returnMap = new HashMap<>();

        //得到所有合作方名字
        String dictType = "partner_no";
        List<Map<String, Object>> dictData = eChartsMapper.getDictData(dictType);
        Map<String, Object> dictMap = this.dictListToMap(dictData);



        List<Object> returnList = new ArrayList<>();
        ArrayList<String> legendList = new ArrayList<>();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date parse = new Date();
        //当前日期
        String format = simpleDateFormat.format(parse);
        //获取12个月之前的时间
        String beginTime = DateNumerationUtils.getMonthBefore(format, 12);
        //X轴 时间
        List<String> xixas = DateNumerationUtils.getBetweenDate(beginTime,format);
        //查询排名
        List<Map<String, Object>> partnerList =  dDataMapper.getPartnerStack(platforms, custNos, partnerNos, fundNos,products,beginTime,format,dData);

        //根据排名组装数据List
        if(partnerList.size()>0){
            List<Map<String, Object>> partnerLegend =  dDataMapper.getPartnerStackdes(platforms, custNos, partnerNos, fundNos,products,beginTime,format,dData);
            for (Map<String, Object> map : partnerLegend) {
                String partnerNo = map.get("partnerNo").toString();
                Object partnerName = dictMap.get(partnerNo);
                System.out.println("partnerNo:"+partnerNo+" partnerName:"+partnerName);
                legendList.add(partnerName.toString());
            }

        for (Map<String, Object> map : partnerList) {
            String partnerNo = map.get("partnerNo").toString();
            Object partnerName = dictMap.get(partnerNo);
            //循环排名 根据排名调用查询数据，
              List<Map<String,Object>> partnerStackData = dDataMapper.getPartnerStackData(partnerNo,beginTime,format,dData);

                Map<String, Object> dataMap = this.partnerStackData(partnerStackData, xixas,partnerName.toString(),true);

                returnList.add(dataMap);
            }
        }
        returnMap.put("stackData",returnList);
        returnMap.put("stackXaxis",xixas);
        returnMap.put("legendList",legendList);
       return returnMap;

    }


    public Map<String,Object> partnerEchartLineStackLimit(List<String> platforms,List<String> custNos,List<String> partnerNos,List<String> fundNos,DData dData){
        BigDecimal tenThousand = new BigDecimal("10000");
        HashMap<String, Object> returnMap = new HashMap<>();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date parse = new Date();
        long l = parse.getTime() - 86400000l;
        parse = new Date(l);
        //当前日期
        String format = simpleDateFormat.format(parse);
        //找近90天的日期（字符串）
        String beginTime = DateNumerationUtils.getMonthBefore(format, 3);
        List<HomePageDataSystemVo> dayList = dDataMapper.getPartnerStackGroupByDay(platforms, custNos, partnerNos, fundNos, beginTime, format, dData);
        //X轴 时间
        List<String> dayXixasData = DateNumerationUtils.getBetweenDate(beginTime,format);
        //进行echarts组装
        List<Map<String, Object>> dayMapList = this.partnerStackDatashouye1(dayList, dayXixasData, tenThousand,true, true, format, dData);
        //找近30周的数据
        //X轴的时间
        List<String> queryWeekDataList = this.getWeekendXaxisData(30);
        //再加入昨天的时间
        List<HomePageDataSystemVo> weekList = dDataMapper.getPartnerStackAndDateStringList(platforms, custNos, partnerNos, fundNos, dData, queryWeekDataList);
        List<Map<String, Object>> weekMapList = this.partnerStackDatashouye1(weekList, queryWeekDataList, tenThousand,true, false, format, dData);
        //找近24个月的数据
        List<String> queryMonthDataList = this.getMonthendXaxisData(24);
        List<HomePageDataSystemVo> monthList = dDataMapper.getPartnerStackAndDateStringList(platforms, custNos, partnerNos, fundNos, dData, queryMonthDataList);
        List<Map<String, Object>> monthMapList = this.partnerStackDatashouye1(monthList, queryMonthDataList, tenThousand,true, false, format, dData);
        //找近5年的数据
        List<String> queryYearDataList = this.getYearendXaxisData(5);
        List<HomePageDataSystemVo> yearList = dDataMapper.getPartnerStackAndDateStringList(platforms, custNos, partnerNos, fundNos, dData, queryYearDataList);
        List<Map<String, Object>> yearMapList = this.partnerStackDatashouye1(yearList, queryYearDataList, tenThousand,true, false, format, dData);
        returnMap.put("partnerStackData", dayMapList);
        returnMap.put("partnerStackXaxis", dayXixasData);
        returnMap.put("weekRepayAmountData", weekMapList);
        returnMap.put("weekRepayAmountXaxis", queryWeekDataList);
        returnMap.put("monthRepayAmountData", monthMapList);
        returnMap.put("monthRepayAmountXaxis", queryMonthDataList);
        returnMap.put("yearRepayAmountData", yearMapList);
        returnMap.put("yearRepayAmountXaxis", queryYearDataList);
        return returnMap;

    }

    /**
     * 处理合作方堆叠数据 返回echart  series中的对象
     *
     * @param partnerStackData 合作伙伴堆栈数据
     * @param xixas            xixas
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    private List<Map<String, Object>> partnerStackDatashouye1(List<HomePageDataSystemVo> dataList, List<String> xixas, BigDecimal tenThousand, boolean tenThousandFlag, boolean dayFlag, String endTime, DData dData) {
        List<Map<String, Object>> returnList = new ArrayList<>();
        Map<String, Object> emphasis = new HashMap<>();
        emphasis.put("focus","series");
        //先按照产品维度分组
        List<HomePageDataSystemVo> recentlyDataList = null;
        //集合里的最后一天
        String lastDay = xixas.get(xixas.size() - 1);
        if (!dayFlag && !lastDay.equals(endTime)) {
            List<String> thirdPartyNo = dataList.stream().map(HomePageDataSystemVo::getThirdPartyNo).distinct().collect(Collectors.toList());
            //dData参数用于过滤数据范围，必须要有
            if (thirdPartyNo.size() > 0) {
                recentlyDataList = dDataMapper.getPartnerStackRecentlyByPartnerNo(thirdPartyNo, dData);
            } else {
                recentlyDataList = new ArrayList<>();
            }
        }
        Map<String, List<HomePageDataSystemVo>> collect = dataList.stream().collect(Collectors.groupingBy(HomePageDataSystemVo::getThirdPartyNo));
        List<HomePageDataSystemVo> finalRecentlyDataList = recentlyDataList;
        collect.forEach((thirdPartyNo, homePageDataSystemVoList) -> {
            Map<String, Object> returnMap = new HashMap<>();
            List<BigDecimal> amountDatas = new ArrayList<>();
            //类型
            returnMap.put("type", "line");
            //鼠标放置后淡化其他区域
            returnMap.put("emphasis", emphasis);
            //得到产品代码，然后去获取产品的名字
            String thirdPartyName = homePageDataSystemVoList.get(0).getThirdPartyName();
            //产品分完组以后，进行日期的对比，处理数据
            for (String xixa : xixas) {
                //去找数据集合种的这个日期的数据，找到了进行原数据组装，没找到用0替代
                HomePageDataSystemVo homePageDataSystemVo = homePageDataSystemVoList.stream().filter(t -> xixa.equals(t.getReconDateString())).findFirst().orElse(null);
                if (homePageDataSystemVo != null){
                    //找到产品了
                    if (tenThousandFlag) {
                        //说明要处理的数据是以万元为单位的
                        amountDatas.add(new BigDecimal(homePageDataSystemVo.getAmount().toString()).divide(tenThousand, 2, BigDecimal.ROUND_HALF_UP));
                    } else {
                        amountDatas.add(homePageDataSystemVo.getAmount());
                    }
                } else {
                    amountDatas.add(BigDecimal.ZERO);
                }
            }
            if (!dayFlag && !lastDay.equals(endTime)) {
                //找最近有数据的一天
                //说明是按照周、月、年来进行操作的，进行最后的一个数据替换
                //获得昨天的时间 对应的 金额，因为昨天是最后加入到xixas集合里的
                amountDatas.remove(amountDatas.size() - 1);
                HomePageDataSystemVo homePageDataSystemVo = finalRecentlyDataList.stream().filter(t -> t.getThirdPartyNo().equals(thirdPartyNo)).findFirst().orElse(null);
                //找到产品了
                if (tenThousandFlag) {
                    //说明要处理的数据是以万元为单位的
                    amountDatas.add(new BigDecimal(homePageDataSystemVo.getAmount().toString()).divide(tenThousand, 2, BigDecimal.ROUND_HALF_UP));
                } else {
                    amountDatas.add(homePageDataSystemVo.getAmount());
                }
            }
            //名字
            returnMap.put("name", thirdPartyName);
            //数据
            returnMap.put("data", amountDatas);
            returnList.add(returnMap);
        });
        return returnList;
    }

    private List<Map<String, Object>> fundStackDatashouye1(List<HomePageDataSystemVo> dataList, List<String> xixas, BigDecimal tenThousand, boolean tenThousandFlag, boolean dayFlag, String endTime, DData dData) {
        List<Map<String, Object>> returnList = new ArrayList<>();
        Map<String, Object> emphasis = new HashMap<>();
        emphasis.put("focus","series");
        //先按照产品维度分组
        List<HomePageDataSystemVo> recentlyDataList = null;
        //集合里的最后一天
        String lastDay = xixas.get(xixas.size() - 1);
        if (!dayFlag && !lastDay.equals(endTime)) {
            List<String> thirdPartyNo = dataList.stream().map(HomePageDataSystemVo::getThirdPartyNo).distinct().collect(Collectors.toList());
            //dData参数用于过滤数据范围，必须要有
            if (thirdPartyNo.size() > 0) {
                recentlyDataList = dDataMapper.getFundStackRecentlyByPartnerNo(thirdPartyNo, dData);
            } else {
                recentlyDataList = new ArrayList<>();
            }
        }
        Map<String, List<HomePageDataSystemVo>> collect = dataList.stream().collect(Collectors.groupingBy(HomePageDataSystemVo::getThirdPartyNo));
        List<HomePageDataSystemVo> finalRecentlyDataList = recentlyDataList;
        collect.forEach((thirdPartyNo, homePageDataSystemVoList) -> {
            Map<String, Object> returnMap = new HashMap<>();
            List<BigDecimal> amountDatas = new ArrayList<>();
            //类型
            returnMap.put("type", "line");
            //鼠标放置后淡化其他区域
            returnMap.put("emphasis", emphasis);
            //得到产品代码，然后去获取产品的名字
            String thirdPartyName = homePageDataSystemVoList.get(0).getThirdPartyName();
            //产品分完组以后，进行日期的对比，处理数据
            for (String xixa : xixas) {
                //去找数据集合种的这个日期的数据，找到了进行原数据组装，没找到用0替代
                HomePageDataSystemVo homePageDataSystemVo = homePageDataSystemVoList.stream().filter(t -> xixa.equals(t.getReconDateString())).findFirst().orElse(null);
                if (homePageDataSystemVo != null){
                    //找到产品了
                    if (tenThousandFlag) {
                        //说明要处理的数据是以万元为单位的
                        amountDatas.add(new BigDecimal(homePageDataSystemVo.getAmount().toString()).divide(tenThousand, 2, BigDecimal.ROUND_HALF_UP));
                    } else {
                        amountDatas.add(homePageDataSystemVo.getAmount());
                    }
                } else {
                    amountDatas.add(BigDecimal.ZERO);
                }
            }
            if (!dayFlag && !lastDay.equals(endTime)) {
                //找最近有数据的一天
                //说明是按照周、月、年来进行操作的，进行最后的一个数据替换
                //获得昨天的时间 对应的 金额，因为昨天是最后加入到xixas集合里的
                amountDatas.remove(amountDatas.size() - 1);
                HomePageDataSystemVo homePageDataSystemVo = finalRecentlyDataList.stream().filter(t -> t.getThirdPartyNo().equals(thirdPartyNo)).findFirst().orElse(null);
                //找到产品了
                if (tenThousandFlag) {
                    //说明要处理的数据是以万元为单位的
                    amountDatas.add(new BigDecimal(homePageDataSystemVo.getAmount().toString()).divide(tenThousand, 2, BigDecimal.ROUND_HALF_UP));
                } else {
                    amountDatas.add(homePageDataSystemVo.getAmount());
                }
            }
            //名字
            returnMap.put("name", thirdPartyName);
            //数据
            returnMap.put("data", amountDatas);
            returnList.add(returnMap);
        });
        return returnList;
    }









    /**
     * 处理合作方堆叠数据 返回echart  series中的对象
     *
     * @param partnerStackData 合作伙伴堆栈数据
     * @param xixas            xixas
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    public Map<String,Object> partnerStackData(List<Map<String,Object>> partnerStackData, List<String> xixas,String partnerNo,boolean b){
        HashMap<String, Object> returnMap = new HashMap<>();

        //数据转换为map以日期做key
        Map<String, Object> dataMap = this.partnerDataToMap(partnerStackData);

        List<BigDecimal> datas = new ArrayList<>();
        for (String xixa : xixas) {
            if(dataMap.containsKey(xixa)){
                if(b){
                    BigDecimal bigDecimal = new BigDecimal(dataMap.get(xixa).toString());
                    BigDecimal addAmountDivide = bigDecimal.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP);
                    datas.add(addAmountDivide);
                }else {
                    datas.add(new BigDecimal(dataMap.get(xixa).toString()));
                }

            }else {
                datas.add(BigDecimal.ZERO);
            }
        }
        HashMap<String, Object> emphasis = new HashMap<>();
        emphasis.put("focus","series");
        //名字
        returnMap.put("name",partnerNo);
        //类型
        returnMap.put("type","line");
        //堆叠标记
        //returnMap.put("stack","Total");
        //区域样式
        //returnMap.put("areaStyle","");
        //鼠标放置后淡化其他区域
        returnMap.put("emphasis",emphasis);
        //取消圆点
  /*      returnMap.put("symbol","none");*/
        //数据
        returnMap.put("data",datas);
        return returnMap;
    }

    public  String getDayPrevious(String time) {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Date parse = null;
        try {
            parse = df.parse(time);
        } catch (ParseException e) {
            System.out.println("时间转换异常");
            throw new RuntimeException(e);
        }
        // 获取当前时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(parse);
        // 将时间减去一天
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        // 获取前一天的年、月、日
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1; // 月份从0开始，所以要加1
        int day = calendar.get(Calendar.DAY_OF_MONTH);
       String months = null;
        // 输出前一天的时间
        if (month<10){
              months=  "0"+month;
        }else {
            months=""+ month;
        }
     /*   System.out.println("前一天的时间为：" + year + "-" + months + "-" + day);*/
        return  year + "-" + months + "-" + day;
    }



        /**
         * 日期做key 数据做value
         *
         * @param partnerStackData 合作伙伴堆栈数据
         * @return {@link Map}<{@link String}, {@link Object}>
         */
    public Map<String,Object> partnerDataToMap(List<Map<String,Object>> partnerStackData){
        HashMap<String, Object> returnMap = new HashMap<>();
        for (Map<String, Object> partnerStackDatum : partnerStackData) {
            returnMap.put(partnerStackDatum.get("reconDate").toString(),partnerStackDatum.get("fundAmt"));
        }
        return returnMap;
    }
    public Map<String,Object> partnerDataToMaps(List<Map<String,Object>> partnerStackData){
        HashMap<String, Object> returnMap = new HashMap<>();
        for (Map<String, Object> partnerStackDatum : partnerStackData) {
            returnMap.put(partnerStackDatum.get("reconDates").toString(),partnerStackDatum.get("fundAmt"));
        }
        return returnMap;
    }
    /**
     * 字典编码做key name做value
     *
     * @param dictData dict类型数据
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    public Map<String,Object> dictListToMap(List<Map<String, Object>> dictData){
        HashMap<String, Object> returnMap = new HashMap<>();
        for (Map<String, Object> dictDatum : dictData) {
            returnMap.put(dictDatum.get("dictValue").toString(),dictDatum.get("dictName"));
        }
        return returnMap;
    }


    @Override
    @DataScope()
    public Map<String, Object> fundEchartData(DData dData) {

        //参数
        List<String> platforms = null;
        //        if (Strings.isNotEmpty(dData.getPlatformNo())) {
//            platforms = Arrays.asList(dData.getPlatformNo().split(","));
//        }
        List<String> custNos = null;
//        if (Strings.isNotEmpty(dData.getCustNo())) {
//            custNos = Arrays.asList(dData.getCustNo().split(","));
//        }
        List<String> partnerNos = null;
//        if (Strings.isNotEmpty(dData.getPartnerNo())) {
//
//            partnerNos = Arrays.asList(dData.getPartnerNo().split(","));
//        }
        List<String> fundNos = null;
//        if (Strings.isNotEmpty(dData.getFundNo())) {
//
//            fundNos = Arrays.asList(dData.getFundNo().split(","));
//        }
        List<String> products = null;
        if (Strings.isNotEmpty(dData.getProductNo())) {
            products = Arrays.asList(dData.getProductNo().split(","));
        }
        //权限sql语句字段替换
//        HashMap<String, Object> roleSql = new HashMap<>();
//        roleSql.put("dataScope",this.roleStringCost(dData.getParams().get("dataScope").toString()));
//        dData.setParams(roleSql);
        HashMap<String, Object> returnMap = new HashMap<>();
        //echart柱状图表数据
//        if (dData.getMoreSearch() != null && dData.getMoreSearch().length() > 2){
//            Gson gson = new Gson();
//            // 将字符串转换为 Map<String, List<Long>>
//            Map<String, List<String>> moreSearch = gson.fromJson(dData.getMoreSearch(), Map.class);
//            dData.setMoreSearchMap(moreSearch);
//        }
        List<Map<String, Object>> partnerEchartData = eChartsMapper.getFundEchartData(platforms, custNos, partnerNos, fundNos,products,dData);
        //所有和
        Map<String, Object> allPartnerSum = eChartsMapper.getAllFundSum(platforms, custNos, partnerNos, fundNos, products,dData);
        //得到所有合作方名字
        String dictType = "fund_no";
        List<Map<String, Object>> dictData = eChartsMapper.getDictData(dictType);
        Map<String, Object> stackMap = this.fundEchartLineStack(platforms, custNos, partnerNos, fundNos,products, dData);
        if(CollectionUtils.isNotEmpty(partnerEchartData) && null != allPartnerSum.get("count")){
            //数据转换
            Map<String, Object> map = this.listToMap(partnerEchartData);
            List<Map<String, Object>> dictAndData = this.dictAddData(map,dictData);
            // 降序
            List<Map<String, Object>> sortedByHeightDescList = dictAndData.stream().sorted((h1, h2) -> (new BigDecimal(h2.get("count").toString())).compareTo(new BigDecimal(h1.get("count").toString()))).collect(Collectors.toList());

            //数据分割
            Map<String, List> countData = this.getXixasData(sortedByHeightDescList,allPartnerSum.get("count").toString());
            //柱状图数据
            HashMap<String, Object> pillarData = new HashMap<>();
            pillarData.put("type","bar");
            pillarData.put("data",countData.get("data"));

            //柱状图数据
            returnMap.put("pillarEchart",pillarData);
            //X轴数据
            returnMap.put("xaxis",countData.get("xaxis"));
            //饼图数据
            returnMap.put("pieData",countData.get("pie"));
            returnMap.put("fundTableData",countData.get("fundTableData"));
            returnMap.putAll(stackMap);
            return returnMap;
        }
        return returnMap;
    }



    public Map<String,Object> fundEchartLineStack(List<String> platforms,List<String> custNos,List<String> partnerNos,List<String> fundNos,List<String> products,DData dData){
        HashMap<String, Object> returnMap = new HashMap<>();

        //得到所有合作方名字
        String dictType = "fund_no";
        List<Map<String, Object>> dictData = eChartsMapper.getDictData(dictType);
        Map<String, Object> dictMap = this.dictListToMap(dictData);



        List<Object> returnList = new ArrayList<>();
        ArrayList<String> legendList = new ArrayList<>();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date parse = new Date();
        //当前日期
        String format = simpleDateFormat.format(parse);
        //获取12个月之前的时间
        String beginTime = DateNumerationUtils.getMonthBefore(format, 12);
        //X轴 时间
        List<String> xixas = DateNumerationUtils.getBetweenDate(beginTime,format);
        //查询排名
        List<Map<String, Object>> partnerList =  dDataMapper.getFundStack(platforms, custNos, partnerNos, fundNos,products,beginTime,format,dData);

        //根据排名组装数据List
        if(partnerList.size()>0){
            List<Map<String, Object>> partnerLegend =  dDataMapper.getFundStackdes(platforms, custNos, partnerNos, fundNos,products,beginTime,format,dData);
            for (Map<String, Object> map : partnerLegend) {
                String partnerNo = map.get("fundNo").toString();
                Object partnerName = dictMap.get(partnerNo);
                legendList.add(partnerName.toString());
            }

            for (Map<String, Object> map : partnerList) {
                String fundNo = map.get("fundNo").toString();
                Object fundName = dictMap.get(fundNo);
                //循环排名 根据排名调用查询数据，
                List<Map<String,Object>> fundStackData = dDataMapper.getFundStackData(fundNo,beginTime,format,dData);

                Map<String, Object> dataMap = this.partnerStackData(fundStackData, xixas,fundName.toString(),true);

                returnList.add(dataMap);
            }
        }
        returnMap.put("stackData",returnList);
        returnMap.put("stackXaxis",xixas);
        returnMap.put("legendList",legendList);
        return returnMap;

    }

    /**
     * 资金方堆叠
     *
     * @param platforms  平台
     * @param custNos    cust号
     * @param partnerNos 伴侣号
     * @param fundNos    基金号
     * @param dData      维数据
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    public Map<String,Object> fundEchartLineStackLimit(List<String> platforms,List<String> custNos,List<String> partnerNos,List<String> fundNos,DData dData ){
        BigDecimal tenThousand = new BigDecimal("10000");
        HashMap<String, Object> returnMap = new HashMap<>();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date parse = new Date();
        long l = parse.getTime() - 86400000l;
        parse = new Date(l);
        //当前日期
        String format = simpleDateFormat.format(parse);
        //找近90天的日期（字符串）
        String beginTime = DateNumerationUtils.getMonthBefore(format, 3);
        List<HomePageDataSystemVo> dayList = dDataMapper.getFundStackGroupByDay(platforms, custNos, partnerNos, fundNos, beginTime, format, dData);
        //X轴 时间
        List<String> dayXixasData = DateNumerationUtils.getBetweenDate(beginTime,format);
        //进行echarts组装
        List<Map<String, Object>> dayMapList = this.fundStackDatashouye1(dayList, dayXixasData, tenThousand,true, true, format, dData);
        //找近30周的数据
        //X轴的时间
        List<String> queryWeekDataList = this.getWeekendXaxisData(30);
        List<HomePageDataSystemVo> weekList = dDataMapper.getFundStackAndDateStringList(platforms, custNos, partnerNos, fundNos, dData, queryWeekDataList);
        List<Map<String, Object>> weekMapList = this.fundStackDatashouye1(weekList, queryWeekDataList, tenThousand,true, false, format, dData);
        //找近24个月的数据
        List<String> queryMonthDataList = this.getMonthendXaxisData(24);
        List<HomePageDataSystemVo> monthList = dDataMapper.getFundStackAndDateStringList(platforms, custNos, partnerNos, fundNos, dData, queryMonthDataList);
        List<Map<String, Object>> monthMapList = this.fundStackDatashouye1(monthList, queryMonthDataList, tenThousand,true, false, format, dData);
        //找近5年的数据
        List<String> queryYearDataList = this.getYearendXaxisData(5);
        List<HomePageDataSystemVo> yearList = dDataMapper.getFundStackAndDateStringList(platforms, custNos, partnerNos, fundNos, dData, queryYearDataList);
        List<Map<String, Object>> yearMapList = this.fundStackDatashouye1(yearList, queryYearDataList, tenThousand,true, false, format, dData);
        returnMap.put("fundStackData", dayMapList);
        returnMap.put("fundStackXaxis", dayXixasData);
        returnMap.put("weekfundAmountData", weekMapList);
        returnMap.put("weekfundAmountXaxis", queryWeekDataList);
        returnMap.put("monthfundAmountData", monthMapList);
        returnMap.put("monthfundAmountXaxis", queryMonthDataList);
        returnMap.put("yearfundAmountData", yearMapList);
        returnMap.put("yearfundAmountXaxis", queryYearDataList);
        return returnMap;

    }

    public String year1( ) {
        // 获取当前时间
        Calendar calendar = Calendar.getInstance();

        // 将月份和日期设置为1，代表1月1日
        calendar.set(Calendar.MONTH, 0); // 月份从0开始，所以0代表1月
        calendar.set(Calendar.DAY_OF_MONTH, 1); // 设置日期为1

        // 获取当年的年份
        int year = calendar.get(Calendar.YEAR);

        // 输出前一年的第一天
        year = year - 1;
        return year + "-01-01";
    }
    /**
     * 坏账率数据
     *
     * @param dProfitData d利润数据
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    @Override
    @DataScope()
    public Map<String, Object> badDebtRateData(DProfitData dProfitData) {
        HashMap<String, Object> returnMap = new HashMap<>();
        //参数
        //系统
        List<String> platformNo = null;
//        if (Strings.isNotEmpty(dVintageMonth.getPlatformNo())) {
//            platforms = Arrays.asList(dVintageMonth.getPlatformNo().split(","));
//        }
        List<String> custNo = null;
//        if (Strings.isNotEmpty(dVintageMonth.getCustNo())) {
//            custNos = Arrays.asList(dVintageMonth.getCustNo().split(","));
//        }
        List<String> partnerNo = null;
//        if (Strings.isNotEmpty(dVintageMonth.getPartnerNo())) {
//
//            partnerNos = Arrays.asList(dVintageMonth.getPartnerNo().split(","));
//        }
        List<String> fundNo = null;

//        if (Strings.isNotEmpty(dVintageMonth.getFundNo())) {
//            fundNos = Arrays.asList(dVintageMonth.getFundNo().split(","));
//        }
        List<String> products = null;
        if (Strings.isNotEmpty(dProfitData.getProductNo())) {
            products = Arrays.asList(dProfitData.getProductNo().split(","));
        }

        String reconYear = dProfitData.getReconYear();
        //拆分日期 年月拆开，月去寻找所对应的字段
        String tworeconYear =  reconYear.substring(0,4);
        String tworeconMonth = reconYear.substring(5,7);
        Map<String, Object> month = this.getMonth();
        Object o = month.get(tworeconMonth);
        ArrayList<String> xaxisData = new ArrayList<>();
        ArrayList<String> dateData = new ArrayList<>();
//        if (dProfitData.getMoreSearch() != null && dProfitData.getMoreSearch().length() > 2){
//            Gson gson = new Gson();
//            // 将字符串转换为 Map<String, List<Long>>
//            Map<String, List<String>> moreSearch = gson.fromJson(dProfitData.getMoreSearch(), Map.class);
//            dProfitData.setMoreSearchMap(moreSearch);
//        }
        List<Map<String,Object>> profitDatas =  eChartsMapper.profitData(platformNo,custNo,partnerNo,fundNo,products,tworeconYear,o.toString(),dProfitData);

        List<DProfitData> hzlList =new ArrayList<>();
        DProfitData hzl;
        //得到所有的编码Map
        Map<String, Object> dictMap = this.getDictMap();
                for (Map<String, Object> profitData : profitDatas) {
                	hzl=new DProfitData();
                	hzl.setPartnerNo(profitData.get("partnerNo")+"");
                	hzl.setFundNo(profitData.get("fundNo")+"");
                	hzl.setPlatformNo(dictMap.get("partner_no_"+profitData.get("partnerNo")) +"+"+  dictMap.get("fund_no_"+profitData.get("fundNo")));
//                    xaxisData.add(dictMap.get("partner_no_"+profitData.get("partnerNo")) +"+"+  dictMap.get("fund_no_"+profitData.get("fundNo"))   );
                    if(null == profitData.get("dataa")){
                    	hzl.setDataJanuary(new BigDecimal(0));
//                        dateData.add("0.00");
//                        profitData.put("dataa","0.00");
                    }else {
                    	BigDecimal fundAmt=eChartsMapper.profitDataFundBalance(profitData.get("partnerNo")+"",profitData.get("fundNo")+"",com.ruoyi.common.utils.DateUtils.findLastMonthEndDayByStr(tworeconYear+"-"+tworeconMonth+"-01"),dProfitData);
                    	BigDecimal huaizhangAmt= new BigDecimal(profitData.get("dataa").toString());
                    	if(fundAmt !=null && fundAmt.compareTo(new BigDecimal(0))>0 && huaizhangAmt !=null && huaizhangAmt.compareTo(new BigDecimal(0))>0) {
//                    		dateData.add(huaizhangAmt.multiply(new BigDecimal("100")).divide(fundAmt, 2, BigDecimal.ROUND_HALF_UP)+"");
//                            profitData.put("dataa",huaizhangAmt.multiply(new BigDecimal("100")).divide(fundAmt, 2, BigDecimal.ROUND_HALF_UP)+"");
                        	hzl.setDataJanuary(huaizhangAmt.multiply(new BigDecimal("100")).multiply(new BigDecimal("12")).divide(fundAmt, 2, BigDecimal.ROUND_HALF_UP));
                    	}else {
                        	hzl.setDataJanuary(new BigDecimal(0));
//                    		dateData.add("0.00");
//                            profitData.put("dataa","0.00");
                    	}
//                        dateData.add(profitData.get("dataa").toString());
                    }
                    hzlList.add(hzl);
                }

                hzlList.sort(Comparator.comparing(DProfitData::getDataJanuary).reversed());

                List<Map<String,Object>> profitDatas2 =new ArrayList<Map<String,Object>>();
                Map<String,Object> map1 = new HashMap<String, Object>();
                for (DProfitData dProfitData2 : hzlList) {
                	xaxisData.add(dProfitData2.getPlatformNo());
                	dateData.add(dProfitData2.getDataJanuary().setScale(2, BigDecimal.ROUND_HALF_UP)+"");
                	map1 = new HashMap<String, Object>();
                	map1.put("partnerNo", dProfitData2.getPartnerNo());
                	map1.put("fundNo", dProfitData2.getFundNo());
                	map1.put("dataa", dProfitData2.getDataJanuary().setScale(2, BigDecimal.ROUND_HALF_UP)+"");
                	profitDatas2.add(map1);
				}


        Collections.reverse(dateData);
        Collections.reverse(xaxisData);
        returnMap.put("xaxisData",xaxisData);
        returnMap.put("dataList",dateData);
        returnMap.put("tableDataList",profitDatas2);
        return returnMap;
    }


    @Override
    public String getNearDataDate(){
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy");
        Date parse = new Date();
        String format = simpleDateFormat.format(parse);
        String returnString = "";
        //查询有数据的最近一年的第一条数据，然后判断月份字段是否为null如果为null就证明没计算过当前字段对应月的数据如果为0.00或者不为null的数据
        DProfitData profitData =  eChartsMapper.getprofitNearDate();
        if(null != profitData) {
            //先取出年份
            //returnString = returnString + profitData.getReconYear();
            //1月
            if (null != profitData.getDataJanuary()) {
                returnString = profitData.getReconYear() + "-01";
            }
            //2
            if (null != profitData.getDataFebruary()) {
                returnString = profitData.getReconYear() + "-02";
            }
            //3
            if (null != profitData.getDataMarch()) {
                returnString = profitData.getReconYear() + "-03";
            }
            //4
            if (null != profitData.getDataApril()) {
                returnString = profitData.getReconYear() + "-04";
            }
            //5
            if (null != profitData.getDataMay()) {
                returnString = profitData.getReconYear() + "-05";
            }
            //6
            if (null != profitData.getDataJune()) {
                returnString = profitData.getReconYear() + "-06";
            }
            //7
            if (null != profitData.getDataJuly()) {
                returnString = profitData.getReconYear() + "-07";
            }
            //8
            if (null != profitData.getDataAugust()) {
                returnString = profitData.getReconYear() + "-08";
            }
            //9
            if (null != profitData.getDataSeptember()) {
                returnString = profitData.getReconYear() + "-09";
            }
            //10
            if (null != profitData.getDataOctober()) {
                returnString = profitData.getReconYear() + "-10";
            }
            //11
            if (null != profitData.getDataNovember()) {
                returnString = profitData.getReconYear() + "-11";
            }
            //12
            if (null != profitData.getDataDecember()) {
                returnString = profitData.getReconYear() + "-12";
            }
        }else {
         returnString =   format+"-01";
        }
        return returnString;


    }

    @Override
    @DataScope()
    public Map<String, Object> custEchartData(DData dData) {
        //参数
        List<String> platforms = null;
        //        if (Strings.isNotEmpty(dData.getPlatformNo())) {
//            platforms = Arrays.asList(dData.getPlatformNo().split(","));
//        }
        List<String> custNos = null;
//        if (Strings.isNotEmpty(dData.getCustNo())) {
//            custNos = Arrays.asList(dData.getCustNo().split(","));
//        }
        List<String> partnerNos = null;
//        if (Strings.isNotEmpty(dData.getPartnerNo())) {
//
//            partnerNos = Arrays.asList(dData.getPartnerNo().split(","));
//        }
        List<String> fundNos = null;
//        if (Strings.isNotEmpty(dData.getFundNo())) {
//
//            fundNos = Arrays.asList(dData.getFundNo().split(","));
//        }
        List<String> products = null;
        if (Strings.isNotEmpty(dData.getProductNo())) {
            products = Arrays.asList(dData.getProductNo().split(","));
        }
        //权限sql语句字段替换
//        HashMap<String, Object> roleSql = new HashMap<>();
//        roleSql.put("dataScope",this.roleStringCost(dData.getParams().get("dataScope").toString()));
//        dData.setParams(roleSql);
        HashMap<String, Object> returnMap = new HashMap<>();
//        if (dData.getMoreSearch() != null && dData.getMoreSearch().length() > 2){
//            Gson gson = new Gson();
//            // 将字符串转换为 Map<String, List<Long>>
//            Map<String, List<String>> moreSearch = gson.fromJson(dData.getMoreSearch(), Map.class);
//            dData.setMoreSearchMap(moreSearch);
//        }
        //echart柱状图表数据
        List<Map<String, Object>> partnerEchartData = eChartsMapper.getCustEchartData(platforms, custNos, partnerNos, fundNos,products,dData);
        //所有和
        Map<String, Object> allPartnerSum = eChartsMapper.getAllCustSum(platforms, custNos, partnerNos, fundNos,products, dData);
        //得到所有合作方名字
        String dictType = "cust_no";
        List<Map<String, Object>> dictData = eChartsMapper.getDictData(dictType);

        if(CollectionUtils.isNotEmpty(partnerEchartData) && null != allPartnerSum.get("count")){
            //数据转换
            Map<String, Object> map = this.listToMap(partnerEchartData);
            List<Map<String, Object>> dictAndData = this.dictAddData(map,dictData);
            // 降序
            List<Map<String, Object>> sortedByHeightDescList = dictAndData.stream().sorted((h1, h2) -> (new BigDecimal(h2.get("count").toString())).compareTo(new BigDecimal(h1.get("count").toString()))).collect(Collectors.toList());

            //数据分割
            Map<String, List> countData = this.getXixasData(sortedByHeightDescList,allPartnerSum.get("count").toString());
            //柱状图数据
            HashMap<String, Object> pillarData = new HashMap<>();
            pillarData.put("type","bar");
            pillarData.put("data",countData.get("data"));

            //柱状图数据
            returnMap.put("pillarEchart",pillarData);
            //X轴数据
            returnMap.put("xaxis",countData.get("xaxis"));
            //饼图数据
            returnMap.put("pieData",countData.get("pie"));
            returnMap.put("custTableData",countData.get("fundTableData"));
            return returnMap;
        }
        return returnMap;
    }

    @Override
    public Map<String, Object> profitRank(ProfitRank profitRank) {
        //产品利润排名（合作方）集合
        String startYear = profitRank.getStartTime().substring(0, 4);
        String startMonth = profitRank.getStartTime().substring(5, 7);
        int startCompare = Integer.parseInt(startMonth);
        String endYear = profitRank.getEndTime().substring(0, 4);
        String endMonth = profitRank.getEndTime().substring(5, 7);
        int endCompare = Integer.parseInt(endMonth);
        List<String> reconYear = DateNumerationUtils.getYearBetweenDate(startYear, endYear);
        //获取X轴数据 也就是两个时间段的所有月份
//        List<String> xAxis = DateNumerationUtils.getMonthBetweenDate(startTime, endTime);
        //echart柱状图表数据
        //产品利润排名（合作方）
        List<Map<String, Object>> partnerEchartData = eChartsMapper.getProfitRankForPartner(reconYear);
        Map<String, Object> map = this.getProfitRankByPartner(partnerEchartData, startYear, startCompare, endYear, endCompare);
        //产品利润排名（合作方+资金方） //添加条件
        List<Map<String, Object>> partnerAndFundEchartData = eChartsMapper.getProfitRankForPartnerAndFund(reconYear,profitRank.getPlatforms(),profitRank.getPartnerNos(),profitRank.getFundNos(),profitRank.getCustNos());
        Map<String, Object> map1 = this.getProfitRankByPartnerAndFund(partnerAndFundEchartData, startYear, startCompare, endYear, endCompare);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("partnerEchartData", map);
        resultMap.put("partnerAndFundEchartData", map1);
        return resultMap;
    }

    @Override
    @DataScope()
    public List<Map<String, Object>> profitRankForIndex(DProfitData dProfitData ) {
        BigDecimal tenThousand = new BigDecimal("10000");
        //参数
        List<String> platforms = null;
//        if (Strings.isNotEmpty(dProfitData.getPlatformNo())) {
//            platforms = Arrays.asList(dProfitData.getPlatformNo().split(","));
//        }
        List<String> custNos = null;
//        if (Strings.isNotEmpty(dProfitData.getCustNo())) {
//            custNos = Arrays.asList(dProfitData.getCustNo().split(","));
//        }
        List<String> partnerNos = null;
//        if (Strings.isNotEmpty(dProfitData.getPartnerNo())) {
//
//            partnerNos = Arrays.asList(dProfitData.getPartnerNo().split(","));
//        }
        List<String> fundNos = null;
//        if (Strings.isNotEmpty(dProfitData.getFundNo())) {
//
//            fundNos = Arrays.asList(dProfitData.getFundNo().split(","));
//        }
        List<String> products = null;
        if (Strings.isNotEmpty(dProfitData.getProductNo())) {
            products = Arrays.asList(dProfitData.getProductNo().split(","));
        }


        List<Map<String, Object>> resultList = new ArrayList<>();
        DateTime dateTime = DateUtil.lastMonth();
        String endTime = DateUtil.format(dateTime, "yyyy-MM");
        String endYear = endTime.substring(0, 4);
        String startYear = String.valueOf(DateUtil.thisYear() - 1);
        String startMonth = endTime.substring(5, 7);
        String startTime = startYear + "-" +startMonth;
        int startCompare = Integer.parseInt(startMonth);
        int endCompare = Integer.parseInt(startMonth);
        List<String> reconYear = DateNumerationUtils.getYearBetweenDate(startTime, endYear);
        //产品利润排名（合作方+资金方）
        List<Map<String, Object>> partnerAndFundEchartData = eChartsMapper.getProfitRankForPartnerAndFundGroupByReconYear(reconYear,platforms,partnerNos,fundNos,custNos, dProfitData, products);
        List<Map<String, Object>> partnerAndFundEchartDataConvert = partnerAndFundEchartData.stream().map(a -> {
            a.put("01", a.remove("a01"));
            a.put("02", a.remove("a02"));
            a.put("03", a.remove("a03"));
            a.put("04", a.remove("a04"));
            a.put("05", a.remove("a05"));
            a.put("06", a.remove("a06"));
            a.put("07", a.remove("a07"));
            a.put("08", a.remove("a08"));
            a.put("09", a.remove("a09"));
            a.put("10", a.remove("a10"));
            a.put("11", a.remove("a11"));
            a.put("12", a.remove("a12"));
            return a;
        }).collect(Collectors.toList());
//        //获取字段表映射值，获取合作方和资金方
//        List<SysDictData> paramsDictData = dictDataService.getParamsDictData();
//        List<SysDictData> dictData = paramsDictData.stream().filter(t -> "partner_no".equals(t.getDictType()) || "fund_no".equals(t.getDictType())).collect(Collectors.toList());
        if (partnerAndFundEchartDataConvert.size() != 0) {
            List<Map<String, Object>> finalResultList = new ArrayList<>();
            //按照年的维度去划分
            Map<Object, List<Map<String, Object>>> partnerNoCollect = partnerAndFundEchartDataConvert.stream().collect(Collectors.groupingBy(t -> t.get("recon_year")));
            //然后去去除其他不符合逻辑的月份
            for (Map.Entry<Object, List<Map<String, Object>>> partnerNoList:partnerNoCollect.entrySet()) {
                String year = partnerNoList.getKey().toString();
                if (year.equals(startYear)) {
                    //小于开始月份的数据剔除
                    for (Map<String, Object> obj:partnerNoList.getValue()) {
                        Iterator<Map.Entry<String, Object>> iterator = obj.entrySet().iterator();
                        while (iterator.hasNext()) {
                            Map.Entry<String, Object> next = iterator.next();
                            if (!"recon_year".equals(next.getKey()) && !"statistical_index".equals(next.getKey()) && !"partner_no".equals(next.getKey())) {
                                int i = Integer.parseInt(next.getKey());
                                if (i < startCompare) {
                                    iterator.remove();
                                }
                            }
                        }
                    }
                } else if (year.equals(endYear)) {
                    //大于结束月份的数据剔除
                    for (Map<String, Object> obj:partnerNoList.getValue()) {
                        Iterator<Map.Entry<String, Object>> iterator = obj.entrySet().iterator();
                        while (iterator.hasNext()) {
                            Map.Entry<String, Object> next = iterator.next();
                            if (!"recon_year".equals(next.getKey()) && !"statistical_index".equals(next.getKey()) && !"partner_no".equals(next.getKey())) {
                                int i = Integer.parseInt(next.getKey());
                                if (i > endCompare) {
                                    iterator.remove();
                                }
                            }
                        }
                    }
                }
            }
            //然后进行数据的组装，先从 开始年份 开始组装
            List<Map<String, Object>> list = partnerNoCollect.get(startYear);
            if (CollectionUtils.isNotEmpty(list)) {
                //找 开始月份 往后一次拿数据
                Map<Object, List<Map<String, Object>>> partnerObjMap = list.stream().collect(Collectors.groupingBy(t -> t.get("partner_no")));
                for (Map.Entry<Object, List<Map<String, Object>>> partnerObjList : partnerObjMap.entrySet()) {
                    //存放具体对象的集合
                    List<Map<String, Object>> incomeList = new ArrayList<>();
                    List<Map<String, Object>> badDebtList = new ArrayList<>();
                    List<Map<String, Object>> accessFeeList = new ArrayList<>();
                    List<Map<String, Object>> flowRateList = new ArrayList<>();
                    List<Map<String, Object>> operatingCostList = new ArrayList<>();
                    for (Map<String, Object> obj : partnerObjList.getValue()) {
                        String statisticalIndex = (String) obj.get("statistical_index");
                        if ("1".equals(statisticalIndex)) {
                            //月收入
                            Iterator<Map.Entry<String, Object>> iterator = obj.entrySet().iterator();
                            while (iterator.hasNext()) {
                                Map.Entry<String, Object> next = iterator.next();
                                if (!"recon_year".equals(next.getKey()) && !"statistical_index".equals(next.getKey()) && !"partner_no".equals(next.getKey())) {
                                    Map<String, Object> m = new HashMap<>();
                                    m.put("name", obj.get("partner_no"));
                                    m.put("year", obj.get("recon_year"));
                                    m.put("time", obj.get("recon_year") + "-" + next.getKey());
                                    m.put("month", next.getKey());
                                    if (obj.get(next.getKey()) != null) {
//                                        m.put("money", obj.get(next.getKey()));
                                        m.put("money", new BigDecimal(obj.get(next.getKey()).toString()).divide(tenThousand, 2, BigDecimal.ROUND_HALF_UP));
                                    } else {
                                        m.put("money", BigDecimal.ZERO);
                                    }
                                    incomeList.add(m);
                                }
                            }
                        } else if ("2".equals(statisticalIndex)) {
                            //月坏账
                            Iterator<Map.Entry<String, Object>> iterator = obj.entrySet().iterator();
                            while (iterator.hasNext()) {
                                Map.Entry<String, Object> next = iterator.next();
                                if (!"recon_year".equals(next.getKey()) && !"statistical_index".equals(next.getKey()) && !"partner_no".equals(next.getKey())) {
                                    Map<String, Object> m = new HashMap<>();
                                    m.put("name", obj.get("partner_no"));
                                    m.put("year", obj.get("recon_year"));
                                    m.put("time", obj.get("recon_year") + "-" + next.getKey());
                                    m.put("month", next.getKey());
                                    if (obj.get(next.getKey()) != null) {
//                                        m.put("money", obj.get(next.getKey()));
                                        m.put("money", new BigDecimal(obj.get(next.getKey()).toString()).divide(tenThousand, 2, BigDecimal.ROUND_HALF_UP));
                                    } else {
                                        m.put("money", BigDecimal.ZERO);
                                    }
                                    badDebtList.add(m);
                                }
                            }
                        } else if ("4".equals(statisticalIndex)) {
                            //月通道费
                            Iterator<Map.Entry<String, Object>> iterator = obj.entrySet().iterator();
                            while (iterator.hasNext()) {
                                Map.Entry<String, Object> next = iterator.next();
                                if (!"recon_year".equals(next.getKey()) && !"statistical_index".equals(next.getKey()) && !"partner_no".equals(next.getKey())) {
                                    Map<String, Object> m = new HashMap<>();
                                    m.put("name", obj.get("partner_no"));
                                    m.put("year", obj.get("recon_year"));
                                    m.put("time", obj.get("recon_year") + "-" + next.getKey());
                                    m.put("month", next.getKey());
                                    if (obj.get(next.getKey()) != null) {
//                                        m.put("money", obj.get(next.getKey()));
                                        m.put("money", new BigDecimal(obj.get(next.getKey()).toString()).divide(tenThousand, 2, BigDecimal.ROUND_HALF_UP));
                                    } else {
                                        m.put("money", BigDecimal.ZERO);
                                    }
                                    accessFeeList.add(m);
                                }
                            }
                        } else if ("5".equals(statisticalIndex)) {
                            //月流量费
                            Iterator<Map.Entry<String, Object>> iterator = obj.entrySet().iterator();
                            while (iterator.hasNext()) {
                                Map.Entry<String, Object> next = iterator.next();
                                if (!"recon_year".equals(next.getKey()) && !"statistical_index".equals(next.getKey()) && !"partner_no".equals(next.getKey())) {
                                    Map<String, Object> m = new HashMap<>();
                                    m.put("name", obj.get("partner_no"));
                                    m.put("year", obj.get("recon_year"));
                                    m.put("time", obj.get("recon_year") + "-" + next.getKey());
                                    m.put("month", next.getKey());
                                    if (obj.get(next.getKey()) != null) {
//                                        m.put("money", obj.get(next.getKey()));
                                        m.put("money", new BigDecimal(obj.get(next.getKey()).toString()).divide(tenThousand, 2, BigDecimal.ROUND_HALF_UP));
                                    } else {
                                        m.put("money", BigDecimal.ZERO);
                                    }
                                    flowRateList.add(m);
                                }
                            }
                        } else if ("6".equals(statisticalIndex)) {
                            //月运营成本
                            Iterator<Map.Entry<String, Object>> iterator = obj.entrySet().iterator();
                            while (iterator.hasNext()) {
                                Map.Entry<String, Object> next = iterator.next();
                                if (!"recon_year".equals(next.getKey()) && !"statistical_index".equals(next.getKey()) && !"partner_no".equals(next.getKey())) {
                                    Map<String, Object> m = new HashMap<>();
                                    m.put("name", obj.get("partner_no"));
                                    m.put("year", obj.get("recon_year"));
                                    m.put("time", obj.get("recon_year") + "-" + next.getKey());
                                    m.put("month", next.getKey());
                                    if (obj.get(next.getKey()) != null) {
//                                        m.put("money", obj.get(next.getKey()));
                                        m.put("money", new BigDecimal(obj.get(next.getKey()).toString()).divide(tenThousand, 2, BigDecimal.ROUND_HALF_UP));
                                    } else {
                                        m.put("money", BigDecimal.ZERO);
                                    }
                                    operatingCostList.add(m);
                                }
                            }
                        }
                    }
                    //开始的数据整合完毕
                    //处理数据
                    for (Map<String, Object> a : incomeList) {
                        String name = (String) a.get("name");
                        String time = (String) a.get("time");
                        BigDecimal moneyIncome = (BigDecimal) a.get("money");
                        BigDecimal sumOther = BigDecimal.ZERO;
                        List<Map<String, Object>> collect1 = badDebtList.stream().filter(t -> name.equals(t.get("name")) && time.equals(t.get("time"))).collect(Collectors.toList());
                        List<Map<String, Object>> collect2 = accessFeeList.stream().filter(t -> name.equals(t.get("name")) && time.equals(t.get("time"))).collect(Collectors.toList());
                        List<Map<String, Object>> collect3 = flowRateList.stream().filter(t -> name.equals(t.get("name")) && time.equals(t.get("time"))).collect(Collectors.toList());
                        List<Map<String, Object>> collect4 = operatingCostList.stream().filter(t -> name.equals(t.get("name")) && time.equals(t.get("time"))).collect(Collectors.toList());
                        for (Map<String, Object> a1 : collect1) {
                            BigDecimal money = (BigDecimal) a1.get("money");
                            sumOther = sumOther.add(money);
                        }
                        for (Map<String, Object> a2 : collect2) {
                            BigDecimal money = (BigDecimal) a2.get("money");
                            sumOther = sumOther.add(money);
                        }
                        for (Map<String, Object> a3 : collect3) {
                            BigDecimal money = (BigDecimal) a3.get("money");
                            sumOther = sumOther.add(money);
                        }
                        for (Map<String, Object> a4 : collect4) {
                            BigDecimal money = (BigDecimal) a4.get("money");
                            sumOther = sumOther.add(money);
                        }
                        BigDecimal resultBigDecimal = moneyIncome.subtract(sumOther);
                        a.put("money", resultBigDecimal);
                        finalResultList.add(a);
                    }
                }
            }
            //然后 结束年份 组装
            List<Map<String, Object>> list1 = partnerNoCollect.get(endYear);
                if (CollectionUtils.isNotEmpty(list1)){
                Map<Object, List<Map<String, Object>>> partnerObjMap1 = list1.stream().collect(Collectors.groupingBy(t -> t.get("partner_no")));
                for (Map.Entry<Object, List<Map<String, Object>>> partnerObjList1:partnerObjMap1.entrySet()) {
                //存放具体对象的集合
                List<Map<String, Object>> incomeList = new ArrayList<>();
                List<Map<String, Object>> badDebtList = new ArrayList<>();
                List<Map<String, Object>> accessFeeList = new ArrayList<>();
                List<Map<String, Object>> flowRateList = new ArrayList<>();
                List<Map<String, Object>> operatingCostList = new ArrayList<>();
                for (Map<String, Object> obj:partnerObjList1.getValue()) {
                    String statisticalIndex= (String) obj.get("statistical_index");
                    if ("1".equals(statisticalIndex)) {
                        //月收入
                        Iterator<Map.Entry<String, Object>> iterator = obj.entrySet().iterator();
                        while (iterator.hasNext()) {
                            Map.Entry<String, Object> next = iterator.next();
                            if (!"recon_year".equals(next.getKey()) && !"statistical_index".equals(next.getKey()) && !"partner_no".equals(next.getKey())) {
                                Map<String, Object> m = new HashMap<>();
                                m.put("name", obj.get("partner_no"));
                                m.put("year", obj.get("recon_year"));
                                m.put("time", obj.get("recon_year") + "-" +next.getKey());
                                m.put("month", next.getKey());
                                if (obj.get(next.getKey()) != null) {
//                                    m.put("money", obj.get(next.getKey()));
                                    m.put("money", new BigDecimal(obj.get(next.getKey()).toString()).divide(tenThousand, 2, BigDecimal.ROUND_HALF_UP));
                                } else {
                                    m.put("money", BigDecimal.ZERO);
                                }
                                incomeList.add(m);
                            }
                        }
                    } else if ("2".equals(statisticalIndex)) {
                        //月坏账
                        Iterator<Map.Entry<String, Object>> iterator = obj.entrySet().iterator();
                        while (iterator.hasNext()) {
                            Map.Entry<String, Object> next = iterator.next();
                            if (!"recon_year".equals(next.getKey()) && !"statistical_index".equals(next.getKey()) && !"partner_no".equals(next.getKey())) {
                                Map<String, Object> m = new HashMap<>();
                                m.put("name", obj.get("partner_no"));
                                m.put("year", obj.get("recon_year"));
                                m.put("time", obj.get("recon_year") + "-" +next.getKey());
                                m.put("month", next.getKey());
                                if (obj.get(next.getKey()) != null) {
//                                    m.put("money", obj.get(next.getKey()));
                                    m.put("money", new BigDecimal(obj.get(next.getKey()).toString()).divide(tenThousand, 2, BigDecimal.ROUND_HALF_UP));
                                } else {
                                    m.put("money", BigDecimal.ZERO);
                                }
                                badDebtList.add(m);
                            }
                        }
                    } else if ("4".equals(statisticalIndex)) {
                        //月通道费
                        Iterator<Map.Entry<String, Object>> iterator = obj.entrySet().iterator();
                        while (iterator.hasNext()) {
                            Map.Entry<String, Object> next = iterator.next();
                            if (!"recon_year".equals(next.getKey()) && !"statistical_index".equals(next.getKey()) && !"partner_no".equals(next.getKey())) {
                                Map<String, Object> m = new HashMap<>();
                                m.put("name", obj.get("partner_no"));
                                m.put("year", obj.get("recon_year"));
                                m.put("time", obj.get("recon_year") + "-" +next.getKey());
                                m.put("month", next.getKey());
                                if (obj.get(next.getKey()) != null) {
//                                    m.put("money", obj.get(next.getKey()));
                                    m.put("money", new BigDecimal(obj.get(next.getKey()).toString()).divide(tenThousand, 2, BigDecimal.ROUND_HALF_UP));
                                } else {
                                    m.put("money", BigDecimal.ZERO);
                                }
                                accessFeeList.add(m);
                            }
                        }
                    } else if ("5".equals(statisticalIndex)) {
                        //月流量费
                        Iterator<Map.Entry<String, Object>> iterator = obj.entrySet().iterator();
                        while (iterator.hasNext()) {
                            Map.Entry<String, Object> next = iterator.next();
                            if (!"recon_year".equals(next.getKey()) && !"statistical_index".equals(next.getKey()) && !"partner_no".equals(next.getKey())) {
                                Map<String, Object> m = new HashMap<>();
                                m.put("name", obj.get("partner_no"));
                                m.put("year", obj.get("recon_year"));
                                m.put("time", obj.get("recon_year") + "-" +next.getKey());
                                m.put("month", next.getKey());
                                if (obj.get(next.getKey()) != null) {
//                                    m.put("money", obj.get(next.getKey()));
                                    m.put("money", new BigDecimal(obj.get(next.getKey()).toString()).divide(tenThousand, 2, BigDecimal.ROUND_HALF_UP));
                                } else {
                                    m.put("money", BigDecimal.ZERO);
                                }
                                flowRateList.add(m);
                            }
                        }
                    } else if ("6".equals(statisticalIndex)) {
                        //月运营成本
                        Iterator<Map.Entry<String, Object>> iterator = obj.entrySet().iterator();
                        while (iterator.hasNext()) {
                            Map.Entry<String, Object> next = iterator.next();
                            if (!"recon_year".equals(next.getKey()) && !"statistical_index".equals(next.getKey()) && !"partner_no".equals(next.getKey())) {
                                Map<String, Object> m = new HashMap<>();
                                m.put("name", obj.get("partner_no"));
                                m.put("year", obj.get("recon_year"));
                                m.put("time", obj.get("recon_year") + "-" +next.getKey());
                                m.put("month", next.getKey());
                                if (obj.get(next.getKey()) != null) {
//                                    m.put("money", obj.get(next.getKey()));
                                    m.put("money", new BigDecimal(obj.get(next.getKey()).toString()).divide(tenThousand, 2, BigDecimal.ROUND_HALF_UP));
                                } else {
                                    m.put("money", BigDecimal.ZERO);
                                }
                                operatingCostList.add(m);
                            }
                        }
                    }
                }
                //开始的数据整合完毕
                //处理数据
                for (Map<String, Object> a:incomeList) {
                    String name = (String) a.get("name");
                    String time = (String) a.get("time");
                    BigDecimal moneyIncome = (BigDecimal) a.get("money");
                    BigDecimal sumOther = BigDecimal.ZERO;
                    List<Map<String, Object>> collect1 = badDebtList.stream().filter(t -> name.equals(t.get("name")) && time.equals(t.get("time"))).collect(Collectors.toList());
                    List<Map<String, Object>> collect2 = accessFeeList.stream().filter(t -> name.equals(t.get("name")) && time.equals(t.get("time"))).collect(Collectors.toList());
                    List<Map<String, Object>> collect3 = flowRateList.stream().filter(t -> name.equals(t.get("name")) && time.equals(t.get("time"))).collect(Collectors.toList());
                    List<Map<String, Object>> collect4 = operatingCostList.stream().filter(t -> name.equals(t.get("name")) && time.equals(t.get("time"))).collect(Collectors.toList());
                    for (Map<String, Object> a1:collect1) {
                        BigDecimal money = (BigDecimal) a1.get("money");
                        sumOther = sumOther.add(money);
                    }
                    for (Map<String, Object> a2:collect2) {
                        BigDecimal money = (BigDecimal) a2.get("money");
                        sumOther = sumOther.add(money);
                    }
                    for (Map<String, Object> a3:collect3) {
                        BigDecimal money = (BigDecimal) a3.get("money");
                        sumOther = sumOther.add(money);
                    }
                    for (Map<String, Object> a4:collect4) {
                        BigDecimal money = (BigDecimal) a4.get("money");
                        sumOther = sumOther.add(money);
                    }
                    BigDecimal resultBigDecimal = moneyIncome.subtract(sumOther);
                    a.put("money", resultBigDecimal);
                    finalResultList.add(a);
                }
            }
            }
            //给排序问题解决
//            finalResultList.stream().map(t -> (BigDecimal) t.get("money"))
          /*  List<String> platforms = null;

            List<String> custNos = null;

            List<String> partnerNos = null;

            List<String> fundNos = null;*/


            ProfitRank profitRank = new ProfitRank();
            profitRank.setStartTime(startTime);
            profitRank.setEndTime(endTime);
            profitRank.setPlatforms(platforms);
            profitRank.setCustNos(custNos);
            profitRank.setPartnerNos(partnerNos);
            profitRank.setFundNos(fundNos);
            Map<String, Object> map = this.profitRank(profitRank);
            Map<String, Object> partnerAndFundEchartData1 = (Map<String, Object>) map.get("partnerAndFundEchartData");
            List<String> rankList = (List<String>) partnerAndFundEchartData1.get("rankList");
            List<String> xaxisData = (List<String>) partnerAndFundEchartData1.get("xaxisData");
            Map<Object, List<Map<String, Object>>> groupByName = finalResultList.stream().collect(Collectors.groupingBy(t -> t.get("name")));
            //找前九名，那就是找索引为0-8的，后面的就合计了
            int a = 0;
            if (rankList.size() > 9) {
                a = 9;
            } else {
                a = rankList.size();
            }
            if (a==0){
                Map<String, Object> rankObj = new HashMap<>();
                rankObj.put("areaStyle", StrUtil.EMPTY);
                Map<String, String> stringStringHashMap = new HashMap<>();
                stringStringHashMap.put("focus", "series");
                rankObj.put("emphasis", stringStringHashMap);
                rankObj.put("name", "");
                rankObj.put("stack", "Total");
                rankObj.put("type", "line");
                rankObj.put("symbol","none");
                List<BigDecimal> objects = new ArrayList<>();
                for (int i = 0; i < 13; i++) {
                    objects.add(BigDecimal.ZERO);
                }
                rankObj.put("data", objects);
                resultList.add(rankObj);
            }
            for (int i = 0; i < a; i++) {
//                if (i < 9) {
                    //代表的是资产方+合作方数据的缩写
                    String name = rankList.get(i);
                    //转换后的资产方+合作方数据的全称
                    String convertName = xaxisData.get(i);
                //给一个空对象用来装数据
                Map<String, Object> rankObj = new HashMap<>();
                List<BigDecimal> rankObjDataList = new ArrayList<>();
                    for (Map.Entry<Object, List<Map<String, Object>>> nameObj:groupByName.entrySet()) {
                        rankObj.put("areaStyle", StrUtil.EMPTY);
                        Map<String, String> stringStringHashMap = new HashMap<>();
                        stringStringHashMap.put("focus", "series");
                        rankObj.put("emphasis", stringStringHashMap);
                        rankObj.put("name", convertName);
                        rankObj.put("stack", "Total");
                        rankObj.put("type", "line");
                        rankObj.put("symbol","none");
                        String key = (String) nameObj.getKey();
                        List<Map<String, Object>> value = nameObj.getValue();
                        //从0-8按顺序查找
                        if (key.equals(name)) {
                            //按照时间排序，排序完取money即可
                            value.sort(new Comparator<Map<String, Object>>() {
                                @Override
                                public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                                    String time1 = (String) o1.get("time");
                                    String time2 = (String) o2.get("time");
                                    return time1.compareTo(time2);
                                }
                            });
                            for (Map<String, Object> val:value) {
                                BigDecimal money = (BigDecimal) val.get("money");
                                rankObjDataList.add(money);
                            }
                            if (value.size() != 13) {
                                //说明某些排名是缺数据的
                                Map<String, Object> map1 = value.get(0);
                                String year = (String) map1.get("year");
                                if (year.equals(startYear)) {
                                    //说明缺的是后面的数据，那么就补后面的0
                                    for (int j = 0; j < endCompare; j++) {
                                        rankObjDataList.add(BigDecimal.ZERO);
                                    }
                                    rankObj.put("data", rankObjDataList);
                                } else if (year.equals(endYear)) {
                                    List<BigDecimal> rankObjDataList1 = new ArrayList<>();
                                    //说明缺的是前面的数据
                                    for (int j = startCompare - 1; j < 12; j++) {
                                        rankObjDataList1.add(BigDecimal.ZERO);
                                    }
                                    rankObjDataList1.addAll(rankObjDataList);
                                    rankObj.put("data", rankObjDataList1);
//                                    rankObjDataList = rankObjDataList1;
                                }
                            } else {
                                rankObj.put("data", rankObjDataList);
                            }
                        }
//                        rankObj.put("data", rankObjDataList);
                    }
                /*} else {
                    //9以后合成为一块为其他
                    String name = "其他";
                    Map<String, Object> rankObj = new HashMap<>();
                    for (Map.Entry<Object, List<Map<String, Object>>> nameObj:groupByName.entrySet()) {

                    }
                }*/
                resultList.add(rankObj);
            }

            if (a==0){
                Map<String, Object> rankObj = new HashMap<>();
                rankObj.put("areaStyle", StrUtil.EMPTY);
                Map<String, String> stringStringHashMap = new HashMap<>();
                stringStringHashMap.put("focus", "series");
                rankObj.put("emphasis", stringStringHashMap);
                rankObj.put("name", "其他");
                rankObj.put("stack", "Total");
                rankObj.put("type", "line");
                rankObj.put("symbol","none");
                List<BigDecimal> objects = new ArrayList<>();
                for (int i = 0; i < 13; i++) {
                    objects.add(BigDecimal.ZERO);
                }
                rankObj.put("data", objects);
                resultList.add(rankObj);
            }else if (rankList.size()>9){

            List<Map<String, Object>> hebingList = new ArrayList<>();
            //把后面的合计掉
            for (int i = 9; i < rankList.size(); i++) {
                //代表的是资产方+合作方数据的缩写
                String name = rankList.get(i);
                //转换后的资产方+合作方数据的全称
                String convertName = xaxisData.get(i);
                for (Map.Entry<Object, List<Map<String, Object>>> nameObj:groupByName.entrySet()) {
                    String key = (String) nameObj.getKey();
                    List<Map<String, Object>> value = nameObj.getValue();
                    if (key.equals(name)) {
                        hebingList.addAll(value);
                    }
                }
            }
            //对合并集合时间相等的进行金额的累加
            Map<Object, List<Map<String, Object>>> time = hebingList.stream().collect(Collectors.groupingBy(t -> t.get("time")));
            List<Map<String, Object>> rankMapList = new ArrayList<>();
            for (Map.Entry<Object, List<Map<String, Object>>> timeObj:time.entrySet()) {
                Map<String, Object> rankMap = new HashMap<>();
                BigDecimal moneySum = BigDecimal.ZERO;
                List<Map<String, Object>> value = timeObj.getValue();
                for (Map<String, Object> hejiObj:value) {
                    BigDecimal money = (BigDecimal) hejiObj.get("money");
                    moneySum = moneySum.add(money);
                }
                rankMap.put("time", timeObj.getKey());
                rankMap.put("money", moneySum);
                rankMap.put("month", value.get(0).get("month"));
                rankMap.put("year", value.get(0).get("month"));
                rankMap.put("name", "其他");
                rankMapList.add(rankMap);
            }
            //对合并的集合进行日期排序
            rankMapList.sort(new Comparator<Map<String, Object>>() {
                @Override
                public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                    String time1 = (String) o1.get("time");
                    String time2 = (String) o2.get("time");
                    return time1.compareTo(time2);
                }
            });
            Map<Object, List<Map<String, Object>>> nameMap = rankMapList.stream().collect(Collectors.groupingBy(t -> t.get("name")));
            //给一个空对象用来装数据
            Map<String, Object> rankObj = new HashMap<>();
            List<BigDecimal> rankObjDataList = new ArrayList<>();
            for (Map.Entry<Object, List<Map<String, Object>>> name:nameMap.entrySet()) {
                List<Map<String, Object>> value = name.getValue();
                rankObj.put("areaStyle", StrUtil.EMPTY);
                Map<String, String> stringStringHashMap = new HashMap<>();
                stringStringHashMap.put("focus", "series");
                rankObj.put("emphasis", stringStringHashMap);
                rankObj.put("name", "其他");
                rankObj.put("stack", "Total");
                rankObj.put("type", "line");
                rankObj.put("symbol","none");
                for (Map<String, Object> val:value) {
                    BigDecimal money = (BigDecimal) val.get("money");
                    rankObjDataList.add(money);
                }
                if (value.size() != 13) {
                    Map<String, Object> map1 = value.get(0);
                    String year = (String) map1.get("year");
                    if (year.equals(startYear)) {
                        for (int i = 0; i < endCompare; i++) {
                            rankObjDataList.add(BigDecimal.ZERO);
                        }
                        rankObj.put("data", rankObjDataList);
                    } else if (year.equals(endYear)) {
                        List<BigDecimal> rankObjDataList1 = new ArrayList<>();
                        for (int i = startCompare - 1; i < 12; i++) {
                            rankObjDataList1.add(BigDecimal.ZERO);
                        }
                        rankObjDataList1.addAll(rankObjDataList);
                        rankObj.put("data", rankObjDataList1);
                    }
                } else {
                    rankObj.put("data", rankObjDataList);
                }
            }
            resultList.add(rankObj);
        }
            /*Map<Object, List<Map<String, Object>>> groupByName = finalResultList.stream().collect(Collectors.groupingBy(t -> t.get("name")));
            for (Map.Entry<Object, List<Map<String, Object>>> nameObj:groupByName.entrySet()) {
                //给一个空对象用来装数据
                Map<String, Object> rankObj = new HashMap<>();
                String key = (String) nameObj.getKey();
                //从0-8按顺序查找
                if (key.equals(name)) {
                    //按照时间排序，排序完取money即可
                }
                Object value = nameObj.getValue();
//                rankObj
            }*/


           /* for (Map<String, Object> obj:list) {
                Iterator<Map.Entry<String, Object>> iterator = obj.entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry<String, Object> next = iterator.next();

//                    if ("statistical_index".equals("1")) {
//                        //月收入
//                        if (!"recon_year".equals(next.getKey()) && !"statistical_index".equals(next.getKey()) && !"partner_no".equals(next.getKey())) {
//                            BigDecimal bigDecimal = (BigDecimal) obj.get(next.getKey());
//                            income = income.add(bigDecimal);
//                        }
//                    } else if ("statistical_index".equals("2")) {
//                        //月坏账
//                        if (!"recon_year".equals(next.getKey()) && !"statistical_index".equals(next.getKey()) && !"partner_no".equals(next.getKey())) {
//                            BigDecimal bigDecimal = (BigDecimal) obj.get(next.getKey());
//                            badDebt = badDebt.add(bigDecimal);
//                        }
//                    } else if ("statistical_index".equals("4")) {
//                        //月通道费
//                        if (!"recon_year".equals(next.getKey()) && !"statistical_index".equals(next.getKey()) && !"partner_no".equals(next.getKey())) {
//                            BigDecimal bigDecimal = (BigDecimal) obj.get(next.getKey());
//                            accessFee = accessFee.add(bigDecimal);
//                        }
//                    } else if ("statistical_index".equals("5")) {
//                        //月流量费
//                        if (!"recon_year".equals(next.getKey()) && !"statistical_index".equals(next.getKey()) && !"partner_no".equals(next.getKey())) {
//                            BigDecimal bigDecimal = (BigDecimal) obj.get(next.getKey());
//                            flowRate = flowRate.add(bigDecimal);
//                        }
//                    } else if ("statistical_index".equals("6")) {
//                        //月运营成本
//                        if (!"recon_year".equals(next.getKey()) && !"statistical_index".equals(next.getKey()) && !"partner_no".equals(next.getKey())) {
//                            BigDecimal bigDecimal = (BigDecimal) obj.get(next.getKey());
//                            operatingCost = operatingCost.add(bigDecimal);
//                        }
//                    }



//                    if (!"recon_year".equals(next.getKey()) && !"statistical_index".equals(next.getKey()) && !"partner_no".equals(next.getKey())) {
//                        BigDecimal value = (BigDecimal) obj.get(next.getKey());
//                    }
                }
            }*/

        }

        return resultList;
    }

    //产品利润排名（合作方+资金方）
    private Map<String, Object> getProfitRankByPartnerAndFund(List<Map<String, Object>> partnerAndFundEchartData, String startYear, int startCompare, String endYear, int endCompare) {
        BigDecimal tenThousand = new BigDecimal("10000");
        Map<String, Object> objectMap = new HashMap<>();
        List<Map<String, Object>> profitRankByPartnerList = new ArrayList<>();
        List<Map<String, Object>> objList = new ArrayList<>();
        List<Map<String, Object>> partnerAndFundEchartDataConvert = partnerAndFundEchartData.stream().map(a -> {
            a.put("01", a.remove("a01"));
            a.put("02", a.remove("a02"));
            a.put("03", a.remove("a03"));
            a.put("04", a.remove("a04"));
            a.put("05", a.remove("a05"));
            a.put("06", a.remove("a06"));
            a.put("07", a.remove("a07"));
            a.put("08", a.remove("a08"));
            a.put("09", a.remove("a09"));
            a.put("10", a.remove("a10"));
            a.put("11", a.remove("a11"));
            a.put("12", a.remove("a12"));
            return a;
        }).collect(Collectors.toList());
        //获取字段表映射值，获取合作方和资金方
        List<SysDictData> paramsDictData = dictDataService.getParamsDictData();
        List<SysDictData> dictData = paramsDictData.stream().filter(t -> "partner_no".equals(t.getDictType()) || "fund_no".equals(t.getDictType())).collect(Collectors.toList());
        if (partnerAndFundEchartDataConvert.size() != 0) {
            //对合作方进行分组
            Map<Object, List<Map<String, Object>>> partnerNoCollect = partnerAndFundEchartDataConvert.stream().collect(Collectors.groupingBy(t -> t.get("partner_no")));
            for (Map.Entry<Object, List<Map<String, Object>>> partnerNoList:partnerNoCollect.entrySet()) {
                if (startYear.equals(endYear)) {
                    //开始和结束年份一样，那么只看月份了，月份差多少那就是多少
                    for (Map<String, Object> obj:partnerNoList.getValue()) {
                        Iterator<Map.Entry<String, Object>> iterator = obj.entrySet().iterator();
                        while (iterator.hasNext()) {
                            Map.Entry<String, Object> next = iterator.next();
                            if (!"recon_year".equals(next.getKey()) && !"statistical_index".equals(next.getKey()) && !"partner_no".equals(next.getKey())) {
                                int i = Integer.parseInt(next.getKey());
                                if (i < startCompare || i > endCompare) {
                                    iterator.remove();
                                }
//                                Object value = next.getValue();
//                                if (value == null) {
//                                    iterator.remove();
//                                }
                            }
                        }
                    }
                } else {
                    //开始和结束年份不一样，分三个部分去处理
                    //第一个部分，开始的年
                    List<Map<String, Object>> firstPart = partnerNoList.getValue().stream().filter(t -> startYear.equals(t.get("recon_year"))).collect(Collectors.toList());
                    //第二个部分，结束的年
                    List<Map<String, Object>> secondPart = partnerNoList.getValue().stream().filter(t -> endYear.equals(t.get("recon_year"))).collect(Collectors.toList());
                    //第三个部分，除了开始的年和结束的年
                    List<Map<String, Object>> thirdPart = partnerNoList.getValue().stream().filter(t -> !startYear.equals(t.get("recon_year")) && !endYear.equals(t.get("recon_year"))).collect(Collectors.toList());
                    //处理第一个部分
                    for (Map<String, Object> obj:firstPart) {
                        Iterator<Map.Entry<String, Object>> iterator = obj.entrySet().iterator();
                        while (iterator.hasNext()) {
                            Map.Entry<String, Object> next = iterator.next();
                            if (!"recon_year".equals(next.getKey()) && !"statistical_index".equals(next.getKey()) && !"partner_no".equals(next.getKey())) {
                                int i = Integer.parseInt(next.getKey());
                                if (i < startCompare) {
                                    iterator.remove();
                                }
//                                Object value = next.getValue();
//                                if (value == null) {
//                                    iterator.remove();
//                                }
                            }
                        }
                    }
                    //处理第二个部分
                    for (Map<String, Object> obj:secondPart) {
                        Iterator<Map.Entry<String, Object>> iterator = obj.entrySet().iterator();
                        while (iterator.hasNext()) {
                            Map.Entry<String, Object> next = iterator.next();
                            if (!"recon_year".equals(next.getKey()) && !"statistical_index".equals(next.getKey()) && !"partner_no".equals(next.getKey())) {
                                int i = Integer.parseInt(next.getKey());
                                if (i > endCompare) {
                                    iterator.remove();
                                }
                            }
                        }
                    }
                    //第三个部分不用处理
                }
                //计算数据
                //计算收入合计
                partnerNoList.getValue().forEach(obj -> {
                    Iterator<Map.Entry<String, Object>> iterator = obj.entrySet().iterator();
                    while (iterator.hasNext()) {
                        Map.Entry<String, Object> next = iterator.next();
                        Object value = next.getValue();
                        if (value == null) {
                            iterator.remove();
                        }
                        if ("recon_year".equals(next.getKey()) || "partner_no".equals(next.getKey())) {
                            iterator.remove();
                        }
                    }
                });
                //月收入
                BigDecimal income = BigDecimal.ZERO;
                List<Map<String, Object>> incomeList = partnerNoList.getValue().stream().filter(t -> "1".equals(t.get("statistical_index"))).collect(Collectors.toList());
                for (Map<String, Object> m:incomeList) {
                    Collection<Object> values = m.values();
                    for (Object o:values) {
                        //月收入多加了一个1
                        income = income.add(new BigDecimal(o.toString()));
                    }
                }
                //月坏账
                BigDecimal badDebt = BigDecimal.ZERO;
                List<Map<String, Object>> badDebtList = partnerNoList.getValue().stream().filter(t -> "2".equals(t.get("statistical_index"))).collect(Collectors.toList());
                for (Map<String, Object> m:badDebtList) {
                    Collection<Object> values = m.values();
                    for (Object o:values) {
                        //月收入多加了一个2
                        badDebt = badDebt.add(new BigDecimal(o.toString()));
                    }
                }
                //月通道费
                BigDecimal accessFee = BigDecimal.ZERO;
                List<Map<String, Object>> accessFeeList = partnerNoList.getValue().stream().filter(t -> "4".equals(t.get("statistical_index"))).collect(Collectors.toList());
                for (Map<String, Object> m:accessFeeList) {
                    Collection<Object> values = m.values();
                    for (Object o:values) {
                        //月收入多加了一个4
                        accessFee = accessFee.add(new BigDecimal(o.toString()));
                    }
                }
                //月流量费
                BigDecimal flowRate = BigDecimal.ZERO;
                List<Map<String, Object>> flowRateList = partnerNoList.getValue().stream().filter(t -> "5".equals(t.get("statistical_index"))).collect(Collectors.toList());
                for (Map<String, Object> m:flowRateList) {
                    Collection<Object> values = m.values();
                    for (Object o:values) {
                        //月收入多加了一个5
                        flowRate = flowRate.add(new BigDecimal(o.toString()));
                    }
                }
                //月运营成本
                BigDecimal operatingCost = BigDecimal.ZERO;
                List<Map<String, Object>> operatingCostList = partnerNoList.getValue().stream().filter(t -> "6".equals(t.get("statistical_index"))).collect(Collectors.toList());
                for (Map<String, Object> m:operatingCostList) {
                    Collection<Object> values = m.values();
                    for (Object o:values) {
                        //月收入多加了一个6
                        operatingCost = operatingCost.add(new BigDecimal(o.toString()));
                    }
                }
                //计算利润
                income = income.subtract(new BigDecimal("1"));
                badDebt = badDebt.subtract(new BigDecimal("2"));
                accessFee = accessFee.subtract(new BigDecimal("4"));
                flowRate = flowRate.subtract(new BigDecimal("5"));
                operatingCost = operatingCost.subtract(new BigDecimal("6"));
                BigDecimal profit = income.subtract(badDebt).subtract(accessFee).subtract(flowRate).subtract(operatingCost);
                BigDecimal cost = badDebt.add(accessFee).add(flowRate).add(operatingCost);
                //映射
                String key = partnerNoList.getKey().toString();
                int length = key.length();
                int plusIndex = key.indexOf("+");
                String firstPart = key.substring(0, plusIndex);
                String secondPart = key.substring(plusIndex + 1, length);
                SysDictData sysDictData1 = dictData.stream().filter(t -> firstPart.equals(t.getDictValue())).findFirst().get();
                SysDictData sysDictData2 = dictData.stream().filter(t -> secondPart.equals(t.getDictValue())).findFirst().get();
                //重新搞一个Map装数据
                Map<String, Object> newObj = new HashMap<>();
//                newObj.put(sysDictData1.getDictLabel() + "+" + sysDictData2.getDictLabel(), profit);
                newObj.put("partner", sysDictData1.getDictLabel() + "+" + sysDictData2.getDictLabel());
                newObj.put("rank", sysDictData1.getDictValue() + "+" + sysDictData2.getDictValue());
                newObj.put("profit", profit.divide(tenThousand, 2, BigDecimal.ROUND_HALF_UP));
                profitRankByPartnerList.add(newObj);
                //搞一个Map装列表的数据
                Map<String, Object> map = new HashMap<>();
                map.put("partner", sysDictData1.getDictLabel() + "+" + sysDictData2.getDictLabel());
                map.put("income", income.divide(tenThousand, 2, BigDecimal.ROUND_HALF_UP));
                map.put("cost", cost.divide(tenThousand, 2, BigDecimal.ROUND_HALF_UP));
                map.put("profit", profit.divide(tenThousand, 2, BigDecimal.ROUND_HALF_UP));
                objList.add(map);
            }
        }
        //做默认排序
        profitRankByPartnerList.sort(new Comparator<Map<String, Object>>() {
            @Override
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                BigDecimal profit1 = (BigDecimal) o1.get("profit");
                BigDecimal profit2 = (BigDecimal) o2.get("profit");
                return profit2.compareTo(profit1);
            }
        });
        objList.sort(new Comparator<Map<String, Object>>() {
            @Override
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                BigDecimal profit1 = (BigDecimal) o1.get("profit");
                BigDecimal profit2 = (BigDecimal) o2.get("profit");
                return  profit2.compareTo(profit1);
            }
        });
        //再来一个给echarts下标
        List<String> xaxisData = new ArrayList<>();
        List<String> rankList = new ArrayList<>();
        List<BigDecimal> dataList = new ArrayList<>();
        profitRankByPartnerList.forEach(t -> {
            String partner = (String) t.get("partner");
            String rank = (String) t.get("rank");
            BigDecimal profit = (BigDecimal) t.get("profit");
            xaxisData.add(partner);
            rankList.add(rank);
            dataList.add(profit);
        });
        objectMap.put("profitRankByPartnerList", profitRankByPartnerList);
        objectMap.put("objList", objList);
        objectMap.put("xaxisData", xaxisData);
        objectMap.put("rankList", rankList);
        objectMap.put("dataList", dataList);
        return objectMap;
    }

    //获取产品利润排名（合作方）
    private Map<String, Object> getProfitRankByPartner(List<Map<String, Object>> partnerEchartData, String startYear, int startCompare, String endYear, int endCompare) {
        BigDecimal tenThousand = new BigDecimal("10000");
        Map<String, Object> objectMap = new HashMap<>();
        List<Map<String, Object>> profitRankByPartnerList = new ArrayList<>();
        List<Map<String, Object>> objList = new ArrayList<>();
        List<Map<String, Object>> partnerEchartDataConvert = partnerEchartData.stream().map(a -> {
            a.put("01", a.remove("a01"));
            a.put("02", a.remove("a02"));
            a.put("03", a.remove("a03"));
            a.put("04", a.remove("a04"));
            a.put("05", a.remove("a05"));
            a.put("06", a.remove("a06"));
            a.put("07", a.remove("a07"));
            a.put("08", a.remove("a08"));
            a.put("09", a.remove("a09"));
            a.put("10", a.remove("a10"));
            a.put("11", a.remove("a11"));
            a.put("12", a.remove("a12"));
            return a;
        }).collect(Collectors.toList());
        //获取字段表映射值，获取合作方和资金方
        List<SysDictData> paramsDictData = dictDataService.getParamsDictData();
        List<SysDictData> dictData = paramsDictData.stream().filter(t -> "partner_no".equals(t.getDictType()) || "fund_no".equals(t.getDictType())).collect(Collectors.toList());
        List<SysDictData> dictDataEchartsOne = paramsDictData.stream().filter(t -> "partner_no".equals(t.getDictType())).collect(Collectors.toList());
        if (partnerEchartDataConvert.size() != 0) {
            //对合作方进行分组
            Map<Object, List<Map<String, Object>>> partnerNoCollect = partnerEchartDataConvert.stream().collect(Collectors.groupingBy(t -> t.get("partner_no")));
            for (Map.Entry<Object, List<Map<String, Object>>> partnerNoList:partnerNoCollect.entrySet()) {
                if (startYear.equals(endYear)) {
                    //开始和结束年份一样，那么只看月份了，月份差多少那就是多少
                    for (Map<String, Object> obj:partnerNoList.getValue()) {
                        Iterator<Map.Entry<String, Object>> iterator = obj.entrySet().iterator();
                        while (iterator.hasNext()) {
                            Map.Entry<String, Object> next = iterator.next();
                            if (!"recon_year".equals(next.getKey()) && !"statistical_index".equals(next.getKey()) && !"partner_no".equals(next.getKey())) {
                                int i = Integer.parseInt(next.getKey());
                                if (i < startCompare || i > endCompare) {
                                    iterator.remove();
                                }
//                                Object value = next.getValue();
//                                if (value == null) {
//                                    iterator.remove();
//                                }
                            }
                        }
                    }
                } else {
                    //开始和结束年份不一样，分三个部分去处理
                    //第一个部分，开始的年
                    List<Map<String, Object>> firstPart = partnerNoList.getValue().stream().filter(t -> startYear.equals(t.get("recon_year"))).collect(Collectors.toList());
                    //第二个部分，结束的年
                    List<Map<String, Object>> secondPart = partnerNoList.getValue().stream().filter(t -> endYear.equals(t.get("recon_year"))).collect(Collectors.toList());
                    //第三个部分，除了开始的年和结束的年
                    List<Map<String, Object>> thirdPart = partnerNoList.getValue().stream().filter(t -> !startYear.equals(t.get("recon_year")) && !endYear.equals(t.get("recon_year"))).collect(Collectors.toList());
                    //处理第一个部分
                    for (Map<String, Object> obj:firstPart) {
                        Iterator<Map.Entry<String, Object>> iterator = obj.entrySet().iterator();
                        while (iterator.hasNext()) {
                            Map.Entry<String, Object> next = iterator.next();
                            if (!"recon_year".equals(next.getKey()) && !"statistical_index".equals(next.getKey()) && !"partner_no".equals(next.getKey())) {
                                int i = Integer.parseInt(next.getKey());
                                if (i < startCompare) {
                                    iterator.remove();
                                }
//                                Object value = next.getValue();
//                                if (value == null) {
//                                    iterator.remove();
//                                }
                            }
                        }
                    }
                    //处理第二个部分
                    for (Map<String, Object> obj:secondPart) {
                        Iterator<Map.Entry<String, Object>> iterator = obj.entrySet().iterator();
                        while (iterator.hasNext()) {
                            Map.Entry<String, Object> next = iterator.next();
                            if (!"recon_year".equals(next.getKey()) && !"statistical_index".equals(next.getKey()) && !"partner_no".equals(next.getKey())) {
                                int i = Integer.parseInt(next.getKey());
                                if (i > endCompare) {
                                    iterator.remove();
                                }
                            }
                        }
                    }
                    //第三个部分不用处理
                }
                //计算数据
                //计算收入合计
                partnerNoList.getValue().forEach(obj -> {
                    Iterator<Map.Entry<String, Object>> iterator = obj.entrySet().iterator();
                    while (iterator.hasNext()) {
                        Map.Entry<String, Object> next = iterator.next();
                        Object value = next.getValue();
                        if (value == null) {
                            iterator.remove();
                        }
                        if ("recon_year".equals(next.getKey()) || "partner_no".equals(next.getKey())) {
                            iterator.remove();
                        }
                    }
                });
                //月收入
                BigDecimal income = BigDecimal.ZERO;
                List<Map<String, Object>> incomeList = partnerNoList.getValue().stream().filter(t -> "1".equals(t.get("statistical_index"))).collect(Collectors.toList());
                for (Map<String, Object> m:incomeList) {
                    Collection<Object> values = m.values();
                    for (Object o:values) {
                        //月收入多加了一个1
                        income = income.add(new BigDecimal(o.toString()));
                    }
                }
                //月坏账
                BigDecimal badDebt = BigDecimal.ZERO;
                List<Map<String, Object>> badDebtList = partnerNoList.getValue().stream().filter(t -> "2".equals(t.get("statistical_index"))).collect(Collectors.toList());
                for (Map<String, Object> m:badDebtList) {
                    Collection<Object> values = m.values();
                    for (Object o:values) {
                        //月收入多加了一个2
                        badDebt = badDebt.add(new BigDecimal(o.toString()));
                    }
                }
                //月通道费
                BigDecimal accessFee = BigDecimal.ZERO;
                List<Map<String, Object>> accessFeeList = partnerNoList.getValue().stream().filter(t -> "4".equals(t.get("statistical_index"))).collect(Collectors.toList());
                for (Map<String, Object> m:accessFeeList) {
                    Collection<Object> values = m.values();
                    for (Object o:values) {
                        //月收入多加了一个4
                        accessFee = accessFee.add(new BigDecimal(o.toString()));
                    }
                }
                //月流量费
                BigDecimal flowRate = BigDecimal.ZERO;
                List<Map<String, Object>> flowRateList = partnerNoList.getValue().stream().filter(t -> "5".equals(t.get("statistical_index"))).collect(Collectors.toList());
                for (Map<String, Object> m:flowRateList) {
                    Collection<Object> values = m.values();
                    for (Object o:values) {
                        //月收入多加了一个5
                        flowRate = flowRate.add(new BigDecimal(o.toString()));
                    }
                }
                //月运营成本
                BigDecimal operatingCost = BigDecimal.ZERO;
                List<Map<String, Object>> operatingCostList = partnerNoList.getValue().stream().filter(t -> "6".equals(t.get("statistical_index"))).collect(Collectors.toList());
                for (Map<String, Object> m:operatingCostList) {
                    Collection<Object> values = m.values();
                    for (Object o:values) {
                        //月收入多加了一个6
                        operatingCost = operatingCost.add(new BigDecimal(o.toString()));
                    }
                }
                //计算利润
                income = income.subtract(new BigDecimal("1"));
                badDebt = badDebt.subtract(new BigDecimal("2"));
                accessFee = accessFee.subtract(new BigDecimal("4"));
                flowRate = flowRate.subtract(new BigDecimal("5"));
                operatingCost = operatingCost.subtract(new BigDecimal("6"));
                BigDecimal profit = income.subtract(badDebt).subtract(accessFee).subtract(flowRate).subtract(operatingCost);
                BigDecimal cost = badDebt.add(accessFee).add(flowRate).add(operatingCost);
                //映射
                SysDictData sysDictData = dictDataEchartsOne.stream().filter(t -> partnerNoList.getKey().toString().equals(t.getDictValue())).findFirst().get();
                //重新搞一个Map装echarts的数据
                Map<String, Object> newObj = new HashMap<>();
//                newObj.put(sysDictData.getDictLabel(), profit);
                newObj.put("partner", sysDictData.getDictLabel());
                newObj.put("profit", profit.divide(tenThousand, 2, BigDecimal.ROUND_HALF_UP));
                profitRankByPartnerList.add(newObj);
                //搞一个Map装列表的数据
                Map<String, Object> map = new HashMap<>();
                map.put("partner", sysDictData.getDictLabel());
                map.put("income", income.divide(tenThousand, 2, BigDecimal.ROUND_HALF_UP));
                map.put("cost", cost.divide(tenThousand, 2, BigDecimal.ROUND_HALF_UP));
                map.put("profit", profit.divide(tenThousand, 2, BigDecimal.ROUND_HALF_UP));
                objList.add(map);
            }
        }
        //做默认排序
        profitRankByPartnerList.sort(new Comparator<Map<String, Object>>() {
            @Override
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                BigDecimal profit1 = (BigDecimal) o1.get("profit");
                BigDecimal profit2 = (BigDecimal) o2.get("profit");
                return profit2.compareTo(profit1);
            }
        });
        objList.sort(new Comparator<Map<String, Object>>() {
            @Override
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                BigDecimal profit1 = (BigDecimal) o1.get("profit");
                BigDecimal profit2 = (BigDecimal) o2.get("profit");
                return  profit2.compareTo(profit1);
            }
        });
        //再来一个给echarts下标
        List<String> xaxisData = new ArrayList<>();
        List<BigDecimal> dataList = new ArrayList<>();
        profitRankByPartnerList.forEach(t -> {
            String partner = (String) t.get("partner");
            BigDecimal profit = (BigDecimal) t.get("profit");
            xaxisData.add(partner);
            dataList.add(profit);
        });
        objectMap.put("profitRankByPartnerList", profitRankByPartnerList);
        objectMap.put("objList", objList);
        objectMap.put("xaxisData", xaxisData);
        objectMap.put("dataList", dataList);
        return objectMap;
    }

    /**
     * 月份对应的字段
     *
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    public  Map<String,Object> getMonth(){
        HashMap<String, Object> monthMap = new HashMap<>();
        monthMap.put("01","data_January");
        monthMap.put("02","data_February");
        monthMap.put("03","data_March");
        monthMap.put("04","data_April");
        monthMap.put("05","data_May");
        monthMap.put("06","data_June");
        monthMap.put("07","data_July");
        monthMap.put("08","data_August");
        monthMap.put("09","data_September");
        monthMap.put("10","data_October");
        monthMap.put("11","data_November");
        monthMap.put("12","data_December");
        return monthMap;
    }

    /**
     * 得到所有系统 担保公司 合作方资金方产品等编码 用字典类型+字典编码做key label做value
     * @return
     */
    public Map<String,Object> getDictMap(){
        HashMap<String, Object> returnMap = new HashMap<>();
        List<SysDictData> paramsDictData = dictDataService.getParamsDictData();
        for (SysDictData paramsDictDatum : paramsDictData) {
            returnMap.put(paramsDictDatum.getDictType()+"_"+paramsDictDatum.getDictValue(),paramsDictDatum.getDictLabel());
        }
        return returnMap;
    }
    /**
     * 角色权限sql转换字段
     *
     * @param s 年代
     * @return {@link String}
     */
    public static String roleStringCost(String s){
        String cost1 = s.replaceAll("platform_no", "dta.platform_no");
        String cost2 = cost1.replaceAll("cust_no", "dta.cust_no");
        String cost3 = cost2.replaceAll("partner_no", "dta.partner_no");
        String cost4 = cost3.replaceAll("fund_no", "dta.fund_no");

        return cost4;
    }
    public static List<Map<String, Object>> dictAddData(Map<String, Object> data,List<Map<String, Object>> dictData ){
        List<Map<String, Object>> returnList = new ArrayList<>();
        for (Map<String, Object> datum : dictData) {
            if( data.containsKey(datum.get("dictValue"))){
                datum.put("count",data.get(datum.get("dictValue")).toString());
                returnList.add(datum);
            }

        }
        return returnList;

    }
    public static Map<String,Object> listToMap(List<Map<String, Object>> data){
        HashMap<String, Object> returnMap = new HashMap<>();
        for (Map<String, Object> datum : data) {
            returnMap.put(datum.get("dictValue").toString(),datum.get("count"));
        }
        return returnMap;
    }

    /**
     * 获得数据
     *
     * @param data 数据
     * @return {@link List}<{@link String}>
     */
    public  Map<String,List> getXixasData(List<Map<String, Object>> data,String count){
        BigDecimal nums = new BigDecimal(count);
        DecimalFormat df = new DecimalFormat("###,###.00");

        HashMap<String,List> slMap = new HashMap<>();
        ArrayList<String> dataList= new ArrayList<>();
        ArrayList<String> xaxisList= new ArrayList<>();
        ArrayList<Map<String,Object>> pieList= new ArrayList<>();
        //单位 万
        BigDecimal tenThousand = new BigDecimal("10000");
        for (Map<String, Object> datum : data) {
            //计算百分比

            HashMap<String, Object> pieMap = new HashMap<>();
            if(null == datum.get("count")){
                dataList.add("0.00");
                pieMap.put("value","0.00");
                datum.put("ratio","0.00%");
                datum.put("count","0.00");
            }else {
                //处理各资金方在贷余额分布 Y轴 单位 元 变为 万元
                BigDecimal count1 = new BigDecimal(datum.get("count").toString()).divide(tenThousand, 2, BigDecimal.ROUND_HALF_UP);
                dataList.add(count1.toString());
                pieMap.put("value",datum.get("count"));

                BigDecimal bigDecimal2 = new BigDecimal(datum.get("count").toString());
                //算出占比
                BigDecimal divide = bigDecimal2.divide(nums,4,BigDecimal.ROUND_HALF_UP);
                //保留两位小数
                BigDecimal multiply = divide.multiply(new BigDecimal("100"));
                BigDecimal bigDecimal = multiply.setScale(2, BigDecimal.ROUND_HALF_UP);
                datum.put("ratio",bigDecimal+"%");
                //各资金方在贷余额明细 在贷余额 单位元 改为 万元
                BigDecimal count2 = new BigDecimal(datum.get("count").toString()).divide(tenThousand, 2, BigDecimal.ROUND_HALF_UP);
                double v = Double.parseDouble(count2.toString());
                datum.put("count", df.format(v));
                pieMap.put("value",v);
            }

            pieMap.put("name",datum.get("dictName").toString());
            pieList.add(pieMap);
            xaxisList.add(datum.get("dictName").toString());
        }
        slMap.put("data",dataList);
        slMap.put("xaxis",xaxisList);
        slMap.put("fundTableData",data);
        slMap.put("pie",pieList);
        return slMap;
    }

    public HashMap<String, Object> profitDataListToMap(List<Map<String,Object>> profitList){
        HashMap<String, Object> profitDataMap = new HashMap<>();
        //指标_年份_月为key 对应的月份值为value
        for (Map<String, Object> stringObjectMap : profitList) {

            profitDataMap.put(stringObjectMap.get("statisticalIndex")+"_"+stringObjectMap.get("reconYear")+"-01",stringObjectMap.get("sum1"));
            profitDataMap.put(stringObjectMap.get("statisticalIndex")+"_"+stringObjectMap.get("reconYear")+"-02",stringObjectMap.get("sum2"));
            profitDataMap.put(stringObjectMap.get("statisticalIndex")+"_"+stringObjectMap.get("reconYear")+"-03",stringObjectMap.get("sum3"));
            profitDataMap.put(stringObjectMap.get("statisticalIndex")+"_"+stringObjectMap.get("reconYear")+"-04",stringObjectMap.get("sum4"));
            profitDataMap.put(stringObjectMap.get("statisticalIndex")+"_"+stringObjectMap.get("reconYear")+"-05",stringObjectMap.get("sum5"));
            profitDataMap.put(stringObjectMap.get("statisticalIndex")+"_"+stringObjectMap.get("reconYear")+"-06",stringObjectMap.get("sum6"));
            profitDataMap.put(stringObjectMap.get("statisticalIndex")+"_"+stringObjectMap.get("reconYear")+"-07",stringObjectMap.get("sum7"));
            profitDataMap.put(stringObjectMap.get("statisticalIndex")+"_"+stringObjectMap.get("reconYear")+"-08",stringObjectMap.get("sum8"));
            profitDataMap.put(stringObjectMap.get("statisticalIndex")+"_"+stringObjectMap.get("reconYear")+"-09",stringObjectMap.get("sum9"));
            profitDataMap.put(stringObjectMap.get("statisticalIndex")+"_"+stringObjectMap.get("reconYear")+"-10",stringObjectMap.get("sum10"));
            profitDataMap.put(stringObjectMap.get("statisticalIndex")+"_"+stringObjectMap.get("reconYear")+"-11",stringObjectMap.get("sum11"));
            profitDataMap.put(stringObjectMap.get("statisticalIndex")+"_"+stringObjectMap.get("reconYear")+"-12",stringObjectMap.get("sum12"));
        }
        return profitDataMap;
    }

    /**
     * 获取系统运行统计数据 堆叠不加维度
     * @param dDataCope 系统运行实体
     * @return
     */

    public HashMap<String, Object> getdDataEChartDataStack(DData dDataCope) {
        //获取逻辑修改
        //  修改获取逻辑
        List<String> platforms = null;
        //        if (Strings.isNotEmpty(dData.getPlatformNo())) {
//            platforms = Arrays.asList(dData.getPlatformNo().split(","));
//        }
        List<String> custNos = null;
//        if (Strings.isNotEmpty(dData.getCustNo())) {
//            custNos = Arrays.asList(dData.getCustNo().split(","));
//        }
        List<String> partnerNos = null;
//        if (Strings.isNotEmpty(dData.getPartnerNo())) {
//
//            partnerNos = Arrays.asList(dData.getPartnerNo().split(","));
//        }
        List<String> fundNos = null;
//        if (Strings.isNotEmpty(dData.getFundNo())) {
//
//            fundNos = Arrays.asList(dData.getFundNo().split(","));
//        }
        List<String> products = null;
        if (Strings.isNotEmpty(dDataCope.getProductNo())) {
            products = Arrays.asList(dDataCope.getProductNo().split(","));
        }

        String beginTime  = null;
        if (Strings.isNotEmpty(dDataCope.getPortrayalNo())) {
            beginTime = dDataCope.getPortrayalNo();
        }

        String endTime  = null;
        if (Strings.isNotEmpty(dDataCope.getRemark())) {
            endTime = dDataCope.getRemark();
        }
        //end
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        String format = simpleDateFormat.format(date);
        List<Map<String, Object>> maps= eChartsMapper.dDataEchart(platforms,custNos,partnerNos,fundNos,products,beginTime,endTime,dDataCope);
        //周数据
        List<Map<String, Object>> weekMaps= eChartsMapper.dDataWeekEchart(platforms,custNos,partnerNos,fundNos,products,beginTime,endTime,dDataCope);
        //月数据
        List<Map<String, Object>> monthMaps= eChartsMapper.dDataMonthEchart(platforms,custNos,partnerNos,fundNos,products,beginTime,endTime,dDataCope);
        //年数据
        List<Map<String, Object>> yearMaps= eChartsMapper.dDataYearEchart(platforms,custNos,partnerNos,fundNos,products,beginTime,endTime,dDataCope);

        //获取累计数据 需要取时间点的数据
        //获取一段时间内周末的数据
        List<String> strings = com.ruoyi.common.utils.DateUtils.getweekDays(beginTime, endTime);
        //获取一段时间内月末的日期
        List<String> monthXaxis= DateNumerationUtils.getMonthBetweenDate(beginTime, endTime);
        List<String> monthEndDate = com.ruoyi.common.utils.DateUtils.getMonthEndDate(monthXaxis);
        //获取时间段内年末的日期
        List<String> yearXaxisData = new ArrayList<>();
        for (Map<String, Object> yearData : yearMaps) {
            if(!yearXaxisData.contains(yearData.get("reconDate").toString())){
                yearXaxisData.add(yearData.get("reconDate").toString()+"-12-31");
            }
        }
        //周数据count
        List<Map<String, Object>> weekCountMaps= eChartsMapper.dDataWeekCountEchart(platforms,custNos,partnerNos,fundNos,products,strings,dDataCope);
        //月数据count
        List<Map<String, Object>> monthCountMaps= eChartsMapper.dDataMonthCountEchart(platforms,custNos,partnerNos,fundNos,products,monthEndDate,dDataCope);
        //年数据count
        List<Map<String, Object>> yearCountMaps= eChartsMapper.dDataYearCountEchart(platforms,custNos,partnerNos,fundNos,products,yearXaxisData,dDataCope);

        String year =  endTime.substring(0,4);
        String month = endTime.substring(0,7);
        String month1 = endTime.substring(5,7);
        //判断当天日期和结束时间日期 如果当天大于结束时间则取最近
        int i = endTime.compareTo(format);
        if(i>0 || i == 0){
            //本周周末
            String weekendTimeSection = com.ruoyi.common.utils.DateUtils.getWeekendTimeSection(0);
            //本月月末
            String lastDayOfMonth = com.ruoyi.common.utils.DateUtils.getLastDayOfMonth(Integer.parseInt(year), Integer.parseInt(month1));
            //年末
            String yeardate = year+"-12-31";
            //获取endtime当天的数据
            //周
            List<Map<String, Object>> totalcount = dDataMapper.getnearTotalCountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> totalAmount = dDataMapper.getnearTotalAmountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> balanceAmount = dDataMapper.getnearDayData(platforms, custNos, partnerNos, fundNos,products,dDataCope);
            List<Map<String, Object>> repayAmount = dDataMapper.getnearTotalRepayAmountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> repayCount =dDataMapper.getnearTotalRepayCountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            totalcount.removeAll(Collections.singleton(null));
            totalAmount.removeAll(Collections.singleton(null));
            balanceAmount.removeAll(Collections.singleton(null));
            repayAmount.removeAll(Collections.singleton(null));
            repayCount.removeAll(Collections.singleton(null));
            if(totalcount.size()>0 && totalAmount.size()>0 && balanceAmount.size()>0 && repayAmount.size()>0 && repayCount.size()>0 ){
                strings.add(weekendTimeSection);
                Map<String, Object> map = totalcount.get(0);
                map.put("reconDate",weekendTimeSection);
                map.put("totalAmount",totalAmount.get(0).get("totalAmount"));
                map.put("balanceAmount",balanceAmount.get(0).get("balanceAmount"));
                map.put("repayPrintAmount",repayAmount.get(0).get("totalRepayPrintAmount"));
                map.put("repayCount",repayCount.get(0).get("totalRepayCount"));
                weekCountMaps.add(map);
            }
            //月

            List<Map<String, Object>> totalmountcount = dDataMapper.getnearTotalCountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> totalmountAmount = dDataMapper.getnearTotalAmountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> balancemountAmount = dDataMapper.getnearDayData(platforms, custNos, partnerNos, fundNos,products,dDataCope);
            List<Map<String, Object>> repaymountAmount = dDataMapper.getnearTotalRepayAmountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> repaymountCount =dDataMapper.getnearTotalRepayCountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            totalmountcount.removeAll(Collections.singleton(null));
            totalmountAmount.removeAll(Collections.singleton(null));
            balancemountAmount.removeAll(Collections.singleton(null));
            repaymountAmount.removeAll(Collections.singleton(null));
            repaymountCount.removeAll(Collections.singleton(null));
            if(totalmountcount.size()>0 && totalmountAmount.size()>0 && balancemountAmount.size()>0 && repaymountAmount.size()>0 && repaymountCount.size()>0 ) {
                Map<String, Object> mapmonth = totalmountcount.get(0);
                monthXaxis.add(lastDayOfMonth);
                mapmonth.put("reconDate", lastDayOfMonth);
                mapmonth.put("totalAmount", totalmountAmount.get(0).get("totalAmount"));
                mapmonth.put("balanceAmount", balancemountAmount.get(0).get("balanceAmount"));
                mapmonth.put("repayPrintAmount", repaymountAmount.get(0).get("totalRepayPrintAmount"));
                mapmonth.put("repayCount", repaymountCount.get(0).get("totalRepayCount"));
                monthCountMaps.add(mapmonth);
            }
            //年

            List<Map<String, Object>> totalyearcount = dDataMapper.getnearTotalCountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> totalyearAmount = dDataMapper.getnearTotalAmountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> balanceyearAmount = dDataMapper.getnearDayData(platforms, custNos, partnerNos, fundNos,products,dDataCope);
            List<Map<String, Object>> repayyearAmount = dDataMapper.getnearTotalRepayAmountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> repayyearCount =dDataMapper.getnearTotalRepayCountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);

            totalyearcount.removeAll(Collections.singleton(null));
            totalyearAmount.removeAll(Collections.singleton(null));
            balanceyearAmount.removeAll(Collections.singleton(null));
            repayyearAmount.removeAll(Collections.singleton(null));
            repayyearCount.removeAll(Collections.singleton(null));
            if(totalyearcount.size()>0 && totalyearAmount.size()>0 && balanceyearAmount.size()>0 && repayyearAmount.size()>0 && repaymountCount.size()>0 ) {

                Map<String, Object> mapyear = totalyearcount.get(0);
                mapyear.put("reconDate", yeardate);
                mapyear.put("totalAmount", totalyearAmount.get(0).get("totalAmount"));
                mapyear.put("balanceAmount", balanceyearAmount.get(0).get("balanceAmount"));
                mapyear.put("repayPrintAmount", repayyearAmount.get(0).get("totalRepayPrintAmount"));
                mapyear.put("repayCount", repayyearCount.get(0).get("totalRepayCount"));
                yearCountMaps.add(mapyear);
            }

        }



        //X轴 时间
        //X轴 时间
        List<String> xixas = DateNumerationUtils.getBetweenDate(beginTime,endTime);
        //添加累计贷款余额
        ArrayList<Object> balanceAList = new ArrayList<>();
        //累计贷款笔数
        ArrayList<Object> tCountList = new ArrayList<>();
        //累计贷款本金金额
        ArrayList<Object> tAmountList = new ArrayList<>();
        //新增贷款本金
        ArrayList<Object> addAmountList = new ArrayList<>();
        //新增贷款笔数
        ArrayList<Object> addCountList = new ArrayList<>();
        //累计还款本金
        ArrayList<Object> repayAmountList = new ArrayList<>();
        //累计还款笔数
        ArrayList<Object> repayCountList = new ArrayList<>();
        //新增还款本金
        ArrayList<Object> addRepayAmountList = new ArrayList<>();
        //新增还款笔数
        ArrayList<Object> addRepayCountList = new ArrayList<>();

        String mapKey = "reconDate";
        //将 List<Map<String, Object>> 格式转换为Map<String, Map<String, Object>> 方便用日期获取数据
        Map<String, Map<String, Object>> echartDataMap = DataDisposeUtil.listToMap(maps, mapKey);
        //周
        Map<String, Map<String, Object>> weekDataMap = DataDisposeUtil.listToMap(weekMaps, mapKey);
        //月
        Map<String, Map<String, Object>> monthDataMap = DataDisposeUtil.listToMap(monthMaps, mapKey);
        //年
        Map<String, Map<String, Object>> yearDataMap = DataDisposeUtil.listToMap(yearMaps, mapKey);
        //周count
        Map<String, Map<String, Object>> weekDataMapcount = DataDisposeUtil.listToMap(weekCountMaps, mapKey);
        //月count
        Map<String, Map<String, Object>> monthDataMapcount = DataDisposeUtil.listToMap(monthCountMaps, mapKey);
        //年count
        Map<String, Map<String, Object>> yearDataMapcount = DataDisposeUtil.listToMap(yearCountMaps, mapKey);
        //带维度不堆叠

        Map<String, Object> map = org.ruoyi.core.util.EChartDataUtil.noweiduData(weekDataMap, monthDataMap, yearDataMap, yearMaps,strings,monthEndDate,yearXaxisData,weekDataMapcount,monthDataMapcount,yearDataMapcount, beginTime, endTime, null, "N", "Y");
        for (String xixa : xixas) {
                if(null==echartDataMap.get(xixa)){
                    //添加累计贷款本金余额
                    balanceAList.add("0.00");

                    //累计贷款笔数
                    tCountList.add("0");

                    //累计贷款本金金额
                    tAmountList.add("0.00");

                    //新增贷款本金
                    addAmountList.add("0.00");

                    //新增贷款笔数
                    addCountList.add("0");

                    //累计还款本金
                    repayAmountList.add("0.00");

                    //累计还款笔数
                    repayCountList.add("0");

                    //新增还款本金
                    addRepayAmountList.add("0.00");

                    //新增还款笔数
                    addRepayCountList.add("0");
                }else {
                    Map<String, Object> dataMap = echartDataMap.get(xixa);
                    //添加累计贷款本金余额
                    balanceAList.add(dataMap.get("balanceAmount"));

                    //累计贷款笔数
                    tCountList.add(dataMap.get("totalCount"));

                    //累计贷款本金金额
                    tAmountList.add(dataMap.get("totalAmount"));

                    //新增贷款本金
                    addAmountList.add(dataMap.get("addAmount"));

                    //新增贷款笔数
                    addCountList.add(dataMap.get("addCount"));

                    //累计还款本金
                    repayAmountList.add(dataMap.get("repayPrintAmount"));

                    //累计还款笔数
                    repayCountList.add(dataMap.get("repayCount"));

                    //新增还款本金
                    addRepayAmountList.add(dataMap.get("addRepayPrintAmount"));

                    //新增还款笔数
                    addRepayCountList.add(dataMap.get("addRepayCount"));
                }
        }

        //累计贷款余额
        HashMap<String, Object> balanceData = new HashMap<>();
        balanceData.put("name","贷款余额");
        balanceData.put("type","bar");
        balanceData.put("stack","abc");
        balanceData.put("data",balanceAList);
        //累计贷款笔数
        HashMap<String, Object> totalData = new HashMap<>();
        totalData.put("name","在贷笔数");
        totalData.put("type","bar");
        totalData.put("stack","abc");
        totalData.put("data",tCountList);
        //累计贷款金额
        HashMap<String, Object> amountData = new HashMap<>();
        amountData.put("name","贷款金额");
        amountData.put("type","bar");
        amountData.put("stack","abc");
        amountData.put("data",tAmountList);
        //新增贷款本金
        HashMap<String, Object> addMountData = new HashMap<>();
        addMountData.put("name","新增贷款本金");
        addMountData.put("type","bar");
        addMountData.put("stack","abc");
        addMountData.put("data",addAmountList);

        //新增贷款笔数
        HashMap<String, Object> addCountData = new HashMap<>();
        addCountData.put("name","新增贷款笔数");
        addCountData.put("type","bar");
        addCountData.put("stack","abc");
        addCountData.put("data",addCountList);
        //累计还款本金
        HashMap<String, Object> repayAmountData = new HashMap<>();
        repayAmountData.put("name","累计还款本金");
        repayAmountData.put("type","bar");
        repayAmountData.put("stack","abc");
        repayAmountData.put("data",repayAmountList);
        //累计还款笔数
        HashMap<String, Object> repayCountData = new HashMap<>();
        repayCountData.put("name","累计还款笔数");
        repayCountData.put("type","bar");
        repayCountData.put("stack","abc");
        repayCountData.put("data",repayCountList);
        //新增还款金额
        HashMap<String, Object> addRepayAmountData = new HashMap<>();
        addRepayAmountData.put("name","新增还款金额");
        addRepayAmountData.put("type","bar");
        addRepayAmountData.put("stack","abc");
        addRepayAmountData.put("data",addRepayAmountList);
        //新增还款笔数
        HashMap<String, Object> addRepayCountData = new HashMap<>();
        addRepayCountData.put("name","新增还款笔数");
        addRepayCountData.put("type","bar");
        addRepayCountData.put("stack","abc");
        addRepayCountData.put("data",addRepayCountList);

        //1、添加累计贷款余额与笔数图标显示，
        ArrayList<Map<String, Object>> balanceAndTotalData = new ArrayList<>();
        balanceAndTotalData.add(balanceData);
        balanceAndTotalData.add(totalData);
//        2、添加累计贷款本金金额与笔数图表显示
        ArrayList<Map<String, Object>> amountAndTotalData = new ArrayList<>();
        amountAndTotalData.add(amountData);
        amountAndTotalData.add(totalData);
//        3、新增贷款本金金额与笔数图表显示
        ArrayList<Map<String, Object>> addAmountAndTotalData = new ArrayList<>();
        addAmountAndTotalData.add(addMountData);
        addAmountAndTotalData.add(addCountData);
//        4、累计还款本金与笔数图表显示
        ArrayList<Map<String, Object>>  totalPrintAmountAndTotalData = new ArrayList<>();
        totalPrintAmountAndTotalData.add(repayAmountData);
        totalPrintAmountAndTotalData.add(repayCountData);
//        5、新增还款本金与笔数图表显示
        ArrayList<Map<String, Object>> addRepayAmountAndTotalData = new ArrayList<>();
        addRepayAmountAndTotalData.add(addRepayAmountData);
        addRepayAmountAndTotalData.add(addRepayCountData);

        HashMap<String, Object> returnData = new HashMap<>();

        returnData.put("balanceAndTotalData",balanceAndTotalData);
        returnData.put("amountAndTotalData",amountAndTotalData);
        returnData.put("addAmountAndTotalData",addAmountAndTotalData);
        returnData.put("totalPrintAmountAndTotalData",totalPrintAmountAndTotalData);
        returnData.put("addRepayAmountAndTotalData",addRepayAmountAndTotalData);
        returnData.put("xiaxis",xixas);
        returnData.putAll(map);
        returnData.put("weekcountxaxis",strings);
        returnData.put("monthcountxaxis",monthEndDate);
        returnData.put("yearcountxaxis",yearXaxisData);
        return returnData;
    }


    /**
     * 运营情况统计堆叠+维度
     *
     * @param dDataCope 维数据处理
     * @return {@link HashMap}<{@link String}, {@link Object}>
     */
    public HashMap<String, Object> echartStack(DData dDataCope){

        //获取逻辑修改
        //  修改获取逻辑
        List<String> platforms = null;
        //        if (Strings.isNotEmpty(dData.getPlatformNo())) {
//            platforms = Arrays.asList(dData.getPlatformNo().split(","));
//        }
        List<String> custNos = null;
//        if (Strings.isNotEmpty(dData.getCustNo())) {
//            custNos = Arrays.asList(dData.getCustNo().split(","));
//        }
        List<String> partnerNos = null;
//        if (Strings.isNotEmpty(dData.getPartnerNo())) {
//
//            partnerNos = Arrays.asList(dData.getPartnerNo().split(","));
//        }
        List<String> fundNos = null;
//        if (Strings.isNotEmpty(dData.getFundNo())) {
//
//            fundNos = Arrays.asList(dData.getFundNo().split(","));
//        }
        List<String> products = null;
        if (Strings.isNotEmpty(dDataCope.getProductNo())) {
            products = Arrays.asList(dDataCope.getProductNo().split(","));
        }
        String beginTime  = null;
        if (Strings.isNotEmpty(dDataCope.getPortrayalNo())) {
            beginTime = dDataCope.getPortrayalNo();
        }

        String endTime  = null;
        if (Strings.isNotEmpty(dDataCope.getRemark())) {
            endTime = dDataCope.getRemark();
        }
        String portrayalType = null;
        if (Strings.isNotEmpty(dDataCope.getPortrayalType())) {
            portrayalType = dDataCope.getPortrayalType();
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        String format = simpleDateFormat.format(date);
//        if (dDataCope.getMoreSearch() != null && dDataCope.getMoreSearch().length() > 2){
//            Gson gson = new Gson();
//            // 将字符串转换为 Map<String, List<Long>>
//            Map<String, List<String>> moreSearch = gson.fromJson(dDataCope.getMoreSearch(), Map.class);
//            dDataCope.setMoreSearchMap(moreSearch);
//        }
        List<Map<String, Object>> maps = eChartsMapper.dDataDateEchart(platforms,custNos,partnerNos,fundNos,products,beginTime,endTime,portrayalType,dDataCope);
        //周数据
        List<Map<String, Object>> weekMaps = eChartsMapper.dDataWeeKDateEchart(platforms,custNos,partnerNos,fundNos,products,beginTime,endTime,portrayalType,dDataCope);
        //月数据
        List<Map<String, Object>> monthMaps = eChartsMapper.dDataMonthDateEchart(platforms,custNos,partnerNos,fundNos,products,beginTime,endTime,portrayalType,dDataCope);
        //年数据
        List<Map<String, Object>> yearMaps = eChartsMapper.dDataYearDateEchart(platforms,custNos,partnerNos,fundNos,products,beginTime,endTime,portrayalType,dDataCope);

        //获取累计数据 需要取时间点的数据
        //获取一段时间内周末的数据
        List<String> strings = com.ruoyi.common.utils.DateUtils.getweekDays(beginTime, endTime);
        //获取一段时间内月末的日期
        List<String> monthXaxis= DateNumerationUtils.getMonthBetweenDate(beginTime, endTime);
        List<String> monthEndDate = com.ruoyi.common.utils.DateUtils.getMonthEndDate(monthXaxis);
        //获取时间段内年末的日期
        List<String> yearXaxisData = new ArrayList<>();
        for (Map<String, Object> yearData : yearMaps) {
            if(!yearXaxisData.contains(yearData.get("reconDate").toString())){
                yearXaxisData.add(yearData.get("reconDate").toString()+"-12-31");
            }
        }
        //周数据count
        List<Map<String, Object>> weekCountMaps= eChartsMapper.dDataWeeKCountDateEchart(platforms,custNos,partnerNos,fundNos,products,strings,portrayalType,dDataCope);
        //月数据count
        List<Map<String, Object>> monthCountMaps= eChartsMapper.dDataMonthCountDateEchart(platforms,custNos,partnerNos,fundNos,products,monthEndDate,portrayalType,dDataCope);
        //年数据count
        List<Map<String, Object>> yearCountMaps= eChartsMapper.dDataYearCountDateEchart(platforms,custNos,partnerNos,fundNos,products,yearXaxisData,portrayalType,dDataCope);
        String year =  endTime.substring(0,4);
        String month = endTime.substring(0,7);
        String month1 = endTime.substring(5,7);
        String dictValue = "";
        if(dDataCope.getPortrayalType().equals("platform_no")){
            dictValue = "platformNo";
        }else if(dDataCope.getPortrayalType().equals("cust_no")){
            dictValue = "custNo";
        }if(dDataCope.getPortrayalType().equals("partner_no")){
            dictValue = "partnerNo";
        }if(dDataCope.getPortrayalType().equals("fund_no")){
            dictValue = "fundNo";
        }if(dDataCope.getPortrayalType().equals("product_no")){
            dictValue = "productNo";
        }
        //判断当天日期和结束时间日期 如果当天大于结束时间则取最近
        int i = endTime.compareTo(format);
        if(i>0 || i == 0){
            //本周周末
            String weekendTimeSection = com.ruoyi.common.utils.DateUtils.getWeekendTimeSection(0);
            //本月月末
            String lastDayOfMonth = com.ruoyi.common.utils.DateUtils.getLastDayOfMonth(Integer.parseInt(year), Integer.parseInt(month1));
            //年末
            String yeardate = year+"-12-31";
            //获取endtime当天的数据
            //周
            List<Map<String, Object>> totalcount = dDataMapper.getnearTotalCountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> totalAmount = dDataMapper.getnearTotalAmountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> balanceAmount = dDataMapper.getnearDayData(platforms, custNos, partnerNos, fundNos,products,dDataCope);
            List<Map<String, Object>> repayAmount = dDataMapper.getnearTotalRepayAmountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> repayCount =dDataMapper.getnearTotalRepayCountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            totalcount.removeAll(Collections.singleton(null));
            totalAmount.removeAll(Collections.singleton(null));
            balanceAmount.removeAll(Collections.singleton(null));
            repayAmount.removeAll(Collections.singleton(null));
            repayCount.removeAll(Collections.singleton(null));
            if(totalcount.size()>0 && totalAmount.size()>0 && balanceAmount.size()>0 && repayAmount.size()>0 && repayCount.size()>0 ){
                strings.add(weekendTimeSection);
                Map<String, Object> map = totalcount.get(0);
                map.put("reconDate",weekendTimeSection);
                map.put("totalAmount",totalAmount.get(0).get("totalAmount"));
                map.put("balanceAmount",balanceAmount.get(0).get("balanceAmount"));
                map.put("repayPrintAmount",repayAmount.get(0).get("totalRepayPrintAmount"));
                map.put("repayCount",repayCount.get(0).get("totalRepayCount"));
                map.put("dictValue",map.get(dictValue));
                weekCountMaps.add(map);
            }
            //月

            List<Map<String, Object>> totalmountcount = dDataMapper.getnearTotalCountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> totalmountAmount = dDataMapper.getnearTotalAmountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> balancemountAmount = dDataMapper.getnearDayData(platforms, custNos, partnerNos, fundNos,products,dDataCope);
            List<Map<String, Object>> repaymountAmount = dDataMapper.getnearTotalRepayAmountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> repaymountCount =dDataMapper.getnearTotalRepayCountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            totalmountcount.removeAll(Collections.singleton(null));
            totalmountAmount.removeAll(Collections.singleton(null));
            balancemountAmount.removeAll(Collections.singleton(null));
            repaymountAmount.removeAll(Collections.singleton(null));
            repaymountCount.removeAll(Collections.singleton(null));
            if(totalmountcount.size()>0 && totalmountAmount.size()>0 && balancemountAmount.size()>0 && repaymountAmount.size()>0 && repaymountCount.size()>0 ) {
                Map<String, Object> mapmonth = totalmountcount.get(0);
                monthXaxis.add(lastDayOfMonth);
                mapmonth.put("reconDate", lastDayOfMonth);
                mapmonth.put("totalAmount", totalmountAmount.get(0).get("totalAmount"));
                mapmonth.put("balanceAmount", balancemountAmount.get(0).get("balanceAmount"));
                mapmonth.put("repayPrintAmount", repaymountAmount.get(0).get("totalRepayPrintAmount"));
                mapmonth.put("repayCount", repaymountCount.get(0).get("totalRepayCount"));
                mapmonth.put("dictValue",mapmonth.get(dictValue));
                monthCountMaps.add(mapmonth);
            }
            //年

            List<Map<String, Object>> totalyearcount = dDataMapper.getnearTotalCountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> totalyearAmount = dDataMapper.getnearTotalAmountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> balanceyearAmount = dDataMapper.getnearDayData(platforms, custNos, partnerNos, fundNos,products,dDataCope);
            List<Map<String, Object>> repayyearAmount = dDataMapper.getnearTotalRepayAmountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);
            List<Map<String, Object>> repayyearCount =dDataMapper.getnearTotalRepayCountDayData(platforms, custNos, partnerNos, fundNos,dDataCope);

            totalyearcount.removeAll(Collections.singleton(null));
            totalyearAmount.removeAll(Collections.singleton(null));
            balanceyearAmount.removeAll(Collections.singleton(null));
            repayyearAmount.removeAll(Collections.singleton(null));
            repayyearCount.removeAll(Collections.singleton(null));
            if(totalyearcount.size()>0 && totalyearAmount.size()>0 && balanceyearAmount.size()>0 && repayyearAmount.size()>0 && repaymountCount.size()>0 ) {

                Map<String, Object> mapyear = totalyearcount.get(0);
                mapyear.put("reconDate", yeardate);
                mapyear.put("totalAmount", totalyearAmount.get(0).get("totalAmount"));
                mapyear.put("balanceAmount", balanceyearAmount.get(0).get("balanceAmount"));
                mapyear.put("repayPrintAmount", repayyearAmount.get(0).get("totalRepayPrintAmount"));
                mapyear.put("repayCount", repayyearCount.get(0).get("totalRepayCount"));
                mapyear.put("dictValue",mapyear.get(dictValue));
                yearCountMaps.add(mapyear);
            }
        }

        //X轴 时间
        List<String> xixas = DateNumerationUtils.getBetweenDate(beginTime,endTime);
        //echaert数据
//        List<Map<String, Object>> maps = eChartsMapper.dDatabydimensionEchart(dDataCope);
        //所有类型
        List<Map<String, Object>> dimensions =  eChartsMapper.getDDataDimension(platforms,custNos,partnerNos,fundNos,products,beginTime,endTime,portrayalType,dDataCope);

        //1、添加累计贷款余额与笔数图标显示，
        ArrayList<Map<String, Object>> balanceAndTotalData = new ArrayList<>();
//        2、添加累计贷款本金金额与笔数图表显示
        ArrayList<Map<String, Object>> amountAndTotalData = new ArrayList<>();

//        3、新增贷款本金金额与笔数图表显示
        ArrayList<Map<String, Object>> addAmountAndTotalData = new ArrayList<>();

//        4、累计还款本金与笔数图表显示
        ArrayList<Map<String, Object>>  totalPrintAmountAndTotalData = new ArrayList<>();

//        5、新增还款本金与笔数图表显示
        ArrayList<Map<String, Object>> addRepayAmountAndTotalData = new ArrayList<>();

        String mapKey = "reconDate";
        String otherMapKey = "dictValue";
        //将 List<Map<String, Object>> 格式转换为Map<String, Map<String, Object>> 方便用日期获取数据
        Map<String, Map<String, Object>> echartDataMap = DataDisposeUtil.listToMap(maps, mapKey,otherMapKey);
        //周
        Map<String, Map<String, Object>> weekDataMap = DataDisposeUtil.listToMap(weekMaps, mapKey,otherMapKey);
        //月
        Map<String, Map<String, Object>> monthDataMap = DataDisposeUtil.listToMap(monthMaps, mapKey,otherMapKey);
        //年
        Map<String, Map<String, Object>> yearDataMap = DataDisposeUtil.listToMap(yearMaps, mapKey,otherMapKey);

        //周count
        Map<String, Map<String, Object>> weekcountDataMap = DataDisposeUtil.listToMap(weekCountMaps, mapKey,otherMapKey);
        //月count
        Map<String, Map<String, Object>> monthcountDataMap = DataDisposeUtil.listToMap(monthCountMaps, mapKey,otherMapKey);
        //年count
        Map<String, Map<String, Object>> yearcountDataMap = DataDisposeUtil.listToMap(yearCountMaps, mapKey,otherMapKey);

        //带维度  + 堆叠
        Map<String, Object> map = org.ruoyi.core.util.EChartDataUtil.noweiduData(weekDataMap, monthDataMap, yearDataMap, yearMaps,strings,monthEndDate,yearXaxisData,weekcountDataMap,monthcountDataMap,yearcountDataMap, beginTime, endTime, dimensions, "Y", "Y");

        for (Map<String, Object> dimension : dimensions) {
            //添加累计贷款余额
            ArrayList<Object> balanceAList = new ArrayList<>();
            //累计贷款笔数
            ArrayList<Object> tCountList = new ArrayList<>();
            //累计贷款本金金额
            ArrayList<Object> tAmountList = new ArrayList<>();
            //新增贷款本金
            ArrayList<Object> addAmountList = new ArrayList<>();
            //新增贷款笔数
            ArrayList<Object> addCountList = new ArrayList<>();
            //累计还款本金
            ArrayList<Object> repayAmountList = new ArrayList<>();
            //累计还款笔数
            ArrayList<Object> repayCountList = new ArrayList<>();
            //新增还款本金
            ArrayList<Object> addRepayAmountList = new ArrayList<>();
            //新增还款笔数
            ArrayList<Object> addRepayCountList = new ArrayList<>();

            for (String xixa : xixas) {
                if(null == echartDataMap.get(xixa+dimension.get("dictValue"))){
                    //添加累计贷款本金余额
                    balanceAList.add("0.00");

                    //累计贷款笔数
                    tCountList.add("0");

                    //累计贷款本金金额
                    tAmountList.add("0.00");

                    //新增贷款本金
                    addAmountList.add("0.00");

                    //新增贷款笔数
                    addCountList.add("0");

                    //累计还款本金
                    repayAmountList.add("0.00");

                    //累计还款笔数
                    repayCountList.add("0");

                    //新增还款本金
                    addRepayAmountList.add("0.00");

                    //新增还款笔数
                    addRepayCountList.add("0");

                }else {
                    Map<String, Object> dataMap = echartDataMap.get(xixa+dimension.get("dictValue"));
                    if(dataMap.get("dictValue").equals(dimension.get("dictValue"))){
                        //添加累计贷款本金余额
                        balanceAList.add(dataMap.get("balanceAmount"));

                        //累计贷款笔数
                        tCountList.add(dataMap.get("totalCount"));

                        //累计贷款本金金额
                        tAmountList.add(dataMap.get("totalAmount"));

                        //新增贷款本金
                        addAmountList.add(dataMap.get("addAmount"));

                        //新增贷款笔数
                        addCountList.add(dataMap.get("addCount"));

                        //累计还款本金
                        repayAmountList.add(dataMap.get("repayPrintAmount"));

                        //累计还款笔数
                        repayCountList.add(dataMap.get("repayCount"));

                        //新增还款本金
                        addRepayAmountList.add(dataMap.get("addRepayPrintAmount"));

                        //新增还款笔数
                        addRepayCountList.add(dataMap.get("addRepayCount"));

                    }

                }
            }

            //累计贷款余额
            HashMap<String, Object> balanceData = new HashMap<>();
            balanceData.put("name",dimension.get("name")+"贷款余额");
            balanceData.put("type","bar");
            balanceData.put("stack","abc");
            balanceData.put("data",balanceAList);
            //累计贷款笔数
            HashMap<String, Object> totalData = new HashMap<>();
            totalData.put("name",dimension.get("name")+"在贷笔数");
            totalData.put("type","bar");
            totalData.put("stack","abc");
            totalData.put("data",tCountList);
            //累计贷款金额
            HashMap<String, Object> amountData = new HashMap<>();
            amountData.put("name",dimension.get("name")+"贷款金额");
            amountData.put("type","bar");
            amountData.put("stack","abc");
            amountData.put("data",tAmountList);
            //新增贷款本金
            HashMap<String, Object> addMountData = new HashMap<>();
            addMountData.put("name",dimension.get("name")+"新增贷款本金");
            addMountData.put("type","bar");
            addMountData.put("stack","abc");
            addMountData.put("data",addAmountList);

            //新增贷款笔数
            HashMap<String, Object> addCountData = new HashMap<>();
            addCountData.put("name",dimension.get("name")+"新增贷款笔数");
            addCountData.put("type","bar");
            addCountData.put("stack","abc");
            addCountData.put("data",addCountList);
            //累计还款本金
            HashMap<String, Object> repayAmountData = new HashMap<>();
            repayAmountData.put("name",dimension.get("name")+"累计还款本金");
            repayAmountData.put("type","bar");
            repayAmountData.put("stack","abc");
            repayAmountData.put("data",repayAmountList);
            //累计还款笔数
            HashMap<String, Object> repayCountData = new HashMap<>();
            repayCountData.put("name",dimension.get("name")+"累计还款笔数");
            repayCountData.put("type","bar");
            repayCountData.put("stack","abc");
            repayCountData.put("data",repayCountList);
            //新增还款金额
            HashMap<String, Object> addRepayAmountData = new HashMap<>();
            addRepayAmountData.put("name",dimension.get("name")+"新增还款金额");
            addRepayAmountData.put("type","bar");
            addRepayAmountData.put("stack","abc");
            addRepayAmountData.put("data",addRepayAmountList);
            //新增还款笔数
            HashMap<String, Object> addRepayCountData = new HashMap<>();
            addRepayCountData.put("name",dimension.get("name")+"新增还款笔数");
            addRepayCountData.put("type","bar");
            addRepayCountData.put("stack","abc");
            addRepayCountData.put("data",addRepayCountList);
//        1、添加累计贷款余额与笔数图标显示，
            balanceAndTotalData.add(balanceData);
            balanceAndTotalData.add(totalData);
//        2、添加累计贷款本金金额与笔数图表显示
            amountAndTotalData.add(amountData);
            amountAndTotalData.add(totalData);
//        3、新增贷款本金金额与笔数图表显示
            addAmountAndTotalData.add(addMountData);
            addAmountAndTotalData.add(addCountData);
//        4、累计还款本金与笔数图表显示
            totalPrintAmountAndTotalData.add(repayAmountData);
            totalPrintAmountAndTotalData.add(repayCountData);
//        5、新增还款本金与笔数图表显示
            addRepayAmountAndTotalData.add(addRepayAmountData);
            addRepayAmountAndTotalData.add(addRepayCountData);
        }


        HashMap<String, Object> returnData = new HashMap<>();

        returnData.put("balanceAndTotalData",balanceAndTotalData);
        returnData.put("amountAndTotalData",amountAndTotalData);
        returnData.put("addAmountAndTotalData",addAmountAndTotalData);
        returnData.put("totalPrintAmountAndTotalData",totalPrintAmountAndTotalData);
        returnData.put("addRepayAmountAndTotalData",addRepayAmountAndTotalData);
        returnData.put("xiaxis",xixas);
        returnData.putAll(map);
        returnData.put("weekcountxaxis",strings);
        returnData.put("monthcountxaxis",monthEndDate);
        returnData.put("yearcountxaxis",yearXaxisData);
        return returnData;

    }


    /**
     * 周xaxis数据
     *
     * @param weeks 周
     * @return {@link List}<{@link String}>
     */
    public List<String> getWeekXaxisData(int weeks){
        //由于不包括本周 所以得-1 然后在得到数组后再获取本周的日期放入数组即可
        weeks = weeks-1;
        ArrayList<String> datas = new ArrayList<>();
        for (int i = weeks; i>=0 ;i--){
            datas.add(com.ruoyi.common.utils.DateUtils.getWeekTimeSection(i));
        }
        return datas;
    }

    /**
     * 获取月
     *
     * @param months 个月
     * @return {@link List}<{@link String}>
     */
    public List<String> getMonthXaxisData(int months){
        //由于不包括本周 所以得-1 然后在得到数组后再获取本周的日期放入数组即可
        months = months-1;
        ArrayList<String> datas = new ArrayList<>();
        for (int i = months; i>=0 ;i--){
            datas.add(com.ruoyi.common.utils.DateUtils.getLastMonth(i));
        }
        return datas;
    }

    /**
     * 获取年
     *
     * @param years 年
     * @return {@link List}<{@link String}>
     */
    public List<String> getYearXaxisData(int years){
        //由于不包括本周 所以得-1 然后在得到数组后再获取本周的日期放入数组即可
        years = years-1;
        ArrayList<String> datas = new ArrayList<>();
        for (int i = years; i>=0 ;i--){
            datas.add(com.ruoyi.common.utils.DateUtils.getLastYear(i));
        }
        return datas;
    }



    /**
     * 周xaxis数据
     *
     * @param weeks 周
     * @return {@link List}<{@link String}>
     */
    public List<String> getWeekendXaxisData(int weeks){
        //由于不包括本周 所以得-1 然后在得到数组后再获取本周的日期放入数组即可
        weeks = weeks-1;
        ArrayList<String> datas = new ArrayList<>();
        for (int i = weeks; i>=0 ;i--){
            datas.add(com.ruoyi.common.utils.DateUtils.getWeekendTimeSection(i));
        }
        return datas;
    }

    /**
     * 获取月
     *
     * @param months 个月
     * @return {@link List}<{@link String}>
     */
    public List<String> getMonthendXaxisData(int months){
        //由于不包括本周 所以得-1 然后在得到数组后再获取本周的日期放入数组即可
        months = months-1;
        ArrayList<String> datas = new ArrayList<>();
        for (int i = months; i>=0 ;i--){
            datas.add(com.ruoyi.common.utils.DateUtils.getLastMonthEndDate(i));
        }
        return datas;
    }

    /**
     * 获取年
     *
     * @param years 年
     * @return {@link List}<{@link String}>
     */
    public List<String> getYearendXaxisData(int years){
        //由于不包括本周 所以得-1 然后在得到数组后再获取本周的日期放入数组即可
        years = years-1;
        ArrayList<String> datas = new ArrayList<>();
        for (int i = years; i>=0 ;i--){
            datas.add(com.ruoyi.common.utils.DateUtils.getLastYearEnd(i));
        }
        return datas;
    }



}
