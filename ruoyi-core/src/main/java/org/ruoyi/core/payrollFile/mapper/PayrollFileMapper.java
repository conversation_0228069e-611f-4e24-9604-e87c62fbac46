package org.ruoyi.core.payrollFile.mapper;

import com.ruoyi.common.core.domain.entity.SysDictData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.payrollFile.domain.PayrollFile;
import org.ruoyi.core.payrollFile.domain.PayrollFileRecord;
import org.ruoyi.core.payrollFile.domain.PayrollProcess;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 薪资档案Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-18
 */
@Mapper
public interface PayrollFileMapper
{
    /**
     * 查询薪资档案
     *
     * @param id 薪资档案主键
     * @return 薪资档案
     */
    public PayrollFile selectPayrollFileById(String id);

    /**
     * 根据人员档案id
     *
     * @param id 薪资档案主键
     * @return 薪资档案
     */
    public PayrollFile selectPayrollFileByArchivesId(Long id);

    /**
     * 查询薪资档案列表
     *
     * @param payrollFile 薪资档案
     * @return 薪资档案集合
     */
    public List<PayrollFile> selectPayrollFileList(PayrollFile payrollFile);

    /**
     * 新增薪资档案
     *
     * @param payrollFile 薪资档案
     * @return 结果
     */
    public int insertPayrollFile(PayrollFile payrollFile);

    /**
     * 修改薪资档案
     *
     * @param payrollFile 薪资档案
     * @return 结果
     */
    public int updatePayrollFile(PayrollFile payrollFile);

    /**
     * 删除薪资档案
     *
     * @param id 薪资档案主键
     * @return 结果
     */
    public int deletePayrollFileById(String id);

    /**
     * 批量删除薪资档案
     *
     * @param delData 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePayrollFileByIds(List<PayrollFile> delData);

    /**
     * 批量删除薪资保存
     *
     * @param delData 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePayrollFileRecordByPayrollfileIds(List<PayrollFile> delData);

    /**
     * 批量新增薪资保存
     *
     * @param payrollFileRecordList 薪资保存列表
     * @return 结果
     */
    public int batchPayrollFileRecord(List<PayrollFileRecord> payrollFileRecordList);


    /**
     * 通过薪资档案主键删除薪资保存信息
     *
     * @param id 薪资档案ID
     * @return 结果
     */
    public int deletePayrollFileRecordByPayrollfileId(String id);

    int selectCountByCreateTime(String createTime);

    int selectPayrollFileByIdCard(String idCard);

    List<PayrollFile> selectPayrollFileByIds(@Param("ids") String[] ids);

    List<Long> selectPostDeptByUserId(Long userId);

    List<Long> selectRoleDeptByUserId(Long userId);

    List<String> selectDeptByDeptIds(List<Long> deptId);

    List<String> selectUnitByDeptIds(List<Long> deptId);

    List<String> selectPostByUserId(Long userId);

    String selectSalaryAdjustment(Long userId);

    List<PayrollFile> selectListByIdCard(String[] idCards);

    int insertProcess(PayrollProcess payrollProcess);

    BigDecimal selectOldMoney(String workId);

    PayrollProcess selectPayrollFileByBodyId(String id);

    String selectProcessByFlowId(String flowId);

    int updateProcess(@Param("newRecordId") String newRecordId,@Param("date") Date date);

    int updateBodyBill(@Param("newRecordId") String newRecordId,@Param("date") Date date);

    int updatePayrollFileByBodyId(String newRecordId);

    int updateRecordById(String id);

    int checkAutid(String idCard);

    String selectBillIdByProcessId(String processId);
}
