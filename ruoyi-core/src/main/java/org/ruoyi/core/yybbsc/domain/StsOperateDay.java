package org.ruoyi.core.yybbsc.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 每日运营统计对象 sts_operate_day
 *
 * <AUTHOR>
 * @date 2022-12-19
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
public class StsOperateDay extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 业务日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "业务日期", width = 30, dateFormat = "yyyy/MM/dd")
    private Date reconDate;

    /** 产品代码，富邦：0，北部湾:1 */
//    @Excel(name = "产品代码，富邦：0，北部湾:1")
    private String productNo;

    /** 放款金额 */
    @Excel(name = "放款金额")
    private BigDecimal loanAmt;

    /** 实还本金 */
    @Excel(name = "实还本金")
    private BigDecimal actPrintAmt;

    /** 利息 */
    @Excel(name = "利息")
    private BigDecimal intAmt;

    /** 罚息 */
    @Excel(name = "罚息")
    private BigDecimal ointAmt;

    /** 复利 */
    @Excel(name = "复利")
    private BigDecimal flAmt;

    /** 提前还款违约金 */
    @Excel(name = "提前还款违约金")
    private BigDecimal advDefineAmt;

    /** 活动抵扣金额 */
    @Excel(name = "活动抵扣金额")
    private BigDecimal deductAmt;

    /** 红线减免金额 */
    @Excel(name = "红线减免金额")
    private BigDecimal reduceAmt;

    /** 用户实还息费 */
    @Excel(name = "用户实还息费")
    private BigDecimal actIntAmt;

    /** 借条分润 */
    @Excel(name = "借条分润")
    private BigDecimal jtFrAmt;

    /** 中保分账 */
    @Excel(name = "中保分账")
    private BigDecimal fzAmt;

    /** 中保分润 */
    @Excel(name = "中保分润")
    private BigDecimal zbFrAmt;

    /** 资金贷款余额 */
    @Excel(name = "资金贷款余额")
    private BigDecimal fundBalanceAmt;

    /** 客户贷款余额 */
    @Excel(name = "客户贷款余额")
    private BigDecimal userBalanceAmt;

    /** 利润累计 */
    @Excel(name = "利润累计")
    private BigDecimal accumProfitAmt;

    /** 代偿本金 */
    @Excel(name = "代偿本金")
    private BigDecimal compensatePrintAmt;

    /** 代偿利息 */
    @Excel(name = "代偿利息")
    private BigDecimal compensateIntAmt;

    /** 代偿罚息 */
    @Excel(name = "代偿罚息")
    private BigDecimal compensateOintAmt;

    /** 代偿总计 */
    @Excel(name = "代偿总计")
    private BigDecimal compensateTotalAmt;

    /** 代偿后还款本金 */
    @Excel(name = "代偿后还款本金")
    private BigDecimal compensateRepayPrintAmt;

    /** 代偿后还款总金额 */
    @Excel(name = "代偿后还款总金额")
    private BigDecimal compensateRepayTotalAmt;
    /** 代偿后还款总金额 */
    @Excel(name = "备注")
    private String remarks;

    private String remark;

    private String orderByColumn; // 排序字段
    private String isAsc; // 排序规则
}
