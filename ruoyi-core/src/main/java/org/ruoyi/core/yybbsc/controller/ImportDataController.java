package org.ruoyi.core.yybbsc.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.entity.SysDictTypeData;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.VerifyCodeUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.system.service.ISysDictTypeService;
import com.ruoyi.system.service.impl.SysDictDataRefServiceImpl;

import net.sourceforge.pinyin4j.PinyinHelper;

import org.ruoyi.core.domain.DData;
import org.ruoyi.core.service.DDataService;
import org.ruoyi.core.yybbsc.domain.ImportDataTemplate;
import org.ruoyi.core.yybbsc.domain.ImportDataTemplates;
import org.ruoyi.core.yybbsc.domain.dto.ImportData;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 外部系统导入数据Controller
 *
 * <AUTHOR> @date 2023-2-14
 */
@RestController
@RequestMapping("/system/iData")
public class ImportDataController extends BaseController {

    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
   /* name:
    assetName: "合作方"
    fundName: "资金方"
    guaranteeName: "担保公司"
    productName: "产品"
    systemName: "外部系统"
    label:
    dictionariesLabel: "数据平台"*/

    private final String assetNameV = "合作方";
    private final String fundNameV = "资金方";
    private final String guaranteeNameV = "担保公司";
    private final String productNameV = "产品";
    private final String systemNameV = "外部系统";
    private final String dictionariesLabelV = "数据平台";
    @Autowired
    private ISysDictTypeService dictTypeService;
    @Autowired
    private ISysDictDataService dictDataService;
    @Resource
    private SysDictDataRefServiceImpl sysDictDataRefServiceImpl;

    @Autowired
    private DDataService dDataService;
  /*  //担保公司
            dData.setCustNo(guaranteeDictLabel);
    //合作方
            dData.setPartnerNo(assetDictLabel);
    //资金方
            dData.setFundNo(fundDictLabel);
    //产品
            dData.setProductNo(productDictLabel);*/

    @Log(title = "外部系统导入数据", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @Transactional
    public AjaxResult importData(MultipartFile file) throws Exception {

        //查询外部系统编码
        SysDictTypeData label2 = getLabel(systemNameV, dictionariesLabelV);
        String fileSuffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
        if (!fileSuffix.equals(".xlsx")) {
            return AjaxResult.error("上传格式错误");
        }
        ExcelUtil<ImportData> util = new ExcelUtil<ImportData>(ImportData.class);
        InputStream inputStream = file.getInputStream();
        //
        boolean aBoolean = util.importExcelHead("", inputStream, 0, ImportDataTemplate.class);
        if (aBoolean == false) {
            return AjaxResult.error("上传失败");
        }
        //
        List<ImportData> userList = util.importExcelRetainDecimals("", file.getInputStream(), 0);
        if (CollectionUtils.isEmpty(userList)) {
            return AjaxResult.error("导入文件为空");
        }
        List<ImportData> userList1 = userList.stream().sorted(Comparator.comparing(ImportData::getReconDate)).collect(Collectors.toList());//.reversed()
        Integer counts = 0;
        for (ImportData importData1 : userList1) {
            String assetDictLabel = null;
            String fundDictLabel = null;
            String guaranteeDictLabel = null;
            String productDictLabel = null;
            //根据类型+名称查
            if (label2 == null) {
                return AjaxResult.error("联系管理员先添加完系统平台字典数据后再导入");//mashuo edit 20230719 修改提示描述
            } else {
                importData1.setPlatformNo(label2.getDictValue());
            }

            SysDictTypeData guaranteeNameLabel = getLabel(guaranteeNameV, importData1.getGuaranteeName());
            if (guaranteeNameLabel == null) {
                return AjaxResult.error("联系管理员先添加完担保公司字典数据后再导入");//mashuo edit 20230719 修改提示描述
            } else {
                guaranteeDictLabel = guaranteeNameLabel.getDictValue();
            }
            SysDictTypeData label = getLabel(assetNameV, importData1.getAssetName());
            if (label == null) {
                return AjaxResult.error("联系管理员先添加完合作方字典数据后再导入");//mashuo edit 20230719 修改提示描述
            } else {
                assetDictLabel = label.getDictValue();
            }
            SysDictTypeData label1 = getLabel(fundNameV, importData1.getFundName());
            if (label1 == null) {
                return AjaxResult.error("联系管理员先添加完资金方字典数据后再导入");//mashuo edit 20230719 修改提示描述
            } else {
                fundDictLabel = label1.getDictValue();
            }
            
            //mashuo edit 20230719 产品字典自动添加   begin
            String jointProductName=importData1.getGuaranteeName()+"-"+importData1.getAssetName()+"-"+importData1.getFundName()+"-"+importData1.getProductName();
            SysDictTypeData productNameLabel = getLabel(productNameV, jointProductName);
            if (productNameLabel == null) {
//                return AjaxResult.error("联系管理员先添加完产品字典数据后再导入");
            	//判断即将添加到字典编码是否存在重复情况
            	String jointProductNo=getPinYinHeadChar(jointProductName).toUpperCase();
            	String mDictLabel=dictDataService.selectDictLabel("product_no", jointProductNo);//查询拼音首字母大写是否存在重复情况
            	if(mDictLabel != null) {
            		jointProductNo=jointProductNo+"-"+VerifyCodeUtils.generateVerifyCode(5);
            	}
            	//新增字典数据
            	SysDictData dictData=new SysDictData();
            	dictData.setDictType("product_no");
            	dictData.setDictValue(jointProductNo);
            	dictData.setDictLabel(jointProductName);
            	dictData.setDictSort(1699L);
            	dictData.setListClass("default");
            	dictDataService.insertDictData(dictData);
            	productDictLabel = jointProductNo;
            } else {
                productDictLabel = productNameLabel.getDictValue();
            }
            //mashuo edit 20230719 产品字典自动添加  end
            //正常到期本金 +  追偿本金
            BigDecimal add = importData1.getAddRepayPrintAmount().add(importData1.getAddRepay8PrinAmount());
            importData1.setAddRepayPrintAmount(add);//当期新增还款本金
            // 正常到期利息 +
            BigDecimal add1 = importData1.getAddRepayIntAmount().add(importData1.getAddRepay8IntAmount());
            importData1.setAddRepayIntAmount(add1);//当期新增还款利息
            // 正常到期保费 +
            BigDecimal add2 = importData1.getAddRepayGuaranteeAmount().add(importData1.getAddRepay8GuaranteeAmount());
            importData1.setAddRepayGuaranteeAmount(add2);//当期新增还款担保费

            //代偿总金额
            importData1.setAddCompensateAmount(importData1.getAddCompensatePrintAmount().add(importData1.getAddCompensateIntAmount()).add(importData1.getAddCompensateGuaranteeAmount()));
            //追偿总金额
            importData1.setAddRepay8Amount(importData1.getAddRepay8IntAmount().add(importData1.getAddRepay8PrinAmount().add(importData1.getAddRepay8GuaranteeAmount())));
//
            importData1.setCustNo(guaranteeDictLabel);
            importData1.setPartnerNo(assetDictLabel);
            importData1.setFundNo(fundDictLabel);
            importData1.setProductNo(productDictLabel);


            //统计日期
            Date reconDate = importData1.getReconDate();
            //查询历史数据
            DData dData = new DData();
            //担保公司
            dData.setCustNo(guaranteeDictLabel);
            //查询外部系统编码
            dData.setPlatformNo(label2.getDictValue());
            //合作方
            dData.setPartnerNo(assetDictLabel);
            //资金方
            dData.setFundNo(fundDictLabel);
            //产品
            dData.setProductNo(productDictLabel);
            dData.setReconDate(reconDate);
            //
            //累计贷款本金
            BigDecimal loans = new BigDecimal(0);
            //累计还款本金
            BigDecimal repayment = new BigDecimal(0);
            //累计代偿本金
            BigDecimal compensateAmount = new BigDecimal(0);
            //累计追偿本金
            BigDecimal repay8PrinAmount = new BigDecimal(0);

            List<DData> importData = dDataService.selectDdatas(dData);
            if (!CollectionUtils.isEmpty(importData)) {
                for (DData importDatum : importData) {
                    BigDecimal addAmount = importDatum.getAddAmount();
                    loans = loans.add(addAmount);
                    BigDecimal addRepayPrintAmount = importDatum.getAddRepayPrintAmount();
                    repayment = repayment.add(addRepayPrintAmount);
                    BigDecimal addCompensatePrintAmount = importDatum.getAddCompensatePrintAmount();
                    compensateAmount = compensateAmount.add(addCompensatePrintAmount);
                    BigDecimal addRepay8PrinAmount = importDatum.getAddRepay8PrinAmount();
                    repay8PrinAmount = repay8PrinAmount.add(addRepay8PrinAmount);
                }
            }
            //累计贷款本金
            BigDecimal add3 = loans.add(importData1.getAddAmount());
            importData1.setTotalAmount(add3);
            loans = new BigDecimal(0.0);
            //累计还款本金
            BigDecimal add4 = repayment.add(importData1.getAddRepayPrintAmount());
            importData1.setTotalRepayPrintAmount(add4);
            repayment = new BigDecimal(0.0);
            //累计代偿本金
            BigDecimal add5 = compensateAmount.add(importData1.getAddCompensatePrintAmount());
            importData1.setTotalCompensatePrintAmount(add5);
            compensateAmount = new BigDecimal(0.0);
            //累计追偿本金
            BigDecimal add6 = repay8PrinAmount.add(importData1.getAddRepay8PrinAmount());


            //累计贷款本金-累计还款本金
            BigDecimal subtracts = add3.subtract(add4);
            //历史累计-累计贷款本金余额（元）
            BigDecimal subtract = subtracts.subtract(add6);
            importData1.setTotalBalanceAmount(importData1.getLoanBal());//还款信息-用户在贷
            //历史累计-还款计划贷款本金余额
            BigDecimal subtract2 = subtracts.subtract(add6);
            repay8PrinAmount = new BigDecimal(0.0);
            importData1.setTotalPlanBalanceAmt(importData1.getLoanBal());//还款计划-用户在贷

            BigDecimal subtract1 = subtracts.subtract(add5);
            //历史累计-资金贷款本金余额（元）  total_fund_balance_amt
            importData1.setTotalFundBalanceAmt(importData1.getLoanBal());//还款信息-资方在贷
            // 历史累计-还款计划资金贷款本金余额（元） total_plan_fund_balance_amt
            importData1.setTotalPlanFundBalanceAmt(importData1.getLoanBal());//还款计划-资方在贷



            //
            importData1.setTotalBeginDate(new Date());
            importData1.setTotalEndDate(new Date());
            importData1.setAddBeginDate(new Date());
            importData1.setAddEndDate(new Date());
            importData1.setTotalAverageIrr(new BigDecimal(0));
            List<DData> dData2 = dDataService.selectDdata(dData);
            if (CollectionUtils.isEmpty(dData2)) {
                importData1.setCreateTime(new Date());
                importData1.setUpdateTime(new Date());
                importData1.setIsMapping("Y");
                dDataService.insertDData(importData1);
            } else if (dData2.size() == 1) {
                Long id = dData2.get(0).getId();
                importData1.setId(id);
                importData1.setUpdateTime(new Date());
                dDataService.updateDData(importData1);
            } else {
                return AjaxResult.error("数据库多条相同数据");
            }
            counts++;
        }
        //数据整理
        setSimpleDateFormat(label2.getDictValue());
        //刷新级联字典及缓存
        sysDictDataRefServiceImpl.operationSysDictDataRef();
        return AjaxResult.success(counts);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<ImportDataTemplates> util = new ExcelUtil<ImportDataTemplates>(ImportDataTemplates.class);
        util.importTemplateExcel(response, "模板");
    }

    /**
     * 查询标签
     *
     * @param assetNameV
     * @param assetName
     * @return
     */

    public SysDictTypeData getLabel(String assetNameV, String assetName) {
        SysDictTypeData dictType = new SysDictTypeData();
        dictType.setDictName(assetNameV);
        dictType.setDictLabel(assetName);
        dictType.setStatus("0");
        dictType.setStatusd("0");
        SysDictTypeData assetNameLabel = dictTypeService.selectDictDictValue(dictType);
        if (assetNameLabel == null) {
            return null;
        } else {
            return assetNameLabel;
        }
    }


    public static Integer daysCountOfMonth(String time) {
        /*  time = "2020-12-26";*/
        int year = Integer.parseInt(time.substring(0, 4));  //截取出年份，并将其转化为int
        int month1 = Integer.parseInt(time.substring(5, 7));    //截去除月份，并将其转为int
        int month2 = Integer.parseInt(time.substring(8, 10));    //截去除月份，并将其转为int
        System.out.println(month2);
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR, year);    //设置年份
        cal.set(Calendar.MONTH, month1 - 1);  //设置月份
        int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        int i = lastDay - month2;
        System.out.println(i);
        return i;
    }


    /* String format = simpleDateFormat.format(reconDate);*/
    /*  Integer integer = daysCountOfMonth(format);*/


    public void setSimpleDateFormat(String dictValue) {
        DData dData1 = new DData();
        dData1.setPlatformNo(dictValue);
        //数据平台下的数据
        List<DData> dData3 = dDataService.selectDdatabf(dData1);
        Set<String> sb2 = new HashSet<>();
        //产品类型
        for (DData dData : dData3) {
            String sb = "";
            String fundNo = dData.getFundNo();
            sb += fundNo + ",";
            String partnerNo = dData.getPartnerNo();
            sb += partnerNo + ",";
            String custNo = dData.getCustNo();
            sb += custNo + ",";
            String productNo = dData.getProductNo();
            sb += productNo + ",";
            sb2.add(sb);
            sb = "";

        }
        //类型产品数量
        for (String s : sb2) {
            String[] split = s.split(",");
            DData dData = new DData();
            String fundNo = split[0];
            String partnerNo = split[1];
            String custNo = split[2];
            String productNo = split[3];
            //担保公司
            dData.setCustNo(custNo);
            //合作方
            dData.setPartnerNo(partnerNo);
            //资金方
            dData.setFundNo(fundNo);
            //产品
            dData.setProductNo(productNo);
            //外部系统编号
            dData.setPlatformNo(dictValue);
            // 每日一种产品的所有时间
            Set<String> datacp = new HashSet<>();
            //每一种查询数据
         //   List<DData> datass = dDataService.selectDdatabf(dData);
            List<DData> datass = dDataService.queryAllbf(dData);
            // 当前已有的数据进行更新   累计值
            for (DData data : datass) {
             /*   //正常到期本金 +  追偿本金
                BigDecimal add = data.getAddRepayPrintAmount().add(data.getAddRepay8PrinAmount());
                data.setAddRepayPrintAmount(add);
                // 正常到期利息 +
                BigDecimal add1 = data.getAddRepayIntAmount().add(data.getAddRepay8IntAmount());
                data.setAddRepayIntAmount(add1);
                // 正常到期保费 +
                BigDecimal add2 = data.getAddRepayGuaranteeAmount().add(data.getAddRepay8GuaranteeAmount());
                data.setAddRepayGuaranteeAmount(add2);*/

                //代偿总金额
                data.setAddCompensateAmount(data.getAddCompensatePrintAmount().add(data.getAddCompensateIntAmount()).add(data.getAddCompensateGuaranteeAmount()));
                //追偿总金额
                data.setAddRepay8Amount(data.getAddRepay8IntAmount().add(data.getAddRepay8PrinAmount().add(data.getAddRepay8GuaranteeAmount())));
//
            /*    data.setCustNo(data);
                data.setPartnerNo(assetDictLabel);
                data.setFundNo(fundDictLabel);
                data.setProductNo(productDictLabel);*/

                //统计日期
                Date reconDate = data.getReconDate();
                //查询历史数据
                DData dDataas = new DData();
                //担保公司
                dDataas.setCustNo(data.getCustNo());
                //查询外部系统编码
                dDataas.setPlatformNo(data.getPlatformNo());
                //合作方
                dDataas.setPartnerNo(data.getPartnerNo());
                //资金方
                dDataas.setFundNo(data.getFundNo());
                //产品
                dDataas.setProductNo(data.getProductNo());
                dDataas.setReconDate(reconDate);
                //查询累计数据
                DData importDatum = dDataService.selectDdataslj(dDataas);
                //累计贷款本金
                BigDecimal totalAmount = importDatum.getTotalAmount();
                //累计还款本金
                BigDecimal totalRepayPrintAmount = importDatum.getTotalRepayPrintAmount();
                //累计代偿本金
                BigDecimal totalCompensatePrintAmount = importDatum.getTotalCompensatePrintAmount();
                //累计追偿本金
                BigDecimal totalRepay8PrinAmount = importDatum.getAddRepay8PrinAmount();

                //累计贷款本金
                data.setTotalAmount(totalAmount);
                //累计还款本金
                data.setTotalRepayPrintAmount(totalRepayPrintAmount);
                //累计代偿本金
                data.setTotalCompensatePrintAmount(totalCompensatePrintAmount);

                //累计追偿本金
                /*    BigDecimal add6 = repay8PrinAmount.add(dDataas.getAddRepay8PrinAmount());*/
                //累计贷款本金-累计还款本金
                BigDecimal subtracts = totalAmount.subtract(totalRepayPrintAmount);
                //历史累计-累计贷款本金余额（元）
               /* BigDecimal subtract = subtracts.subtract(totalRepay8PrinAmount);*/
                //data.setTotalBalanceAmount(subtracts);
                //历史累计-还款计划贷款本金余额
               /* BigDecimal subtract2 = subtracts.subtract(totalRepay8PrinAmount);*/

                //data.setTotalPlanBalanceAmt(subtracts);

            BigDecimal subtract1 = subtracts.subtract(totalCompensatePrintAmount).add(totalRepay8PrinAmount);
              /*  BigDecimal subtract1 = subtracts.add(totalRepay8PrinAmount);*/
                //历史累计-资金贷款本金余额（元）  total_fund_balance_amt
                //data.setTotalFundBalanceAmt(subtract1);
                // 历史累计-还款计划资金贷款本金余额（元） total_plan_fund_balance_amt
                //data.setTotalPlanFundBalanceAmt(subtract1);
                //
                data.setTotalBeginDate(new Date());
                data.setTotalEndDate(new Date());
                data.setAddBeginDate(new Date());
                data.setAddEndDate(new Date());
                data.setTotalAverageIrr(new BigDecimal(0));
               /* List<DData> dData2 = dDataService.selectDdata(dData);
                if (CollectionUtils.isEmpty(dData2)) {*/
                /*      dDataas.setCreateTime(new Date());*/
                data.setUpdateTime(new Date());
                data.setIsMapping("Y");
                dDataService.update(data);
                /* dDataService.insertDData(importData1);*/
                //多少天的数据存在
                String sb = "";
                String fundNo1 = data.getFundNo();
                sb += fundNo1 + ",";
                String partnerNo1 = data.getPartnerNo();
                sb += partnerNo1 + ",";
                String custNo1 = data.getCustNo();
                sb += custNo1 + ",";
                String productNo1 = data.getProductNo();
                sb += productNo1 + ",";
                Date reconDateb = data.getReconDate();
                String format = simpleDateFormat.format(reconDateb);
                sb += format + ",";
                datacp.add(sb);
                sb = "";
            }
            //最早数据
            //最早统计时间
            Date reconDate = datass.get(0).getReconDate();
            DData data = datass.get(0);
            Date date1 = new Date();
            long time1 = date1.getTime();
            long time2 = reconDate.getTime();
            long l1 = time1 - time2;
            long l2 = l1 / 86400000l;

            long time = reconDate.getTime();
            for (int i = 0; i < l2; i++) {

                //+一天插入
                time += 86400000l;
                Date date = new Date(time);
                String format = simpleDateFormat.format(date);
                System.out.println(format);

                String sb = "";
                String fundNo1 = data.getFundNo();
                sb += fundNo1 + ",";
                String partnerNo1 = data.getPartnerNo();
                sb += partnerNo1 + ",";
                String custNo1 = data.getCustNo();
                sb += custNo1 + ",";
                String productNo1 = data.getProductNo();
                sb += productNo1 + ",";
                Date reconDate1 = date;
                String format1 = simpleDateFormat.format(reconDate1);
                sb += format1 + ",";
                //是否包含已有数据
                boolean contains = datacp.contains(sb);
                sb = "";
                DData dData2 = null;
                if (contains) {
                    for (DData datass1 : datass) {
                        Long time3 = datass1.getReconDate().getTime();
                        //  Long time4 = data.getReconDate().getTime();
                        Long time4 = date.getTime();
                        if (time3.equals(time4)) {
                            dData2 = datass1;
                            break;
                        }
                    }
                     /* DData dDatas = new DData();
                      dDatas.setReconDate(date);
                      dDatas.setCustNo(data.getCustNo()); //担保公司
                      dDatas.setPartnerNo(data.getPartnerNo());  //合作方
                      dDatas.setFundNo(data.getFundNo());  //资金方
                      dDatas.setProductNo(data.getProductNo()); //产品
                      dDatas.setPlatformNo(dictValue);//外部系统编码
                    dData2  = dDataService.selectDdatabf1(dDatas);*/
                }
                if (dData2 == null) {
                    insertion(datass,  dictValue, date, data);
                } else {
                    BeanUtils.copyProperties(dData2, data);
                }
            }
            //对数据进行插入修改
            /* dDataService.selectDdatasinsertorUpdate(datass);*/



        }


    }


    public void insertion(List<DData> datass, String platformno, Date date, DData data) {
        DData dData2 = new DData();
      BeanUtils.copyProperties(data,dData2);
        Date date1 = new Date();
        dData2.setTotalBeginDate(date1);
        dData2.setTotalEndDate(date1);
         dData2.setAddBeginDate(date1);
         dData2.setAddEndDate(date1);
        dData2.setId(null);
        dData2.setReconDate(date);
        dData2.setPlatformNo(platformno);
        dData2.setCreateTime(new Date());
        dData2.setUpdateTime(new Date());
        dData2.setIsMapping("Y");
       /* datass.add(dData2);*/
           dDataService.insertbf(dData2);
    }
    
    
    /**
     * 根据字符串获取拼音小写缩写
     *
     * @param str str
     * @return {@link String}
     */
    public static String getPinYinHeadChar(String str)
    {
        String convert = "";
        for (int j = 0; j < str.length(); j++)
        {
            char word = str.charAt(j);
            String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(word);
            if (pinyinArray != null)
            {
                convert += pinyinArray[0].charAt(0);
            } else
            {
                convert += word;
            }
        }

        return convert;
    }

}