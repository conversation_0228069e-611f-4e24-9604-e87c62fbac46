package org.ruoyi.core.yybbsc.excelutils;

import com.google.common.collect.Maps;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.openxmlformats.schemas.spreadsheetml.x2006.main.CTMergeCell;
import org.openxmlformats.schemas.spreadsheetml.x2006.main.CTMergeCells;
import org.openxmlformats.schemas.spreadsheetml.x2006.main.CTWorksheet;
import org.ruoyi.core.yybbsc.domain.vo.StsIncomeForecastInfoVo;
import org.ruoyi.core.yybbsc.domain.vo.StsIncomeForecastLoanVo;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 导出收入预测报表
 *
 * @Description
 * <AUTHOR>
 * @Date 2023/1/28 9:32
 **/
public class ExportExcelIncomeForecast {

    private static Map<String,CellStyle> cellStyleMap = Maps.newHashMap();
    private static Map<String,Font> fontMap = Maps.newHashMap();

    public static void exportIncomeForecast(HttpServletResponse response, List<StsIncomeForecastInfoVo> list) throws IOException {
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        //列数
        int colSize2 = 26;
        int sheetIndex = 0;
        CTWorksheet ctWorksheet;
        String sheetName;
        SXSSFSheet sheet;
        SXSSFRow sheetHeadRow;
//        SXSSFRow sheetHeadRowRepay;
//        SXSSFRow sheetHeadRowForecast;
        SXSSFCell cell1;
        SXSSFRow sheetTitleRow;
//        SXSSFRow sheetTitleRow2;
//        SXSSFRow sheetTitleRow3;
        SXSSFCell headCell;
        CellRangeAddress cellAddresses = new CellRangeAddress(0, 0, 0, 0);
        //list有几个，就有几个Sheet工作薄
        for (StsIncomeForecastInfoVo s:list) {
            //工作薄名，使用月份来作为Sheet工作薄名
            sheetName = s.getStsIncomeForecastTotalVo().getLoanMonth();
            //创建Sheet
            sheet = workbook.createSheet(sheetName);
            workbook.setSheetName(sheetIndex, sheetName);
            ctWorksheet = sheet.getWorkbook().getXSSFWorkbook().getSheetAt(sheetIndex).getCTWorksheet();
            sheetIndex++;
            //创建主标题行
            sheetTitleRow = sheet.createRow(0);
            for (int i = 0; i < colSize2; i++) {
                headCell = sheetTitleRow.createCell(i);
                switch (i) {
                    case 0: headCell.setCellValue("放款月份"); break;
                    case 1: headCell.setCellValue("放款金额"); break;
                    case 2: headCell.setCellValue("产品类型"); break;
                    case 3: headCell.setCellValue("期数"); break;
                    case 4: headCell.setCellValue("还款月份"); break;
                    case 5: headCell.setCellValue("还款本金"); break;
                    case 6: headCell.setCellValue("还款利息"); break;
                    case 7: headCell.setCellValue("还款罚息"); break;
                    case 8: headCell.setCellValue("还款复利"); break;
                    case 9: headCell.setCellValue("提前还款违约金"); break;
                    case 10: headCell.setCellValue("活动抵扣金额"); break;
                    case 11: headCell.setCellValue("红线减免金额"); break;
                    case 12: headCell.setCellValue("月末余额"); break;
                    case 13: headCell.setCellValue("技术服务费"); break;
                    case 14: headCell.setCellValue("实收息费"); break;
                    case 15: headCell.setCellValue("借条分润"); break;
                    case 16: headCell.setCellValue("中保分润"); break;
                    case 17: headCell.setCellValue("资金成本"); break;
                    case 18: headCell.setCellValue("中保收入"); break;
                    case 19: headCell.setCellValue("净收入"); break;
                    case 20: headCell.setCellValue("代偿总金额"); break;
                    case 21: headCell.setCellValue("净收入-代偿总金额"); break;
                    case 22: headCell.setCellValue("平均余额"); break;
                    case 23: headCell.setCellValue("FA"); break;
                    case 24: headCell.setCellValue("保证金成本"); break;
                    case 25: headCell.setCellValue("净收益"); break;
                    default: break;
                }
                //设置样式
                headCell.setCellStyle(getTitleFont(sheet.getWorkbook()));
            }

            //查找每个loanId出现的次数，key -> loanId     value -> 出现的次数
            Map<Long, Long> collect = s.getStsIncomeForecastLoanVoList().stream().collect(Collectors.groupingBy(StsIncomeForecastLoanVo::getLoanId, Collectors.counting()));

            //用map来记录每一个对象的开始索引以及合并的大小。
//            Map<Integer, Object> map1 = new HashMap<>();
            for (int i = 0; i < s.getStsIncomeForecastLoanVoList().size(); i++) {
                //创建数据行（第二行）
                sheetHeadRow = sheet.createRow(1 + i);
                //放款月份
                cell1 = sheetHeadRow.createCell(0);
                cell1.setCellValue(s.getStsIncomeForecastLoanVoList().get(i).getLoanMonth());
                //给这个单元格设置样式
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //放款金额
                cell1 = sheetHeadRow.createCell(1);
                cell1.setCellValue(s.getStsIncomeForecastLoanVoList().get(i).getLoanAmt() + "");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //产品类型
                cell1 = sheetHeadRow.createCell(2);
                cell1.setCellValue(s.getStsIncomeForecastLoanVoList().get(i).getProductType());
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //期数
                cell1 = sheetHeadRow.createCell(3);
                cell1.setCellValue(s.getStsIncomeForecastLoanVoList().get(i).getPhase());
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //还款月份
                cell1 = sheetHeadRow.createCell(4);
                cell1.setCellValue(s.getStsIncomeForecastLoanVoList().get(i).getRepaymentMonth());
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //还款本金
                cell1 = sheetHeadRow.createCell(5);
                cell1.setCellValue(s.getStsIncomeForecastLoanVoList().get(i).getRepaymentPrintAmount() + "");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //还款利息
                cell1 = sheetHeadRow.createCell(6);
                cell1.setCellValue(s.getStsIncomeForecastLoanVoList().get(i).getRepaymentIntAmount() + "");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //还款罚息
                cell1 = sheetHeadRow.createCell(7);
                cell1.setCellValue(s.getStsIncomeForecastLoanVoList().get(i).getRepaymentOintAmt() + "");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //还款复利
                cell1 = sheetHeadRow.createCell(8);
                cell1.setCellValue(s.getStsIncomeForecastLoanVoList().get(i).getRepaymentFlAmt() + "");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //提前还款违约金
                cell1 = sheetHeadRow.createCell(9);
                cell1.setCellValue(s.getStsIncomeForecastLoanVoList().get(i).getAdvDefineAmt() + "");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //活动抵扣金额
                cell1 = sheetHeadRow.createCell(10);
                cell1.setCellValue(s.getStsIncomeForecastLoanVoList().get(i).getDeductAmt() + "");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //红线减免金额
                cell1 = sheetHeadRow.createCell(11);
                cell1.setCellValue(s.getStsIncomeForecastLoanVoList().get(i).getReduceAmt() + "");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //月末余额
                cell1 = sheetHeadRow.createCell(12);
                cell1.setCellValue(s.getStsIncomeForecastLoanVoList().get(i).getMonthEndBalanceAmt() + "");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //技术服务费
                cell1 = sheetHeadRow.createCell(13);
                cell1.setCellValue(s.getStsIncomeForecastLoanVoList().get(i).getTechnicalServiceFee() + "");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //实收息费 给成-就行
                cell1 = sheetHeadRow.createCell(14);
                cell1.setCellValue("-");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //借条分润 给成-就行
                cell1 = sheetHeadRow.createCell(15);
                cell1.setCellValue("-");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //中保分润 给成-就行
                cell1 = sheetHeadRow.createCell(16);
                cell1.setCellValue("-");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //资金成本 给成-就行
                cell1 = sheetHeadRow.createCell(17);
                cell1.setCellValue("-");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //中报收入 给成-就行
                cell1 = sheetHeadRow.createCell(18);
                cell1.setCellValue("-");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //净收入 给成-就行
                cell1 = sheetHeadRow.createCell(19);
                cell1.setCellValue("-");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //代偿总金额 给成-就行
                cell1 = sheetHeadRow.createCell(20);
                cell1.setCellValue("-");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //净收入-代偿总金额 给成-就行
                cell1 = sheetHeadRow.createCell(21);
                cell1.setCellValue("-");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //平均余额 给成-就行
                cell1 = sheetHeadRow.createCell(22);
                cell1.setCellValue("-");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //FA 给成-就行
                cell1 = sheetHeadRow.createCell(23);
                cell1.setCellValue("-");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //保证金成本 给成-就行
                cell1 = sheetHeadRow.createCell(24);
                cell1.setCellValue("-");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //净收益 给成-就行
                cell1 = sheetHeadRow.createCell(25);
                cell1.setCellValue("-");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //进行loanId的计数
//                if (s.getStsIncomeForecastLoanVoList().get(i).getLoanId().equals(s.getStsIncomeForecastLoanVoList().get(i+1).getLoanId())) {
//                    //这个元素与下一个相同，说明可以计数
//                    mergeCount++;
//                    map1.put()
//                } else {
//                    mergeCount = 1;
//                }
            }
            //合并放款月份，找到list大小，不用减去1，因为第0行被标题占用
            int lastRow = s.getStsIncomeForecastLoanVoList().size();
            if (s.getStsIncomeForecastLoanVoList().size() > 1) {
//                sheet.addMergedRegion(new CellRangeAddress(1, lastRow, 0, 0));
                cellAddresses.setFirstRow(1);
                cellAddresses.setLastRow(lastRow);
                cellAddresses.setFirstColumn(0);
                cellAddresses.setLastColumn(0);
                addMergedReigon(ctWorksheet, cellAddresses);
//                setBorder(new CellRangeAddress(1, lastRow, 0, 0), sheet);
//                 setBorder(cellAddresses, sheet);
            }
            //上面已经得到了有重复的loanId了，获取到该元素所出现的第一个位置，然后进行合并。
            //把次数不为1的loanId记录下来
            for (Map.Entry<Long, Long> m:collect.entrySet()) {
                if (m.getValue() != 1) {
                    StsIncomeForecastLoanVo slv = s.getStsIncomeForecastLoanVoList().stream().filter(t -> t.getLoanId().equals(m.getKey())).findFirst().get();
                    int index = s.getStsIncomeForecastLoanVoList().indexOf(slv);
                    //找到了第一个元素的索引
                    //进行合并
//                    sheet.addMergedRegion(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 1, 1));
                    cellAddresses.setFirstRow((1 + index));
                    cellAddresses.setLastRow((index + Integer.parseInt(String.valueOf(m.getValue()))));
                    cellAddresses.setFirstColumn(1);
                    cellAddresses.setLastColumn(1);
                    addMergedReigon(ctWorksheet, cellAddresses);
//                    setBorder(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 1, 1), sheet);
//                     setBorder(cellAddresses, sheet);
                    cellAddresses.setFirstColumn(2);
                    cellAddresses.setLastColumn(2);
//                    sheet.addMergedRegion(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 2, 2));
                    addMergedReigon(ctWorksheet, cellAddresses);
//                    setBorder(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 2, 2), sheet);
//                     setBorder(cellAddresses, sheet);
//                    sheet.addMergedRegion(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 3, 3));
                    cellAddresses.setFirstColumn(3);
                    cellAddresses.setLastColumn(3);
//                    setBorder(new CellRangeAddress(1 + index, index + Integer.parseInt(String.valueOf(m.getValue())), 3, 3), sheet);
                    addMergedReigon(ctWorksheet, cellAddresses);
//                    for (int i = 0; i < s.getStsIncomeForecastLoanVoList().size(); i++) {
//                        m.getKey().equals(s.getStsIncomeForecastLoanVoList().get(i).getLoanId());
//
//                    }
                }
            }




            //创建主标题行
            sheetTitleRow = sheet.createRow(lastRow + 1);
            for (int i = 0; i < colSize2; i++) {
                headCell = sheetTitleRow.createCell(i);
                switch (i) {
                    case 0: headCell.setCellValue("放款月份"); break;
                    case 1: headCell.setCellValue("放款金额"); break;
                    case 2: headCell.setCellValue("产品类型"); break;
                    case 3: headCell.setCellValue("期数"); break;
                    case 4: headCell.setCellValue("还款月份"); break;
                    case 5: headCell.setCellValue("还款本金"); break;
                    case 6: headCell.setCellValue("还款利息"); break;
                    case 7: headCell.setCellValue("还款罚息"); break;
                    case 8: headCell.setCellValue("还款复利"); break;
                    case 9: headCell.setCellValue("提前还款违约金"); break;
                    case 10: headCell.setCellValue("活动抵扣金额"); break;
                    case 11: headCell.setCellValue("红线减免金额"); break;
                    case 12: headCell.setCellValue("月末余额"); break;
                    case 13: headCell.setCellValue("技术服务费"); break;
                    case 14: headCell.setCellValue("实收息费"); break;
                    case 15: headCell.setCellValue("借条分润"); break;
                    case 16: headCell.setCellValue("中保分润"); break;
                    case 17: headCell.setCellValue("资金成本"); break;
                    case 18: headCell.setCellValue("中保收入"); break;
                    case 19: headCell.setCellValue("净收入"); break;
                    case 20: headCell.setCellValue("代偿总金额"); break;
                    case 21: headCell.setCellValue("净收入-代偿总金额"); break;
                    case 22: headCell.setCellValue("平均余额"); break;
                    case 23: headCell.setCellValue("FA"); break;
                    case 24: headCell.setCellValue("保证金成本"); break;
                    case 25: headCell.setCellValue("净收益"); break;
                    default: break;
                }
                //设置样式
                headCell.setCellStyle(getTitleFont(sheet.getWorkbook()));
            }
            for (int i = 0; i < s.getStsIncomeForecastRepayMonthVoList().size(); i++) {
                //创建数据行（前面的list的size+1）
                sheetHeadRow = sheet.createRow(2 + lastRow + i);
                //放款月份
                cell1 = sheetHeadRow.createCell(0);
                cell1.setCellValue(s.getStsIncomeForecastRepayMonthVoList().get(i).getLoanMonth());
                //给这个单元格设置样式
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //放款金额 给成-就行
                cell1 = sheetHeadRow.createCell(1);
                cell1.setCellValue("-");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //产品类型
                cell1 = sheetHeadRow.createCell(2);
                cell1.setCellValue(s.getStsIncomeForecastRepayMonthVoList().get(i).getProductType());
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //期数
                cell1 = sheetHeadRow.createCell(3);
                cell1.setCellValue(s.getStsIncomeForecastRepayMonthVoList().get(i).getPhase());
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //还款月份
                cell1 = sheetHeadRow.createCell(4);
                cell1.setCellValue(s.getStsIncomeForecastRepayMonthVoList().get(i).getRepaymentMonth());
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //还款本金
                cell1 = sheetHeadRow.createCell(5);
                cell1.setCellValue(s.getStsIncomeForecastRepayMonthVoList().get(i).getRepaymentPrintAmount() + "");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //还款利息
                cell1 = sheetHeadRow.createCell(6);
                cell1.setCellValue(s.getStsIncomeForecastRepayMonthVoList().get(i).getRepaymentIntAmount() + "");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //还款罚息
                cell1 = sheetHeadRow.createCell(7);
                cell1.setCellValue(s.getStsIncomeForecastRepayMonthVoList().get(i).getRepaymentOintAmt() + "");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //还款复利
                cell1 = sheetHeadRow.createCell(8);
                cell1.setCellValue(s.getStsIncomeForecastRepayMonthVoList().get(i).getRepaymentFlAmt() + "");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //提前还款违约金
                cell1 = sheetHeadRow.createCell(9);
                cell1.setCellValue(s.getStsIncomeForecastRepayMonthVoList().get(i).getAdvDefineAmt() + "");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //活动抵扣金额
                cell1 = sheetHeadRow.createCell(10);
                cell1.setCellValue(s.getStsIncomeForecastRepayMonthVoList().get(i).getDeductAmt() + "");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //红线减免金额
                cell1 = sheetHeadRow.createCell(11);
                cell1.setCellValue(s.getStsIncomeForecastRepayMonthVoList().get(i).getReduceAmt() + "");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //月末余额
                cell1 = sheetHeadRow.createCell(12);
                cell1.setCellValue(s.getStsIncomeForecastRepayMonthVoList().get(i).getMonthEndBalanceAmt() + "");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //技术服务费
                cell1 = sheetHeadRow.createCell(13);
                cell1.setCellValue(s.getStsIncomeForecastRepayMonthVoList().get(i).getTechnicalServiceFee() + "");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //实收息费
                cell1 = sheetHeadRow.createCell(14);
                cell1.setCellValue(s.getStsIncomeForecastRepayMonthVoList().get(i).getPaidInterestFee() + "");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //借条分润
                cell1 = sheetHeadRow.createCell(15);
                if (Optional.ofNullable(s.getStsIncomeForecastRepayMonthVoList().get(i).getJtFrAmt()).isPresent()) {
                    cell1.setCellValue(s.getStsIncomeForecastRepayMonthVoList().get(i).getJtFrAmt() + "");
                } else {
                    cell1.setCellValue("");
                }
//                cell1.setCellValue(s.getStsIncomeForecastRepayMonthVoList().get(i).getJtFrAmt() + "");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //中保分润
                cell1 = sheetHeadRow.createCell(16);
                if (Optional.ofNullable(s.getStsIncomeForecastRepayMonthVoList().get(i).getZbFrAmt()).isPresent()) {
                    cell1.setCellValue(s.getStsIncomeForecastRepayMonthVoList().get(i).getZbFrAmt() + "");
                } else {
                    cell1.setCellValue("");
                }
//                cell1.setCellValue(s.getStsIncomeForecastRepayMonthVoList().get(i).getZbFrAmt() + "");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //资金成本
                cell1 = sheetHeadRow.createCell(17);
                if (Optional.ofNullable(s.getStsIncomeForecastRepayMonthVoList().get(i).getCostOfCapital()).isPresent()) {
                    cell1.setCellValue(s.getStsIncomeForecastRepayMonthVoList().get(i).getCostOfCapital() + "");
                } else {
                    cell1.setCellValue("");
                }
//                cell1.setCellValue(s.getStsIncomeForecastRepayMonthVoList().get(i).getCostOfCapital() + "");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //中保收入
                cell1 = sheetHeadRow.createCell(18);
                if (Optional.ofNullable(s.getStsIncomeForecastRepayMonthVoList().get(i).getZbIncome()).isPresent()) {
                    cell1.setCellValue(s.getStsIncomeForecastRepayMonthVoList().get(i).getZbIncome() + "");
                } else {
                    cell1.setCellValue("");
                }
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //净收入
                cell1 = sheetHeadRow.createCell(19);
                if (Optional.ofNullable(s.getStsIncomeForecastRepayMonthVoList().get(i).getNetIncome()).isPresent()) {
                    cell1.setCellValue(s.getStsIncomeForecastRepayMonthVoList().get(i).getNetIncome() + "");
                } else {
                    cell1.setCellValue("");
                }
//                cell1.setCellValue(s.getStsIncomeForecastRepayMonthVoList().get(i).getNetIncome() + "");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //代偿总金额
                cell1 = sheetHeadRow.createCell(20);
                if (Optional.ofNullable(s.getStsIncomeForecastRepayMonthVoList().get(i).getCompensateTotalAmt()).isPresent()) {
                    cell1.setCellValue(s.getStsIncomeForecastRepayMonthVoList().get(i).getCompensateTotalAmt() + "");
                } else {
                    cell1.setCellValue("");
                }
//                cell1.setCellValue(s.getStsIncomeForecastRepayMonthVoList().get(i).getCompensateTotalAmt() + "");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //净收入-代偿总金额
                cell1 = sheetHeadRow.createCell(21);
                if (Optional.ofNullable(s.getStsIncomeForecastRepayMonthVoList().get(i).getNetIncomeCompensateTotalAmt()).isPresent()) {
                    cell1.setCellValue(s.getStsIncomeForecastRepayMonthVoList().get(i).getNetIncomeCompensateTotalAmt() + "");
                } else {
                    cell1.setCellValue("");
                }
//                cell1.setCellValue(s.getStsIncomeForecastRepayMonthVoList().get(i).getNetIncomeCompensateTotalAmt() + "");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //平均余额
                cell1 = sheetHeadRow.createCell(22);
                cell1.setCellValue(s.getStsIncomeForecastRepayMonthVoList().get(i).getAvgBalance() + "");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //FA
                cell1 = sheetHeadRow.createCell(23);
                cell1.setCellValue(s.getStsIncomeForecastRepayMonthVoList().get(i).getFa() + "");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //保证金成本
                cell1 = sheetHeadRow.createCell(24);
                cell1.setCellValue(s.getStsIncomeForecastRepayMonthVoList().get(i).getMarginCost() + "");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
                //净收益
                cell1 = sheetHeadRow.createCell(25);
                if (Optional.ofNullable(s.getStsIncomeForecastRepayMonthVoList().get(i).getIncomeNet()).isPresent()) {
                    cell1.setCellValue(s.getStsIncomeForecastRepayMonthVoList().get(i).getIncomeNet() + "");
                } else {
                    cell1.setCellValue("");
                }
//                cell1.setCellValue(s.getStsIncomeForecastRepayMonthVoList().get(i).getIncomeNet() + "");
                cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
            }
            //合并放款月份，找到list大小，不用减去1，因为第0行被标题占用
            int lastRow1 = lastRow + 1 + s.getStsIncomeForecastRepayMonthVoList().size();
            if (s.getStsIncomeForecastRepayMonthVoList().size() > 1) {
//                sheet.addMergedRegion(new CellRangeAddress(lastRow + 2, lastRow1, 0, 0));
                cellAddresses.setFirstRow((lastRow + 2));
                cellAddresses.setLastRow(lastRow1);
                cellAddresses.setFirstColumn(0);
                cellAddresses.setLastColumn(0);
                addMergedReigon(ctWorksheet, cellAddresses);
//                setBorder(new CellRangeAddress(lastRow + 2, lastRow1, 0, 0), sheet);
//                 setBorder(cellAddresses, sheet);
//                sheet.addMergedRegion(new CellRangeAddress(lastRow + 2, lastRow1, 1, 1));
                cellAddresses.setFirstColumn(1);
                cellAddresses.setLastColumn(1);
                addMergedReigon(ctWorksheet, cellAddresses);
//                setBorder(new CellRangeAddress(lastRow + 2, lastRow1, 1, 1), sheet);
//                 setBorder(cellAddresses, sheet);
//                sheet.addMergedRegion(new CellRangeAddress(lastRow + 2, lastRow1, 2, 2));
                cellAddresses.setFirstColumn(2);
                cellAddresses.setLastColumn(2);
                addMergedReigon(ctWorksheet, cellAddresses);
//                setBorder(new CellRangeAddress(lastRow + 2, lastRow1, 2, 2), sheet);
//                 setBorder(cellAddresses, sheet);
//                sheet.addMergedRegion(new CellRangeAddress(lastRow + 2, lastRow1, 3, 3));
                cellAddresses.setFirstColumn(3);
                cellAddresses.setLastColumn(3);
                addMergedReigon(ctWorksheet, cellAddresses);
//                setBorder(new CellRangeAddress(lastRow + 2, lastRow1, 3, 3), sheet);
//                 setBorder(cellAddresses, sheet);
            }


            //创建主标题行
            sheetTitleRow = sheet.createRow(lastRow1 + 1);
            for (int i = 0; i < colSize2; i++) {
                headCell = sheetTitleRow.createCell(i);
                switch (i) {
                    case 0: headCell.setCellValue("放款月份"); break;
                    case 1: headCell.setCellValue("放款金额"); break;
                    case 2: headCell.setCellValue("产品类型"); break;
                    case 3: headCell.setCellValue("期数"); break;
                    case 4: headCell.setCellValue("还款月份"); break;
                    case 5: headCell.setCellValue("还款本金"); break;
                    case 6: headCell.setCellValue("还款利息"); break;
                    case 7: headCell.setCellValue("还款罚息"); break;
                    case 8: headCell.setCellValue("还款复利"); break;
                    case 9: headCell.setCellValue("提前还款违约金"); break;
                    case 10: headCell.setCellValue("活动抵扣金额"); break;
                    case 11: headCell.setCellValue("红线减免金额"); break;
                    case 12: headCell.setCellValue("月末余额"); break;
                    case 13: headCell.setCellValue("技术服务费"); break;
                    case 14: headCell.setCellValue("实收息费"); break;
                    case 15: headCell.setCellValue("借条分润"); break;
                    case 16: headCell.setCellValue("中保分润"); break;
                    case 17: headCell.setCellValue("资金成本"); break;
                    case 18: headCell.setCellValue("中保收入"); break;
                    case 19: headCell.setCellValue("净收入"); break;
                    case 20: headCell.setCellValue("代偿总金额"); break;
                    case 21: headCell.setCellValue("净收入-代偿总金额"); break;
                    case 22: headCell.setCellValue("平均余额"); break;
                    case 23: headCell.setCellValue("FA"); break;
                    case 24: headCell.setCellValue("保证金成本"); break;
                    case 25: headCell.setCellValue("净收益"); break;
                    default: break;
                }
                //设置样式
                headCell.setCellStyle(getTitleFont(sheet.getWorkbook()));
            }
            sheetHeadRow = sheet.createRow(2 + lastRow1);
            //放款月份
            cell1 = sheetHeadRow.createCell(0);
            cell1.setCellValue(s.getStsIncomeForecastTotalVo().getLoanMonth());
            cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
            //放款金额
            cell1 = sheetHeadRow.createCell(1);
            cell1.setCellValue(s.getStsIncomeForecastTotalVo().getLoanAmt() + "");
            cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
            //产品类型
            cell1 = sheetHeadRow.createCell(2);
            cell1.setCellValue(s.getStsIncomeForecastTotalVo().getProductType());
            cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
            //期数
            cell1 = sheetHeadRow.createCell(3);
            cell1.setCellValue(s.getStsIncomeForecastTotalVo().getPhase());
            cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
            //还款月份
            cell1 = sheetHeadRow.createCell(4);
            cell1.setCellValue(s.getStsIncomeForecastTotalVo().getRepaymentMonth());
            cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
            //还款本金 给成-就行
            cell1 = sheetHeadRow.createCell(5);
            cell1.setCellValue("-");
            cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
            //还款利息 给成-就行
            cell1 = sheetHeadRow.createCell(6);
            cell1.setCellValue("-");
            cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
            //还款罚息 给成-就行
            cell1 = sheetHeadRow.createCell(7);
            cell1.setCellValue("-");
            cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
            //还款复利 给成-就行
            cell1 = sheetHeadRow.createCell(8);
            cell1.setCellValue("-");
            cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
            //提前还款违约金 给成-就行
            cell1 = sheetHeadRow.createCell(9);
            cell1.setCellValue("-");
            cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
            //活动减免金额 给成-就行
            cell1 = sheetHeadRow.createCell(10);
            cell1.setCellValue("-");
            cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
            //红线减免金额 给成-就行
            cell1 = sheetHeadRow.createCell(11);
            cell1.setCellValue("-");
            cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
            //月末余额 给成-就行
            cell1 = sheetHeadRow.createCell(12);
            cell1.setCellValue("-");
            cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
            //技术服务费
            cell1 = sheetHeadRow.createCell(13);
            cell1.setCellValue(s.getStsIncomeForecastTotalVo().getTechnicalServiceFee() + "");
            cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
            //实收息费
            cell1 = sheetHeadRow.createCell(14);
            cell1.setCellValue(s.getStsIncomeForecastTotalVo().getPaidInterestFee() + "");
            cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
            //借条分润
            cell1 = sheetHeadRow.createCell(15);
            if (Optional.ofNullable(s.getStsIncomeForecastTotalVo().getJtFrAmt()).isPresent()) {
                cell1.setCellValue(s.getStsIncomeForecastTotalVo().getJtFrAmt() + "");
            } else {
                cell1.setCellValue("");
            }
//            cell1.setCellValue(s.getStsIncomeForecastTotalVo().getJtFrAmt() + "");
            cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
            //中保分润
            cell1 = sheetHeadRow.createCell(16);
            if (Optional.ofNullable(s.getStsIncomeForecastTotalVo().getZbFrAmt()).isPresent()) {
                cell1.setCellValue(s.getStsIncomeForecastTotalVo().getZbFrAmt() + "");
            } else {
                cell1.setCellValue("");
            }
//            cell1.setCellValue(s.getStsIncomeForecastTotalVo().getZbFrAmt() + "");
            cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
            //资金成本
            cell1 = sheetHeadRow.createCell(17);
            if (Optional.ofNullable(s.getStsIncomeForecastTotalVo().getCostOfCapital()).isPresent()) {
                cell1.setCellValue(s.getStsIncomeForecastTotalVo().getCostOfCapital() + "");
            } else {
                cell1.setCellValue("");
            }
//            cell1.setCellValue(s.getStsIncomeForecastTotalVo().getCostOfCapital() + "");
            cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
            //中保收入
            cell1 = sheetHeadRow.createCell(18);
            if (Optional.ofNullable(s.getStsIncomeForecastTotalVo().getZbIncome()).isPresent()) {
                cell1.setCellValue(s.getStsIncomeForecastTotalVo().getZbIncome() + "");
            } else {
                cell1.setCellValue("");
            }
            cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
            //净收入
            cell1 = sheetHeadRow.createCell(19);
            if (Optional.ofNullable(s.getStsIncomeForecastTotalVo().getNetIncome()).isPresent()) {
                cell1.setCellValue(s.getStsIncomeForecastTotalVo().getNetIncome() + "");
            } else {
                cell1.setCellValue("");
            }
//            cell1.setCellValue(s.getStsIncomeForecastTotalVo().getNetIncome() + "");
            cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
            //代偿总金额 给成-就行
            cell1 = sheetHeadRow.createCell(20);
            cell1.setCellValue("-");
            cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
            //净收入-代偿总金额
            cell1 = sheetHeadRow.createCell(21);
            if (Optional.ofNullable(s.getStsIncomeForecastTotalVo().getNetIncomeCompensateTotalAmt()).isPresent()) {
                cell1.setCellValue(s.getStsIncomeForecastTotalVo().getNetIncomeCompensateTotalAmt() + "");
            } else {
                cell1.setCellValue("");
            }
//            cell1.setCellValue(s.getStsIncomeForecastTotalVo().getNetIncomeCompensateTotalAmt() + "");
            cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
            //平均余额 给成-就行
            cell1 = sheetHeadRow.createCell(22);
            cell1.setCellValue("-");
            cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
            //FA
            cell1 = sheetHeadRow.createCell(23);
            cell1.setCellValue(s.getStsIncomeForecastTotalVo().getFa() + "");
            cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
            //保证金成本
            cell1 = sheetHeadRow.createCell(24);
            cell1.setCellValue(s.getStsIncomeForecastTotalVo().getMarginCost() + "");
            cell1.setCellStyle(getDataFont(sheet.getWorkbook()));
            //净收益
            cell1 = sheetHeadRow.createCell(25);
            if (Optional.ofNullable(s.getStsIncomeForecastTotalVo().getIncomeNet()).isPresent()) {
                cell1.setCellValue(s.getStsIncomeForecastTotalVo().getIncomeNet() + "");
            } else {
                cell1.setCellValue("");
            }
//            cell1.setCellValue(s.getStsIncomeForecastTotalVo().getIncomeNet() + "");
            cell1.setCellStyle(getDataFont(sheet.getWorkbook()));

            // 自动调整列宽
            // sheet.trackAllColumnsForAutoSizing();
            for (int i = 0; i < colSize2; i++) {
                // sheet.autoSizeColumn(i,true);
                if (sheet.getColumnWidth(i) * 12 / 10 > 65280) {
                    sheet.setColumnWidth(i, 65280);
                } else {
                    sheet.setColumnWidth(i, sheet.getColumnWidth(i) * 15 / 10);
                }
            }
        }

        //生成完成，输出下载
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        try {

            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            if (response.getOutputStream() != null) {
                try {
                    response.getOutputStream().close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }

    }

    public static void setBorder(CellRangeAddress a,SXSSFSheet sheet) {
        RegionUtil.setBorderTop(BorderStyle.THIN, a, sheet);
        RegionUtil.setBorderBottom(BorderStyle.THIN, a, sheet);
        RegionUtil.setBorderLeft(BorderStyle.THIN, a, sheet);
        RegionUtil.setBorderRight(BorderStyle.THIN, a, sheet);
    }

    //表头样式
    public static CellStyle getTitleFont(Workbook workbook){
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 13);//字体大小
        font.setBold(true);//加粗
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFont(font);
        cellStyle.setAlignment(HorizontalAlignment.CENTER_SELECTION);//设置水平居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);//设置垂直居中

        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        return cellStyle;
    }

    //内容样式
    public static CellStyle getDataFont(Workbook workbook){
        CellStyle cellStyle = cellStyleMap.get("cellStyleDeal1");
        Font font = fontMap.get("dealFont1");
        if(cellStyle == null){
            cellStyle = workbook.createCellStyle();
            cellStyleMap.put("cellStyleDeal1", cellStyle);
        }
        if(font == null){
            font = workbook.createFont();
            fontMap.put("dealFont1",font);
        }
//        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 12);//字体大小
        font.setBold(false);//不加粗
//        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFont(font);
        cellStyle.setAlignment(HorizontalAlignment.CENTER_SELECTION);//设置水平居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);//设置垂直居中

        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setWrapText(true);
        return cellStyle;
    }

    //处理数据
    public static String getValue(Object object){
        if (object==null){
            return "";
        }else {
            return object.toString();
        }
    }

    /**
     *  合并单元格
     */
    private static void addMergedReigon(CTWorksheet sheetX, CellRangeAddress cellRangeAddress) {
        CTMergeCells ctMergeCells;
        if (sheetX.isSetMergeCells()) {
            ctMergeCells = sheetX.getMergeCells();
        } else {
            ctMergeCells = sheetX.addNewMergeCells();
        }

        CTMergeCell ctMergeCell = ctMergeCells.addNewMergeCell();
        ctMergeCell.setRef(cellRangeAddress.formatAsString());
    }
}
