package org.ruoyi.core.yybbsc.mapper;


import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.yybbsc.domain.StsOperateDay;
import org.ruoyi.core.yybbsc.domain.dto.StsOperateDayDto;

import java.util.Date;
import java.util.List;

/**
 * 每日运营统计Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-19
 */
public interface StsOperateDayMapper
{

    List<StsOperateDay> selectStsOperateDayList(@Param("productNo") String productNo,@Param("isAsc") String isAsc);

    List<StsOperateDay> selectStsOperateDayListByExport(@Param("productNo") String productNo,@Param("isAsc") String isAsc);

    StsOperateDay selectStsOperateDayID(Integer id);

    StsOperateDay selectTheLastStsOperateDay(@Param("reconDate") String reconDate, @Param("productNo") String productNo);

    Long selectStsOperateDayByReconDate(@Param("reconDate") Date reconDate, @Param("productNo") String productNo);

    int insertStsOperateDay(StsOperateDay stsOperateDay);

    int updateStsOperateDayById(@Param("id") Long id, @Param("stsOperateDay") StsOperateDay stsOperateDay);

    List<StsOperateDayDto> selectStsOperateDayDtoList(String productNo);

    List<String> selectReconDateListByProductNo(@Param("productNo") String productNo, @Param("reconDate") String reconDate);

    List<StsOperateDay> selectStsOperateDayListByReconDate(@Param("productNo") String productNo, @Param("reconDate") String reconDate);

    int updateStsOperateDayList(@Param(value = "stsOperateDayList") List<StsOperateDay> stsOperateDayList);
}
