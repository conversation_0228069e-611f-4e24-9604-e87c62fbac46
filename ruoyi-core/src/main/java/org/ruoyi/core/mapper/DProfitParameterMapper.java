package org.ruoyi.core.mapper;

import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.domain.DProfitParameter;

import java.util.List;

/**
 * 【利润测算参数设置】Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-07-25
 */
public interface DProfitParameterMapper 
{
    /**
     * 查询【利润测算参数设置】
     * 
     * @param id 【利润测算参数设置】主键
     * @return 【请填写功能名称】
     */
    public DProfitParameter selectDProfitParameterById(Long id);

    /**
     * 查询【利润测算参数设置】列表
     * 
     * @param dProfitParameter 【利润测算参数设置】
     * @return 【请填写功能名称】集合
     */
    public List<DProfitParameter> selectDProfitParameterList(DProfitParameter dProfitParameter);



    /**
     * 新增【利润测算参数设置】
     * 
     * @param dProfitParameter 【利润测算参数设置】
     * @return 结果
     */
    public int insertDProfitParameter(DProfitParameter dProfitParameter);

    /**
     * 修改【利润测算参数设置】
     * 
     * @param dProfitParameter 【利润测算参数设置】
     * @return 结果
     */
    public int updateDProfitParameter(DProfitParameter dProfitParameter);

    /**
     * 删除【利润测算参数设置】
     * 
     * @param id 【利润测算参数设置】主键
     * @return 结果
     */
    public int deleteDProfitParameterById(Long id);

    /**
     * 批量删除【利润测算参数设置】
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDProfitParameterByIds(Long[] ids);

    /**
     * 获得验证数据
     *
     * @param platformNo 平台
     * @param partnerNo  合作方
     * @param fundNo     资金方
     * @return {@link List}<{@link DProfitParameter}>
     */
    List<DProfitParameter> getVerifyData(@Param("platformNo") String platformNo, @Param("partnerNo") String partnerNo, @Param("fundNo") String fundNo,@Param("id")String id);

    /**
     * 查询上个月利润参数
     *
     * @param lastMonth 上个月
     * @return {@link List}<{@link DProfitParameter}>
     */
    List<DProfitParameter> getProfitParamByLastMonth(@Param("lastMonth") String lastMonth);

    /**
     * 获取最近修改的验证数据
     *
     * @param platformNo 平台不
     * @param partnerNo  合作伙伴没有
     * @param fundNo     基金没有
     * @return {@link List}<{@link DProfitParameter}>
     */
    List<DProfitParameter> getNearVerifyData(@Param("platformNo") String platformNo, @Param("partnerNo") String partnerNo, @Param("fundNo") String fundNo);


    /**
     * 查询【利润测算参数设置】列表
     *
     * @param platformNos 号平台
     * @param partnerNos  伴侣号
     * @param fundNos     基金号
     * @param dProfitParameter 用户角色sql
     * @return {@link List}<{@link DProfitParameter}>
     */
    public List<DProfitParameter> selectDProfitParameter(@Param("platformNos") List<String> platformNos
            , @Param("partnerNos") List<String> partnerNos
            , @Param("fundNos") List<String> fundNos
            ,@Param("dProfitParameter") DProfitParameter dProfitParameter);

    /**
     * 校验新增的数据是否存在
     *
     * @param dProfitParameter 利润测算参数
     * @return 结果
     */
    DProfitParameter selectDProfitParameterByPlatformNoAndPartnerNoAndFundNo(DProfitParameter dProfitParameter);
}
