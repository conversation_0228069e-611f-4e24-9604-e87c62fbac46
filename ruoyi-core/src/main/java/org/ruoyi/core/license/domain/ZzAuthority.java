package org.ruoyi.core.license.domain;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 证照授权对象 zz_authority
 * 
 * <AUTHOR>
 * @date 2024-01-25
 */
public class ZzAuthority extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 关联目录id */
    @Excel(name = "关联目录id")
    private Long billId;

    /** 类型(0 目录 ) */
    @Excel(name = "类型(0 目录 )")
    private String billType;

    /** 权限类型(0:部门 1:岗位 2:人员) */
    @Excel(name = "权限类型(0:部门 1:岗位 2:人员)")
    private String authorityType;

    /** 关联权限id */
    @Excel(name = "关联权限id")
    private Long authorityId;

    /** 关联权限id集合 */
    private Long[] authorityIds;

    /** 授权开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "授权开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 授权结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "授权结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;

    /** 删除标志(0 未删除  2 已删除) */
    private String delFlag;

    /** 授权人 */
    @Excel(name = "授权人")
    private String impower;

    /** 授权时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "授权时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date impowerTime;

    /** 版本 */
    @Excel(name = "版本")
    private Long version;

    /** 列表页修改授权，接参用 */
    List<ZzAuthority> authority;

    /** 目录所属公司id */
    private Long companyId;

    /** 用户id */
    private Long userId;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public List<ZzAuthority> getAuthority() {
        return authority;
    }

    public void setAuthority(List<ZzAuthority> authority) {
        this.authority = authority;
    }

    public Long[] getAuthorityIds() {
        return authorityIds;
    }

    public void setAuthorityIds(Long[] authorityIds) {
        this.authorityIds = authorityIds;
    }

    public void setBillId(Long billId)
    {
        this.billId = billId;
    }

    public Long getBillId() 
    {
        return billId;
    }
    public void setBillType(String billType) 
    {
        this.billType = billType;
    }

    public String getBillType() 
    {
        return billType;
    }
    public void setAuthorityType(String authorityType) 
    {
        this.authorityType = authorityType;
    }

    public String getAuthorityType() 
    {
        return authorityType;
    }
    public void setAuthorityId(Long authorityId) 
    {
        this.authorityId = authorityId;
    }

    public Long getAuthorityId() 
    {
        return authorityId;
    }
    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    public Date getStartTime() 
    {
        return startTime;
    }
    public void setEndTime(Date endTime) 
    {
        this.endTime = endTime;
    }

    public Date getEndTime() 
    {
        return endTime;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }
    public void setImpower(String impower) 
    {
        this.impower = impower;
    }

    public String getImpower() 
    {
        return impower;
    }
    public void setImpowerTime(Date impowerTime) 
    {
        this.impowerTime = impowerTime;
    }

    public Date getImpowerTime() 
    {
        return impowerTime;
    }
    public void setVersion(Long version) 
    {
        this.version = version;
    }

    public Long getVersion() 
    {
        return version;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("billId", getBillId())
            .append("billType", getBillType())
            .append("authorityType", getAuthorityType())
            .append("authorityId", getAuthorityId())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("delFlag", getDelFlag())
            .append("impower", getImpower())
            .append("impowerTime", getImpowerTime())
            .append("version", getVersion())
            .toString();
    }
}
