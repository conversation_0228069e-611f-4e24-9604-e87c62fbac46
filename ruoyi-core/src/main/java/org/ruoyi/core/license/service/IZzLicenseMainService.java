package org.ruoyi.core.license.service;

import java.text.ParseException;
import java.util.List;

import com.ruoyi.common.core.domain.AjaxResult;
import org.ruoyi.core.license.domain.ZzLicenseMain;
import org.ruoyi.core.license.domain.vo.ZzLicenseArrangeVo;
import org.ruoyi.core.license.domain.vo.ZzLicenseMoveVo;

/**
 * 证照Service接口
 *
 * <AUTHOR>
 * @date 2024-01-25
 */
public interface IZzLicenseMainService
{
    /**
     * 查询证照
     *
     * @param id 证照主键
     * @return 证照
     */
    public ZzLicenseMain selectZzLicenseMainById(String id);

    /**
     * 查询证照列表
     *
     * @param zzLicenseMain 证照
     * @return 证照集合
     */
    public List<ZzLicenseMain> selectZzLicenseMainList(ZzLicenseMain zzLicenseMain);

    /**
     * 新增证照
     *
     * @param zzLicenseMain 证照
     * @return 结果
     */
    public int insertZzLicenseMain(ZzLicenseMain zzLicenseMain) throws ParseException;

    /**
     * 修改证照
     *
     * @param zzLicenseMain 证照
     * @return 结果
     */
    public int updateZzLicenseMain(ZzLicenseMain zzLicenseMain);

    /**
     * 批量删除证照
     *
     * @param ids 需要删除的证照主键集合
     * @return 结果
     */
    public int deleteZzLicenseMainByIds(String[] ids);

    /**
     * 删除证照信息
     *
     * @param id 证照主键
     * @return 结果
     */
    public int deleteZzLicenseMainById(String id);

    /**
     * 根据系统编号查询历史数据
     * @param zzLicenseMain
     * @return
     */
    List<ZzLicenseMain> selectZzLicenseMainHistoryById(ZzLicenseMain zzLicenseMain);

    /**
     * 根据目录id查询当前目录下是否存在证照
     * @param catalogueId
     * @return
     */
    List<ZzLicenseMain> selectZzLicenseMainByCatalogueId(Long catalogueId);

    /**
     * 查询证照预警列表
     * @param zzLicenseMain
     * @return
     */
    List<ZzLicenseMain> selectWarnLicenseList(ZzLicenseMain zzLicenseMain);

    /**
     * 证照移动
     * @param zzLicenseMoveVo
     * @return
     */
    int updateLicenseMove(ZzLicenseMoveVo zzLicenseMoveVo);

    /**
     * 证照历史数据导入
     * @return
     */
    int licenseHistoryExport();

    /**
     * 查看证照借用安排
     * @param zzLicenseArrangeVo
     * @return
     */
    List<ZzLicenseArrangeVo> zzLicenseArrangeVoList(ZzLicenseArrangeVo zzLicenseArrangeVo);

    /**
     * 根据证照系统编号更新证照状态
     * @param licenseSysCodes
     */
    void updateLicenseStatusBySysCode(List<String> licenseSysCodes);
}