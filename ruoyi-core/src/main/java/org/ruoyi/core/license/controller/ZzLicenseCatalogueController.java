package org.ruoyi.core.license.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.RepeatSubmit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import org.ruoyi.core.license.domain.ZzLicenseCatalogue;
import org.ruoyi.core.license.service.IZzLicenseCatalogueService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 证照目录Controller
 * 
 * <AUTHOR>
 * @date 2024-01-25
 */
@RestController
@RequestMapping("/licenseCatalogue/catalogue")
public class ZzLicenseCatalogueController extends BaseController
{
    @Autowired
    private IZzLicenseCatalogueService zzLicenseCatalogueService;

    /**
     * 查询证照目录列表
     */
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody ZzLicenseCatalogue zzLicenseCatalogue)
    {
        List<ZzLicenseCatalogue> list = zzLicenseCatalogueService.selectZzLicenseCatalogueList(zzLicenseCatalogue);
        return getDataTable(list);
    }

    /**
     * 导出证照目录列表
     */
    @Log(title = "证照目录", businessType = BusinessType.EXPORT)
    @RepeatSubmit(interval = 3000,message = "请勿重复提交！")
    @PostMapping("/export")
    public void export(HttpServletResponse response, ZzLicenseCatalogue zzLicenseCatalogue)
    {
        List<ZzLicenseCatalogue> list = zzLicenseCatalogueService.selectZzLicenseCatalogueList(zzLicenseCatalogue);
        ExcelUtil<ZzLicenseCatalogue> util = new ExcelUtil<ZzLicenseCatalogue>(ZzLicenseCatalogue.class);
        util.exportExcel(response, list, "证照目录数据");
    }

    /**
     * 获取证照目录详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(zzLicenseCatalogueService.selectZzLicenseCatalogueById(id));
    }

    /**
     * 新增证照目录
     */
    @Log(title = "证照目录", businessType = BusinessType.INSERT)
    @RepeatSubmit(interval = 1000,message = "请勿重复提交！")
    @PostMapping
    public AjaxResult add(@Validated @RequestBody ZzLicenseCatalogue zzLicenseCatalogue)
    {
        return toAjax(zzLicenseCatalogueService.insertZzLicenseCatalogue(zzLicenseCatalogue));
    }

    /**
     * 修改证照目录
     */
    @Log(title = "证照目录", businessType = BusinessType.UPDATE)
    @RepeatSubmit(interval = 1000,message = "请勿重复提交！")
    @PutMapping
    public AjaxResult edit(@RequestBody ZzLicenseCatalogue zzLicenseCatalogue)
    {
        return toAjax(zzLicenseCatalogueService.updateZzLicenseCatalogue(zzLicenseCatalogue));
    }

    /**
     * 删除证照目录
     */
    @Log(title = "证照目录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id)
    {
        return toAjax(zzLicenseCatalogueService.deleteZzLicenseCatalogueById(id));
    }

    /**
     * 目录树状列表
     * @param
     * @return
     */
    @GetMapping(value = "/getTreeList")
    public AjaxResult getTreeList(ZzLicenseCatalogue zzLicenseCatalogue)
    {
        return AjaxResult.success(zzLicenseCatalogueService.getTreeList(zzLicenseCatalogue));
    }

    /**
     * 新增证照目录时可选的所属部门按新权限处理
     */
    @GetMapping("/authTreeList")
    public AjaxResult authTreeList(){
        return AjaxResult.success(zzLicenseCatalogueService.authTreeList());
    }
}
