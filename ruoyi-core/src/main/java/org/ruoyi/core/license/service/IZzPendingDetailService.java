package org.ruoyi.core.license.service;

import java.text.ParseException;
import java.util.List;

import com.ruoyi.common.core.domain.AjaxResult;
import org.ruoyi.core.license.domain.ZzPendingDetail;

/**
 * 证照收回Service接口
 *
 * <AUTHOR>
 * @date 2024-01-25
 */
public interface IZzPendingDetailService
{
    /**
     * 查询证照收回
     *
     * @param id 证照收回主键
     * @return 证照收回
     */
    public ZzPendingDetail selectZzPendingDetailById(Long id);

    /**
     * 查询证照收回列表
     *
     * @param zzPendingDetail 证照收回
     * @return 证照收回集合
     */
    public List<ZzPendingDetail> selectZzPendingDetailList(ZzPendingDetail zzPendingDetail);

    /**
     * 新增证照收回
     *
     * @param zzPendingDetail 证照收回
     * @return 结果
     */
    public int insertZzPendingDetail(ZzPendingDetail zzPendingDetail) throws ParseException;

    /**
     * 修改证照收回
     * (1待签领;2审核中;3已签领;4不签领;5:已收回;6:已废弃)
     * @param zzPendingDetail 证照收回
     * @return 结果
     */
    public int updateZzPendingDetail(ZzPendingDetail zzPendingDetail);

    /**
     * 批量删除证照收回
     *
     * @param ids 需要删除的证照收回主键集合
     * @return 结果
     */
    public int deleteZzPendingDetailByIds(Long[] ids);

    /**
     * 删除证照收回信息
     *
     * @param id 证照收回主键
     * @return 结果
     */
    public int deleteZzPendingDetailById(Long id);

    /**
     * 待处理证照页面-查看流程
     * @param flowId
     * @return
     */
    public List<ZzPendingDetail> selectPendLicenseById(String flowId);
}