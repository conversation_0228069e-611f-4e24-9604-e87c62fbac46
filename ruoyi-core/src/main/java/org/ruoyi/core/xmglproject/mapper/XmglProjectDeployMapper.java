package org.ruoyi.core.xmglproject.mapper;

import com.ruoyi.system.domain.AuthDetail;
import com.ruoyi.system.domain.AuthMain;
import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.oasystem.domain.OaEditApproveGeneralityEditRecords;
import org.ruoyi.core.xmglproject.domain.AuthDetailVo;
import org.ruoyi.core.xmglproject.domain.XmglDeployProject;
import org.ruoyi.core.xmglproject.domain.XmglProject;

import java.util.List;
import java.util.Set;

public interface XmglProjectDeployMapper {
    /**
     * 查询立项项目-项目名称关联
     *
     * @param deployId 立项项目-项目名称关联主键
     * @return 立项项目-项目名称关联
     */
    public XmglDeployProject selectXmglProjectDeployByDeployId(Long deployId);

    /**
     * 查询立项项目-项目名称关联
     * @param projectId 立项项目模块的项目id
     * @return
     */
    XmglDeployProject selectXmglProjectDeployByProjectId(Long projectId);

    /**
     * 查询当前用户在‘授权’中是担任了什么角色(项目负责人/业务管理员/查看权限)
     * @param collect
     * @param deployId
     */
    List<AuthDetailVo> selectXmglProjectDeployInfoByDeployId(@Param("collect") List<Long> collect, @Param("deployId")Long deployId);

    /**
     * 查询当前用户在'项目名称'模块是否是该项目的业务责任人
     * @param userId 用户id
     * @param deployId 项目名称模块的项目id
     * @return
     */
    OaEditApproveGeneralityEditRecords selectProjectYWPerson(@Param("userId")Long userId, @Param("deployId") Long deployId);

    /**
     * 立项项目终止后，根据立项项目id更新项目名称-立项项目关联表状态为已终止
     * @param xmglDeployProject
     * @return
     */
    int updateProjectDeployStatus(XmglDeployProject xmglDeployProject);

    /**
     * 根据项目名称id查询所有的立项项目id
     * @param deployIdSet
     * @return
     */
    List<Long> selectProjectInfoByDeployIdList(@Param("deployIdSet") Set<Long> deployIdSet);

    /**
     * 根据立项项目id查询正在关联的项目名称模块的项目id
     * @param projectId
     * @return
     */
    XmglDeployProject selectBeingProjectDeployInfoByProjectId(Long projectId);

    /**
     * 根据用户id查询权限主表
     * @param userId
     * @return
     */
    List<AuthMain> selectAuthMainInfoByDeployId(Long userId);

    AuthDetail selectAuthDetailByMainId(Long mainId);

    /**
     * 根据业务信息配置的项目名称id查询立项项目信息列表
     * @param authProjectIds 项目名称id
     * @return
     */
    List<XmglProject> selectProjectInfoListByDeployIds(@Param("authProjectIds") List<Long> authProjectIds);
}
