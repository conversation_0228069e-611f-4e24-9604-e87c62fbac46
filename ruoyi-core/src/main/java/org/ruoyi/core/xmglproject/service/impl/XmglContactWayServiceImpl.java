package org.ruoyi.core.xmglproject.service.impl;

import java.util.List;
import java.util.Set;

import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.xmglproject.domain.XmglContactWay;
import org.ruoyi.core.xmglproject.mapper.XmglContactWayMapper;
import org.ruoyi.core.xmglproject.service.IXmglContactWayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-12-16
 */
@Service
public class XmglContactWayServiceImpl implements IXmglContactWayService
{
    @Autowired
    private XmglContactWayMapper xmglContactWayMapper;

    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public XmglContactWay selectXmglContactWayById(Long id)
    {
        return xmglContactWayMapper.selectXmglContactWayById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param xmglContactWay 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<XmglContactWay> selectXmglContactWayList(XmglContactWay xmglContactWay)
    {
        return xmglContactWayMapper.selectXmglContactWayList(xmglContactWay);
    }

    /**
     * 新增【请填写功能名称】
     * 
     * @param xmglContactWay 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertXmglContactWay(XmglContactWay xmglContactWay)
    {
        xmglContactWay.setCreateTime(DateUtils.getNowDate());
        xmglContactWay.setUpdateTime(DateUtils.getNowDate());
        return xmglContactWayMapper.insertXmglContactWay(xmglContactWay);
    }

    /**
     * 修改【请填写功能名称】
     * 
     * @param xmglContactWay 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateXmglContactWay(XmglContactWay xmglContactWay)
    {
        xmglContactWay.setUpdateTime(DateUtils.getNowDate());
        return xmglContactWayMapper.updateXmglContactWay(xmglContactWay);
    }

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteXmglContactWayByIds(Long[] ids)
    {
        return xmglContactWayMapper.deleteXmglContactWayByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteXmglContactWayById(Long id)
    {
        return xmglContactWayMapper.deleteXmglContactWayById(id);
    }
}
