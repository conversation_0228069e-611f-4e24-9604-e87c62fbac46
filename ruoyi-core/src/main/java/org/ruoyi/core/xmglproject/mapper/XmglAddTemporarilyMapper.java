package org.ruoyi.core.xmglproject.mapper;

import com.ruoyi.system.domain.AuthDetail;
import com.ruoyi.system.domain.SysCompany;
import com.ruoyi.system.domain.vo.CompanyTypeMappingVo;
import com.ruoyi.system.domain.vo.SysCompanyVo;
import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.oasystem.domain.OaProcessTemplate;
import org.ruoyi.core.oasystem.domain.OaProjectDeploy;
import org.ruoyi.core.oasystem.domain.ProjectCompanyRelevance;
import org.ruoyi.core.xmglproject.domain.*;

import java.util.List;
import java.util.Set;


/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-12-16
 */
public interface XmglAddTemporarilyMapper
{
    /**
     * 查询新增立项项目临时
     *
     * @param id 新增立项项目临时主键
     * @return 新增立项项目临时
     */
    public XmglAddTemporarily selectXmglAddTemporarilyById(Long id);

    /**
     * 查询新增立项项目临时列表
     *
     * @param xmglAddTemporarily 新增立项项目临时
     * @return 新增立项项目临时集合
     */
    public List<XmglAddTemporarily> selectXmglAddTemporarilyList(XmglAddTemporarily xmglAddTemporarily);

    /**
     * 新增新增立项项目临时
     *
     * @param xmglAddTemporarily 新增立项项目临时
     * @return 结果
     */
    public int insertXmglAddTemporarily(XmglAddTemporarily xmglAddTemporarily);

    /**
     * 修改新增立项项目临时
     *
     * @param xmglAddTemporarily 新增立项项目临时
     * @return 结果
     */
    public int updateXmglAddTemporarily(XmglAddTemporarily xmglAddTemporarily);

    /**
     * 删除新增立项项目临时
     *
     * @param id 新增立项项目临时主键
     * @return 结果
     */
    public int deleteXmglAddTemporarilyById(Long id);

    /**
     * 批量删除新增立项项目临时
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteXmglAddTemporarilyByIds(Long[] ids);

    /**
     * 根据项目名称id查询临时表中是否存在新创建的数据
     * @param deployId
     * @return
     */
    List<XmglAddTemporarily> selectXmglAddTemporarilyByDeployId(Long deployId);

    /**
     * 新增立项项目审批不通过时，根据项目名称id删除创建的项目信息
     * @param deployIdList
     */
    void deleteOaProjectDeployByIds(@Param("deployIdList") List<Long> deployIdList);

    /**
     * 新增立项项目审批不通过时，根据项目名称id删除创建的公司信息
     * @param companyIdList
     */
    void deleteCompanyInfoByCompanyIds(@Param("companyIdList") List<Long> companyIdList);

    /**
     * 新增立项项目审批不通过时，根据项目名称id删除创建的公司类型信息
     * @param companyTypeList
     */
    void deleteCompanyTypeByIds(@Param("companyTypeList") List<Long> companyTypeList);

    List<XmglAddTemporarily> selectXmglAddTemporarilyByAddUuid(String addUuid);

    /**
     * 根据项目名称id集合更新项目名称状态
     * @param deployIdList
     */
    void updateOaProjectDeployCheckStatusByIds(@Param("deployIdList") List<Long> deployIdList);

    /**
     * 根据公司id集合更新项目公司状态
     * @param companyIdList
     */
    void updateCompanyCheckStatusByIds(@Param("companyIdList") List<Long> companyIdList);

    /**
     * 根据项目id集合查询数据
     * @param deployIdList
     * @return
     */
    List<OaProjectDeploy> selectOaProjectDeployInfoById(@Param("deployIdList") List<Long> deployIdList);

    /**
     * 根据公司id集合查询数据
     * @param companyIdList
     * @return
     */
    List<CompanyTypeMappingVo> selectSysCompanyInfoById(@Param("companyIdList") List<Long> companyIdList);

    /**
     * 获取当前登陆人有哪些发起流程权限的公司
     * @return
     */
    List<SysCompany> selectLoginCompanyInfoByUserId(Long userId);

    /**
     * 发起流程时校验当前当前公司是否存在流程模板
     * 13新增立项项目申请 14修改立项项目申请 15认领立项项目申请 16修改项目项目申请 17延期立项项目申请 18终止立项项目申请
     * @param oaProcessTemplate
     * @return
     */
    OaProcessTemplate queryTemplateByOaModuleTypeCompanyIdAndModuleType(OaProcessTemplate oaProcessTemplate);

    /**
     * 根据公司id集合查询公司信息
     * @param companyIdList
     * @return
     */
    List<SysCompanyVo> selectSysCompanyInfoListById(@Param("companyIdList") List<Long> companyIdList);

    /**
     * 根据本次统一批次新增的项目名称id查询立项项目id
     * @param deployIdList
     * @return
     */
    List<XmglDeployProject> selectXmglProjectInfoListByDeployIds(@Param("deployIdList") List<Long> deployIdList);

    /**
     * 根据公司id集合获取所有公司的所有公司类型
     * @param allList
     * @return
     */
    List<Long> selectProjectCompanyRelevanceListByIds(@Param("allList") List<Long> allList, @Param("deployId")Long deployId);

    /**
     * 根据授权信息查询明细表数据
     * @param applyIds
     * @return
     */
    List<AuthDetail> selectAuthDetailByDeployId(@Param("applyIds") List<Long> applyIds, @Param("type") String type);

    /**
     * 根据权限明细表id集合逻辑删除授权数据
     * @param authDetailIds
     */
    void deleteAuthDetailByIds(@Param("authIds") List<Long> authDetailIds);

    /**
     * 根据权限主表id集合逻辑删除授权数据
     * @param authMainIds
     */
    void deleteAuthMainByIds(@Param("authMainIds")List<Long> authMainIds);
}
