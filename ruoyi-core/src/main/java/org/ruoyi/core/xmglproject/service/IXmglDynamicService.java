package org.ruoyi.core.xmglproject.service;

import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.xmglproject.domain.XmglDynamic;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName IXmglDynamicService.java
 * @Description wangzeyu
 * @createTime 2022年12月16日 14:37:00
 */
public interface IXmglDynamicService {
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */

    public XmglDynamic selectXmglDynamicById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param xmglDynamic 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    public List<XmglDynamic> selectXmglDynamicList(XmglDynamic xmglDynamic);

    /**
     * 新增【请填写功能名称】
     *
     * @param xmglDynamic 【请填写功能名称】
     * @return 结果
     */
    public int insertXmglDynamic(XmglDynamic xmglDynamic);

    /**
     * 修改【请填写功能名称】
     *
     * @param xmglDynamic 【请填写功能名称】
     * @return 结果
     */

    public int updateXmglDynamic(XmglDynamic xmglDynamic);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */

    public int deleteXmglDynamicByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */

    public int deleteXmglDynamicById(Long id);
}
