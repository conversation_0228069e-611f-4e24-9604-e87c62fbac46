package org.ruoyi.core.xmglproject.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

public class XmglProjectTypeRelevance extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 项目立项id */
    @Excel(name = "项目立项id")
    private Long projectId;

    /** 项目名称id */
    @Excel(name = "项目名称id")
    private Long deployId;

    /** 类型id */
    @Excel(name = "类型id")
    private Long typeId;

    /** 0项目类型，1业务类型 */
    @Excel(name = "0项目类型，1业务类型")
    private String dataType;

    /** 状态，0正常 1停用 */
    @Excel(name = "状态，0正常 1停用")
    private String status;

    private String typeName;

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setProjectId(Long projectId)
    {
        this.projectId = projectId;
    }

    public Long getProjectId()
    {
        return projectId;
    }
    public void setDeployId(Long deployId)
    {
        this.deployId = deployId;
    }

    public Long getDeployId()
    {
        return deployId;
    }
    public void setTypeId(Long typeId)
    {
        this.typeId = typeId;
    }

    public Long getTypeId()
    {
        return typeId;
    }
    public void setDataType(String dataType)
    {
        this.dataType = dataType;
    }

    public String getDataType()
    {
        return dataType;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
}
