package org.ruoyi.core.xmglproject.service;

import com.ruoyi.system.domain.AuthMain;
import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.oasystem.domain.OaEditApproveGeneralityEditRecords;
import org.ruoyi.core.xmglproject.domain.AuthDetailVo;
import org.ruoyi.core.xmglproject.domain.XmglDeployProject;
import org.ruoyi.core.xmglproject.domain.XmglProject;
import org.ruoyi.core.xmglproject.domain.XmglProjectCompanyRelevance;

import java.util.List;
import java.util.Set;

public interface IXmglProjectDeployService {

    /**
     * 查询立项项目-项目名称关联
     *
     * @param deployId 项目名称模块的项目id
     * @return 立项项目-项目名称关联
     */
    public XmglDeployProject selectXmglProjectDeployByDeployId(Long deployId);

    /**
     * 查询立项项目-项目名称关联
     * @param projectId 项目立项模块的项目id
     * @return
     */
    public XmglDeployProject selectXmglProjectDeployByProjectId(Long projectId);

    /**
     * 查询当前用户在‘授权’中是担任了什么角色(项目负责人/业务管理员/查看权限)
     * @param userId
     * @param deployId
     */
    List<AuthMain> selectXmglProjectDeployInfoByDeployId(Long userId, Long deployId);

    /**
     * 查询当前用户在'项目名称'模块是否是该项目的业务责任人
     * @param userId 用户id
     * @param deployId 项目名称模块的项目id
     * @return
     */
    OaEditApproveGeneralityEditRecords selectProjectYWPerson(Long userId, Long deployId);

    /**
     * 项目终止后，更新项目名称-立项项目关联表状态为已终止
     * @param xmglDeployProject
     * @return
     */
    int updateProjectDeployStatus(XmglDeployProject xmglDeployProject);

    /**
     * 根据项目名称id查询所有的立项项目id
     * @param deployIdSet
     * @return
     */
    List<Long> selectProjectInfoByDeployIdList(Set<Long> deployIdSet);

    /**
     * 获取立项项目关联的项目名称id
     * @param projectId
     * @return
     */
    XmglDeployProject selectBeingProjectDeployInfoByProjectId(Long projectId);

    /**
     * 根据业务信息配置的项目名称id查询立项项目信息列表
     * @param authProjectIds
     * @return
     */
    List<XmglProject> selectProjectInfoListByDeployIds(List<Long> authProjectIds);
}
