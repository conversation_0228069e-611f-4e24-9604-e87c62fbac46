package org.ruoyi.core.xmglproject.mapper;

import com.ruoyi.system.domain.AuthDetail;
import com.ruoyi.system.domain.AuthMain;
import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.xmglproject.domain.XmglFlowRelation;

import java.util.List;

/**
 * 立项项目-审批流程关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-09
 */
public interface XmglFlowRelationMapper 
{
    /**
     * 查询立项项目-审批流程关联
     * 
     * @param flowId 立项项目-审批流程关联主键
     * @return 立项项目-审批流程关联
     */
    public XmglFlowRelation selectXmglFlowRelationById(String flowId);

    /**
     * 新增立项项目-审批流程关联
     * 
     * @param xmglFlowRelation 立项项目-审批流程关联
     * @return 结果
     */
    public int insertXmglFlowRelation(XmglFlowRelation xmglFlowRelation);

    /**
     * 修改立项项目-审批流程关联
     * 
     * @param xmglFlowRelation 立项项目-审批流程关联
     * @return 结果
     */
    public int updateXmglFlowRelation(XmglFlowRelation xmglFlowRelation);

    int insertAuthMain(AuthMain authMain);

    int insertAuthDteail(AuthDetail authDetail);

    /**
     * 根据立项项目id和流程分类查询流程
     * @param projectId
     * @param flowClassify
     * @return
     */
    XmglFlowRelation getFlowRelationInfoByProjectIdAndFlowClassify(@Param("projectId") Long projectId, @Param("flowClassify") String flowClassify);

    /**
     * 批量插入权限主表
     * @param authMainList
     */
    int insertAuthMainList(@Param("authMainList") List<AuthMain> authMainList);

    /**
     * 批量插入权限附表
     * @param authDetailList
     */
    int insertAuthDteailList(@Param("authDetailList")List<AuthDetail> authDetailList);
}
