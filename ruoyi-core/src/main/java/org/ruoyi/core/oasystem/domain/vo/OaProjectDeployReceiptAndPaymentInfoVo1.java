package org.ruoyi.core.oasystem.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 项目信息-收付款信息表（返回前端的数据   需要在外面再包一层）
 *
 * @Description
 * <AUTHOR>
 * @Date 2024/12/23 16:17
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class OaProjectDeployReceiptAndPaymentInfoVo1 {
    private static final long serialVersionUID = 1L;

    //备用的oldId集合
//    private String oldIdListStr;

    //新增/修改标识  0-新增 1-修改
    private String editFlag;

    List<OaProjectDeployReceiptAndPaymentInfoVo> oaProjectDeployReceiptAndPaymentInfoVoList;
}
