package org.ruoyi.core.oasystem.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.oasystem.domain.OaVoucherRulesVice;

/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-11-09
 */
public interface OaVoucherRulesViceMapper 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public OaVoucherRulesVice selectOaVoucherRulesViceById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param oaVoucherRulesVice 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<OaVoucherRulesVice> selectOaVoucherRulesViceList(OaVoucherRulesVice oaVoucherRulesVice);

    /**
     * 新增【请填写功能名称】
     * 
     * @param oaVoucherRulesVice 【请填写功能名称】
     * @return 结果
     */
    public int insertOaVoucherRulesVice(OaVoucherRulesVice oaVoucherRulesVice);

    /**
     * 修改【请填写功能名称】
     * 
     * @param oaVoucherRulesVice 【请填写功能名称】
     * @return 结果
     */
    public int updateOaVoucherRulesVice(OaVoucherRulesVice oaVoucherRulesVice);

    /**
     * 删除【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteOaVoucherRulesViceById(Long id);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOaVoucherRulesViceByIds(Long[] ids);

    List<Long> selectIdByMainId(@Param("id") Long id);

    void deleteViceByMainId(@Param("id") Long id);
}
