package org.ruoyi.core.oasystem.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.entity.SysUserPost;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import org.ruoyi.core.cwproject.domain.TopNotify;
import org.ruoyi.core.cwproject.mapper.TopNotifyMapper;
import org.ruoyi.core.oasystem.domain.*;
import org.ruoyi.core.oasystem.domain.bo.OaProjectFlowMainBo;
import org.ruoyi.core.oasystem.domain.bo.OaTraderBo;
import org.ruoyi.core.oasystem.domain.dto.OaProjectFlowUtilDto;
import org.ruoyi.core.oasystem.domain.dto.OaTraderDto;
import org.ruoyi.core.oasystem.domain.vo.OaEditApproveGeneralityEditRecordsVo;
import org.ruoyi.core.oasystem.mapper.*;
import org.ruoyi.core.oasystem.service.IOaCommonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * OA公共的方法接口 - Service业务层处理
 *
 * @Description
 * <AUTHOR>
 * @Date 2024/2/2 16:22
 **/
@Service
public class OaCommonServiceImpl implements IOaCommonService {
    @Autowired
    private SysDeptMapper sysDeptMapper;

    @Autowired
    private TopNotifyMapper topNotifyMapper;

    @Autowired
    private OaEditApproveGeneralityUserMapper oaEditApproveGeneralityUserMapper;

    @Autowired
    private OaEditApproveGeneralityEditRecordsMapper oaEditApproveGeneralityEditRecordsMapper;

    @Autowired
    private OaEditApproveGeneralityRecordsMapper oaEditApproveGeneralityRecordsMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private OaTraderDynamicMapper oaTraderDynamicMapper;

    //付款人配置
    private static final String OA_TRADER_1 = "1";
    //收款人配置
    private static final String OA_TRADER_2 = "2";
    //收/付款人配置
    private static final String OA_TRADER_9 = "9";
    //项目与流程关联
    private static final String OA_PROJECT_FLOW_MAIN = "3";
    //OA流程审核中 状态
    private static final String OA_LAUNCH_CHECK = "99";

    @Autowired
    private OaTraderMapper oaTraderMapper;

    @Autowired
    private OaProjectFlowMainMapper oaProjectFlowMainMapper;

    @Autowired
    private OaProjectFlowAssociationMapper oaProjectFlowAssociationMapper;

    @Autowired
    private OaProjectPayerAssociationMapper oaProjectPayerAssociationMapper;

    @Autowired
    private OaProjectFlowMainServiceImpl oaProjectFlowMainService;

    @Override
    public Map<String, Object> selectListBeforeParam(LoginUser loginUser, String selectType, String oaNotifyType) {
        Map<String, Object> paramMap = new HashMap<>();
        boolean present = loginUser.getUser().getRoles().stream().anyMatch(r -> "admin".equals(r.getRoleKey()) || "caiwuAdmin".equals(r.getRoleKey()) || "OA".equals(r.getRoleKey()) || "yewuAdmin".equals(r.getRoleKey()));
        //进行角色的判断，如果是上述角色，则没有公司限制，如果不是，则查找自己所属的公司
        if (present) {
            paramMap.put("companyIdList", null);
        } else {
            List<SysUserPost> userPostList = loginUser.getUser().getUserPostList();
            //筛选出用户所在的岗位
            List<Long> postList = userPostList.stream().map(SysUserPost::getPostId).collect(Collectors.toList());
            //根据岗位集合找到公司的集合
            //post找到了，找dept，然后找unit
            List<SysDept> deptList = sysDeptMapper.selectDeptListByPostIds(postList);
            List<Long> companyIdList = deptList.stream().map(SysDept::getUnitId).distinct().collect(Collectors.toList());
            if (companyIdList.size() > 0) {
                paramMap.put("companyIdList", companyIdList);
            } else {
                paramMap.put("companyIdList", null);
            }
        }
        //查待办通知表，筛选出来通知表中存在的的项目Id（oaApplyId）再根据方法传过来的oaNotifyType来确定具体筛选出来的结果
        if (!"0".equals(selectType)) {
            TopNotify topNotify = new TopNotify();
            topNotify.setNotifyType("1");
            topNotify.setDisposeUser(loginUser.getUserId());
            topNotify.setOaNotifyType(oaNotifyType);
            topNotify.setOaNotifyStep(selectType);
            List<TopNotify> topNotifies = topNotifyMapper.selectTopNotifyList1(topNotify);
            List<Long> filterOaApplyId = topNotifies.stream().map(TopNotify::getOaApplyId).distinct().collect(Collectors.toList());
            if (filterOaApplyId.size() > 0) {
                paramMap.put("filterOaApplyId", filterOaApplyId);
            } else {
                paramMap.put("filterOaApplyId", null);
            }
        }
        return paramMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addNewEditInfo(OaTraderBo oaTraderBo, LoginUser loginUser) {
        //来的请求是针对于付款人还是收款人
        String currentOaApplyType = "0".equals(oaTraderBo.getTraderType())?OA_TRADER_1:OA_TRADER_2;
        Long oaApplyId = oaTraderBo.getId();
        Date nowDate = DateUtils.getNowDate();
        //当前用户的姓名
        String nickName = loginUser.getUser().getNickName();
        //当前用户的用户id
        Long currentUserId = loginUser.getUserId();
        //当前用户的角色相关信息
        List<SysRole> roles = loginUser.getUser().getRoles();
        boolean yewuFlag = roles.stream().anyMatch(t -> "yewu".equals(t.getRoleKey()));
        boolean yewuAdminFlag = roles.stream().anyMatch(t -> "yewuAdmin".equals(t.getRoleKey()));
        //财务角色 ----> 会计或者出纳
        boolean caiwuFlag = roles.stream().anyMatch(t -> "kuaiji".equals(t.getRoleKey()) || "chuna".equals(t.getRoleKey()));
        boolean caiwuAdminFlag = roles.stream().anyMatch(t -> "caiwuAdmin".equals(t.getRoleKey()));
        String checkStatus = StringUtils.EMPTY;
        //实体的存在，是为了对库里的数据进行操作，新增，修改，或者删除
        OaTrader oaTrader = new OaTrader();
        //这个DTO对象的存在，是为了获取一个JSON字符串
        OaTraderDto oaTraderDto = new OaTraderDto();
        //首先进行判断编辑的类型
        String editType = oaTraderBo.getEditType();
        List<Long> salesmanList = oaTraderBo.getSalesmanList();
        List<Long> financialStaffList = oaTraderBo.getFinancialStaffList();
        if (yewuFlag || yewuAdminFlag) {
            //如果操作者为业务，那么需要财务去审核
            checkStatus = "1";
//            if (yewuFlag) {
//                //将用户id加入到业务集合中
//                financialStaffList.add(currentUserId);
//            }
        } else if (caiwuFlag || caiwuAdminFlag){
            checkStatus = "0";
//            //将用户id加入到业务集合中
//            if (caiwuFlag) {
//                salesmanList.add(currentUserId);
//            }
        }
        OaEditApproveGeneralityUser salesmanUser = new OaEditApproveGeneralityUser();
        //oaApplyType为1或2代表是收付款人
        salesmanUser.setOaApplyId(oaApplyId);
        salesmanUser.setOaApplyType(currentOaApplyType);
        salesmanUser.setUserFlag("0");
        salesmanUser.setStatus("0");
        salesmanUser.setCreateBy(nickName);
        salesmanUser.setCreateTime(nowDate);
        salesmanUser.setUpdateBy(nickName);
        salesmanUser.setUpdateTime(nowDate);
        OaEditApproveGeneralityUser financialStaffUser = new OaEditApproveGeneralityUser();
        //oaApplyType为1或2代表是收付款人
        financialStaffUser.setOaApplyId(oaApplyId);
        financialStaffUser.setOaApplyType(currentOaApplyType);
        financialStaffUser.setUserFlag("1");
        financialStaffUser.setStatus("0");
        financialStaffUser.setCreateBy(nickName);
        financialStaffUser.setCreateTime(nowDate);
        financialStaffUser.setUpdateBy(nickName);
        financialStaffUser.setUpdateTime(nowDate);
        if ("0".equals(editType)) {
            //新增状态的话，就是直接落库，落一个数据结构JSON，不审核，添加人员到项目当中
            //新增数据
            OaTrader oaTrader1 = new OaTrader();
            oaTrader1.setCompanyNo(oaTraderBo.getCompanyNo());
            oaTrader1.setTraderType(oaTraderBo.getTraderType());
            oaTrader1.setType(oaTraderBo.getType());
            oaTrader1.setUserName(oaTraderBo.getUserName());
            oaTrader1.setBankOfDeposit(oaTraderBo.getBankOfDeposit());
            oaTrader1.setAccountNumber(oaTraderBo.getAccountNumber());
            oaTrader1.setAbbreviation(oaTraderBo.getAbbreviation());
            oaTrader1.setAccountId(oaTraderBo.getAccountId());
            oaTrader1.setIsAccount(oaTraderBo.getIsAccount());
            oaTrader1.setIsEnable(oaTraderBo.getIsEnable());
            Date nowDate1 = DateUtils.getNowDate();
            oaTrader1.setCreateBy(nickName);
            oaTrader1.setUpdateBy(nickName);
            oaTrader1.setEndUpdateTime(nowDate1);
            oaTrader1.setCreateTime(nowDate1);
            oaTrader1.setUpdateTime(nowDate1);
            oaTrader1.setAddNotApprove("0");
            int i = oaTraderMapper.insertOaTrader(oaTrader1);
            Long oaTraderId = oaTrader1.getId();
            //得到插入生成的id 添加动态表
            OaTraderDynamic oaTraderDynamic = new OaTraderDynamic();
            oaTraderDynamic.setOaTraderId(oaTraderId);
            oaTraderDynamic.setOperationTime(DateUtils.getNowDate());
            oaTraderDynamic.setOperationContent("创建付款人");
            oaTraderDynamic.setOperationBrId(loginUser.getUserId());
            oaTraderDynamic.setOperationBr(loginUser.getUser().getNickName());
            oaTraderDynamicMapper.insertOaTraderDynamic(oaTraderDynamic);
            //新增数据结束，开始操作其他
            BeanUtil.copyProperties(oaTraderBo, oaTrader);
            String jsonString = JSONObject.toJSONString(oaTrader);
            //新增一条记录到oa_edit_approve_generality_records表中
            OaEditApproveGeneralityRecords oaEditApproveGeneralityRecords = new OaEditApproveGeneralityRecords();
            oaEditApproveGeneralityRecords.setOaApplyType(currentOaApplyType);
            oaEditApproveGeneralityRecords.setOaApplyId(oaTraderId);
            oaEditApproveGeneralityRecords.setData(jsonString);
            oaEditApproveGeneralityRecords.setStatus("0");
            oaEditApproveGeneralityRecords.setCreateBy(nickName);
            oaEditApproveGeneralityRecords.setCreateTime(nowDate);
            oaEditApproveGeneralityRecords.setUpdateBy(nickName);
            oaEditApproveGeneralityRecords.setUpdateTime(nowDate);
            int i22 = oaEditApproveGeneralityRecordsMapper.insertOaEditApproveGeneralityRecords(oaEditApproveGeneralityRecords);
            //根据主表id删除所有人员
            int i1 = oaEditApproveGeneralityUserMapper.deleteOaEditApproveGeneralityUserByOaApplyTypeAndOaApplyId(currentOaApplyType, oaApplyId);
            //新增本项目负责的用户数据
            for (Long userId:salesmanList) {
                //用户为财务
                salesmanUser.setOaApplyId(oaTraderId);
                salesmanUser.setUserId(userId);
                int i2 = oaEditApproveGeneralityUserMapper.insertOaEditApproveGeneralityUser(salesmanUser);
            }
            for (Long userId:financialStaffList) {
                //用户为业务
                financialStaffUser.setOaApplyId(oaTraderId);
                financialStaffUser.setUserId(userId);
                int i2 = oaEditApproveGeneralityUserMapper.insertOaEditApproveGeneralityUser(financialStaffUser);
            }
            //新增的id获取到，添加一个记录到oa_edit_approve_generality_edit_records表中
            if (i > 0) {
                Long oaApplyRecordsNewId = oaEditApproveGeneralityRecords.getId();
                OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = new OaEditApproveGeneralityEditRecords();
                oaEditApproveGeneralityEditRecords.setOaApplyType(currentOaApplyType);
                oaEditApproveGeneralityEditRecords.setOaApplyId(oaTraderId);
                oaEditApproveGeneralityEditRecords.setOaApplyRecordsOldId(oaTraderBo.getOaApplyRecordsOldId());
                oaEditApproveGeneralityEditRecords.setOaApplyRecordsNewId(oaApplyRecordsNewId);
                oaEditApproveGeneralityEditRecords.setEditUserId(loginUser.getUserId());
                oaEditApproveGeneralityEditRecords.setEditTime(DateUtils.getNowDate());
                oaEditApproveGeneralityEditRecords.setEditInfo(oaTraderBo.getEditInfo());
                oaEditApproveGeneralityEditRecords.setCheckStatus(checkStatus);
                oaEditApproveGeneralityEditRecords.setRejectFlag("0");
                oaEditApproveGeneralityEditRecords.setConfirmFlag("1");
                int i2 = oaEditApproveGeneralityEditRecordsMapper.insertOaEditApproveGeneralityEditRecords(oaEditApproveGeneralityEditRecords);
            }
            return 1;
        }

        if ("1".equals(editType)) {
            Long oaApplyRecordsOldIdForAddFirst = null;
            //做修改的时候，首先做一个判断，查一下历史数据有没有初始化入库，如果没有，则入一个新增的数据。如果有，则正常业务进行
            //查询所有的编辑记录
            List<OaEditApproveGeneralityEditRecordsVo> oaEditApproveGeneralityEditRecordsVos = oaEditApproveGeneralityEditRecordsMapper.selectAllEditRecordListByOaApplyTypeAndOaApplyId(currentOaApplyType, oaApplyId);
            if (oaEditApproveGeneralityEditRecordsVos.size() == 0) {
                //超管身份进行落库
                List<SysUser> userList = sysUserMapper.selectUserByRoleKey("admin");
                Long editUserIdOfAdmin = null;
                String adminNickName = null;
                if (userList.size() > 0) {
                    editUserIdOfAdmin = userList.get(0).getUserId();
                    adminNickName = userList.get(0).getNickName();
                }
                //说明之前没有进行过数据落库，那边进行一个数据的落库
                OaTrader oaTrader2 = oaTraderMapper.selectOaTraderById(oaApplyId);
                String jsonString = JSONObject.toJSONString(oaTrader2);
                //新增一条记录到oa_edit_approve_generality_records表中
                OaEditApproveGeneralityRecords oaEditApproveGeneralityRecords = new OaEditApproveGeneralityRecords();
                oaEditApproveGeneralityRecords.setOaApplyType(currentOaApplyType);
                oaEditApproveGeneralityRecords.setOaApplyId(oaApplyId);
                oaEditApproveGeneralityRecords.setData(jsonString);
                oaEditApproveGeneralityRecords.setStatus("0");
                oaEditApproveGeneralityRecords.setCreateBy(adminNickName);
                oaEditApproveGeneralityRecords.setCreateTime(nowDate);
                oaEditApproveGeneralityRecords.setUpdateBy(adminNickName);
                oaEditApproveGeneralityRecords.setUpdateTime(nowDate);
                int i = oaEditApproveGeneralityRecordsMapper.insertOaEditApproveGeneralityRecords(oaEditApproveGeneralityRecords);
                //新增的id获取到，添加一个记录到oa_edit_approve_generality_edit_records表中
                if (i > 0) {
                    Long oaApplyRecordsNewId = oaEditApproveGeneralityRecords.getId();
                    OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = new OaEditApproveGeneralityEditRecords();
                    oaEditApproveGeneralityEditRecords.setOaApplyType(currentOaApplyType);
                    oaEditApproveGeneralityEditRecords.setOaApplyId(oaApplyId);
                    oaEditApproveGeneralityEditRecords.setOaApplyRecordsOldId(null);
                    oaEditApproveGeneralityEditRecords.setOaApplyRecordsNewId(oaApplyRecordsNewId);
                    oaEditApproveGeneralityEditRecords.setEditUserId(editUserIdOfAdmin);
                    oaEditApproveGeneralityEditRecords.setEditTime(DateUtils.getNowDate());
                    oaEditApproveGeneralityEditRecords.setEditInfo("超级管理员初始化现存数据");
                    oaEditApproveGeneralityEditRecords.setCheckStatus("9");
                    oaEditApproveGeneralityEditRecords.setRejectFlag("0");
                    oaEditApproveGeneralityEditRecords.setConfirmFlag("1");
                    int i2 = oaEditApproveGeneralityEditRecordsMapper.insertOaEditApproveGeneralityEditRecords(oaEditApproveGeneralityEditRecords);
                    //新增的新 = 修改的老
                    oaApplyRecordsOldIdForAddFirst = oaApplyRecordsNewId;
                }
            }


            //类型为修改的一条审核记录
            BeanUtil.copyProperties(oaTraderBo, oaTrader);
            String jsonString = JSONObject.toJSONString(oaTrader);
            //新增一条记录到oa_edit_approve_generality_records表中
            OaEditApproveGeneralityRecords oaEditApproveGeneralityRecords = new OaEditApproveGeneralityRecords();
            oaEditApproveGeneralityRecords.setOaApplyType(currentOaApplyType);
            oaEditApproveGeneralityRecords.setOaApplyId(oaApplyId);
            oaEditApproveGeneralityRecords.setData(jsonString);
            oaEditApproveGeneralityRecords.setStatus("0");
            oaEditApproveGeneralityRecords.setCreateBy(nickName);
            oaEditApproveGeneralityRecords.setCreateTime(nowDate);
            oaEditApproveGeneralityRecords.setUpdateBy(nickName);
            oaEditApproveGeneralityRecords.setUpdateTime(nowDate);
            int i = oaEditApproveGeneralityRecordsMapper.insertOaEditApproveGeneralityRecords(oaEditApproveGeneralityRecords);
            //根据主表id删除所有人员
            int i1 = oaEditApproveGeneralityUserMapper.deleteOaEditApproveGeneralityUserByOaApplyTypeAndOaApplyId(currentOaApplyType, oaApplyId);
            //新增本项目负责的用户数据
            for (Long userId:salesmanList) {
                //用户为财务
                salesmanUser.setUserId(userId);
                int i2 = oaEditApproveGeneralityUserMapper.insertOaEditApproveGeneralityUser(salesmanUser);
            }
            for (Long userId:financialStaffList) {
                //用户为业务
                financialStaffUser.setUserId(userId);
                int i2 = oaEditApproveGeneralityUserMapper.insertOaEditApproveGeneralityUser(financialStaffUser);
            }
            //新增的id获取到，添加一个记录到oa_edit_approve_generality_edit_records表中
            if (i > 0) {
                Long oaApplyRecordsNewId = oaEditApproveGeneralityRecords.getId();
                OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = new OaEditApproveGeneralityEditRecords();
                oaEditApproveGeneralityEditRecords.setOaApplyType(currentOaApplyType);
                oaEditApproveGeneralityEditRecords.setOaApplyId(oaApplyId);
                if (oaTraderBo.getOaApplyRecordsOldId() == null) {
                    oaTraderBo.setOaApplyRecordsOldId(oaApplyRecordsOldIdForAddFirst);
                }
                oaEditApproveGeneralityEditRecords.setOaApplyRecordsOldId(oaTraderBo.getOaApplyRecordsOldId());
                oaEditApproveGeneralityEditRecords.setOaApplyRecordsNewId(oaApplyRecordsNewId);
                oaEditApproveGeneralityEditRecords.setEditUserId(loginUser.getUserId());
                oaEditApproveGeneralityEditRecords.setEditTime(DateUtils.getNowDate());
                oaEditApproveGeneralityEditRecords.setEditInfo(oaTraderBo.getEditInfo());
                oaEditApproveGeneralityEditRecords.setCheckStatus(checkStatus);
                oaEditApproveGeneralityEditRecords.setConfirmFlag("0");
                int i2 = oaEditApproveGeneralityEditRecordsMapper.insertOaEditApproveGeneralityEditRecords(oaEditApproveGeneralityEditRecords);
            }
            //修改的oa_trader一个状态，这个状态用来显示是否可以进行编辑
            OaTrader oaTrader1 = new OaTrader();
            oaTrader1.setId(oaApplyId);
            oaTrader1.setAddNotApprove("1");
            int i3 = oaTraderMapper.updateOaTrader(oaTrader1);
        }
        //待办通知
        TopNotify topNotify = new TopNotify();
        topNotify.setNotifyType("1");
        if (OA_TRADER_1.equals(currentOaApplyType)) {
            topNotify.setNotifyModule("付款人信息发生修改");
            topNotify.setUrl("/oa/payment");
            topNotify.setNotifyMsg(nickName + "提交[" + oaTraderBo.getUserName() + "]的付款人信息修改，请审核");
        } else if (OA_TRADER_2.equals(currentOaApplyType)){
            topNotify.setNotifyModule("收款人信息发生修改");
            topNotify.setUrl("/oa/collection");
            topNotify.setNotifyMsg(nickName + "提交[" + oaTraderBo.getUserName() + "]的收款人信息修改，请审核");
        }
        //这里projectId不能为空，否则入库会报错
        topNotify.setProjectId(0L);
        //这里incomeId不能为空，否则入库会报错
        topNotify.setIncomeId(0L);
        topNotify.setViewFlag("0");
        topNotify.setCreateBy(nickName);
        topNotify.setCreateTime(nowDate);
        topNotify.setUpdateBy(nickName);
        topNotify.setUpdateTime(nowDate);
        //代表通知是项目名称配置的
        topNotify.setOaNotifyType(currentOaApplyType);
        //代表跳转URL的视图为待我审核
        topNotify.setOaNotifyStep("1");
        //OA功能的id
        topNotify.setOaApplyId(oaApplyId);
        if (yewuFlag || yewuAdminFlag) {
            //用户为业务或者是业务管理员，通知财务去待办
            for (Long userId:salesmanList) {
                //用户为财务
                topNotify.setDisposeUser(userId);
                topNotifyMapper.insertTopNotify(topNotify);
            }
        } else if (caiwuFlag || caiwuAdminFlag) {
            //用户为财务或者是财务管理员，通知财务去待办
            for (Long userId:financialStaffList) {
                //用户为业务
                topNotify.setDisposeUser(userId);
                topNotifyMapper.insertTopNotify(topNotify);
            }
        }
        return 1;
    }

    @Override
    public OaEditApproveGeneralityEditRecordsVo selectOaEditApproveGeneralityEditRecordsDetailByOaApplyTypeAndOaApplyId(String oaApplyType, Long oaApplyId) {
        PageHelper.clearPage();
        OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = oaEditApproveGeneralityEditRecordsMapper.selectNewOaEditApproveGeneralityEditRecordsByOaApplyTypeAndOaApplyId(oaApplyType, oaApplyId, "0");
        OaEditApproveGeneralityEditRecordsVo oaEditApproveGeneralityEditRecordsVo = new OaEditApproveGeneralityEditRecordsVo();
        BeanUtil.copyProperties(oaEditApproveGeneralityEditRecords, oaEditApproveGeneralityEditRecordsVo);
        if (oaEditApproveGeneralityEditRecords.getOaApplyRecordsOldId() == null && oaEditApproveGeneralityEditRecords.getOaApplyRecordsNewId() != null) {
            //新增
            oaEditApproveGeneralityEditRecordsVo.setApplyType("0");
        } else if (oaEditApproveGeneralityEditRecords.getOaApplyRecordsOldId() != null && oaEditApproveGeneralityEditRecords.getOaApplyRecordsNewId() != null) {
            //修改
            oaEditApproveGeneralityEditRecordsVo.setApplyType("1");
        } else if (oaEditApproveGeneralityEditRecords.getOaApplyRecordsOldId() != null && oaEditApproveGeneralityEditRecords.getOaApplyRecordsNewId() == null) {
            //删除
            oaEditApproveGeneralityEditRecordsVo.setApplyType("2");
        }
        if ("0".equals(oaEditApproveGeneralityEditRecordsVo.getCheckStatus())) {
            oaEditApproveGeneralityEditRecordsVo.setIdentity("财务负责人");
        } else if ("1".equals(oaEditApproveGeneralityEditRecordsVo.getCheckStatus())) {
            oaEditApproveGeneralityEditRecordsVo.setIdentity("业务负责人");
        }
        //查询财务管理员和业务管理员
        List<SysUser> caiwuAdminList = sysUserMapper.selectUserByRoleKey("caiwuAdmin");
        List<SysUser> yewuAdminList = sysUserMapper.selectUserByRoleKey("yewuAdmin");
        List<SysUser> adminList = sysUserMapper.selectUserByRoleKey("admin");
        OaEditApproveGeneralityUser editUser = oaEditApproveGeneralityUserMapper.selectOaEditApproveGeneralityUserByOaApplyTypeAndOaApplyIdAndUserId(oaApplyType, oaApplyId, oaEditApproveGeneralityEditRecords.getEditUserId());
        if (editUser != null) {
            oaEditApproveGeneralityEditRecordsVo.setEditIdentity(editUser.getUserFlag());
        } else {
            List<SysUser> caiwuAdmin = caiwuAdminList.stream().filter(t -> oaEditApproveGeneralityEditRecordsVo.getEditUserId().equals(t.getUserId())).collect(Collectors.toList());
            if (caiwuAdmin.size() == 0) {
                List<SysUser> yewuAdmin = yewuAdminList.stream().filter(t -> oaEditApproveGeneralityEditRecordsVo.getEditUserId().equals(t.getUserId())).collect(Collectors.toList());
                if (yewuAdmin.size() == 0) {
                    boolean b = adminList.stream().anyMatch(t -> oaEditApproveGeneralityEditRecordsVo.getEditUserId().equals(t.getUserId()));
                    if (b) {
                        oaEditApproveGeneralityEditRecordsVo.setEditIdentity("9");
                    } else {
                        oaEditApproveGeneralityEditRecordsVo.setEditIdentity(null);
                    }
                } else {
                    oaEditApproveGeneralityEditRecordsVo.setEditIdentity("3");
                }
            } else {
                oaEditApproveGeneralityEditRecordsVo.setEditIdentity("2");
            }
        }
        OaEditApproveGeneralityUser checkUser = oaEditApproveGeneralityUserMapper.selectOaEditApproveGeneralityUserByOaApplyTypeAndOaApplyIdAndUserId(oaApplyType, oaApplyId, oaEditApproveGeneralityEditRecords.getCheckUserId());
        if (checkUser != null) {
            oaEditApproveGeneralityEditRecordsVo.setEditIdentity(checkUser.getUserFlag());
        } else {
            List<SysUser> caiwuAdmin = caiwuAdminList.stream().filter(t -> oaEditApproveGeneralityEditRecordsVo.getEditUserId().equals(t.getUserId())).collect(Collectors.toList());
            if (caiwuAdmin.size() == 0) {
                List<SysUser> yewuAdmin = yewuAdminList.stream().filter(t -> oaEditApproveGeneralityEditRecordsVo.getEditUserId().equals(t.getUserId())).collect(Collectors.toList());
                if (yewuAdmin.size() == 0) {
                    oaEditApproveGeneralityEditRecordsVo.setEditIdentity(null);
                } else {
                    oaEditApproveGeneralityEditRecordsVo.setEditIdentity("3");
                }
            } else {
                oaEditApproveGeneralityEditRecordsVo.setEditIdentity("2");
            }
        }
        return oaEditApproveGeneralityEditRecordsVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int checkOaEditApproveGenerality(OaEditApproveGeneralityEditRecordsVo oaEditApproveGeneralityEditRecordsVo, LoginUser loginUser) {
        String oaApplyType = oaEditApproveGeneralityEditRecordsVo.getOaApplyType();
        Long oaApplyId = oaEditApproveGeneralityEditRecordsVo.getOaApplyId();
        Date nowDate = DateUtils.getNowDate();
        //当前用户的姓名
        String nickName = loginUser.getUser().getNickName();
        //当前用户的用户id
        Long currentUserId = loginUser.getUserId();
        //提交人id（也就是编辑发起人）
        Long editUserId = oaEditApproveGeneralityEditRecordsVo.getEditUserId();
        //提交人对象
//        SysUser editUser = sysUserMapper.selectUserById(editUserId);
        String rejectFlag = oaEditApproveGeneralityEditRecordsVo.getRejectFlag();
        if ("0".equals(rejectFlag)) {
            //通过之后，落实修改
            //收付款人只有修改的请求
            Long oaApplyRecordsOldId = oaEditApproveGeneralityEditRecordsVo.getOaApplyRecordsOldId();
            Long oaApplyRecordsNewId = oaEditApproveGeneralityEditRecordsVo.getOaApplyRecordsNewId();
            if (oaApplyRecordsOldId != null && oaApplyRecordsNewId != null) {
                //修改
                OaEditApproveGeneralityRecords oaEditApproveGeneralityRecords = oaEditApproveGeneralityRecordsMapper.selectOaEditApproveGeneralityRecordsById(oaApplyRecordsNewId);
                String data = oaEditApproveGeneralityRecords.getData();
                OaTrader oaTrader = JSON.toJavaObject(JSON.parseObject(data), OaTrader.class);
                oaTrader.setAddNotApprove("1");
                oaTrader.setId(oaApplyId);
                int i = oaTraderMapper.updateOaTrader(oaTrader);
            }
        }
        //更新oa_edit_approve_generality_edit_records表状态
        OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = new OaEditApproveGeneralityEditRecords();
        oaEditApproveGeneralityEditRecords.setId(oaEditApproveGeneralityEditRecordsVo.getId());
        oaEditApproveGeneralityEditRecords.setOaApplyType(oaApplyType);
        oaEditApproveGeneralityEditRecords.setOaApplyId(oaApplyId);
        oaEditApproveGeneralityEditRecords.setCheckUserId(currentUserId);
        oaEditApproveGeneralityEditRecords.setCheckTime(nowDate);
        oaEditApproveGeneralityEditRecords.setRejectFlag(rejectFlag);
        oaEditApproveGeneralityEditRecords.setCheckRejectInfo(oaEditApproveGeneralityEditRecordsVo.getCheckRejectInfo());
        //把之前提交的待办状态改掉
        TopNotify topNotify = new TopNotify();
        topNotify.setNotifyType("1");
        topNotify.setOaNotifyType(oaApplyType);
        topNotify.setOaNotifyStep("1");
        topNotify.setOaApplyId(oaApplyId);
        List<TopNotify> topNotifies = topNotifyMapper.selectTopNotifyList1(topNotify);
        //通过主表id找到当前项目的所有审批中的通知
        List<Long> collect = topNotifies.stream().map(TopNotify::getId).collect(Collectors.toList());
        //修改之前的待办
        if (collect.size() > 0) {
            int a = topNotifyMapper.updateTopNotifyTypeAndViewFlagByIds(collect, "0", "1", "0");
        }
        //新的待办
        OaTrader oaTrader = oaTraderMapper.selectOaTraderById(oaApplyId);
        TopNotify topNotify1 = new TopNotify();
        if (OA_TRADER_1.equals(oaApplyType)) {
            topNotify1.setNotifyModule("付款人信息发生修改");
            topNotify1.setUrl("/oa/payment");
            if ("0".equals(rejectFlag)) {
                //通过  -->  通过后给提交人发待办
                topNotify1.setNotifyMsg("您提交[" + oaTrader.getUserName() + "]的付款人信息修改已生效");
            } else {
                //驳回  -->  驳回后给提交人发待办
                topNotify1.setNotifyMsg("您提交[" + oaTrader.getUserName() + "]的付款人信息修改被驳回");
            }
        } else if (OA_TRADER_2.equals(oaApplyType)) {
            topNotify1.setNotifyModule("收款人信息发生修改");
            topNotify1.setUrl("/oa/collection");
            if ("0".equals(rejectFlag)) {
                //通过  -->  通过后给提交人发待办
                topNotify1.setNotifyMsg("您提交[" + oaTrader.getUserName() + "]的收款人信息修改已生效");
            } else {
                //驳回  -->  驳回后给提交人发待办
                topNotify1.setNotifyMsg("您提交[" + oaTrader.getUserName() + "]收款人信息修改被驳回");
            }
        }
        topNotify1.setDisposeUser(editUserId);
        topNotify1.setNotifyType("1");
        //这里projectId不能为空，否则入库会报错
        topNotify1.setProjectId(0L);
        //这里incomeId不能为空，否则入库会报错
        topNotify1.setIncomeId(0L);
        topNotify1.setViewFlag("0");
        topNotify1.setCreateBy(nickName);
        topNotify1.setCreateTime(nowDate);
        topNotify1.setUpdateBy(nickName);
        topNotify1.setUpdateTime(nowDate);
        //代表通知是收付款人的
        topNotify1.setOaNotifyType(oaApplyType);
        //代表跳转URL的视图为我的提交
        topNotify1.setOaNotifyStep("2");
        //OA功能的id
        topNotify1.setOaApplyId(oaApplyId);
        topNotifyMapper.insertTopNotify(topNotify1);
        return oaEditApproveGeneralityEditRecordsMapper.updateOaEditApproveGeneralityEditRecords(oaEditApproveGeneralityEditRecords);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int confirmOaEditApproveGeneralityEditRecords(OaEditApproveGeneralityEditRecordsVo oaEditApproveGeneralityEditRecordsVo, LoginUser loginUser) {
        Long userId = loginUser.getUserId();
        String oaApplyType = oaEditApproveGeneralityEditRecordsVo.getOaApplyType();
        Long oaApplyId = oaEditApproveGeneralityEditRecordsVo.getOaApplyId();
        PageHelper.clearPage();
        OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = oaEditApproveGeneralityEditRecordsMapper.selectNewOaEditApproveGeneralityEditRecordsByOaApplyTypeAndOaApplyId(oaApplyType, oaApplyId, "0");
        //在进行已知悉的时候，把待办改为通知
        TopNotify topNotify = new TopNotify();
        topNotify.setNotifyType("1");
        topNotify.setOaNotifyType(oaApplyType);
        topNotify.setOaNotifyStep("2");
        topNotify.setOaApplyId(oaApplyId);
        List<TopNotify> topNotifies = topNotifyMapper.selectTopNotifyList1(topNotify);
        List<Long> collect = topNotifies.stream().map(TopNotify::getId).collect(Collectors.toList());
        //修改之前的待办
        if (collect.size() > 0) {
            int a = topNotifyMapper.updateTopNotifyTypeAndViewFlagByIds(collect, "0", "1", "0");
        }
        //收付款人没有删除，只有修改。所以在确认已知悉的时候，就直接已知悉就行
        OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords1 = new OaEditApproveGeneralityEditRecords();
        oaEditApproveGeneralityEditRecords1.setId(oaEditApproveGeneralityEditRecords.getId());
        oaEditApproveGeneralityEditRecords1.setConfirmFlag("1");
        int i = oaEditApproveGeneralityEditRecordsMapper.updateOaEditApproveGeneralityEditRecords(oaEditApproveGeneralityEditRecords1);
        //状态已经改为了已知悉，改主表的状态
        OaTrader oaTrader = new OaTrader();
        oaTrader.setId(oaApplyId);
        oaTrader.setAddNotApprove("0");
        return oaTraderMapper.updateOaTrader(oaTrader);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addNewEditInfoOfOaProjectFlow(OaProjectFlowMainBo oaProjectFlowMainBo, LoginUser loginUser) {
        //主表id
        Long oaApplyId = oaProjectFlowMainBo.getId();
        Date nowDate = DateUtils.getNowDate();
        //当前用户的姓名
        String nickName = loginUser.getUser().getNickName();
        //当前用户的用户id
        Long currentUserId = loginUser.getUserId();
        //当前模板名称
        String modelName = oaProjectFlowMainBo.getModelName();
        //当前用户的角色相关信息
        List<SysRole> roles = loginUser.getUser().getRoles();
        boolean yewuFlag = roles.stream().anyMatch(t -> "yewu".equals(t.getRoleKey()));
        boolean yewuAdminFlag = roles.stream().anyMatch(t -> "yewuAdmin".equals(t.getRoleKey()));
        //财务角色 ----> 会计或者出纳
        boolean caiwuFlag = roles.stream().anyMatch(t -> "kuaiji".equals(t.getRoleKey()) || "chuna".equals(t.getRoleKey()));
        boolean caiwuAdminFlag = roles.stream().anyMatch(t -> "caiwuAdmin".equals(t.getRoleKey()));
        String checkStatus = StringUtils.EMPTY;
        //首先进行判断编辑的类型
        String editType = oaProjectFlowMainBo.getEditType();
        OaProjectFlowUtilDto oaProjectFlowUtilDto = new OaProjectFlowUtilDto();
        List<Long> salesmanList = oaProjectFlowMainBo.getSalesmanList();
        List<Long> financialStaffList = oaProjectFlowMainBo.getFinancialStaffList();
        if (yewuFlag || yewuAdminFlag) {
            //如果操作者为业务，那么需要财务去审核
            checkStatus = "1";
//            if (yewuFlag) {
//                //将用户id加入到业务集合中
//                financialStaffList.add(currentUserId);
//            }
        } else if (caiwuFlag || caiwuAdminFlag){
            checkStatus = "0";
            //将用户id加入到业务集合中
//            if (caiwuFlag) {
//                salesmanList.add(currentUserId);
//            }
        }
        OaEditApproveGeneralityUser salesmanUser = new OaEditApproveGeneralityUser();
        salesmanUser.setOaApplyId(oaApplyId);
        salesmanUser.setOaApplyType(OA_PROJECT_FLOW_MAIN);
        salesmanUser.setUserFlag("0");
        salesmanUser.setStatus("0");
        salesmanUser.setCreateBy(nickName);
        salesmanUser.setCreateTime(nowDate);
        salesmanUser.setUpdateBy(nickName);
        salesmanUser.setUpdateTime(nowDate);
        OaEditApproveGeneralityUser financialStaffUser = new OaEditApproveGeneralityUser();
        //oaApplyType为1或2代表是收付款人
        financialStaffUser.setOaApplyId(oaApplyId);
        financialStaffUser.setOaApplyType(OA_PROJECT_FLOW_MAIN);
        financialStaffUser.setUserFlag("1");
        financialStaffUser.setStatus("0");
        financialStaffUser.setCreateBy(nickName);
        financialStaffUser.setCreateTime(nowDate);
        financialStaffUser.setUpdateBy(nickName);
        financialStaffUser.setUpdateTime(nowDate);
        //修改主表 - 实体类
        OaProjectFlowMain oaProjectFlowMain = new OaProjectFlowMain();
        oaProjectFlowMain.setId(oaApplyId);
        if ("0".equals(editType)) {
            //新增状态的话，就是直接落库，落一个数据结构JSON，不审核，添加人员到项目当中
            //新增数据
            OaProjectFlowMain oaProjectFlowMain1 = new OaProjectFlowMain();
            oaProjectFlowMain1.setCompanyNo(oaProjectFlowMainBo.getCompanyNo());
            oaProjectFlowMain1.setModelId(oaProjectFlowMainBo.getModelId());
            oaProjectFlowMain1.setModelName(oaProjectFlowMainBo.getModelName());

            oaProjectFlowMain.setAccountingField(oaProjectFlowUtilDto.getAccountingField());
            oaProjectFlowMain.setAccountingFieldName(oaProjectFlowUtilDto.getAccountingFieldName());
            oaProjectFlowMain.setProjectTypeField(oaProjectFlowUtilDto.getProjectTypeField());
            oaProjectFlowMain.setProjectTypeFieldName(oaProjectFlowUtilDto.getProjectTypeFieldName());
            oaProjectFlowMain.setProjectField(oaProjectFlowUtilDto.getProjectField());

            oaProjectFlowMain1.setRemark(oaProjectFlowMainBo.getRemark());
            oaProjectFlowMain1.setCreateTime(DateUtils.getNowDate());
            oaProjectFlowMain1.setStatus(oaProjectFlowMainBo.getStatus());
            oaProjectFlowMain1.setCreateBr(loginUser.getUsername());
            int i = oaProjectFlowMainMapper.insertOaProjectFlowMain(oaProjectFlowMain1);

            Long mainId = oaProjectFlowMain1.getId();
            List<OaProjectFlowUtil2> proList = oaProjectFlowMainBo.getProList();
            // 得到id后添加关联项目
            for (OaProjectFlowUtil2 oaProjectFlowUtil2 : proList) {
                OaProjectFlowAssociation oaProjectFlowAssociation = new OaProjectFlowAssociation();
                oaProjectFlowAssociation.setProjectFlowMainId(mainId);
                oaProjectFlowAssociation.setProjectId(Long.valueOf(oaProjectFlowUtil2.getProjectId()));
                oaProjectFlowAssociation.setProjectName(oaProjectFlowUtil2.getProjectName());
                oaProjectFlowAssociation.setStatus("0");
                oaProjectFlowAssociation.setCreateBr(loginUser.getUsername());
                oaProjectFlowAssociation.setCreateTime(DateUtils.getNowDate());
                oaProjectFlowAssociationMapper.insertOaProjectFlowAssociation(oaProjectFlowAssociation);
                Long projectid = oaProjectFlowAssociation.getId();
                List<OaProjectPayerAssociation> tableList = oaProjectFlowUtil2.getTableList();
                for (OaProjectPayerAssociation oaProjectPayerAssociation : tableList) {
                    oaProjectPayerAssociation.setPfaId(projectid);
                    oaProjectPayerAssociation.setStatus("0");
                    oaProjectPayerAssociation.setCreateBr(loginUser.getUsername());
                    oaProjectFlowAssociation.setCreateTime(DateUtils.getNowDate());
                    oaProjectPayerAssociationMapper.insertOaProjectPayerAssociation(oaProjectPayerAssociation);
                }
            }
            //新增数据结束，开始操作其他
            BeanUtil.copyProperties(oaProjectFlowMainBo, oaProjectFlowUtilDto);
            String jsonString = JSONObject.toJSONString(oaProjectFlowUtilDto);
            //新增一条记录到oa_edit_approve_generality_records表中
            OaEditApproveGeneralityRecords oaEditApproveGeneralityRecords = new OaEditApproveGeneralityRecords();
            oaEditApproveGeneralityRecords.setOaApplyType(OA_PROJECT_FLOW_MAIN);
            oaEditApproveGeneralityRecords.setOaApplyId(mainId);
            oaEditApproveGeneralityRecords.setData(jsonString);
            oaEditApproveGeneralityRecords.setStatus("0");
            oaEditApproveGeneralityRecords.setCreateBy(nickName);
            oaEditApproveGeneralityRecords.setCreateTime(nowDate);
            oaEditApproveGeneralityRecords.setUpdateBy(nickName);
            oaEditApproveGeneralityRecords.setUpdateTime(nowDate);
            int i22 = oaEditApproveGeneralityRecordsMapper.insertOaEditApproveGeneralityRecords(oaEditApproveGeneralityRecords);
            //根据主表id删除所有人员
            int i1 = oaEditApproveGeneralityUserMapper.deleteOaEditApproveGeneralityUserByOaApplyTypeAndOaApplyId(OA_PROJECT_FLOW_MAIN, oaApplyId);
            //新增本项目负责的用户数据
            for (Long userId:salesmanList) {
                //用户为财务
                salesmanUser.setOaApplyId(mainId);
                salesmanUser.setUserId(userId);
                int i2 = oaEditApproveGeneralityUserMapper.insertOaEditApproveGeneralityUser(salesmanUser);
            }
            for (Long userId:financialStaffList) {
                //用户为业务
                financialStaffUser.setOaApplyId(mainId);
                financialStaffUser.setUserId(userId);
                int i2 = oaEditApproveGeneralityUserMapper.insertOaEditApproveGeneralityUser(financialStaffUser);
            }
            //新增的id获取到，添加一个记录到oa_edit_approve_generality_edit_records表中
            if (i > 0) {
                Long oaApplyRecordsNewId = oaEditApproveGeneralityRecords.getId();
                OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = new OaEditApproveGeneralityEditRecords();
                oaEditApproveGeneralityEditRecords.setOaApplyType(OA_PROJECT_FLOW_MAIN);
                oaEditApproveGeneralityEditRecords.setOaApplyId(mainId);
                oaEditApproveGeneralityEditRecords.setOaApplyRecordsOldId(oaProjectFlowMainBo.getOaApplyRecordsOldId());
                oaEditApproveGeneralityEditRecords.setOaApplyRecordsNewId(oaApplyRecordsNewId);
                oaEditApproveGeneralityEditRecords.setEditUserId(loginUser.getUserId());
                oaEditApproveGeneralityEditRecords.setEditTime(DateUtils.getNowDate());
                oaEditApproveGeneralityEditRecords.setEditInfo(oaProjectFlowMainBo.getEditInfo());
                oaEditApproveGeneralityEditRecords.setCheckStatus(checkStatus);
                oaEditApproveGeneralityEditRecords.setRejectFlag("0");
                oaEditApproveGeneralityEditRecords.setConfirmFlag("1");
                int i2 = oaEditApproveGeneralityEditRecordsMapper.insertOaEditApproveGeneralityEditRecords(oaEditApproveGeneralityEditRecords);
            }
            return 1;
        }

        if ("1".equals(editType)) {
            Long oaApplyRecordsOldIdForAddFirst = null;
            //做修改的时候，首先做一个判断，查一下历史数据有没有初始化入库，如果没有，则入一个新增的数据。如果有，则正常业务进行
            //查询所有的编辑记录
            List<OaEditApproveGeneralityEditRecordsVo> oaEditApproveGeneralityEditRecordsVos = oaEditApproveGeneralityEditRecordsMapper.selectAllEditRecordListByOaApplyTypeAndOaApplyId(OA_PROJECT_FLOW_MAIN, oaApplyId);
            if (oaEditApproveGeneralityEditRecordsVos.size() == 0) {
                //超管身份进行落库
                List<SysUser> userList = sysUserMapper.selectUserByRoleKey("admin");
                Long editUserIdOfAdmin = null;
                String adminNickName = null;
                if (userList.size() > 0) {
                    editUserIdOfAdmin = userList.get(0).getUserId();
                    adminNickName = userList.get(0).getNickName();
                }
                //说明之前没有进行过数据落库，那边进行一个数据的落库
                OaProjectFlowMain oaProjectFlowMain1 = oaProjectFlowMainMapper.selectOaProjectFlowMainById(oaApplyId);
                OaProjectFlowUtilDto oaProjectFlowUtilDto1 = new OaProjectFlowUtilDto();
                BeanUtil.copyProperties(oaProjectFlowMain1, oaProjectFlowUtilDto1);
                List<Map<String, Object>> projectList = oaProjectFlowAssociationMapper.getProjectByMainId(oaApplyId);
                List<OaProjectFlowUtil2> proList = new ArrayList<>();
                for (Map<String, Object> map : projectList) {
                    OaProjectFlowUtil2 oaProjectFlowUtil2 = new OaProjectFlowUtil2();
                    //查询关联的联系人数据
                    List<OaProjectPayerAssociation> tableList = oaProjectPayerAssociationMapper.selectDataByProId1(map.get("id").toString());
                    oaProjectFlowUtil2.setId(Long.parseLong(map.get("id").toString()));
                    oaProjectFlowUtil2.setProjectId(map.get("projectId").toString());
                    oaProjectFlowUtil2.setProjectName(map.get("projectName").toString());
                    oaProjectFlowUtil2.setTableList(tableList);
                    proList.add(oaProjectFlowUtil2);
                }
                oaProjectFlowUtilDto1.setProList(proList);
                String jsonString = JSONObject.toJSONString(oaProjectFlowUtilDto1);
                //新增一条记录到oa_edit_approve_generality_records表中
                OaEditApproveGeneralityRecords oaEditApproveGeneralityRecords = new OaEditApproveGeneralityRecords();
                oaEditApproveGeneralityRecords.setOaApplyType(OA_PROJECT_FLOW_MAIN);
                oaEditApproveGeneralityRecords.setOaApplyId(oaApplyId);
                oaEditApproveGeneralityRecords.setData(jsonString);
                oaEditApproveGeneralityRecords.setStatus("0");
                oaEditApproveGeneralityRecords.setCreateBy(adminNickName);
                oaEditApproveGeneralityRecords.setCreateTime(nowDate);
                oaEditApproveGeneralityRecords.setUpdateBy(adminNickName);
                oaEditApproveGeneralityRecords.setUpdateTime(nowDate);
                int i = oaEditApproveGeneralityRecordsMapper.insertOaEditApproveGeneralityRecords(oaEditApproveGeneralityRecords);
                //新增的id获取到，添加一个记录到oa_edit_approve_generality_edit_records表中
                if (i > 0) {
                    Long oaApplyRecordsNewId = oaEditApproveGeneralityRecords.getId();
                    OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = new OaEditApproveGeneralityEditRecords();
                    oaEditApproveGeneralityEditRecords.setOaApplyType(OA_PROJECT_FLOW_MAIN);
                    oaEditApproveGeneralityEditRecords.setOaApplyId(oaApplyId);
                    oaEditApproveGeneralityEditRecords.setOaApplyRecordsOldId(null);
                    oaEditApproveGeneralityEditRecords.setOaApplyRecordsNewId(oaApplyRecordsNewId);
                    oaEditApproveGeneralityEditRecords.setEditUserId(editUserIdOfAdmin);
                    oaEditApproveGeneralityEditRecords.setEditTime(DateUtils.getNowDate());
                    oaEditApproveGeneralityEditRecords.setEditInfo("超级管理员初始化现存数据");
                    oaEditApproveGeneralityEditRecords.setCheckStatus("9");
                    oaEditApproveGeneralityEditRecords.setRejectFlag("0");
                    oaEditApproveGeneralityEditRecords.setConfirmFlag("1");
                    int i2 = oaEditApproveGeneralityEditRecordsMapper.insertOaEditApproveGeneralityEditRecords(oaEditApproveGeneralityEditRecords);
                    //新增的新 = 修改的老
                    oaApplyRecordsOldIdForAddFirst = oaApplyRecordsNewId;
                }
            }


            //类型为修改的一条审核记录
            //主表状态暂时改为1-不允许编辑
            oaProjectFlowMainBo.setStatus("1");
            BeanUtil.copyProperties(oaProjectFlowMainBo, oaProjectFlowUtilDto);
            String jsonString = JSONObject.toJSONString(oaProjectFlowUtilDto);
            //新增一条记录到oa_edit_approve_generality_records表中
            OaEditApproveGeneralityRecords oaEditApproveGeneralityRecords = new OaEditApproveGeneralityRecords();
            oaEditApproveGeneralityRecords.setOaApplyType(OA_PROJECT_FLOW_MAIN);
            oaEditApproveGeneralityRecords.setOaApplyId(oaApplyId);
            oaEditApproveGeneralityRecords.setData(jsonString);
            oaEditApproveGeneralityRecords.setStatus("0");
            oaEditApproveGeneralityRecords.setCreateBy(nickName);
            oaEditApproveGeneralityRecords.setCreateTime(nowDate);
            oaEditApproveGeneralityRecords.setUpdateBy(nickName);
            oaEditApproveGeneralityRecords.setUpdateTime(nowDate);
            int i = oaEditApproveGeneralityRecordsMapper.insertOaEditApproveGeneralityRecords(oaEditApproveGeneralityRecords);
            //根据主表id删除所有人员
            int i1 = oaEditApproveGeneralityUserMapper.deleteOaEditApproveGeneralityUserByOaApplyTypeAndOaApplyId(OA_PROJECT_FLOW_MAIN, oaApplyId);
            //新增本项目负责的用户数据
            for (Long userId:salesmanList) {
                //用户为财务
                salesmanUser.setUserId(userId);
                int i2 = oaEditApproveGeneralityUserMapper.insertOaEditApproveGeneralityUser(salesmanUser);
            }
            for (Long userId:financialStaffList) {
                //用户为业务
                financialStaffUser.setUserId(userId);
                int i2 = oaEditApproveGeneralityUserMapper.insertOaEditApproveGeneralityUser(financialStaffUser);
            }
            //新增的id获取到，添加一个记录到oa_edit_approve_generality_edit_records表中
            if (i > 0) {
                Long oaApplyRecordsNewId = oaEditApproveGeneralityRecords.getId();
                OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = new OaEditApproveGeneralityEditRecords();
                oaEditApproveGeneralityEditRecords.setOaApplyType(OA_PROJECT_FLOW_MAIN);
                oaEditApproveGeneralityEditRecords.setOaApplyId(oaApplyId);
                if (oaProjectFlowMainBo.getOaApplyRecordsOldId() == null) {
                    oaProjectFlowMainBo.setOaApplyRecordsOldId(oaApplyRecordsOldIdForAddFirst);
                }
                oaEditApproveGeneralityEditRecords.setOaApplyRecordsOldId(oaProjectFlowMainBo.getOaApplyRecordsOldId());
                oaEditApproveGeneralityEditRecords.setOaApplyRecordsNewId(oaApplyRecordsNewId);
                oaEditApproveGeneralityEditRecords.setEditUserId(loginUser.getUserId());
                oaEditApproveGeneralityEditRecords.setEditTime(DateUtils.getNowDate());
                oaEditApproveGeneralityEditRecords.setEditInfo(oaProjectFlowMainBo.getEditInfo());
                oaEditApproveGeneralityEditRecords.setCheckStatus(checkStatus);
                oaEditApproveGeneralityEditRecords.setConfirmFlag("0");
                int i2 = oaEditApproveGeneralityEditRecordsMapper.insertOaEditApproveGeneralityEditRecords(oaEditApproveGeneralityEditRecords);
            }
            //修改主表
            oaProjectFlowMain.setStatus("1");
        } else if ("2".equals(editType)) {
            //类型为删除的一条审核记录
            //根据主表id删除所有人员
            int i1 = oaEditApproveGeneralityUserMapper.deleteOaEditApproveGeneralityUserByOaApplyTypeAndOaApplyId(OA_PROJECT_FLOW_MAIN, oaApplyId);
            //新增本项目负责的用户数据
            for (Long userId:salesmanList) {
                //用户为财务
                salesmanUser.setUserId(userId);
                int i2 = oaEditApproveGeneralityUserMapper.insertOaEditApproveGeneralityUser(salesmanUser);
            }
            for (Long userId:financialStaffList) {
                //用户为业务
                financialStaffUser.setUserId(userId);
                int i2 = oaEditApproveGeneralityUserMapper.insertOaEditApproveGeneralityUser(financialStaffUser);
            }
            //新增的id获取到，添加一个记录到oa_edit_approve_generality_edit_records表中
            OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = new OaEditApproveGeneralityEditRecords();
            oaEditApproveGeneralityEditRecords.setOaApplyType(OA_PROJECT_FLOW_MAIN);
            oaEditApproveGeneralityEditRecords.setOaApplyId(oaApplyId);
            oaEditApproveGeneralityEditRecords.setOaApplyRecordsOldId(oaProjectFlowMainBo.getOaApplyRecordsOldId());
            oaEditApproveGeneralityEditRecords.setOaApplyRecordsNewId(null);
            oaEditApproveGeneralityEditRecords.setEditUserId(loginUser.getUserId());
            oaEditApproveGeneralityEditRecords.setEditTime(DateUtils.getNowDate());
            oaEditApproveGeneralityEditRecords.setEditInfo(oaProjectFlowMainBo.getEditInfo());
            oaEditApproveGeneralityEditRecords.setCheckStatus(checkStatus);
            oaEditApproveGeneralityEditRecords.setConfirmFlag("0");
            int i2 = oaEditApproveGeneralityEditRecordsMapper.insertOaEditApproveGeneralityEditRecords(oaEditApproveGeneralityEditRecords);
            //主表状态暂时改为2-删除编辑状态
            oaProjectFlowMain.setStatus("2");
        }
        oaProjectFlowMainMapper.updateOaProjectFlowMain(oaProjectFlowMain);
        //待办通知
        TopNotify topNotify = new TopNotify();
        topNotify.setNotifyModule("项目与流程关联发生修改");
        topNotify.setNotifyType("1");
        topNotify.setUrl("/oa/projectAndFlow");
        //这里projectId不能为空，否则入库会报错
        topNotify.setProjectId(0L);
        //这里incomeId不能为空，否则入库会报错
        topNotify.setIncomeId(0L);
        topNotify.setViewFlag("0");
        topNotify.setCreateBy(nickName);
        topNotify.setCreateTime(nowDate);
        topNotify.setUpdateBy(nickName);
        topNotify.setUpdateTime(nowDate);
        //代表通知是项目与流程关联的
        topNotify.setOaNotifyType("3");
        //代表跳转URL的视图为待我审核
        topNotify.setOaNotifyStep("1");
        //OA功能的id
        topNotify.setOaApplyId(oaApplyId);
        if ("1".equals(oaProjectFlowMain.getStatus())) {
            topNotify.setNotifyMsg(nickName + "提交[" + modelName + "]的项目与流程关联修改，请审核");
        } else if ("2".equals(oaProjectFlowMain.getStatus())) {
            topNotify.setNotifyMsg(nickName + "提交申请删除[" + modelName + "]的项目与流程关联，请审核");
        }
        if (yewuFlag || yewuAdminFlag) {
            //用户为业务或者是业务管理员，通知财务去待办
            for (Long userId:salesmanList) {
                //用户为财务
                topNotify.setDisposeUser(userId);
                topNotifyMapper.insertTopNotify(topNotify);
            }
        } else if (caiwuFlag || caiwuAdminFlag) {
            //用户为财务或者是财务管理员，通知财务去待办
            for (Long userId:financialStaffList) {
                //用户为业务
                topNotify.setDisposeUser(userId);
                topNotifyMapper.insertTopNotify(topNotify);
            }
        }
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int checkOaEditApproveGeneralityOfOaProjectFlow(OaEditApproveGeneralityEditRecordsVo oaEditApproveGeneralityEditRecordsVo, LoginUser loginUser) {
        String oaApplyType = oaEditApproveGeneralityEditRecordsVo.getOaApplyType();
        Long oaApplyId = oaEditApproveGeneralityEditRecordsVo.getOaApplyId();
        Date nowDate = DateUtils.getNowDate();
        //当前用户的姓名
        String nickName = loginUser.getUser().getNickName();
        //当前用户的用户id
        Long currentUserId = loginUser.getUserId();
        //提交人id（也就是编辑发起人）
        Long editUserId = oaEditApproveGeneralityEditRecordsVo.getEditUserId();
        String rejectFlag = oaEditApproveGeneralityEditRecordsVo.getRejectFlag();
        boolean deleteFlag = false;
        if ("0".equals(rejectFlag)) {
            //通过之后，落实修改
            //落实修改有两种种情况 修改 删除 -----> 对应的调取之前的入库方法
            Long oaApplyRecordsNewId = oaEditApproveGeneralityEditRecordsVo.getOaApplyRecordsNewId();
            if (oaApplyRecordsNewId != null) {
                //修改
                OaEditApproveGeneralityRecords oaEditApproveGeneralityRecords = oaEditApproveGeneralityRecordsMapper.selectOaEditApproveGeneralityRecordsById(oaApplyRecordsNewId);
                String data = oaEditApproveGeneralityRecords.getData();
                OaProjectFlowUtilDto oaProjectFlowUtilDto = JSON.toJavaObject(JSON.parseObject(data), OaProjectFlowUtilDto.class);
                //这里相当于重新的删除新增，所以在这里要加一个标识 1 - 代表这个只能是查看还不能审核
                oaProjectFlowUtilDto.setStatus("1");
                oaProjectFlowUtilDto.setId(oaApplyId);
                int i = oaProjectFlowMainService.addProjectAndFlowDto(oaProjectFlowUtilDto, loginUser);
            } else {
                //oaApplyRecordsNewId为空，说明是删除
                //这里先不删除，主表状态为了避免出错，再更新一边为待删除状态
                OaProjectFlowMain oaProjectFlowMain = new OaProjectFlowMain();
                oaProjectFlowMain.setId(oaApplyId);
                oaProjectFlowMain.setStatus("2");
                int i = oaProjectFlowMainMapper.updateOaProjectFlowMain(oaProjectFlowMain);
                deleteFlag = true;
            }
        }
        //更新oa_edit_approve_generality_edit_records表状态
        OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = new OaEditApproveGeneralityEditRecords();
        oaEditApproveGeneralityEditRecords.setId(oaEditApproveGeneralityEditRecordsVo.getId());
        oaEditApproveGeneralityEditRecords.setOaApplyType(oaApplyType);
        oaEditApproveGeneralityEditRecords.setOaApplyId(oaApplyId);
        oaEditApproveGeneralityEditRecords.setCheckUserId(currentUserId);
        oaEditApproveGeneralityEditRecords.setCheckTime(nowDate);
        oaEditApproveGeneralityEditRecords.setRejectFlag(rejectFlag);
        oaEditApproveGeneralityEditRecords.setCheckRejectInfo(oaEditApproveGeneralityEditRecordsVo.getCheckRejectInfo());
        //把之前提交的待办状态改掉
        TopNotify topNotify = new TopNotify();
        topNotify.setNotifyType("1");
        topNotify.setOaNotifyType(oaApplyType);
        topNotify.setOaNotifyStep("1");
        topNotify.setOaApplyId(oaApplyId);
        List<TopNotify> topNotifies = topNotifyMapper.selectTopNotifyList1(topNotify);
        //通过主表id找到当前项目的所有审批中的通知
        List<Long> collect = topNotifies.stream().map(TopNotify::getId).collect(Collectors.toList());
        //修改之前的待办
        if (collect.size() > 0) {
            int a = topNotifyMapper.updateTopNotifyTypeAndViewFlagByIds(collect, "0", "1", "0");
        }
        //新的待办
        //todo 可能会出错
        OaProjectFlowMain oaProjectFlowMain = new OaProjectFlowMain();
        oaProjectFlowMain.setId(oaApplyId);
        List<Map<String, Object>> list = oaProjectFlowMainMapper.selectOaProjectFlowList(oaProjectFlowMain);
        String modelName = (String) list.get(0).get("modelName");
        TopNotify topNotify1 = new TopNotify();
        topNotify1.setNotifyModule("项目与流程关联发生修改");
        topNotify1.setNotifyType("1");
        if (deleteFlag) {
            //是删除的提示
            if ("0".equals(rejectFlag)) {
                //通过
                topNotify1.setNotifyMsg("您提交删除" + modelName + "的项目与流程关联已生效");
            } else {
                topNotify1.setNotifyMsg("您提交删除" + modelName + "的项目与流程关联被驳回");
            }
        } else {
            //是修改的提示
            if ("0".equals(rejectFlag)) {
                topNotify1.setNotifyMsg("您提交" + modelName + "的项目与流程关联修改已生效");
            } else {
                topNotify1.setNotifyMsg("您提交" + modelName + "的项目与流程关联修改被驳回");
            }
        }
        topNotify1.setUrl("/oa/projectAndFlow");
        //这里projectId不能为空，否则入库会报错
        topNotify1.setProjectId(0L);
        //这里incomeId不能为空，否则入库会报错
        topNotify1.setIncomeId(0L);
        topNotify1.setViewFlag("0");
        topNotify1.setDisposeUser(editUserId);
        topNotify1.setCreateBy(nickName);
        topNotify1.setCreateTime(nowDate);
        topNotify1.setUpdateBy(nickName);
        topNotify1.setUpdateTime(nowDate);
        //代表通知是记账凭证规则的
        topNotify1.setOaNotifyType("3");
        //代表跳转URL的视图为我的提交
        topNotify1.setOaNotifyStep("2");
        //OA功能的id
        topNotify1.setOaApplyId(oaApplyId);
        topNotifyMapper.insertTopNotify(topNotify1);
        return oaEditApproveGeneralityEditRecordsMapper.updateOaEditApproveGeneralityEditRecords(oaEditApproveGeneralityEditRecords);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int confirmOaEditApproveGeneralityEditRecordsOfOaProjectFlow(OaEditApproveGeneralityEditRecordsVo oaEditApproveGeneralityEditRecordsVo, LoginUser loginUser) {
        Long userId = loginUser.getUserId();
        //根据主表找审批记录表
        String oaApplyType = oaEditApproveGeneralityEditRecordsVo.getOaApplyType();
        Long oaApplyId = oaEditApproveGeneralityEditRecordsVo.getOaApplyId();
        PageHelper.clearPage();
        OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = oaEditApproveGeneralityEditRecordsMapper.selectNewOaEditApproveGeneralityEditRecordsByOaApplyTypeAndOaApplyId(oaApplyType, oaApplyId, "0");
        //判断是否是删除
        if (oaEditApproveGeneralityEditRecords.getOaApplyRecordsNewId() == null) {
            //是删除
            if ("0".equals(oaEditApproveGeneralityEditRecords.getRejectFlag())) {
                //审批通过的，先删除通知、删除审批相关记录
                int i = topNotifyMapper.deleteTopNotifyByOaNotifyTypeAndOaApplyId(oaApplyType, oaApplyId);
                int i1 = oaEditApproveGeneralityEditRecordsMapper.deleteOaEditApproveGeneralityEditRecordsByOaApplyTypeAndOaApplyId(oaApplyType, oaApplyId);
                int i2 = oaEditApproveGeneralityRecordsMapper.deleteOaEditApproveGeneralityRecordsByOaApplyTypeAndOaApplyId(oaApplyType, oaApplyId);
                int i3 = oaEditApproveGeneralityUserMapper.deleteOaEditApproveGeneralityUserByOaApplyTypeAndOaApplyId(oaApplyType, oaApplyId);
                //主表
                int i4 = oaProjectFlowMainMapper.deleteOaProjectFlowMainById(oaApplyId);
                int a = 0;
                int b = 0;
                //先通过主表找association表所涉及的所有
                List<Long> pfaIds = oaProjectFlowAssociationMapper.selectOaProjectFlowIdsAssociationByMainId(oaApplyId);
                if (i4 > 0) {
                    a = oaProjectFlowAssociationMapper.deleteByMainId(oaApplyId);
                }
                if (a > 0) {
                    b = oaProjectPayerAssociationMapper.deleteByProAssId(pfaIds);
                }
                return i4;
            }
        }
        //审批被驳回，那么一切留痕操作依然还在，把待办改为通知
        TopNotify topNotify = new TopNotify();
        topNotify.setNotifyType("1");
        topNotify.setOaNotifyType(oaApplyType);
        topNotify.setOaNotifyStep("2");
        topNotify.setOaApplyId(oaApplyId);
        List<TopNotify> topNotifies = topNotifyMapper.selectTopNotifyList1(topNotify);
        List<Long> collect = topNotifies.stream().map(TopNotify::getId).collect(Collectors.toList());
        //修改之前的待办
        if (collect.size() > 0) {
            int a = topNotifyMapper.updateTopNotifyTypeAndViewFlagByIds(collect, "0", "1", "0");
        }
        OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords1 = new OaEditApproveGeneralityEditRecords();
        oaEditApproveGeneralityEditRecords1.setId(oaEditApproveGeneralityEditRecords.getId());
        oaEditApproveGeneralityEditRecords1.setConfirmFlag("1");
        int i = oaEditApproveGeneralityEditRecordsMapper.updateOaEditApproveGeneralityEditRecords(oaEditApproveGeneralityEditRecords1);
        //状态已经改为了已知悉，改主表的状态
        OaProjectFlowMain oaProjectFlowMain = new OaProjectFlowMain();
        oaProjectFlowMain.setId(oaApplyId);
        oaProjectFlowMain.setStatus("0");
        return oaProjectFlowMainMapper.updateOaProjectFlowMain(oaProjectFlowMain);
    }

    @Override
    public List<OaEditApproveGeneralityEditRecordsVo> selectEditRecordByOaVoucherRulesMain(String oaApplyType, Long oaApplyId) {
        List<OaEditApproveGeneralityEditRecordsVo> oaEditApproveGeneralityEditRecords = oaEditApproveGeneralityEditRecordsMapper.selectEditRecordListByOaApplyTypeAndOaApplyId(oaApplyType, oaApplyId);
        //查询财务管理员和业务管理员
        List<SysUser> caiwuAdminList = sysUserMapper.selectUserByRoleKey("caiwuAdmin");
        List<SysUser> yewuAdminList = sysUserMapper.selectUserByRoleKey("yewuAdmin");
        List<SysUser> adminList = sysUserMapper.selectUserByRoleKey("admin");
        for (OaEditApproveGeneralityEditRecordsVo oeager:oaEditApproveGeneralityEditRecords) {
            if (oeager.getOaApplyRecordsOldId() == null && oeager.getOaApplyRecordsNewId() != null) {
                //新增
                oeager.setApplyType("0");
            } else if (oeager.getOaApplyRecordsOldId() != null && oeager.getOaApplyRecordsNewId() != null) {
                //修改
                oeager.setApplyType("1");
            } else if (oeager.getOaApplyRecordsOldId() != null && oeager.getOaApplyRecordsNewId() == null) {
                //删除
                oeager.setApplyType("2");
            }
            Long oaApplyId1 = oeager.getOaApplyId();
            String oaApplyType1 = oeager.getApplyType();
            OaEditApproveGeneralityUser editUser = oaEditApproveGeneralityUserMapper.selectOaEditApproveGeneralityUserByOaApplyTypeAndOaApplyIdAndUserId(oaApplyType1, oaApplyId1, oeager.getEditUserId());
            if (editUser != null) {
                oeager.setEditIdentity(editUser.getUserFlag());
            } else {
                List<SysUser> caiwuAdmin = caiwuAdminList.stream().filter(t -> oeager.getEditUserId().equals(t.getUserId())).collect(Collectors.toList());
                if (caiwuAdmin.size() == 0) {
                    List<SysUser> yewuAdmin = yewuAdminList.stream().filter(t -> oeager.getEditUserId().equals(t.getUserId())).collect(Collectors.toList());
                    if (yewuAdmin.size() == 0) {
                        boolean b = adminList.stream().anyMatch(t -> oeager.getEditUserId().equals(t.getUserId()));
                        if (b) {
                            oeager.setEditIdentity("9");
                        } else {
                            oeager.setEditIdentity(null);
                        }
                    } else {
                        oeager.setEditIdentity("3");
                    }
                } else {
                    oeager.setEditIdentity("2");
                }
            }
            OaEditApproveGeneralityUser checkUser = oaEditApproveGeneralityUserMapper.selectOaEditApproveGeneralityUserByOaApplyTypeAndOaApplyIdAndUserId(oaApplyType1, oaApplyId1, oeager.getCheckUserId());
            if (checkUser != null) {
                oeager.setCheckIdentity(checkUser.getUserFlag());
            } else {
                List<SysUser> caiwuAdmin = caiwuAdminList.stream().filter(t -> oeager.getEditUserId().equals(t.getUserId())).collect(Collectors.toList());
                if (caiwuAdmin.size() == 0) {
                    List<SysUser> yewuAdmin = yewuAdminList.stream().filter(t -> oeager.getEditUserId().equals(t.getUserId())).collect(Collectors.toList());
                    if (yewuAdmin.size() == 0) {
                        oeager.setEditIdentity(null);
                    } else {
                        oeager.setEditIdentity("3");
                    }
                } else {
                    oeager.setEditIdentity("2");
                }
            }
            //项目名称配置与其他三个不一样，要单独处理额外处理
            if ("4".equals(oaApplyType1) && "3".equals(oeager.getEditIdentity())) {
                //3-业务管理员
                //如果是业务管理员新增、修改或者删除的时候
                oeager.setCheckUserId(null);
                oeager.setCheckIdentity(null);
                oeager.setCheckTime(null);
                oeager.setCheckStatus(null);
                oeager.setCheckRejectInfo(null);
            }
        }
        return oaEditApproveGeneralityEditRecords;
    }

    @Override
    public List<OaEditApproveGeneralityEditRecordsVo> selectEditRecordByOaVoucherRulesMainNew(Long oaApplyId) {
        List<String> oaApplyTypeList = new ArrayList<>();
        oaApplyTypeList.add("1");
        oaApplyTypeList.add("2");
        oaApplyTypeList.add("9");
        return oaEditApproveGeneralityEditRecordsMapper.selectEditRecordListByOaApplyTypeListAndOaApplyIdNew(oaApplyTypeList, oaApplyId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addNewEditInfoNew(OaTraderBo oaTraderBo, LoginUser loginUser) {
        //来的请求是针对于付款人还是收款人
        String currentOaApplyType = "0".equals(oaTraderBo.getTraderType())?OA_TRADER_1:"1".equals(oaTraderBo.getTraderType())?OA_TRADER_2:OA_TRADER_9;
        Long oaApplyId = oaTraderBo.getId();
        Date nowDate = DateUtils.getNowDate();
        //当前用户的姓名
        String nickName = loginUser.getUser().getNickName();
        //当前用户的用户id
        Long currentUserId = loginUser.getUserId();
        //审批状态为99 OA流程审核中
        String checkStatus = OA_LAUNCH_CHECK;
        //实体的存在，是为了对库里的数据进行操作，新增，修改，或者删除
        OaTrader oaTrader = new OaTrader();
        //这个DTO对象的存在，是为了获取一个JSON字符串
        OaTraderDto oaTraderDto = new OaTraderDto();
        //首先进行判断编辑的类型 0-新增 1-修改 2-删除
        String editType = oaTraderBo.getEditType();

        String oaApplyRecordsOldData = oaTraderBo.getOaApplyRecordsOldData();
        String oaApplyRecordsNewData = oaTraderBo.getOaApplyRecordsNewData();
//        OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords1 = null;
//        Long lastOaApplyRecordsNewId = null;
//        if (!"0".equals(editType)) {
//            List<String> oaApplyTypeList = new ArrayList<>();
//            oaApplyTypeList.add("1");
//            oaApplyTypeList.add("2");
//            oaApplyTypeList.add("9");
//            //获取上一次审批的记录
//            oaEditApproveGeneralityEditRecords1 = oaEditApproveGeneralityEditRecordsMapper.selectLastOaEditApproveGeneralityEditRecordsByOaApplyTypeListAndoaApplyId(oaApplyTypeList, oaApplyId);
//            //上一次审批的NewDataId是这次的OldDataId
//            if (oaEditApproveGeneralityEditRecords1 != null) {
//                lastOaApplyRecordsNewId = oaEditApproveGeneralityEditRecords1.getOaApplyRecordsNewId();
//            }
//        }
        if ("0".equals(editType)) {
            //新增状态的话，就是直接落库，落一个数据结构JSON，不审核，添加人员到项目当中
            //新增数据
            OaTrader oaTrader1 = new OaTrader();
            oaTrader1.setCompanyNo(oaTraderBo.getCompanyNo());
            oaTrader1.setTraderType(oaTraderBo.getTraderType());
            oaTrader1.setType(oaTraderBo.getType());
            oaTrader1.setUserName(oaTraderBo.getUserName());
            oaTrader1.setBankOfDeposit(oaTraderBo.getBankOfDeposit());
            oaTrader1.setAccountNumber(oaTraderBo.getAccountNumber());
            oaTrader1.setAbbreviation(oaTraderBo.getAbbreviation());
            oaTrader1.setAccountId(oaTraderBo.getAccountId());
            oaTrader1.setIsAccount(oaTraderBo.getIsAccount());
            oaTrader1.setIsEnable(oaTraderBo.getIsEnable());
            Date nowDate1 = DateUtils.getNowDate();
            oaTrader1.setCreateBy(nickName);
            oaTrader1.setUpdateBy(nickName);
            oaTrader1.setEndUpdateTime(nowDate1);
            oaTrader1.setCreateTime(nowDate1);
            oaTrader1.setUpdateTime(nowDate1);
            oaTrader1.setAddNotApprove("99");
            int i = oaTraderMapper.insertOaTrader(oaTrader1);
            Long oaTraderId = oaTrader1.getId();
            //新增数据结束，开始操作其他
            BeanUtil.copyProperties(oaTraderBo, oaTraderDto);
//            String jsonString = JSONObject.toJSONString(oaTraderDto);
//            //新增一条记录到oa_edit_approve_generality_records表中
//            OaEditApproveGeneralityRecords oaEditApproveGeneralityRecords = new OaEditApproveGeneralityRecords();
//            oaEditApproveGeneralityRecords.setOaApplyType(currentOaApplyType);
//            oaEditApproveGeneralityRecords.setOaApplyId(oaTraderId);
//            oaEditApproveGeneralityRecords.setData(jsonString);
//            oaEditApproveGeneralityRecords.setStatus("0");
//            oaEditApproveGeneralityRecords.setCreateBy(nickName);
//            oaEditApproveGeneralityRecords.setCreateTime(nowDate);
//            oaEditApproveGeneralityRecords.setUpdateBy(nickName);
//            oaEditApproveGeneralityRecords.setUpdateTime(nowDate);
//            int i22 = oaEditApproveGeneralityRecordsMapper.insertOaEditApproveGeneralityRecords(oaEditApproveGeneralityRecords);
            //新增的id获取到，添加一个记录到oa_edit_approve_generality_edit_records表中
            if (i > 0) {
//                Long oaApplyRecordsNewId = oaEditApproveGeneralityRecords.getId();
                OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = new OaEditApproveGeneralityEditRecords();
                oaEditApproveGeneralityEditRecords.setOaApplyType(currentOaApplyType);
                oaEditApproveGeneralityEditRecords.setOaApplyId(oaTraderId);
//                oaEditApproveGeneralityEditRecords.setOaApplyRecordsOldId(null);
//                oaEditApproveGeneralityEditRecords.setOaApplyRecordsNewId(oaApplyRecordsNewId);
                oaEditApproveGeneralityEditRecords.setEditUserId(loginUser.getUserId());
                oaEditApproveGeneralityEditRecords.setEditTime(DateUtils.getNowDate());
                oaEditApproveGeneralityEditRecords.setEditInfo(oaTraderBo.getEditInfo());
                oaEditApproveGeneralityEditRecords.setCheckStatus(checkStatus);
                oaEditApproveGeneralityEditRecords.setStatus("0");
                oaEditApproveGeneralityEditRecords.setProcessId(oaTraderBo.getProcessId());
                oaEditApproveGeneralityEditRecords.setEditType(editType);
                oaEditApproveGeneralityEditRecords.setOaApplyRecordsOldData(oaApplyRecordsOldData);
                oaEditApproveGeneralityEditRecords.setOaApplyRecordsNewData(oaApplyRecordsNewData);
                int i2 = oaEditApproveGeneralityEditRecordsMapper.insertOaEditApproveGeneralityEditRecords(oaEditApproveGeneralityEditRecords);
            }
        }
        if ("1".equals(editType)) {
//            Long oaApplyRecordsOldIdForAddFirst = null;
            //做修改的时候，首先做一个判断，查一下历史数据有没有初始化入库，如果没有，则入一个新增的数据。如果有，则正常业务进行
            //查询所有的编辑记录
//            List<OaEditApproveGeneralityEditRecordsVo> oaEditApproveGeneralityEditRecordsVos = oaEditApproveGeneralityEditRecordsMapper.selectAllEditRecordListByOaApplyTypeAndOaApplyId(currentOaApplyType, oaApplyId);
//            if (oaEditApproveGeneralityEditRecordsVos.size() == 0) {
//                //超管身份进行落库
//                List<SysUser> userList = sysUserMapper.selectUserByRoleKey("admin");
//                Long editUserIdOfAdmin = null;
//                String adminNickName = null;
//                if (userList.size() > 0) {
//                    editUserIdOfAdmin = userList.get(0).getUserId();
//                    adminNickName = userList.get(0).getNickName();
//                }
//                //说明之前没有进行过数据落库，那边进行一个数据的落库
//                OaTrader oaTrader2 = oaTraderMapper.selectOaTraderById(oaApplyId);
//                String jsonString = JSONObject.toJSONString(oaTrader2);
//                //新增一条记录到oa_edit_approve_generality_records表中
//                OaEditApproveGeneralityRecords oaEditApproveGeneralityRecords = new OaEditApproveGeneralityRecords();
//                oaEditApproveGeneralityRecords.setOaApplyType(currentOaApplyType);
//                oaEditApproveGeneralityRecords.setOaApplyId(oaApplyId);
//                oaEditApproveGeneralityRecords.setData(jsonString);
//                oaEditApproveGeneralityRecords.setStatus("0");
//                oaEditApproveGeneralityRecords.setCreateBy(adminNickName);
//                oaEditApproveGeneralityRecords.setCreateTime(nowDate);
//                oaEditApproveGeneralityRecords.setUpdateBy(adminNickName);
//                oaEditApproveGeneralityRecords.setUpdateTime(nowDate);
//                int i = oaEditApproveGeneralityRecordsMapper.insertOaEditApproveGeneralityRecords(oaEditApproveGeneralityRecords);
//                //新增的id获取到，添加一个记录到oa_edit_approve_generality_edit_records表中
//                if (i > 0) {
//                    Long oaApplyRecordsNewId = oaEditApproveGeneralityRecords.getId();
//                    OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = new OaEditApproveGeneralityEditRecords();
//                    oaEditApproveGeneralityEditRecords.setOaApplyType(currentOaApplyType);
//                    oaEditApproveGeneralityEditRecords.setOaApplyId(oaApplyId);
//                    oaEditApproveGeneralityEditRecords.setOaApplyRecordsOldId(null);
//                    oaEditApproveGeneralityEditRecords.setOaApplyRecordsNewId(oaApplyRecordsNewId);
//                    oaEditApproveGeneralityEditRecords.setEditUserId(editUserIdOfAdmin);
//                    oaEditApproveGeneralityEditRecords.setEditTime(DateUtils.getNowDate());
//                    oaEditApproveGeneralityEditRecords.setEditInfo("现存数据之前没有过编辑记录，初始化现存数据的记录");
//                    oaEditApproveGeneralityEditRecords.setCheckStatus("96");
//                    oaEditApproveGeneralityEditRecords.setStatus("1");
//                    int i2 = oaEditApproveGeneralityEditRecordsMapper.insertOaEditApproveGeneralityEditRecords(oaEditApproveGeneralityEditRecords);
//                    //新增的新 = 修改的老
//                    oaApplyRecordsOldIdForAddFirst = oaApplyRecordsNewId;
//                }
//            }

            //类型为修改的一条审核记录
            BeanUtil.copyProperties(oaTraderBo, oaTraderDto);
//            String jsonString = JSONObject.toJSONString(oaTraderDto);
//            //新增一条记录到oa_edit_approve_generality_records表中
//            OaEditApproveGeneralityRecords oaEditApproveGeneralityRecords = new OaEditApproveGeneralityRecords();
//            oaEditApproveGeneralityRecords.setOaApplyType(currentOaApplyType);
//            oaEditApproveGeneralityRecords.setOaApplyId(oaApplyId);
//            oaEditApproveGeneralityRecords.setData(jsonString);
//            oaEditApproveGeneralityRecords.setStatus("0");
//            oaEditApproveGeneralityRecords.setCreateBy(nickName);
//            oaEditApproveGeneralityRecords.setCreateTime(nowDate);
//            oaEditApproveGeneralityRecords.setUpdateBy(nickName);
//            oaEditApproveGeneralityRecords.setUpdateTime(nowDate);
//            int i = oaEditApproveGeneralityRecordsMapper.insertOaEditApproveGeneralityRecords(oaEditApproveGeneralityRecords);
            //新增的id获取到，添加一个记录到oa_edit_approve_generality_edit_records表中
//            if (i > 0) {
            OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = new OaEditApproveGeneralityEditRecords();
            oaEditApproveGeneralityEditRecords.setOaApplyType(currentOaApplyType);
            oaEditApproveGeneralityEditRecords.setOaApplyId(oaApplyId);
//                if (oaTraderBo.getOaApplyRecordsOldId() == null) {
//                    oaTraderBo.setOaApplyRecordsOldId(oaApplyRecordsOldIdForAddFirst);
//                }
//            oaEditApproveGeneralityEditRecords.setOaApplyRecordsOldId(lastOaApplyRecordsNewId);
//            oaEditApproveGeneralityEditRecords.setOaApplyRecordsNewId(oaApplyRecordsNewId);
            oaEditApproveGeneralityEditRecords.setEditUserId(loginUser.getUserId());
            oaEditApproveGeneralityEditRecords.setEditTime(DateUtils.getNowDate());
            oaEditApproveGeneralityEditRecords.setEditInfo(oaTraderBo.getEditInfo());
            oaEditApproveGeneralityEditRecords.setCheckStatus(checkStatus);
            oaEditApproveGeneralityEditRecords.setStatus("0");
            oaEditApproveGeneralityEditRecords.setProcessId(oaTraderBo.getProcessId());
            oaEditApproveGeneralityEditRecords.setEditType(editType);
            oaEditApproveGeneralityEditRecords.setOaApplyRecordsOldData(oaApplyRecordsOldData);
            oaEditApproveGeneralityEditRecords.setOaApplyRecordsNewData(oaApplyRecordsNewData);
            int i2 = oaEditApproveGeneralityEditRecordsMapper.insertOaEditApproveGeneralityEditRecords(oaEditApproveGeneralityEditRecords);
//            }
            //修改的oa_trader一个状态，这个状态用来显示是否可以进行编辑
            OaTrader oaTrader2 = new OaTrader();
            oaTrader2.setId(oaApplyId);
            oaTrader2.setAddNotApprove("98");
            int i3 = oaTraderMapper.updateOaTrader(oaTrader2);
        }

        if ("2".equals(editType)) {
//            //新增一条记录到oa_edit_approve_generality_records表中
//            OaEditApproveGeneralityRecords oaEditApproveGeneralityRecords = new OaEditApproveGeneralityRecords();
//            oaEditApproveGeneralityRecords.setOaApplyType(currentOaApplyType);
//            oaEditApproveGeneralityRecords.setOaApplyId(oaApplyId);
//            oaEditApproveGeneralityRecords.setData(null);
//            oaEditApproveGeneralityRecords.setStatus("0");
//            oaEditApproveGeneralityRecords.setCreateBy(nickName);
//            oaEditApproveGeneralityRecords.setCreateTime(nowDate);
//            oaEditApproveGeneralityRecords.setUpdateBy(nickName);
//            oaEditApproveGeneralityRecords.setUpdateTime(nowDate);
//            int i22 = oaEditApproveGeneralityRecordsMapper.insertOaEditApproveGeneralityRecords(oaEditApproveGeneralityRecords);
            //新增的id获取到，添加一个记录到oa_edit_approve_generality_edit_records表中
//            Long oaApplyRecordsNewId = oaEditApproveGeneralityRecords.getId();
            OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = new OaEditApproveGeneralityEditRecords();
            oaEditApproveGeneralityEditRecords.setOaApplyType(currentOaApplyType);
            oaEditApproveGeneralityEditRecords.setOaApplyId(oaApplyId);
//            oaEditApproveGeneralityEditRecords.setOaApplyRecordsOldId(lastOaApplyRecordsNewId);
//            oaEditApproveGeneralityEditRecords.setOaApplyRecordsNewId(null);
            oaEditApproveGeneralityEditRecords.setEditUserId(loginUser.getUserId());
            oaEditApproveGeneralityEditRecords.setEditTime(DateUtils.getNowDate());
            oaEditApproveGeneralityEditRecords.setEditInfo(oaTraderBo.getEditInfo());
            oaEditApproveGeneralityEditRecords.setCheckStatus(checkStatus);
            oaEditApproveGeneralityEditRecords.setStatus("0");
            oaEditApproveGeneralityEditRecords.setProcessId(oaTraderBo.getProcessId());
            oaEditApproveGeneralityEditRecords.setEditType(editType);
            oaEditApproveGeneralityEditRecords.setOaApplyRecordsOldData(oaApplyRecordsOldData);
            oaEditApproveGeneralityEditRecords.setOaApplyRecordsNewData(oaApplyRecordsNewData);
            int i2 = oaEditApproveGeneralityEditRecordsMapper.insertOaEditApproveGeneralityEditRecords(oaEditApproveGeneralityEditRecords);
            //修改的oa_trader一个状态，这个状态用来显示是否可以进行编辑
            OaTrader oaTrader2 = new OaTrader();
            oaTrader2.setId(oaApplyId);
            oaTrader2.setAddNotApprove("97");
            int i3 = oaTraderMapper.updateOaTrader(oaTrader2);
        }

        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int oaChekHandle(String processId, String checkFlag) {
        //查找对应的最新的那一条审批记录oa_edit_approve_generality_edit_records表记录
        OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = oaEditApproveGeneralityEditRecordsMapper.selectNewOaEditApproveGeneralityEditRecordsByProcessId(processId);
        Long oaApplyId = oaEditApproveGeneralityEditRecords.getOaApplyId();
        String oaApplyType = oaEditApproveGeneralityEditRecords.getOaApplyType();
        if ("1".equals(checkFlag)) {
            //通过之后，进行落实
            if ("2".equals(oaEditApproveGeneralityEditRecords.getEditType())) {
                //删除落实逻辑
                List<String> oaApplyTypeList = new ArrayList<>();
                oaApplyTypeList.add("1");
                oaApplyTypeList.add("2");
                oaApplyTypeList.add("9");
                OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords1 = new OaEditApproveGeneralityEditRecords();
                oaEditApproveGeneralityEditRecords1.setOaApplyId(oaEditApproveGeneralityEditRecords.getOaApplyId());
                oaEditApproveGeneralityEditRecords1.setStatus("1");
                oaEditApproveGeneralityEditRecords1.setDelFlag("1");
                int i1 = oaEditApproveGeneralityEditRecordsMapper.updateOaEditApproveGeneralityEditRecordsAndOaApplyTypeList(oaEditApproveGeneralityEditRecords, oaApplyTypeList);
//                int i2 = oaEditApproveGeneralityRecordsMapper.deleteOaEditApproveGeneralityRecordsByOaApplyTypeAndOaApplyId(oaApplyType, oaApplyId);
                //主表
                OaTrader oaTrader = new OaTrader();
                oaTrader.setId(oaApplyId);
                oaTrader.setDelFlag("1");
                oaTrader.setAddNotApprove("96");
                int i4 = oaTraderMapper.updateOaTrader(oaTrader);
                return i4;
            } else if ("0".equals(oaEditApproveGeneralityEditRecords.getEditType())) {
                //新增落实逻辑
                //把对应的oaTrader的add_not_approve状态进行更改
                OaTrader oaTrader = new OaTrader();
                oaTrader.setId(oaApplyId);
                oaTrader.setAddNotApprove("96");
                int i = oaTraderMapper.updateOaTrader(oaTrader);
            } else if ("1".equals(oaEditApproveGeneralityEditRecords.getEditType())) {
                //修改落实逻辑
//                Long oaApplyRecordsNewId = oaEditApproveGeneralityEditRecords.getOaApplyRecordsNewId();
//                OaEditApproveGeneralityRecords oaEditApproveGeneralityRecords = oaEditApproveGeneralityRecordsMapper.selectOaEditApproveGeneralityRecordsById(oaApplyRecordsNewId);
//                String data = oaEditApproveGeneralityRecords.getData();
                String data = oaEditApproveGeneralityEditRecords.getOaApplyRecordsNewData();
                OaTraderDto oaTraderDto = JSON.toJavaObject(JSON.parseObject(data), OaTraderDto.class);
                OaTrader oaTrader = new OaTrader();
                BeanUtil.copyProperties(oaTraderDto, oaTrader);
                //todo 可能需要这个id
                oaTrader.setId(oaApplyId);
                oaTrader.setAddNotApprove("96");
                int i = oaTraderMapper.updateOaTrader(oaTrader);
            }
            // （因为新增已经存在于oa_trader表中，查询逻辑把关联审批表为check_status='99'，status='0'
            // 所以只需要把check_status='98  'status='1'改完之后，就可以保证正常查询了）
            oaEditApproveGeneralityEditRecords.setStatus("1");
            oaEditApproveGeneralityEditRecords.setCheckStatus("98");
        } else if ("2".equals(checkFlag)) {
            //驳回之后，进行反馈
            if ("2".equals(oaEditApproveGeneralityEditRecords.getEditType())) {
                //删除逻辑，修改审核状态，原表数据不动
                OaTrader oaTrader = new OaTrader();
                oaTrader.setId(oaApplyId);
                oaTrader.setAddNotApprove("96");
                int i = oaTraderMapper.updateOaTrader(oaTrader);
            } else if ("0".equals(oaEditApproveGeneralityEditRecords.getEditType())) {
                //新增落实逻辑
                // （因为新增已经存在于oa_trader表中，驳回之后，把之前对应的表进行删除）
                //todo 暂时不需要这些分步
//                if (OA_PROJECT_FLOW_MAIN.equals(oaApplyType)) {
//                    //删除 项目与流程关联 数据
//                    List<Long> pfaIds = oaProjectFlowAssociationMapper.selectOaProjectFlowIdsAssociationByMainId(oaApplyId);
//                    oaProjectFlowAssociationMapper.deleteByMainId(oaApplyId);
//                    oaProjectPayerAssociationMapper.deleteByProAssId(pfaIds);
//                } else if (OA_TRADER_1.equals(oaApplyType) || OA_TRADER_2.equals(oaApplyType) || OA_TRADER_9.equals(oaApplyType)) {
//                    //删除 收付款人 数据
//                    oaTraderMapper.deleteOaTraderById(oaApplyId);
//                }
                oaTraderMapper.deleteOaTraderById(oaApplyId);
                //审核驳回后，删除新增的那些审批相关数据
//                int i = oaEditApproveGeneralityRecordsMapper.deleteOaEditApproveGeneralityRecordsByOaApplyTypeAndOaApplyId(oaApplyType, oaApplyId);
                int i1 = oaEditApproveGeneralityEditRecordsMapper.deleteOaEditApproveGeneralityEditRecordsByOaApplyTypeAndOaApplyId(oaApplyType, oaApplyId);
                return i1;
            } else if ("1".equals(oaEditApproveGeneralityEditRecords.getEditType())) {
                //修改落实逻辑，修改审核状态，原表数据不动
                OaTrader oaTrader = new OaTrader();
                oaTrader.setId(oaApplyId);
                oaTrader.setAddNotApprove("96");
                int i = oaTraderMapper.updateOaTrader(oaTrader);
            }
            // （因为新增已经存在于oa_trader表中，查询逻辑把关联审批表为check_status='99'，status='0'
            // 所以只需要把check_status='97  'status='1'改完之后，就可以保证正常查询了）
            oaEditApproveGeneralityEditRecords.setStatus("1");
            oaEditApproveGeneralityEditRecords.setCheckStatus("97");
        }
        return oaEditApproveGeneralityEditRecordsMapper.updateOaEditApproveGeneralityEditRecords(oaEditApproveGeneralityEditRecords);
    }

    @Override
    public Map<String, String> getDataByProcessId(String processId) {
        OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = oaEditApproveGeneralityEditRecordsMapper.selectNewOaEditApproveGeneralityEditRecordsByProcessId(processId);
        Map<String, String> map = new HashMap<>();
        map.put("oldData", oaEditApproveGeneralityEditRecords.getOaApplyRecordsOldData());
        map.put("newData", oaEditApproveGeneralityEditRecords.getOaApplyRecordsNewData());
        map.put("editInfo", oaEditApproveGeneralityEditRecords.getEditInfo());
        map.put("editType", oaEditApproveGeneralityEditRecords.getEditType());
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int abandonedProd(OaEditApproveGeneralityEditRecordsVo oaEditApproveGeneralityEditRecordsVo, LoginUser loginUser) {
        int i= 0;
        OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = oaEditApproveGeneralityEditRecordsMapper.selectNewOaEditApproveGeneralityEditRecordsByProcessId(oaEditApproveGeneralityEditRecordsVo.getProcessId());
        Long oaApplyId = oaEditApproveGeneralityEditRecords.getOaApplyId();
        String editType = oaEditApproveGeneralityEditRecords.getEditType();
        if ("0".equals(editType)) {
            //因为是新增，所以把之前收付款人数据删除掉
            i = oaTraderMapper.deleteOaTraderById(oaEditApproveGeneralityEditRecords.getOaApplyId());
            //然后删除记录表的数据
            oaEditApproveGeneralityEditRecordsMapper.deleteOaEditApproveGeneralityEditRecordsByOaApplyTypeAndOaApplyId(oaEditApproveGeneralityEditRecords.getOaApplyType(), oaEditApproveGeneralityEditRecords.getOaApplyId());
        } else if ("1".equals(editType) || "2".equals(editType)) {
            //修改或删除的时候，各种数据还没有完全落实，所以老表不变，只改下状态即可
            OaTrader oaTrader = new OaTrader();
            oaTrader.setId(oaApplyId);
            oaTrader.setAddNotApprove("96");
            i = oaTraderMapper.updateOaTrader(oaTrader);
            //修改记录表的状态
            OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords1 = new OaEditApproveGeneralityEditRecords();
            oaEditApproveGeneralityEditRecords1.setId(oaEditApproveGeneralityEditRecords.getId());
            oaEditApproveGeneralityEditRecords1.setCheckStatus("97");
            oaEditApproveGeneralityEditRecordsMapper.updateOaEditApproveGeneralityEditRecords(oaEditApproveGeneralityEditRecords1);
        }
        return i;
    }
}
