package org.ruoyi.core.oasystem.domain.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.ruoyi.core.oasystem.domain.OaProjectFlowUtil2;

import java.util.Date;
import java.util.List;

/**
 * 项目与流程关联 - 实体类（接收前端对象用）
 *
 * @Description
 * <AUTHOR>
 * @Date 2024/02/20 17:03
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class OaProjectFlowMainBo
{
    private static final long serialVersionUID = 1L;

    private Long id;
    private String companyNo;
    private String compony;
    private Long modelId;
    private String modelName;
    private String remark;
    private List<OaProjectFlowUtil2> proList;

    private String status;

    private String createBr;

    private Date createTime;

    private String updateBr;

    private Date updateTime;

    //财务负责人（新增）
    private List<Long> salesmanList;

    //业务负责人（新增）
    private List<Long> financialStaffList;

    //修改前的JSON对应的id
    private Long oaApplyRecordsOldId;

    //修改说明
    private String editInfo;

    //操作的类型 0-新增 1-修改 2-删除
    private String editType;
}
