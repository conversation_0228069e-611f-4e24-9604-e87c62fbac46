package org.ruoyi.core.oasystem.mapper;



import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.oasystem.domain.vo.ProcFormAttrVo;
import org.ruoyi.core.oasystem.domain.vo.ProcFormDefVo;

import java.util.List;

/**
 * 单定义Mapper接口
 *
 * @date 2022-07-27
 */
public interface ProcFormMapper
{
    /**
     * 查询单定义
     *
     * @param id 单定义ID
     * @return 单定义
     */
    public ProcFormDefVo selectProcFormDefById(String id);

    /**
     * 新增单定义
     *
     * @param procFormDef 单定义
     * @return 结果
     */
    public int insertProcFormDef(ProcFormDefVo procFormDef);

    /**
     * 查询表单属性列表
     *
     * @param formId 表单属性
     * @return 表单属性集合
     */
    public List<ProcFormAttrVo> selectProcFormAttrList(String formId);

    /**
     * 新增表单属性
     *
     * @param procFormAttr 表单属性
     * @return 结果
     */
    public int insertProcFormAttr(ProcFormAttrVo procFormAttr);

    /**
     * 根据businessKey获取最近一条数据的审批状态
     * @param businessKey
     * @return
     */
    public String selectLastPassByBusinessKey(@Param("businessKey") String businessKey);
}
