package org.ruoyi.core.oasystem.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 oa_voucher_rules_subject
 * 
 * <AUTHOR>
 * @date 2023-11-09
 */
public class OaVoucherRulesSubject extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 是否可删 */
    @Excel(name = "是否可删")
    private String subDelete;

    /** 凭证规则id */
    @Excel(name = "凭证规则id")
    private Long rulesViceId;

    /** 是否关联动态表单 */
    @Excel(name = "是否关联动态表单")
    private String isAssociationBatch;

    /** 动态表单字段 */
    @Excel(name = "动态表单字段")
    private String associationBatchField;

    /** 动态表单金额 */
    @Excel(name = "动态表单金额")
    private String batchAmountField;

    /** 动态表单科目 */
    @Excel(name = "动态表单科目")
    private String batchSubjectField;

    /** 动态表单摘要 */
    @Excel(name = "动态表单摘要")
    private String batchAbstractField;

    /** 记账金额字段 */
    @Excel(name = "记账金额字段")
    private String accountingField;

    /** 记账金额字段名称 */
    @Excel(name = "记账金额字段名称")
    private String accountingFieldName;

    /** 科目类型（0借1贷） */
    @Excel(name = "科目类型", readConverterExp = "0=借1贷")
    private String subjectType;

    /** 账套科目类型 */
    @Excel(name = "账套科目类型")
    private String accountSetsSubjectType;

    /** 一级科目id */
    @Excel(name = "一级科目id")
    private Long firstSubjectId;

    /** 一级科目名称 */
    @Excel(name = "一级科目名称")
    private String firstSubjectName;

    /** 是否存在二级科目（0存在1不存在） */
    @Excel(name = "是否存在二级科目", readConverterExp = "0=存在1不存在")
    private String isSubjectSecond;

    /** 二级科目取值方式（0固定名称1动态名称） */
    @Excel(name = "二级科目取值方式", readConverterExp = "0=固定名称1动态名称")
    private String secondValueMode;

    /** 二级科目id */
    @Excel(name = "二级科目id")
    private Long secondSubjectId;

    /** 二级科目名称 */
    @Excel(name = "二级科目名称")
    private String secondSubjectName;

    /** 二级科目名称json */
    @Excel(name = "二级科目名称json")
    private String secondSubjectJson;

    /** 是否存在三级科目 */
    @Excel(name = "是否存在三级科目")
    private String isSubjectThird;

    /** 三级科目取值方式 */
    @Excel(name = "三级科目取值方式")
    private String thirdValueMode;

    /** 三级科目id */
    @Excel(name = "三级科目id")
    private Long thirdSubjectId;

    /** 三级科目名称 */
    @Excel(name = "三级科目名称")
    private String thirdSubjectName;

    /** 三级科目名称json */
    @Excel(name = "三级科目名称json")
    private String thirdSubjectJson;

    /** 是否存在四级科目 */
    @Excel(name = "是否存在四级科目")
    private String isSubjectFourth;

    /** 四级科目取值方式 */
    @Excel(name = "四级科目取值方式")
    private String fourthValueMode;

    /** 四级科目id */
    @Excel(name = "四级科目id")
    private Long fourthSubjectId;

    /** 四级科目名称 */
    @Excel(name = "四级科目名称")
    private String fourthSubjectName;

    /** 四级科目名称json */
    @Excel(name = "四级科目名称json")
    private String fourthSubjectJson;

    /** 科目汇总 */
    @Excel(name = "科目汇总")
    private String subjectCollect;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setSubDelete(String subDelete)
    {
        this.subDelete = subDelete;
    }

    public String getSubDelete()
    {
        return subDelete;
    }
    public void setRulesViceId(Long rulesViceId)
    {
        this.rulesViceId = rulesViceId;
    }

    public Long getRulesViceId()
    {
        return rulesViceId;
    }
    public void setIsAssociationBatch(String isAssociationBatch)
    {
        this.isAssociationBatch = isAssociationBatch;
    }

    public String getIsAssociationBatch()
    {
        return isAssociationBatch;
    }
    public void setAssociationBatchField(String associationBatchField)
    {
        this.associationBatchField = associationBatchField;
    }

    public String getAssociationBatchField()
    {
        return associationBatchField;
    }
    public void setBatchAmountField(String batchAmountField)
    {
        this.batchAmountField = batchAmountField;
    }

    public String getBatchAmountField()
    {
        return batchAmountField;
    }
    public void setBatchSubjectField(String batchSubjectField)
    {
        this.batchSubjectField = batchSubjectField;
    }

    public String getBatchSubjectField()
    {
        return batchSubjectField;
    }
    public void setBatchAbstractField(String batchAbstractField)
    {
        this.batchAbstractField = batchAbstractField;
    }

    public String getBatchAbstractField()
    {
        return batchAbstractField;
    }
    public void setAccountingField(String accountingField)
    {
        this.accountingField = accountingField;
    }

    public String getAccountingField()
    {
        return accountingField;
    }
    public void setAccountingFieldName(String accountingFieldName)
    {
        this.accountingFieldName = accountingFieldName;
    }

    public String getAccountingFieldName()
    {
        return accountingFieldName;
    }
    public void setSubjectType(String subjectType)
    {
        this.subjectType = subjectType;
    }

    public String getSubjectType()
    {
        return subjectType;
    }
    public void setAccountSetsSubjectType(String accountSetsSubjectType)
    {
        this.accountSetsSubjectType = accountSetsSubjectType;
    }

    public String getAccountSetsSubjectType()
    {
        return accountSetsSubjectType;
    }
    public void setFirstSubjectId(Long firstSubjectId)
    {
        this.firstSubjectId = firstSubjectId;
    }

    public Long getFirstSubjectId()
    {
        return firstSubjectId;
    }
    public void setFirstSubjectName(String firstSubjectName)
    {
        this.firstSubjectName = firstSubjectName;
    }

    public String getFirstSubjectName()
    {
        return firstSubjectName;
    }
    public void setIsSubjectSecond(String isSubjectSecond)
    {
        this.isSubjectSecond = isSubjectSecond;
    }

    public String getIsSubjectSecond()
    {
        return isSubjectSecond;
    }
    public void setSecondValueMode(String secondValueMode)
    {
        this.secondValueMode = secondValueMode;
    }

    public String getSecondValueMode()
    {
        return secondValueMode;
    }
    public void setSecondSubjectId(Long secondSubjectId)
    {
        this.secondSubjectId = secondSubjectId;
    }

    public Long getSecondSubjectId()
    {
        return secondSubjectId;
    }
    public void setSecondSubjectName(String secondSubjectName)
    {
        this.secondSubjectName = secondSubjectName;
    }

    public String getSecondSubjectName()
    {
        return secondSubjectName;
    }
    public void setSecondSubjectJson(String secondSubjectJson)
    {
        this.secondSubjectJson = secondSubjectJson;
    }

    public String getSecondSubjectJson()
    {
        return secondSubjectJson;
    }
    public void setIsSubjectThird(String isSubjectThird)
    {
        this.isSubjectThird = isSubjectThird;
    }

    public String getIsSubjectThird()
    {
        return isSubjectThird;
    }
    public void setThirdValueMode(String thirdValueMode)
    {
        this.thirdValueMode = thirdValueMode;
    }

    public String getThirdValueMode()
    {
        return thirdValueMode;
    }
    public void setThirdSubjectId(Long thirdSubjectId)
    {
        this.thirdSubjectId = thirdSubjectId;
    }

    public Long getThirdSubjectId()
    {
        return thirdSubjectId;
    }
    public void setThirdSubjectName(String thirdSubjectName)
    {
        this.thirdSubjectName = thirdSubjectName;
    }

    public String getThirdSubjectName()
    {
        return thirdSubjectName;
    }
    public void setThirdSubjectJson(String thirdSubjectJson)
    {
        this.thirdSubjectJson = thirdSubjectJson;
    }

    public String getThirdSubjectJson()
    {
        return thirdSubjectJson;
    }
    public void setIsSubjectFourth(String isSubjectFourth)
    {
        this.isSubjectFourth = isSubjectFourth;
    }

    public String getIsSubjectFourth()
    {
        return isSubjectFourth;
    }
    public void setFourthValueMode(String fourthValueMode)
    {
        this.fourthValueMode = fourthValueMode;
    }

    public String getFourthValueMode()
    {
        return fourthValueMode;
    }
    public void setFourthSubjectId(Long fourthSubjectId)
    {
        this.fourthSubjectId = fourthSubjectId;
    }

    public Long getFourthSubjectId()
    {
        return fourthSubjectId;
    }
    public void setFourthSubjectName(String fourthSubjectName)
    {
        this.fourthSubjectName = fourthSubjectName;
    }

    public String getFourthSubjectName()
    {
        return fourthSubjectName;
    }
    public void setFourthSubjectJson(String fourthSubjectJson)
    {
        this.fourthSubjectJson = fourthSubjectJson;
    }

    public String getFourthSubjectJson()
    {
        return fourthSubjectJson;
    }
    public void setSubjectCollect(String subjectCollect)
    {
        this.subjectCollect = subjectCollect;
    }

    public String getSubjectCollect()
    {
        return subjectCollect;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("subDelete", getSubDelete())
                .append("rulesViceId", getRulesViceId())
                .append("isAssociationBatch", getIsAssociationBatch())
                .append("associationBatchField", getAssociationBatchField())
                .append("batchAmountField", getBatchAmountField())
                .append("batchSubjectField", getBatchSubjectField())
                .append("batchAbstractField", getBatchAbstractField())
                .append("accountingField", getAccountingField())
                .append("accountingFieldName", getAccountingFieldName())
                .append("subjectType", getSubjectType())
                .append("accountSetsSubjectType", getAccountSetsSubjectType())
                .append("firstSubjectId", getFirstSubjectId())
                .append("firstSubjectName", getFirstSubjectName())
                .append("isSubjectSecond", getIsSubjectSecond())
                .append("secondValueMode", getSecondValueMode())
                .append("secondSubjectId", getSecondSubjectId())
                .append("secondSubjectName", getSecondSubjectName())
                .append("secondSubjectJson", getSecondSubjectJson())
                .append("isSubjectThird", getIsSubjectThird())
                .append("thirdValueMode", getThirdValueMode())
                .append("thirdSubjectId", getThirdSubjectId())
                .append("thirdSubjectName", getThirdSubjectName())
                .append("thirdSubjectJson", getThirdSubjectJson())
                .append("isSubjectFourth", getIsSubjectFourth())
                .append("fourthValueMode", getFourthValueMode())
                .append("fourthSubjectId", getFourthSubjectId())
                .append("fourthSubjectName", getFourthSubjectName())
                .append("fourthSubjectJson", getFourthSubjectJson())
                .append("subjectCollect", getSubjectCollect())
                .append("status", getStatus())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}