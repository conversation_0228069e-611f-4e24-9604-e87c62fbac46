package org.ruoyi.core.oasystem.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.ruoyi.core.oasystem.domain.OaProjectTypeCorrelation;
import org.ruoyi.core.oasystem.domain.vo.OaProjectTypeCorrelationVo;
import org.ruoyi.core.oasystem.service.IOaProjectTypeCorrelationService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 项目类型与功能关联Controller
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@RestController
@RequestMapping("/project/type/correlation")
public class OaProjectTypeCorrelationController extends BaseController
{
    @Autowired
    private IOaProjectTypeCorrelationService oaProjectTypeCorrelationService;

    /**
     * 查询项目类型与功能关联列表
     */
    //@PreAuthorize("@ss.hasPermi('system:correlation:list')")
    @GetMapping("/list")
    public TableDataInfo list(OaProjectTypeCorrelationVo oaProjectTypeCorrelation)
    {
        //startPage();
        List<OaProjectTypeCorrelationVo> list = oaProjectTypeCorrelationService.selectOaProjectTypeCorrelationList(oaProjectTypeCorrelation);
        return getDataTable(list);
    }

    /**
     * 导出项目类型与功能关联列表
     */
    //@PreAuthorize("@ss.hasPermi('system:correlation:export')")
    @Log(title = "项目类型与功能关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OaProjectTypeCorrelationVo oaProjectTypeCorrelation)
    {
        List<OaProjectTypeCorrelationVo> list = oaProjectTypeCorrelationService.selectOaProjectTypeCorrelationList(oaProjectTypeCorrelation);
        ExcelUtil<OaProjectTypeCorrelationVo> util = new ExcelUtil<OaProjectTypeCorrelationVo>(OaProjectTypeCorrelationVo.class);
        util.exportExcel(response, list, "项目类型与功能关联数据");
    }

    /**
     * 获取项目类型与功能关联详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:correlation:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(oaProjectTypeCorrelationService.selectOaProjectTypeCorrelationById(id));
    }

    /**
     * 新增项目类型与功能关联
     */
    //@PreAuthorize("@ss.hasPermi('system:correlation:add')")
    @Log(title = "项目类型与功能关联", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OaProjectTypeCorrelationVo oaProjectTypeCorrelation)
    {
        return toAjax(oaProjectTypeCorrelationService.insertOaProjectTypeCorrelation(oaProjectTypeCorrelation));
    }

    /**
     * 修改项目类型与功能关联
     */
    //@PreAuthorize("@ss.hasPermi('system:correlation:edit')")
    @Log(title = "项目类型与功能关联", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OaProjectTypeCorrelationVo oaProjectTypeCorrelation)
    {
        return toAjax(oaProjectTypeCorrelationService.updateOaProjectTypeCorrelation(oaProjectTypeCorrelation));
    }

    /**
     * 删除项目类型与功能关联
     */
    //@PreAuthorize("@ss.hasPermi('system:correlation:remove')")
    @Log(title = "项目类型与功能关联", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(oaProjectTypeCorrelationService.deleteOaProjectTypeCorrelationByIds(ids));
    }
}
