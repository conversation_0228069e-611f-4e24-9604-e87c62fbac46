package org.ruoyi.core.oasystem.service;

import org.ruoyi.core.oasystem.domain.OaProjectNameRuleSlave;

import java.util.List;

/**
 * 特殊产品分类配置-组合Service接口
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface IOaProjectNameRuleSlaveService
{
    /**
     * 查询特殊产品分类配置-组合
     *
     * @param id 特殊产品分类配置-组合主键
     * @return 特殊产品分类配置-组合
     */
    public OaProjectNameRuleSlave selectOaProjectNameRuleSlaveById(Long id);

    /**
     * 查询特殊产品分类配置-组合列表
     *
     * @param oaProjectNameRuleSlave 特殊产品分类配置-组合
     * @return 特殊产品分类配置-组合集合
     */
    public List<OaProjectNameRuleSlave> selectOaProjectNameRuleSlaveList(OaProjectNameRuleSlave oaProjectNameRuleSlave);

    /**
     * 新增特殊产品分类配置-组合
     *
     * @param oaProjectNameRuleSlave 特殊产品分类配置-组合
     * @return 结果
     */
    public int insertOaProjectNameRuleSlave(OaProjectNameRuleSlave oaProjectNameRuleSlave);

    /**
     * 修改特殊产品分类配置-组合
     *
     * @param oaProjectNameRuleSlave 特殊产品分类配置-组合
     * @return 结果
     */
    public int updateOaProjectNameRuleSlave(OaProjectNameRuleSlave oaProjectNameRuleSlave);

    /**
     * 批量删除特殊产品分类配置-组合
     *
     * @param ids 需要删除的特殊产品分类配置-组合主键集合
     * @return 结果
     */
    public int deleteOaProjectNameRuleSlaveByIds(Long[] ids);

    /**
     * 删除特殊产品分类配置-组合信息
     *
     * @param id 特殊产品分类配置-组合主键
     * @return 结果
     */
    public int deleteOaProjectNameRuleSlaveById(Long id);

    /**
     * 批量新增特殊产品分类配置-组合
     *
     * @param oaProjectNameRuleSlave 特殊产品分类配置-组合
     * @return 结果
     */
    public int batchOaProjectNameRuleSlave(List<OaProjectNameRuleSlave> oaProjectNameRuleSlave);

    /**
     * 删除特殊产品分类配置-组合 根据关联规则
     *
     * @param ruleId 特殊产品分类配置-组合主键
     * @return 结果
     */
    public int deleteOaProjectNameRuleSlaveByRuleId(Long ruleId);
}
