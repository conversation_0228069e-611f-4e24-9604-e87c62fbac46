package org.ruoyi.core.oasystem.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 财务项目管理五期 - OA流程支付信息费记录 标签页
 * 
 * <AUTHOR>
 * @date 2024-01-09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class OaPayRebateRecordVo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** oa流程完成时间 */
    @JsonFormat(pattern = "yyyy年MM月dd日")
    @Excel(name = "oa流程完成时间", width = 30, dateFormat = "yyyy年MM月dd日")
    private Date oaCompleteTime;

    /** oa流程发起时间 */
    @JsonFormat(pattern = "yyyy年MM月dd日 HH:mm:ss")
    @Excel(name = "oa流程完成时间", width = 30, dateFormat = "yyyy年MM月dd日 HH:mm:ss")
    private Date oaInitiatorTime;

    /** 公司编码 */
    @Excel(name = "公司编码")
    private String companyNo;

    /** 流程模板id */
    @Excel(name = "流程模板id")
    private String flowModelId;

    /** 流程模板 */
    @Excel(name = "流程模板")
    private String flowModelName;

    /** 发起人id */
    @Excel(name = "发起人id")
    private Long initiatorId;

    /** 发起人 */
    @Excel(name = "发起人")
    private String initiator;

    /** 项目id */
    @Excel(name = "项目id")
    private Long projectId;

    /** 付款人名称 */
    @Excel(name = "付款人名称")
    private String payName;

    /** 付款人账号 */
    @Excel(name = "付款人账号")
    private String payNumber;

    /** 收款人名称 */
    @Excel(name = "收款人名称")
    private String collName;

    /** 收款人账号 */
    @Excel(name = "收款人账号")
    private String collNumber;

    /** 收款人是否为返费公司 */
    @Excel(name = "收款人是否为返费公司")
    private String isRebateCom;

    /** 支付金额 */
    @Excel(name = "支付金额")
    private BigDecimal amount;

    /** 同步状态（Y成功N失败） */
    @Excel(name = "同步状态", readConverterExp = "Y=成功N失败")
    private String synStatus;

    //信息费未结清-打款前
    private BigDecimal payBefore;

    //信息费未结清-打款后
    private BigDecimal payAfter;

    //期次
    private String termMonth;
}