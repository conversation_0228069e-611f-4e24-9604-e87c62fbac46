package org.ruoyi.core.cdlb.mapper;


import org.ruoyi.core.cdlb.domain.CdlbProjectDynamic;

import java.util.List;

/**
 * 车贷绿本管理-动态Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-03-20
 */
public interface CdlbProjectDynamicMapper 
{
    /**
     * 查询车贷绿本管理-动态
     * 
     * @param id 车贷绿本管理-动态主键
     * @return 车贷绿本管理-动态
     */
    public CdlbProjectDynamic selectCdlbProjectDynamicById(Long id);

    /**
     * 查询车贷绿本管理-动态列表
     * 
     * @param cdlbProjectDynamic 车贷绿本管理-动态
     * @return 车贷绿本管理-动态集合
     */
    public List<CdlbProjectDynamic> selectCdlbProjectDynamicList(CdlbProjectDynamic cdlbProjectDynamic);

    CdlbProjectDynamic selectlimit1(CdlbProjectDynamic cdlbProjectDynamic);
    /**
     * 新增车贷绿本管理-动态
     * 
     * @param cdlbProjectDynamic 车贷绿本管理-动态
     * @return 结果
     */
    public int insertCdlbProjectDynamic(CdlbProjectDynamic cdlbProjectDynamic);

    /**
     * 修改车贷绿本管理-动态
     * 
     * @param cdlbProjectDynamic 车贷绿本管理-动态
     * @return 结果
     */
    public int updateCdlbProjectDynamic(CdlbProjectDynamic cdlbProjectDynamic);

    /**
     * 删除车贷绿本管理-动态
     * 
     * @param id 车贷绿本管理-动态主键
     * @return 结果
     */
    public int deleteCdlbProjectDynamicById(Long id);

    /**
     * 批量删除车贷绿本管理-动态
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCdlbProjectDynamicByIds(Long[] ids);


}