package org.ruoyi.core.cdlb.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * 车贷绿本管理-动态对象 cdlb_project_dynamic
 * 
 * <AUTHOR>
 * @date 2023-03-20
 */
public class CdlbProjectDynamic extends BaseEntity
{
    private static final long serialVersionUID = 1L;





    /** 出入库状态  （01出库，02入库） */
    @Excel(name = "出入库状态  ", readConverterExp = "01出库，02入库")
    private String garageState;

    /** 主键 */
    private Long id;
    /** 车贷申请表id */
    private Long applyId;

    /** 车贷绿本管理表主键 */
    @Excel(name = "车贷绿本管理表主键")
    private Long projectId;

    /** 动态标题 */
    @Excel(name = "动态标题")
    private String dynamicTitle;

    /** 动态内容 */
    @Excel(name = "动态内容")
    private String dynamicMsg;

    /** 动态时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "动态时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dynamicTime;


    /** 出入库审核状态标识：10入库录入11入库申请12入库登记13入库完成 19入库驳回 20出库申请21出库审核22出库登记23出库完成29出库驳回 */
    @Excel(name = "出入库审核状态标识：10入库录入11入库申请12入库登记13入库完成 19入库驳回 20出库申请21出库审核22出库登记23出库完成29出库驳回")
    private String applyFlag;

    /** 操作人员id */
    @Excel(name = "操作人员id")
    private Long operId;

    /** 操作人员姓名 */
    @Excel(name = "操作人员姓名")
    private String operName;

    /** 部门名称 */
    @Excel(name = "部门名称")
    private String deptName;

    /** 操作状态（0正常 1异常） */
    @Excel(name = "操作状态", readConverterExp = "0=正常,1=异常")
    private Integer status;

    /** 错误消息 */
    @Excel(name = "错误消息")
    private String errorMsg;
    /** 动态时间标题 */
    @Excel(name = "动态时间标题")
    private String dynamicTimeTitle;
    /** 动态意见或备注标题 */
    @Excel(name = "动态意见或备注标题")
    private String dynamicRemarkTitle;
    /** 意见或备注 */
    @Excel(name = "意见或备注")
    private String remark;

    public String getGarageState() {
        return garageState;
    }

    public void setGarageState(String garageState) {
        this.garageState = garageState;
    }

    public Long getApplyId() {
        return applyId;
    }

    public void setApplyId(Long applyId) {
        this.applyId = applyId;
    }

    public String getDynamicTimeTitle() {
        return dynamicTimeTitle;
    }

    public void setDynamicTimeTitle(String dynamicTimeTitle) {
        this.dynamicTimeTitle = dynamicTimeTitle;
    }

    public String getDynamicRemarkTitle() {
        return dynamicRemarkTitle;
    }

    public void setDynamicRemarkTitle(String dynamicRemarkTitle) {
        this.dynamicRemarkTitle = dynamicRemarkTitle;
    }

    @Override
    public String getRemark() {
        return remark;
    }

    @Override
    public void setRemark(String remark) {
        this.remark = remark;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setProjectId(Long projectId) 
    {
        this.projectId = projectId;
    }

    public Long getProjectId() 
    {
        return projectId;
    }
    public void setDynamicTitle(String dynamicTitle) 
    {
        this.dynamicTitle = dynamicTitle;
    }

    public String getDynamicTitle() 
    {
        return dynamicTitle;
    }
    public void setDynamicMsg(String dynamicMsg) 
    {
        this.dynamicMsg = dynamicMsg;
    }

    public String getDynamicMsg() 
    {
        return dynamicMsg;
    }
    public void setDynamicTime(Date dynamicTime) 
    {
        this.dynamicTime = dynamicTime;
    }

    public Date getDynamicTime() 
    {
        return dynamicTime;
    }
    public void setApplyFlag(String applyFlag) 
    {
        this.applyFlag = applyFlag;
    }

    public String getApplyFlag() 
    {
        return applyFlag;
    }
    public void setOperId(Long operId) 
    {
        this.operId = operId;
    }

    public Long getOperId() 
    {
        return operId;
    }
    public void setOperName(String operName) 
    {
        this.operName = operName;
    }

    public String getOperName() 
    {
        return operName;
    }
    public void setDeptName(String deptName) 
    {
        this.deptName = deptName;
    }

    public String getDeptName() 
    {
        return deptName;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }
    public void setErrorMsg(String errorMsg) 
    {
        this.errorMsg = errorMsg;
    }

    public String getErrorMsg() 
    {
        return errorMsg;
    }

    @Override
    public String toString() {
        return "CdlbProjectDynamic{" +
                "id=" + id +
                ", projectId=" + projectId +
                ", dynamicTitle='" + dynamicTitle + '\'' +
                ", dynamicMsg='" + dynamicMsg + '\'' +
                ", dynamicTime=" + dynamicTime +
                ", applyFlag='" + applyFlag + '\'' +
                ", operId=" + operId +
                ", operName='" + operName + '\'' +
                ", deptName='" + deptName + '\'' +
                ", status=" + status +
                ", errorMsg='" + errorMsg + '\'' +
                ", dynamicTimeTitle='" + dynamicTimeTitle + '\'' +
                ", dynamicRemarkTitle='" + dynamicRemarkTitle + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }
}