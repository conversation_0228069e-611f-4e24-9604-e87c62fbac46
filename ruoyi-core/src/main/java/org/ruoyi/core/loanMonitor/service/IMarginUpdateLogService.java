package org.ruoyi.core.loanMonitor.service;

import java.util.List;
import org.ruoyi.core.loanMonitor.domain.MarginUpdateLog;
import org.ruoyi.core.loanMonitor.domain.vo.MarginInfoVo;

/**
 * 保证金更新记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-16
 */
public interface IMarginUpdateLogService 
{
    /**
     * 查询保证金更新记录
     * 
     * @param id 保证金更新记录主键
     * @return 保证金更新记录
     */
    //public MarginUpdateLog selectMarginUpdateLogById(Integer id);

    /**
     * 查询保证金更新记录列表
     * 
     * @param marginUpdateLog 保证金更新记录
     * @return 保证金更新记录集合
     */
    public List<MarginUpdateLog> selectMarginUpdateLogList(MarginUpdateLog marginUpdateLog);

    /**
     * 新增保证金更新记录
     * @param marginUpdateLog 保证金更新记录
     * @return 结果
     */
    public int insertMarginUpdateLog(MarginUpdateLog marginUpdateLog);

    /**
     * 根据项目编码查询最新的账户保证金信息
     * @param projectCode
     * @return
     */
    MarginInfoVo selectMarginInfoByProjectCode(Integer projectCode);
}
