package org.ruoyi.core.loanMonitor.service.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.github.pagehelper.PageHelper;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.enums.AuthModuleEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.qiyeVX.AccessTokenUtils;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.dto.AuthorizedFeatureDetailDTO;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.impl.NewAuthorityServiceImpl;
import org.apache.xpath.operations.Bool;
import org.ruoyi.core.loanMonitor.domain.ProjectWarningValue;
import org.ruoyi.core.loanMonitor.domain.sc.LoanBalanceMonitor;
import org.ruoyi.core.loanMonitor.domain.vo.MarginMonitoringVo;
import org.ruoyi.core.loanMonitor.mapper.MarginMonitoringMapper;
import org.ruoyi.core.loanMonitor.mapper.MarginUpdateLogMapper;
import org.ruoyi.core.loanMonitor.mapper.ProjectWarningValueMapper;
import org.ruoyi.core.oasystem.domain.OaEditApproveGeneralityUser;
import org.ruoyi.core.oasystem.domain.OaProjectDeploy;
import org.ruoyi.core.oasystem.mapper.OaEditApproveGeneralityUserMapper;
import org.ruoyi.core.oasystem.mapper.OaProjectDeployMapper;
import org.ruoyi.core.qiyeVX.domain.VxUser;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.ruoyi.core.loanMonitor.mapper.LoanBalanceWarningMapper;
import org.ruoyi.core.loanMonitor.domain.LoanBalanceWarning;
import org.ruoyi.core.loanMonitor.service.ILoanBalanceWarningService;
import org.springframework.util.CollectionUtils;

/**
 * 在贷余额监控预警Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-16
 */
@Service
public class LoanBalanceWarningServiceImpl implements ILoanBalanceWarningService {
    @Autowired
    private LoanBalanceWarningMapper loanBalanceWarningMapper;

    @Autowired
    private MarginUpdateLogMapper marginUpdateLogMapper;

    @Autowired
    private MarginMonitoringMapper marginMoitoringMapper;

    @Autowired
    private OaProjectDeployMapper oaProjectDeployMapper;

    @Autowired
    private AccessTokenUtils accessTokenUtils;

    @Autowired
    private NewAuthorityServiceImpl newAuthorityServiceImpl;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private OaEditApproveGeneralityUserMapper oaEditApproveGeneralityUserMapper;

    @Autowired
    private ProjectWarningValueMapper projectWarningValueMapper;


    /**
     * 查询在贷余额监控预警
     *
     * @param projectCode 项目唯一编码
     * @return 在贷余额监控预警
     */
    @Override
    public LoanBalanceWarning selectLoanBalanceWarningByProjectCode(Integer projectCode) {
        LoanBalanceWarning loanBalanceWarning = loanBalanceWarningMapper.selectLoanBalanceWarningById(projectCode);
        loanBalanceWarning.setRatioMax(loanBalanceWarning.getRatioMax().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP));
        return loanBalanceWarning;
    }

    /**
     * 查询在贷余额监控预警列表
     * @param marginMonitoringVo
     * @return 在贷余额监控列表
     */
    @Override
    public List<MarginMonitoringVo> selectMarginMonitoringList(MarginMonitoringVo marginMonitoringVo) {
        List<MarginMonitoringVo> marginMonitoringVoList = new ArrayList<>();
        if (marginMonitoringVo.getPageNum() != null && marginMonitoringVo.getPageSize() != null){
           PageHelper.startPage(marginMonitoringVo.getPageNum(), marginMonitoringVo.getPageSize());
        }
        //首先从数仓中获取有哪些项目
        List<LoanBalanceMonitor> loanBalanceMonitorList = marginMoitoringMapper.selectGroupProjectCode();
        //为空直接返回
        if (CollectionUtils.isEmpty(loanBalanceMonitorList)) {
            return new ArrayList<>();
        }
        //不为空，循环查询组装数据
        for (LoanBalanceMonitor loanBalanceMonitor : loanBalanceMonitorList) {
            BigDecimal occupancyRatio = BigDecimal.ZERO;
            if (loanBalanceMonitor.getProjectCode() != null) {
                MarginMonitoringVo monitoringVo = new MarginMonitoringVo();
                if(loanBalanceMonitor.getProjectCode() == 517 && loanBalanceMonitor.getProductCode().equals("HNZH-FQL-HEXFJRYXGS-ZHDBYQ-FR-JSCB")){
                    loanBalanceMonitor.setProjectCode(390);
                    //一期项目id
                    monitoringVo.setProjectId(390L);
                }else {
                    //项目id
                    monitoringVo.setProjectId(Long.valueOf(loanBalanceMonitor.getProjectCode()));
                }
                //根据项目唯一编码查询项目信息
                // OaProjectDeploy oaProjectDeploy = oaProjectDeployMapper.selectOaProjectDeployById(Long.valueOf(loanBalanceMonitor.getProjectCode()));
                OaProjectDeploy oaProjectDeploy = marginUpdateLogMapper.selectOaProjectDeployInfoById(Long.valueOf(loanBalanceMonitor.getProjectCode()));

                //项目名称
                monitoringVo.setProjectName(oaProjectDeploy.getProjectName());

                //产品编码
                monitoringVo.setProductCode(loanBalanceMonitor.getProductCode());

                //保证金总额
                BigDecimal additional = marginUpdateLogMapper.selectTotalMarginByProjectCode(loanBalanceMonitor.getProjectCode());//查询追加的总额
                BigDecimal decrease = marginUpdateLogMapper.selectTotalDecreaseByProjectCode(loanBalanceMonitor.getProjectCode());//查询减少的总额
                if (additional == null) {
                    additional = BigDecimal.ZERO;
                }
                if (decrease == null) {
                    decrease = BigDecimal.ZERO;
                }
                BigDecimal totalMargin = additional.subtract(decrease);
                monitoringVo.setTotalMargin(totalMargin);

                //当前在贷金额(等于在贷余额)
                BigDecimal loanBalance = loanBalanceMonitor.getLoanBalance();
                monitoringVo.setLoanBalance(loanBalance);

                //保证金比例
                LoanBalanceWarning loanBalanceWarning = new LoanBalanceWarning();
                LoanBalanceWarning oldLoanBalanceWarning = loanBalanceWarningMapper.selectBalanceLoanByProjectCode(loanBalanceMonitor.getProjectCode());
                if (Objects.isNull(oldLoanBalanceWarning)){
                    // LoanBalanceWarning testLoan = new LoanBalanceWarning();
                    loanBalanceWarning.setMarginRate(BigDecimal.ZERO);
                    loanBalanceWarning.setRatioMax(BigDecimal.ZERO);
                    // BeanUtils.copyProperties(testLoan,loanBalanceWarning);
                }else {
                    BeanUtils.copyProperties(oldLoanBalanceWarning,loanBalanceWarning);
                }
                BigDecimal marginRate = loanBalanceWarning.getMarginRate();
                monitoringVo.setMarginRate(marginRate);
                if (marginRate.compareTo(BigDecimal.ZERO) > 0){
                    monitoringVo.setMarginRateStr(String.valueOf(marginRate.multiply(BigDecimal.valueOf(100).setScale(2, RoundingMode.DOWN)).setScale(2, RoundingMode.DOWN))+ "%");
                }

                //保证金已占用金额(当前在贷金额*保证金比例)
                BigDecimal takenUpAmount = loanBalance.multiply(marginRate).setScale(2, RoundingMode.HALF_UP);
                monitoringVo.setTakenUpAmount(takenUpAmount);

                //当前可放款金额(项目保证金总额/保证金比例-当前在贷金额)
                //校验totalMargin是否为0,不为0时计算其他相关字段值
                if (totalMargin.compareTo(BigDecimal.ZERO) > 0){
                    BigDecimal availableLoanAmount = totalMargin.divide(new BigDecimal(String.valueOf(marginRate)),BigDecimal.ROUND_CEILING).subtract(loanBalance).setScale(2, RoundingMode.HALF_UP);
                    monitoringVo.setAvailableLoanAmount(availableLoanAmount);

                    //未占用保证金金额(项目保证金总额-保证金已占用金额)
                    BigDecimal unappropriatedAmount = totalMargin.subtract(takenUpAmount);
                    monitoringVo.setUnappropriatedAmount(unappropriatedAmount);

                    //机构保证金占用比例[(当前在贷金额*保证金比例)/项目保证金总额]
                    occupancyRatio = loanBalance.multiply(marginRate).divide(totalMargin, 2, RoundingMode.HALF_UP).setScale(2, RoundingMode.HALF_UP);
                    monitoringVo.setOccupancyRatio(occupancyRatio);
                    if (marginRate != null && marginRate.compareTo(BigDecimal.ZERO) > 0){
                        monitoringVo.setOccupancyRatioStr(String.valueOf(occupancyRatio.multiply(BigDecimal.valueOf(100)))+ "%");
                    }
                }

                //获取预警阈值
                BigDecimal ratioMax = loanBalanceWarning.getRatioMax();
                monitoringVo.setRatioMax(ratioMax);

                //是否超过预警阈值标识
                if (occupancyRatio.compareTo(ratioMax) > 0) {
                    monitoringVo.setWarningFlag("1");
                } else {
                    monitoringVo.setWarningFlag("0");
                }

                //昨日实际放款金额
                BigDecimal yesterdayLentActualAmount = marginMoitoringMapper.selectLoanAmountByProjectCode(loanBalanceMonitor.getProjectCode());
                monitoringVo.setYesterdayLentActualAmount(yesterdayLentActualAmount);

                //本月实际放款金额
                BigDecimal monthLentActualAmount = marginMoitoringMapper.selectActualLoanAmountThisMonth(loanBalanceMonitor.getProjectCode());
                monitoringVo.setMonthLentActualAmount(monthLentActualAmount);

                //最后更新时间
                monitoringVo.setUpdateTime(loanBalanceMonitor.getUpdateTime());
                marginMonitoringVoList.add(monitoringVo);
            }
        }
        return marginMonitoringVoList;
    }

    /**
     * 新增在贷余额监控预警
     *
     * @param loanBalanceWarning 在贷余额监控预警
     * @return 结果
     */
    @Override
    public int insertLoanBalanceWarning(LoanBalanceWarning loanBalanceWarning) {
        return loanBalanceWarningMapper.insertLoanBalanceWarning(loanBalanceWarning);
    }

    /**
     * 修改在贷余额监控预警
     *
     * @param loanBalanceWarning 在贷余额监控预警
     * @return 结果
     */
    @Override
    public int updateLoanBalanceWarning(LoanBalanceWarning loanBalanceWarning) {
        //最后更新时间
        loanBalanceWarning.setUpdateTime(DateUtils.getNowDate());
        //如果小数点超过四位，返回小数点过长
        if (loanBalanceWarning.getRatioMax().scale() > 4){
            throw new ServiceException("预警阈值最多可输入两位小数！");
        }
        loanBalanceWarning.setRatioMax(loanBalanceWarning.getRatioMax().divide(BigDecimal.valueOf(100), 4, RoundingMode.DOWN));
        loanBalanceWarning.setCurrentThreshold(0);
        return loanBalanceWarningMapper.updateLoanBalanceWarning(loanBalanceWarning);
    }

    /**
     * 根据项目唯一编码查询账户总金额
     *
     * @param projectCode
     * @return
     */
    @Override
    public BigDecimal selectTotalMarginByProjectCode(Integer projectCode) {
        return marginUpdateLogMapper.selectTotalMarginByProjectCode(projectCode);
    }

    /**
     * 机构保证金占用比例超过阈值时给项目负责人发送企业微信通知
     */
    @Override
    public void sendQYWXNotify() throws IOException {
        // 首先从数仓中获取有哪些项目
        List<LoanBalanceMonitor> loanBalanceMonitorList = marginMoitoringMapper.selectGroupProjectCode();
        // 为空直接返回
        if (CollectionUtils.isEmpty(loanBalanceMonitorList)) {
            return;
        }
        // 不为空，循环查询组装数据
        for (LoanBalanceMonitor loanBalanceMonitor : loanBalanceMonitorList) {
            List<SysUser> sysUserList = new ArrayList<>();
            if (loanBalanceMonitor.getProjectCode() != null) {
                // 根据项目唯一编码查询项目信息,如果是1期的项目，需要特殊处理(517是项目信息的ia，390是立项项目的id)
                if (loanBalanceMonitor.getProjectCode() == 517 && loanBalanceMonitor.getProductCode().equals("HNZH-FQL-HEXFJRYXGS-ZHDBYQ-FR-JSCB")) {
                    loanBalanceMonitor.setProjectCode(390);
                }
                OaProjectDeploy oaProjectDeploy = marginUpdateLogMapper.selectOaProjectDeployInfoById(Long.valueOf(loanBalanceMonitor.getProjectCode()));
                // 项目名称
                String projectName = oaProjectDeploy.getProjectName();

                // 项目id
                Long deployId = oaProjectDeploy.getOaApplyId();

                // 保证金总额
                BigDecimal additional = marginUpdateLogMapper.selectTotalMarginByProjectCode(loanBalanceMonitor.getProjectCode());// 查询追加的总额
                BigDecimal decrease = marginUpdateLogMapper.selectTotalDecreaseByProjectCode(loanBalanceMonitor.getProjectCode());// 查询减少的总额
                if (additional == null) {
                    additional = BigDecimal.ZERO;
                }
                if (decrease == null) {
                    decrease = BigDecimal.ZERO;
                }
                BigDecimal totalMargin = additional.subtract(decrease);

                // 当前在贷金额(等于在贷余额)
                BigDecimal loanBalance = loanBalanceMonitor.getLoanBalance();

                // 保证金占用比例上限
                LoanBalanceWarning loanBalanceWarning = loanBalanceWarningMapper.selectBalanceLoanByProjectCode(loanBalanceMonitor.getProjectCode());
                BigDecimal marginRate = loanBalanceWarning.getMarginRate();
                // 判断保证金比例是否为空
                if (marginRate == null) {
                    marginRate = BigDecimal.ZERO;
                }

                if (totalMargin.compareTo(BigDecimal.ZERO) > 0) {
                    // 机构保证金占用比例[(当前在贷金额*保证金比例)/项目保证金总额]
                    BigDecimal occupancyRatio = loanBalance.multiply(marginRate).divide(totalMargin, 2, RoundingMode.HALF_UP).setScale(2, RoundingMode.HALF_UP);
                    // 查询是否是按次数发送通知，是否存在除默认预警阈值外的其他预警值
                    Boolean flag = false;
                    ProjectWarningValue projectWarningValue = projectWarningValueMapper.selectProjectWarningValueList(loanBalanceMonitor.getProductCode(), loanBalanceMonitor.getProjectCode());
                    if (!Objects.isNull(projectWarningValue)) {
                        flag = true;
                    }
                    // 如果flag为true，表示没有除默认预警阈值外的其他预警值
                    if (!flag) {
                        // 获取项目的预警阈值,和机构保证金占用比例对比，如果预警阈值小于保证金占用比例，则发送企业微信 currentThreshold为0时，表示第一次发送通知
                        if (occupancyRatio.compareTo(loanBalanceWarning.getRatioMax()) > 0 && loanBalanceWarning.getCurrentThreshold() == 0) {

                            /** 发送通知--企业微信通知*/
                            // 组装消息参数
                            String msg = "<div class=\"gray\">" + "【" + projectName + "】项目机构保证金占用比例超过预警阈值，请及时处理！" + "</div>";
                            // 获取项目负责人
                            List<OaEditApproveGeneralityUser> yewuList = oaEditApproveGeneralityUserMapper.queryDataObjectByOaApplyTypeAndOaApplyId("4", oaProjectDeploy.getOaApplyId(), "1");
                            yewuList.removeAll(Collections.singleton(null));
                            if (!CollectionUtils.isEmpty(yewuList)) {
                                // 获取项目负责人id
                                List<Long> featureUserIds = yewuList.stream().map(s -> Long.parseLong(s.toString())).collect(Collectors.toList());
                                // 根据id查询用户账号
                                sysUserList = sysUserMapper.selectUserByUserId(featureUserIds);
                                sendNotify(sysUserList, msg);
                                // 修改预警表发送通知时的阈值为80
                                //LoanBalanceWarning warning = new LoanBalanceWarning();
                                //warning.setCurrentThreshold(0);
                                //warning.setProjectCode(loanBalanceMonitor.getProjectCode());
                                //loanBalanceWarningMapper.updateLoanBalanceWarning(warning);
                            }
                        }
                    // 如果flag为false，表示有除默认预警阈值外的其他预警值
                    } else {
                        // 获取项目的预警阈值,和机构保证金占用比例对比，如果预警阈值小于保证金占用比例，则发送企业微信 currentThreshold为0时，表示第一次发送通知
                        if (occupancyRatio.compareTo(loanBalanceWarning.getRatioMax()) > 0 && loanBalanceWarning.getCurrentThreshold() == 0) {

                            /** 发送通知--企业微信通知*/
                            // 组装消息参数
                            String msg = "<div class=\"gray\">" + "【" + projectName + "】项目机构保证金占用比例超过预警阈值，请及时处理！" + "</div>";
                            // 获取项目负责人
                            List<OaEditApproveGeneralityUser> yewuList = oaEditApproveGeneralityUserMapper.queryDataObjectByOaApplyTypeAndOaApplyId("4", oaProjectDeploy.getOaApplyId(), "1");
                            yewuList.removeAll(Collections.singleton(null));
                            if (!CollectionUtils.isEmpty(yewuList)) {
                                // 获取项目负责人id
                                List<Long> featureUserIds = yewuList.stream().map(OaEditApproveGeneralityUser::getUserId).collect(Collectors.toList());
                                // 根据id查询用户账号
                                sysUserList = sysUserMapper.selectUserByUserId(featureUserIds);
                                sendNotify(sysUserList, msg);
                                // 修改预警表发送通知时的阈值为1(1表示超过预警阈值时已发送过1次)
                                LoanBalanceWarning warning = new LoanBalanceWarning();
                                warning.setCurrentThreshold(1);
                                warning.setProjectCode(loanBalanceMonitor.getProjectCode());
                                loanBalanceWarningMapper.updateLoanBalanceWarning(warning);
                            }
                        }

                        // 达到第二预警阈值时，发送第二次预警通知
                        if (occupancyRatio.compareTo(loanBalanceWarning.getRatioMax()) >= 0 && occupancyRatio.compareTo(projectWarningValue.getSecondRatio()) > 0 && loanBalanceWarning.getCurrentThreshold() != null && loanBalanceWarning.getCurrentThreshold() == 1) {
                            /** 发送通知--企业微信通知*/
                            // 组装消息参数
                            String msg = "<div class=\"gray\">" + "【" + projectName + "】项目机构保证金占用比例超过预警阈值，请及时处理！" + "</div>";
                            // 获取项目负责人
                            List<OaEditApproveGeneralityUser> yewuList = oaEditApproveGeneralityUserMapper.queryDataObjectByOaApplyTypeAndOaApplyId("4", oaProjectDeploy.getOaApplyId(), "1");
                            yewuList.removeAll(Collections.singleton(null));
                            if (!CollectionUtils.isEmpty(yewuList)) {
                                // 获取项目负责人id
                                List<Long> featureUserIds = yewuList.stream().map(OaEditApproveGeneralityUser::getUserId).collect(Collectors.toList());
                                // 根据id查询用户账号
                                sysUserList = sysUserMapper.selectUserByUserId(featureUserIds);
                                sendNotify(sysUserList, msg);
                                // 修改预警表发送通知时的阈值为2(2表示超过第二预警阈值已发送过通知)
                                LoanBalanceWarning warning = new LoanBalanceWarning();
                                warning.setCurrentThreshold(2);
                                warning.setProjectCode(loanBalanceMonitor.getProjectCode());
                                loanBalanceWarningMapper.updateLoanBalanceWarning(warning);
                            }
                        }

                        // 达到第二预警阈值时，发送第三次预警通知
                        if (occupancyRatio.compareTo(loanBalanceWarning.getRatioMax()) >= 0 && occupancyRatio.compareTo(projectWarningValue.getThirdRatio()) > 0 && loanBalanceWarning.getCurrentThreshold() != null && loanBalanceWarning.getCurrentThreshold() == 2) {
                            /** 发送通知--企业微信通知*/
                            // 组装消息参数
                            String msg = "<div class=\"gray\">" + "【" + projectName + "】项目机构保证金占用比例超过预警阈值，请及时处理！" + "</div>";
                            // 获取项目负责人
                            List<OaEditApproveGeneralityUser> yewuList = oaEditApproveGeneralityUserMapper.queryDataObjectByOaApplyTypeAndOaApplyId("4", oaProjectDeploy.getOaApplyId(), "1");
                            yewuList.removeAll(Collections.singleton(null));
                            if (!CollectionUtils.isEmpty(yewuList)) {
                                // 获取项目负责人id
                                List<Long> featureUserIds = yewuList.stream().map(OaEditApproveGeneralityUser::getUserId).collect(Collectors.toList());
                                // 根据id查询用户账号
                                sysUserList = sysUserMapper.selectUserByUserId(featureUserIds);
                                sendNotify(sysUserList, msg);
                                // 修改预警表发送通知时的阈值为3(3表示超过第三预警阈值已发送过通知)
                                LoanBalanceWarning warning = new LoanBalanceWarning();
                                warning.setCurrentThreshold(3);
                                warning.setProjectCode(loanBalanceMonitor.getProjectCode());
                                loanBalanceWarningMapper.updateLoanBalanceWarning(warning);
                            }
                        }
                    }
                }
            }
        }
    }


    /**
     * 发送企业微信通知方法
     * @param sysUserList
     * @param msg
     * @throws IOException
     */
    public void sendNotify(List<SysUser> sysUserList, String msg) throws IOException {
        String sendUser = "";
        if (!CollectionUtils.isEmpty(sysUserList)) {
            for (SysUser sysUser : sysUserList) {
                // 获取项目负责人企业微信id
                VxUser vxUser = loanBalanceWarningMapper.selectUserQYWXInfoByUserName(sysUser.getUserName());
                if (!Objects.isNull(vxUser)) {
                    sendUser = sendUser + "|" + vxUser.getVxId();
                }
                // 发送企业微信消息
                accessTokenUtils.sendMsg(sendUser, msg, "10");
            }
        }
    }
}
