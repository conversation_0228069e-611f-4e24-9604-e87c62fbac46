package org.ruoyi.core.loanMonitor.service.impl;

import java.math.BigDecimal;
import java.security.Security;
import java.util.List;
import java.util.Objects;

import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.mapper.SysUserMapper;
import org.ruoyi.core.loanMonitor.domain.LoanBalanceWarning;
import org.ruoyi.core.loanMonitor.domain.vo.MarginInfoVo;
import org.ruoyi.core.loanMonitor.mapper.LoanBalanceWarningMapper;
import org.ruoyi.core.oasystem.domain.OaProjectDeploy;
import org.ruoyi.core.oasystem.mapper.OaProjectDeployMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.ruoyi.core.loanMonitor.mapper.MarginUpdateLogMapper;
import org.ruoyi.core.loanMonitor.domain.MarginUpdateLog;
import org.ruoyi.core.loanMonitor.service.IMarginUpdateLogService;

/**
 * 保证金更新记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-16
 */
@Service
public class MarginUpdateLogServiceImpl implements IMarginUpdateLogService 
{
    @Autowired
    private MarginUpdateLogMapper marginUpdateLogMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private LoanBalanceWarningMapper loanBalanceWarningMapper;

    @Autowired
    private OaProjectDeployMapper oaProjectDeployMapper;

    /**
     * 查询保证金更新记录
     *
     * @param id 保证金更新记录主键
     * @return 保证金更新记录
     */
    //@Override
    //public MarginUpdateLog selectMarginUpdateLogById(Integer id)
    //{
    //    return marginUpdateLogMapper.selectMarginUpdateLogById(id);
    //}

    /**
     * 查询保证金更新记录列表
     * 
     * @param marginUpdateLog 保证金更新记录
     * @return 保证金更新记录
     */
    @Override
    public List<MarginUpdateLog> selectMarginUpdateLogList(MarginUpdateLog marginUpdateLog)
    {
        return marginUpdateLogMapper.selectMarginUpdateLogList(marginUpdateLog);
    }

    /**
     * 新增保证金更新记录
     * 
     * @param marginUpdateLog 保证金更新记录
     * @return 结果
     */
    @Override
    public int insertMarginUpdateLog(MarginUpdateLog marginUpdateLog)
    {
        //如果小数点超过四位，返回小数点过长
        if (marginUpdateLog.getMarginRate().scale() > 4){
            throw new ServiceException("保证金比例最多可输入两位小数！");
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        marginUpdateLog.setUpdateTime(DateUtils.getNowDate());
        marginUpdateLog.setOperator(loginUser.getUsername());
        //查询预警表
        LoanBalanceWarning loanBalanceWarning = loanBalanceWarningMapper.selectLoanBalanceWarningById(marginUpdateLog.getProjectCode());

        //如果是第一次追加保证金，则在预警表中初始化数据，用来后续查询
        LoanBalanceWarning balanceWarning = new LoanBalanceWarning();
        if (Objects.isNull(loanBalanceWarning)){
            balanceWarning.setProjectCode(marginUpdateLog.getProjectCode());
            balanceWarning.setProductCode(marginUpdateLog.getProductCode());
            balanceWarning.setMarginRate(marginUpdateLog.getMarginRate().divide(BigDecimal.valueOf(100)));
            balanceWarning.setRatioMax(new BigDecimal(0.07));//设置默认保证金预警阈值上限
            balanceWarning.setCurrentThreshold(0);
            loanBalanceWarningMapper.insertLoanBalanceWarning(balanceWarning);
            marginUpdateLog.setWarningId(balanceWarning.getId());
        }else {
            marginUpdateLog.setWarningId(loanBalanceWarning.getId());
            //根据id更新保证金比例字段
            balanceWarning.setId(loanBalanceWarning.getId());
            balanceWarning.setProjectCode(loanBalanceWarning.getProjectCode());
            balanceWarning.setProductCode(marginUpdateLog.getProductCode());
            balanceWarning.setMarginRate(marginUpdateLog.getMarginRate().divide(BigDecimal.valueOf(100)));
            balanceWarning.setCurrentThreshold(0);
            loanBalanceWarningMapper.updateLoanBalanceWarning(balanceWarning);
        }
        //插入保证金追加记录
        return marginUpdateLogMapper.insertMarginUpdateLog(marginUpdateLog);
    }

    /**
     * 根据项目编码查询最新的账户保证金信息
     * @param projectCode
     * @return
     */
    @Override
    public MarginInfoVo selectMarginInfoByProjectCode(Integer projectCode) {
        MarginInfoVo marginInfoVo = new MarginInfoVo();
        ///保证金总额
        BigDecimal additional = marginUpdateLogMapper.selectTotalMarginByProjectCode(projectCode);//查询追加的总额
        BigDecimal decrease = marginUpdateLogMapper.selectTotalDecreaseByProjectCode(projectCode);//查询减少的总额
        BigDecimal totalAmount = additional.subtract(decrease);
        marginInfoVo.setTotalAmount(totalAmount);
        //根据项目唯一编码查询项目信息
        OaProjectDeploy oaProjectDeploy = oaProjectDeployMapper.selectOaProjectDeployById(Long.valueOf(projectCode));

        //项目名称
        marginInfoVo.setProjectName(oaProjectDeploy.getProjectName());
        //项目编号
        marginInfoVo.setProjectCode(projectCode);

        //保证金比例
        LoanBalanceWarning loanBalanceWarning = loanBalanceWarningMapper.selectBalanceLoanByProjectCode(projectCode);
        BigDecimal marginRate = loanBalanceWarning.getMarginRate();
        marginInfoVo.setMarginRate(marginRate);
        if (marginRate != null && marginRate.compareTo(BigDecimal.ZERO) > 0){
            marginInfoVo.setMarginRateStr(String.valueOf(marginRate.multiply(BigDecimal.valueOf(100)))+ "%");
        }
        return marginInfoVo;
    }
}
