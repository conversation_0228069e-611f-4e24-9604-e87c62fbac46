package org.ruoyi.core.offSupply.service;

import java.io.IOException;
import java.util.List;

import com.ruoyi.common.core.domain.AjaxResult;
import org.ruoyi.core.offSupply.domain.OffReceiveMain;
import org.ruoyi.core.offSupply.domain.OffReceivePurchaseDetail;
import org.ruoyi.core.offSupply.domain.vo.OffReceiveReport;
import org.ruoyi.core.offSupply.domain.vo.ReceiveReportVo;

import javax.servlet.http.HttpServletResponse;

/**
 * 办公用品领用主Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-21
 */
public interface IOffReceiveMainService 
{
    /**
     * 查询办公用品领用主
     * 
     * @param id 办公用品领用主主键
     * @return 办公用品领用主
     */
    public OffReceiveMain selectOffReceiveMainById(Long id);

    /**
     * 查询办公用品领用主列表
     * 
     * @param offReceiveMain 办公用品领用主
     * @return 办公用品领用主集合
     */
    public List<OffReceiveMain> selectOffReceiveMainList(OffReceiveMain offReceiveMain);

    /**
     * 新增办公用品领用主
     * 
     * @param offReceiveMain 办公用品领用主
     * @return 结果
     */
    public AjaxResult insertOffReceiveMain(OffReceiveMain offReceiveMain);

    /**
     * 修改办公用品领用主
     * 
     * @param offReceiveMain 办公用品领用主
     * @return 结果
     */
    public AjaxResult updateOffReceiveMain(OffReceiveMain offReceiveMain);

    /**
     * 批量删除办公用品领用主
     * 
     * @param ids 需要删除的办公用品领用主主键集合
     * @return 结果
     */
    public int deleteOffReceiveMainByIds(Long[] ids);

    /**
     * 删除办公用品领用主信息
     * 
     * @param id 办公用品领用主主键
     * @return 结果
     */
    public int deleteOffReceiveMainById(Long id);

    /**
     * 更新办公用品流程状态
     * @param offReceiveMain
     * @return
     */
    int updateProcessStatus(OffReceiveMain offReceiveMain);

    /**
     * 查询物品领用报表
     * @param receiveReportVo
     * @return
     */
    List<OffReceiveReport> selectSupplyReceiveReportList(ReceiveReportVo receiveReportVo);

    /**
     * 导出物品领用报表
     * @param receiveReportVo
     */
    void exportReceiveReport(HttpServletResponse response, ReceiveReportVo receiveReportVo) throws IOException;

    /**
     * 判断用户是否有某公司流程发起权限
     * @return
     */
    Boolean checkUserReceiveAuth(Long companyId);

    /**
     * 根据流程id查询办公用品领用、物品领用、采购申请表单信息
     * @param processId
     * @return
     */
    OffReceivePurchaseDetail selectOffReceiveMainByProcessId(String processId);

    /**
     * 保存表单数据
     * @param offReceivePurchaseDetail
     * @return
     */
    int saveFormData(OffReceivePurchaseDetail offReceivePurchaseDetail);
}
