package org.ruoyi.core.offSupply.mapper;

import java.util.List;

import com.ruoyi.common.core.domain.AjaxResult;
import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.offSupply.domain.*;

/**
 * 办公用品维护Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-18
 */
public interface OffSupplyMainMapper 
{
    /**
     * 查询办公用品维护
     * 
     * @param id 办公用品维护主键
     * @return 办公用品维护
     */
    public OffSupplyMain selectOffSupplyMainById(Long id);

    /**
     * 查询办公用品维护列表
     * 
     * @param offSupplyMain 办公用品维护
     * @return 办公用品维护集合
     */
    public List<OffSupplyMain> selectOffSupplyMainList(OffSupplyMain offSupplyMain);

    /**
     * 新增办公用品维护
     * 
     * @param offSupplyMain 办公用品维护
     * @return 结果
     */
    public int insertOffSupplyMain(OffSupplyMain offSupplyMain);

    /**
     * 修改办公用品维护
     * 
     * @param offSupplyMain 办公用品维护
     * @return 结果
     */
    public int updateOffSupplyMain(OffSupplyMain offSupplyMain);

    /**
     * 删除办公用品维护
     * 
     * @param sysCode 办公用品维护主键
     * @return 结果
     */
    public int deleteOffSupplyMainBySysCode(String sysCode);

    /**
     * 批量删除办公用品维护
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOffSupplyMainByIds(Long[] ids);

    /**
     * 根据类类别id查询类别下的办公用品信息
     * @param categoryId 类别id
     * @return
     */
    List<OffSupplyMain> selectOffSupplyMainByCategoryId(Long categoryId);

    /**
     * 根据类别id集合查询办公用品
     * @param categoryIds
     * @return
     */
    List<OffSupplyMain> selectOffSupplyMainByCategoryIds(@Param("categoryIds") List<Long> categoryIds);

    /**
     * 批量更新物品的所属类别
     * @param supplyIds 物品id集合
     * @param categoryId 所属类别id
     */
    void updateOffSupplyMainCategoryIdByCategoryIds(@Param("supplyIds") List<Long> supplyIds, @Param("categoryId") Long categoryId);

    /**
     * 查询数据总数量，生产系统编码
     * @return
     */
    int selectCountNumber();

    /**
     * 物品整理
     * @param ids
     * @param categoryId
     * @return
     */
    int updateSupplySettle(@Param("ids") List<Long> ids, @Param("categoryId") Long categoryId);

    /**
     * 查询即将过期物品，用于发送通知
     * @return
     */
    List<OffSupplyMain> selectOffSupplyByExpireDateAndUseNotifyList();

    /**
     * 查询办公用品修改记录
     * @param supplySysCode
     * @return
     */
    List<OffSupplyHistory> selectOffSupplyRecord(String supplySysCode);

    /**
     * 插入修改记录
     * @param offSupplyHistory
     */
    void insertOffSupplyHistory(OffSupplyHistory offSupplyHistory);

    /**
     * 根据类型查询物品信息
     * @param offSupplyMain
     * @return
     */
    List<OffSupplyMain> selectOffSupplyListByType(OffSupplyMain offSupplyMain);

    /**
     * 根据流程id查询办公用品详情
     * @param flowId
     * @return
     */
    List<OffReceiveDetail> selectOffReceiveDetailByFlowId(String flowId);

    /**
     * 根据系统编码查询最新的办公用品
     * @param sysCode
     * @return
     */
    OffSupplyMain selectOffSupplyMainBySysCode(String sysCode);

    /**
     * 根据记录id查询修改记录详情
     * @param historyId
     * @return
     */
    OffSupplyHistory selectSupplyHistoryInfoById(String historyId);

    /**
     * 根据物品id集合查询物品信息集合
     * @param ids
     * @return
     */
    List<OffSupplyMain> selectOffSupplyMainByIds(@Param("ids") List<Long> ids);
}
