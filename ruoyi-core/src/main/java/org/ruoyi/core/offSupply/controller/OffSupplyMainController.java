package org.ruoyi.core.offSupply.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.RepeatSubmit;
import org.ruoyi.core.offSupply.domain.vo.SupplySettleVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import org.ruoyi.core.offSupply.domain.OffSupplyMain;
import org.ruoyi.core.offSupply.service.IOffSupplyMainService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 办公用品维护Controller
 * 
 * <AUTHOR>
 * @date 2025-03-18
 */
@RestController
@RequestMapping("/offSupplyMain/supplyMain")
public class OffSupplyMainController extends BaseController
{
    @Autowired
    private IOffSupplyMainService offSupplyMainService;

    /**
     * 查询办公用品列表
     */
    @RepeatSubmit(interval = 1500, message = "请勿重复提交！")
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody OffSupplyMain offSupplyMain)
    {
        List<OffSupplyMain> list = offSupplyMainService.selectOffSupplyMainList(offSupplyMain);
        return getDataTable(list);
    }

    /**
     * 导出办公用品列表
     */
    @PreAuthorize("@ss.hasPermi('maintenance:export')")
    @RepeatSubmit(interval = 3000, message = "请勿重复提交！")
    @Log(title = "办公用品维护", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OffSupplyMain offSupplyMain)
    {
       List<OffSupplyMain> list = offSupplyMainService.selectOffSupplyMainList(offSupplyMain);
       ExcelUtil<OffSupplyMain> util = new ExcelUtil<OffSupplyMain>(OffSupplyMain.class);
       util.exportExcel(response, list, "办公用品维护");
    }

    /**
     * 获取办公用品详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(offSupplyMainService.selectOffSupplyMainById(id));
    }

    /**
     * 新增办公用品
     */
    @PreAuthorize("@ss.hasPermi('maintenance:addEdit')")
    @RepeatSubmit(interval = 2000, message = "请勿重复提交！")
    @Log(title = "办公用品维护", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OffSupplyMain offSupplyMain)
    {
        return toAjax(offSupplyMainService.insertOffSupplyMain(offSupplyMain));
    }

    /**
     * 修改办公用品
     */
    @PreAuthorize("@ss.hasPermi('maintenance:addEdit')")
    @RepeatSubmit(interval = 2000, message = "请勿重复提交！")
    @Log(title = "办公用品维护", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public AjaxResult edit(@RequestBody OffSupplyMain offSupplyMain)
    {
        return toAjax(offSupplyMainService.updateOffSupplyMain(offSupplyMain));
    }

    /**
     * 删除办公用品
     */
    @PreAuthorize("@ss.hasPermi('maintenance:del')")
    @Log(title = "办公用品维护", businessType = BusinessType.DELETE)
	@DeleteMapping("/{sysCode}")
    public AjaxResult remove(@PathVariable String sysCode)
    {
        return toAjax(offSupplyMainService.deleteOffSupplyMainBySysCode(sysCode));
    }

    /**
     * 附件上传
     * @param file
     * @return
     */
    @Anonymous
    @PostMapping("/uploadFile")
    public AjaxResult uploadNoticeFile(@RequestParam("file") MultipartFile file, @RequestParam("fileType") String fileType, @RequestParam("userName") String userName) {
        return offSupplyMainService.uploadFile(file, fileType, userName);
    }

    /**
     * 附件删除
     */
    @DeleteMapping("/deleteFile/{fileId}")
    public AjaxResult deleteFile(@PathVariable Long fileId){
        return toAjax(offSupplyMainService.deleteFileById(fileId));
    }

    /**
     * 物品整理
     */
    @PreAuthorize("@ss.hasPermi('maintenance:changeGroy')")
    @PostMapping("/supplySettle")
    public AjaxResult supplySettle(@RequestBody SupplySettleVo supplySettleVo){
        return toAjax(offSupplyMainService.updateSupplySettle(supplySettleVo));
    }

    /**
     * 查看办公用品修改记录
     */
    @GetMapping("/viewRecord/{sysCode}")
    public AjaxResult viewRecord(@PathVariable String sysCode){
        return AjaxResult.success(offSupplyMainService.selectOffSupplyRecord(sysCode));
    }

    /**
     * 查看物品修改记录详情
     */
    @GetMapping("/recordInfo/{historyId}")
    public AjaxResult viewRecordInfo(@PathVariable String historyId){
        return AjaxResult.success(offSupplyMainService.selectOffSupplyRecordInfoById(historyId));
    }

    /**
     * 获取有权限的公司下拉框列表
     */
    @GetMapping("/getAuthCompany")
    public TableDataInfo getAuthCompany(){
        return getDataTable(offSupplyMainService.selectAuthCompany());
    }

    /**
     * 发起办公用品领用申请
     * 根据所选领用物品类型，查询不同的列表数据
     */
    //@PostMapping("/supplyApply")
    //public AjaxResult supplyApply(@RequestBody OffSupplyMain offSupplyMain)
    //{
    //    return offSupplyMainService.selectOffReceiveMainByDifType(offSupplyMain);
    //}

    /**
     * 修改办公用品启停状态
     */
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody OffSupplyMain offSupplyMain)
    {
        return toAjax(offSupplyMainService.updateOffSupplyMainStatus(offSupplyMain));
    }
}
