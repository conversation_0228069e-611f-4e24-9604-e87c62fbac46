package org.ruoyi.core.superviseInformation.mapper;

import org.ruoyi.core.superviseInformation.domain.SuperviseInformationUsed;

import java.util.List;

/**
 * 资料用印Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-01
 *
 */
public interface SuperviseInformationUsedMapper
{
    /**
     * 查询资料用印
     * @param id 资料用印主键
     * @return 资料用印
     */
    public SuperviseInformationUsed selectInformationUsedById(Long id);

    /**
     * 查询资料用印列表
     *
     * @param informationUsed 资料用印
     * @return 资料用印集合
     */
    public List<SuperviseInformationUsed> selectInformationUsedList(SuperviseInformationUsed informationUsed);

    /**
     * 新增资料用印
     *
     * @param informationUsed 资料用印
     * @return 结果
     */
    public int insertInformationUsed(SuperviseInformationUsed informationUsed);

    /**
     * 修改资料用印
     *
     * @param informationUsed 资料用印
     * @return 结果
     */
    public int updateInformationUsed(SuperviseInformationUsed informationUsed);

    /**
     * 删除资料用印
     *
     * @param id 资料用印主键
     * @return 结果
     */
    public int deleteInformationUsedById(Long id);

    /**
     * 批量删除资料用印
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInformationUsedByIds(Long[] ids);

}
