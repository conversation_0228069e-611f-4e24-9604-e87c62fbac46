package org.ruoyi.core.superviseInformation.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 资料目录对象 zl_information_catalogue
 *
 * <AUTHOR>
 * @date 2023-11-10
 *
 */
@Data
public class SuperviseInformationCatalogue extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 目录名称 */
    @Excel(name = "目录名称")
    @NotNull(message = "目录不能为空")
    private String catalogueName;

    /** 上级目录id */
    @Excel(name = "上级目录id")
    @NotNull(message = "上级目录不能为空")
    private Long parentId;

    /** 系统目录编号 */
    @Excel(name = "系统目录编号")
    private String catalogueSystemCode;

    /** 目录编号 */
    @Excel(name = "目录编号")
    @NotNull(message = "目录编号不能为空")
    private String catalogueCode;

    /** 合作公司 */
    @Excel(name = "合作公司")
    private Long cooperationCompany;

    /** 合作项目 */
    @Excel(name = "合作项目")
    private Long cooperationProject;

    /** 目录类型 */
    @Excel(name = "目录类型 1.资料目录 2.文件目录")
    private String catalogueType;

    /** 是否为公共资料库 1.是 0.否 */
    @Excel(name = "是否为公共资料库 1.是 0.否")
    private String isPublic;

    /** 所属公司 (组织架构)*/
    @Excel(name = "所属公司")
    private Long orgId;

    /** 所属部门 */
    @Excel(name = "所属部门")
    @NotNull(message = "所属部门不能为空")
    private Long deptId;

    /** 排序号 */
    @Excel(name = "排序号")
    @NotNull(message = "排序不能为空")
    private Long orderNum;

    /** 备注 */
    @Excel(name = "备注")
    private String remake;

    private Long auDeptId;

    private Long auUserId;

    private Long auPostId;

//    private List<Long> auDeptIds;
//
//    private List<Long> auUserIds;
//
//    private List<Long> auPostIds;

    /**条件公司字段*/
    private Long unitId;
    /**条件公司字段*/
    private List<Long> orgIds;

    private List<Long> ids;

    private String informationRetrieval;
    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("catalogueName", getCatalogueName())
            .append("parentId", getParentId())
            .append("catalogueSystemCode", getCatalogueSystemCode())
            .append("catalogueCode", getCatalogueCode())
            .append("orgId", getOrgId())
            .append("deptId", getDeptId())
            .append("orderNum", getOrderNum())
            .append("remake", getRemake())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();

    }

}
