package org.ruoyi.core.cwproject.domain.export;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import org.ruoyi.core.cwproject.domain.view.CwProjectLawDetailView;

import java.util.List;
import java.util.Map;

/**
 * 财务项目管理 - 导出主对象 -法催项目
 *
 * <AUTHOR>
 * @date 2023-02-23
 */
@Data
public class ExcelLawCwProject extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 项目名称 */
    @Excel(name = "项目名称")
    private String projectName;

    /** 担保公司 */
    @Excel(name = "担保公司")
    private String custName;

    /** 汇款公司 */
    @Excel(name = "汇款公司")
    private String incomeCustName;

    /** 项目状态：0正常 1终止 */
    @Excel(name = "项目状态：0正常 1终止")
    private String projectFlag;

    /** 状态，0正常 1禁用 */
    @Excel(name = "状态，0正常 1禁用")
    private String status;
    
    private List<ExcelCwProjectCust> custList;
    private List<CwProjectLawDetailView> incomeList;

    private List<Map<String, Object>> memberList;
}
