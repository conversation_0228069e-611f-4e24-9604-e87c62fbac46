package org.ruoyi.core.cwproject.domain.dto;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 财务项目管理-完结项目归档查询详情 CwProjectOverDetailTitleDto
 *
 * <AUTHOR>
 * @date 2022-11-22
 */
@Getter
@Setter
@ToString
public class CwProjectOverDetailTitleDto extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @Excel(name = "项目名称")
    private String projectName;

    @Excel(name = "担保公司")
    private String custName;

    @Excel(name = "收入公司")
    private String incomeCustName;
}
