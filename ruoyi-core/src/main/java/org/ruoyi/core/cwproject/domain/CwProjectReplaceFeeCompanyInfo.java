package org.ruoyi.core.cwproject.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * 财务项目管理替换返费公司信息表 实体对象
 *
 * <AUTHOR>
 * @date 2023-11-22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class CwProjectReplaceFeeCompanyInfo {
    private static final long serialVersionUID = 1L;

    //主键
    private Long id;

    //项目id
    private Long projectId;

    //最初返费公司id(与返费公司表查询用)
    private Long oaTraderId;

    //原返费公司id(被替换的公司)
    private Long oldOaTraderId;

    //新返费公司id
    private Long newOaTraderId;

    //替换时间
    @JsonFormat(pattern = "yyyy年MM月dd日")
    private Date replaceTime;

    private Date replaceTimeDate;

    //状态
    private String status;

    //操作人
    private String replaceBy;

    //操作人id
    private Long replacePersonId;

    //备注
    private String remark;

    //最初返费公司名
    private String oaTraderUserName;

    //原返费公司名
    private String oldOaTraderUserName;

    //新返费公司名
    private String newOaTraderUserName;

    //custId
    private Long custId;

    //replaceFlag
    private String replaceFlag;

    //项目类型 0-正常项目 1-法催项目
    private String projectReplaceType;

    //替换历史已发生期次的记录 0-否 1-是
    private String replaceHistoryIncomeFlag;

    //要替换的期次集合
    private List<Long> replaceIncomeIdList;

    //方案标识（同一个公司可能存在于不同的方案当中，用这个可以去标识具体的方案）
    private String schemeFlag;
}
