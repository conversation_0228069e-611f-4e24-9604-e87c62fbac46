package org.ruoyi.core.cwproject.service;

import com.ruoyi.common.core.domain.model.LoginUser;
import org.ruoyi.core.cwproject.domain.CwProjectAck;
import org.ruoyi.core.cwproject.domain.dto.ProfitUnConfirmDetailsList;
import org.ruoyi.core.cwproject.domain.dto.ProfitUnConfirmList;

import java.util.List;
import java.util.Map;

/**
 * 财务项目管理-提成基数确认Service接口
 *
 * <AUTHOR>
 * @date 2022-11-09
 */
public interface ICwProjectAckService
{
    /**
     * 查询财务项目管理-提成基数确认
     *
     * @param 财务项目管理-提成基数确认主键
     * @return 财务项目管理-提成基数确认
     */
    public CwProjectAck selectCwProjectAckById(Long id);
    /**
     * 查询财务项目管理-利润待提成确认
     *
     * @param
     * @return 财务项目管理-提成基数确认
     */
    List<ProfitUnConfirmList> selectList(CwProjectAck cwProjectAck,Long userId, LoginUser loginUser);

    /**
     * 查询财务项目管理-提成基数代确认列表
     *
     * @param cwProjectAck 财务项目管理-提成基数代确认
     * @return 财务项目管理-提成基数确认集合
     */
    public List<CwProjectAck> selectCwProjectAckList(CwProjectAck cwProjectAck);
    /**
     * 查询财务项目管理-提成基数代确认列表详情
     *
     * @param 财务项目管理-提成基数代详情
     * @return 财务项目管理-提成基数确认集合
     */
    public List<ProfitUnConfirmDetailsList> selectUnCwProjectAckDetailsById(CwProjectAck cwProjectAck);

    /**
     * 新增财务项目管理-提成基数确认
     *
     * @param cwProjectAck 财务项目管理-提成基数确认
     * @return 结果
     */
    public int insertCwProjectAck(CwProjectAck cwProjectAck);

    /**
     * 修改财务项目管理-提成基数确认
     *
     * @param cwProjectAck 财务项目管理-提成基数确认
     * @return 结果
     */
    public int updateCwProjectAck(CwProjectAck cwProjectAck, LoginUser loginUser);

    /**
     * 批量删除财务项目管理-提成基数确认
     *
     * @param ids 需要删除的财务项目管理-提成基数确认主键集合
     * @return 结果
     */
    public int deleteCwProjectAckByIds(Long[] ids);

    /**
     * 删除财务项目管理-提成基数确认信息
     *
     * @param id 财务项目管理-提成基数确认主键
     * @return 结果
     */
    public int deleteCwProjectAckById(Long id);


    List<Map<String, Object>> selectListForAck(CwProjectAck cwProjectAck, Long userId);
}