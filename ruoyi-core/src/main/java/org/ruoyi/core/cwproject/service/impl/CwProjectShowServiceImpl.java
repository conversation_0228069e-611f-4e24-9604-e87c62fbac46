package org.ruoyi.core.cwproject.service.impl;

import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.AuthModuleEnum;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.common.utils.file.ZipUtil;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.dto.AuthorizedFeatureDetailDTO;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.mapper.SysUserRoleMapper;
import com.ruoyi.system.service.impl.NewAuthorityServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.ruoyi.core.cwproject.domain.*;
import org.ruoyi.core.cwproject.domain.dto.CwProjectOverDetailCompanyDto;
import org.ruoyi.core.cwproject.domain.export.*;
import org.ruoyi.core.cwproject.domain.projectVO.*;
import org.ruoyi.core.cwproject.domain.view.CwProjectLawDetailView;
import org.ruoyi.core.cwproject.export.BatchExportExcel;
import org.ruoyi.core.cwproject.export.BeachExportLawCwProject;
import org.ruoyi.core.cwproject.mapper.*;
import org.ruoyi.core.cwproject.service.ICwProjectShowService;
import org.ruoyi.core.oasystem.domain.OaProjectDeploy;
import org.ruoyi.core.oasystem.mapper.OaProjectDeployMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CwProjectShowServiceImpl implements ICwProjectShowService {

  @Resource
  private CwProjectShowMapper cwProjectShowMapper;

  @Resource
  private CwProjectUserMapper cwProjectUserMapper;

  @Autowired
  private CwProjectCustMapper cwProjectCustMapper;

    @Autowired
    private CwProjectAckMapper cwProjectAckMapper;
    @Autowired
    private ExportCwProjectMapper exportCwProjectMapper;
    @Autowired
    private CwProjectCustServiceImpl cwProjectCustService;
  @Resource
  private SysUserMapper sysUserMapper;

  @Autowired
  private OaProjectDeployMapper oaProjectDeployMapper;

  @Autowired
  private NewAuthorityServiceImpl newAuthorityService;
  /**
   * 返费项目数据统计列表
   * @param cwProjectShowVo
   * @return
   */
  @Override
  public List<CwProjectShowVo> selectCwProjectShowList(CwProjectShowVo cwProjectShowVo) {
    //查询所有的项目
    List<CwProjectShowVo> cwProjectShowVos = cwProjectShowMapper.selectCwProjectList(cwProjectShowVo);
    return cwProjectShowVos;
  }

  @Resource
  CwProjectMapper cwProjectMapper;
 @Autowired
 private TopNotifyMapper topNotifyMapper;
  @Resource
  private CwProjectFeeMapper cwProjectFeeMapper;

  @Resource
  CwProjectIncomeMapper cwProjectIncomeMapper;

  @Resource
  CwProjectPayMapper cwProjectPayMapper;

  /**
   * 数据统计列表详细信息
   */
  @Override
  public List<ProjectDataParticularsVo> selectprjectShowById(Long id) {

    ArrayList<ProjectDataParticularsVo> projectDataParticularsVos = new ArrayList<>();
    ProjectDataParticularsVo projectDataParticularsVo = new ProjectDataParticularsVo();



    projectDataParticularsVos.add(projectDataParticularsVo);
    CwProjectIncome cwProjectIncome = new CwProjectIncome();
    cwProjectIncome.setProjectId(id);
    //获取收入表
    List<CwProjectIncome> cwProjectIncomes = cwProjectIncomeMapper.selectCwProjectIncomeList(cwProjectIncome);
    if (cwProjectIncomes.size() > 0){
      cwProjectIncomes.stream().forEach(cwProjectIncome1 -> {
        projectDataParticularsVo.setCwProjectIncome(cwProjectIncome1);
        CwProjectFee CwProjectFee = new CwProjectFee();
        CwProjectFee.setProjectId(id);
        CwProjectFee.setProjectIncomeId(cwProjectIncome1.getId());
        //获取返费列表
        List<CwProjectFee> CwProjectFeees = cwProjectFeeMapper.selectCwProjectFeeList(CwProjectFee);
        projectDataParticularsVo.setCwProjectFeeList(CwProjectFeees);
        if (CwProjectFeees.size() > 0){
          CwProjectFeees.stream().forEach(ProjectFee -> {
            CwProjectPay cwProjectPay = new CwProjectPay();
            cwProjectPay.setProjectId(id);
            cwProjectPay.setProjectFeeId(ProjectFee.getId());
            cwProjectPay.setProjectIncomeId(cwProjectIncome1.getId());
            CwProjectPay cwProjectPay1 = cwProjectPayMapper.selectCwProjectPayList(cwProjectPay).get(0);
            ProjectFee.setCwProjectPay(cwProjectPay1);
          });
        }
      });
    }



    return projectDataParticularsVos;
  }

  @Override
  public List<CwProjectDetail> listDetail(CwProjectDetail cwProjectDetail) {
    return cwProjectShowMapper.listDetail(cwProjectDetail);
  }

  @Override
  public List<CwProjectShowVo> selectCwProjectShowListAll() {
    return cwProjectShowMapper.selectCwProjectShowListAll();

  }

  private String kuaiji = "kuaiji";

  private String chuna = "chuna";

  private String yewu = "yewu";
  @Resource
  private SysUserRoleMapper sysUserRoleMapper;
  @Override
  public List<Map<String,Object>> listKuaiji() {
    List<Map<String,Object>> sysUsers = sysUserMapper.selectInRoleId(kuaiji);
    return sysUsers;
  }

  @Override
  public List<Map<String,Object>> listchuna() {
    List<Map<String,Object>> sysUsers = sysUserMapper.selectInRoleId(chuna);
    return sysUsers;
  }

  @Override
  public List<Map<String,Object>> listYewu() {
    List<Map<String,Object>> sysUsers = sysUserMapper.selectInRoleId(yewu);
    return sysUsers;
  }

  @Override
  @Transactional
  public int addFeeIncomePay(ProjectDataParticularsVo projectDataParticularsVo) {
    try {
      cwProjectIncomeMapper.insertCwProjectIncome(projectDataParticularsVo.getCwProjectIncome());
      projectDataParticularsVo.getCwProjectFeeList().stream().forEach(projectDataParticulars -> {
        CwProjectFee CwProjectFee = new CwProjectFee();
        BeanUtils.copyProperties(projectDataParticulars, CwProjectFee);
        cwProjectFeeMapper.insertCwProjectFee(CwProjectFee);
        cwProjectPayMapper.insertCwProjectPay(projectDataParticulars.getCwProjectPay());
      });
      return 1;
    }catch (Exception e){
      return 0;
    }



  }




//  public int updatePsalesman(List<Long> salesmans,Long projectId){
//    try {
//      String name = SecurityUtils.getLoginUser().getUser().getNickName();
//      Date date = new Date();
//      cwProjectUserMapper.updatePsalesmanStatus(projectId);
//      if (salesmans.size() > 0){
//        salesmans.stream().forEach(salesman ->{
//          CwProjectUser cwProjectUser = new CwProjectUser();
//          cwProjectUser.setProjectId(projectId);
//          cwProjectUser.setUserId(salesman);
//          cwProjectUser.setUserFlag("2");
//          cwProjectUser.setCreateBy(name);
//          cwProjectUser.setCreateTime(date);
//          cwProjectUser.setUpdateBy(name);
//          cwProjectUser.setUpdateTime(date);
//          cwProjectUserMapper.insertCwProjectUser(cwProjectUser);
//        });
//      }
//      return 1;
//    }catch (Exception e){
//      return 0;
//    }
//
//
//  }

//  @Override
//  public List<CwProjectShowVo> selectCwProjectList(CwProjectShowVo cwProjectShowVo) {
//    return cwProjectShowMapper.selectCwProjectList(cwProjectShowVo);
//  }

  @Override
  public String selectprjectShowflagAll(Long id) {
    try {
      FlagVO flagVO =cwProjectShowMapper.selectprjectShowflagAll(id);
      String flag = null;
      if (flagVO.getIncomeFlag().equals("0") && flagVO.getFeeFlag().equals("0")){
        flag="0";
      } else if (flagVO.getIncomeFlag().equals("1") && flagVO.getFeeFlag().equals("0")) {
        flag="1";
      }
      return flag;
    }catch (Exception e){
      return null;
    }

  }

  @Override
  public List<Map<String,Object>> getCustNameXiaLa() {
    return cwProjectShowMapper.getCustNameXiaLa();
  }

  @Override
  public List<Map<String,Object>> getIncomeCustNameXiaLa() {
    return cwProjectShowMapper.getIncomeCustNameXiaLa();
  }

  /**
   * 新增项目
   * @param addParojectVo
   * @param userName
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public Long addProject(AddParojectVo addParojectVo,String userName){
      //2024.06.26新增项目时，增加项目主表的project_type_relevance_type_id
      Long projectTypeCode = addParojectVo.getProjectTypeCode();

//    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    Date now = new Date();
    //插入项目主表
    CwProject cwProject = new CwProject();
    cwProject.setProjectType(addParojectVo.getProjectType());
    cwProject.setCustName(addParojectVo.getCustName());
    cwProject.setProjectName(addParojectVo.getProjectName());
    cwProject.setIncomeCustName(addParojectVo.getIncomeCustName());
    cwProject.setCreateBy(userName);
    cwProject.setCreateTime(now);
    cwProject.setUpdateTime(now);
    cwProject.setProjectFlag("0");
    cwProject.setPrestoreIncomeFlag(addParojectVo.getPrestoreIncomeFlag());
    cwProject.setOaProjectDeployId(addParojectVo.getOaProjectDeployId());
    cwProject.setProjectTypeRelevanceTypeId(projectTypeCode);
    if ("1".equals(addParojectVo.getGenerateCertificateFlag())) {
        cwProject.setGenerateCertificateFlag(addParojectVo.getGenerateCertificateFlag());
        cwProject.setAccountSetsId(addParojectVo.getAccountSetsId());
        cwProject.setGuaranteeIncomeType(addParojectVo.getGuaranteeIncomeType());
        cwProject.setGuarantyPayee(addParojectVo.getGuarantyPayee());
    }
      int i = cwProjectMapper.insertCwProject(cwProject);
    if (i > 0) {
        //更改oa项目名称表的状态
        OaProjectDeploy oaProjectDeploy = new OaProjectDeploy();
        oaProjectDeploy.setId(addParojectVo.getOaProjectDeployId());
        oaProjectDeploy.setCwRelevanceStatus("1");
        int i1 = oaProjectDeployMapper.updateOaProjectDeploy(oaProjectDeploy);
    }
      Long id = cwProject.getId();

    //插入利润确认表数据
      CwProjectAck cwProjectAck = new CwProjectAck();
      cwProjectAck.setProjectId(id);
      cwProjectAck.setAckFlag("0");
      cwProjectAck.setStatus("0");
      cwProjectAck.setAckDate(now);
      cwProjectAck.setCreateBy(userName);
      cwProjectAck.setCreateTime(now);
      cwProjectAck.setUpdateBy(userName);
      cwProjectAck.setUpdateTime(now);
      cwProjectAckMapper.insertCwProjectAck(cwProjectAck);
    
    //插入返费公司与税率表
    List<CwProjectCust> cwProjectCusts = addParojectVo.getCwProjectCusts();
    for (CwProjectCust cwProjectCust : cwProjectCusts) {
      cwProjectCust.setProjectId(id);
      cwProjectCust.setCreateBy(userName);
      cwProjectCust.setCreateTime(now);
      cwProjectCust.setStatus("0");
      cwProjectCust.setUpdateTime(now);
      cwProjectCust.setReplaceFlag("0");
      cwProjectCustMapper.insertCwProjectCust(cwProjectCust);
    }
    //插入角色与项目关联表
    //会计
//    List<Long> accountants = addParojectVo.getAccountants();
//    for (Long accountant : accountants) {
//      CwProjectUser cwProjectUser = new CwProjectUser();
//      cwProjectUser.setProjectId(id);
//      cwProjectUser.setUserId(accountant);
//      cwProjectUser.setUserFlag("0");
//      cwProjectUser.setCreateBy(userName);
//      cwProjectUser.setCreateTime(now);
//      cwProjectUser.setUpdateTime(now);
//      cwProjectUserMapper.insertCwProjectUser(cwProjectUser);
//    }
//    if ("1".equals(addParojectVo.getProjectType())) {
//        //出纳
//        List<Long> cashiers = addParojectVo.getCashiers();
//        for (Long accountant : cashiers) {
//            CwProjectUser cwProjectUser = new CwProjectUser();
//            cwProjectUser.setProjectId(id);
//            cwProjectUser.setUserId(accountant);
//            cwProjectUser.setUserFlag("1");
//            cwProjectUser.setCreateBy(userName);
//            cwProjectUser.setCreateTime(now);
//            cwProjectUser.setUpdateTime(now);
//            cwProjectUserMapper.insertCwProjectUser(cwProjectUser);
//        }
//    }
//    if ("0".equals(addParojectVo.getProjectType()) || "2".equals(addParojectVo.getProjectType()) || "3".equals(addParojectVo.getProjectType())) {
//        //业务
//        List<Long> salesmans = addParojectVo.getSalesmans();
//
//        for (Long accountant : salesmans) {
//            CwProjectUser cwProjectUser = new CwProjectUser();
//            cwProjectUser.setProjectId(id);
//            cwProjectUser.setUserId(accountant);
//            cwProjectUser.setUserFlag("2");
//            cwProjectUser.setCreateBy(userName);
//            cwProjectUser.setCreateTime(now);
//            cwProjectUser.setUpdateTime(now);
//            cwProjectUserMapper.insertCwProjectUser(cwProjectUser);
//        }
//    }
//    //查看人员
//      List<Long> checkUserList = addParojectVo.getCheckUserList();
//      for (Long accountant : checkUserList) {
//          CwProjectUser cwProjectUser = new CwProjectUser();
//          cwProjectUser.setProjectId(id);
//          cwProjectUser.setUserId(accountant);
//          cwProjectUser.setUserFlag("3");
//          cwProjectUser.setCreateBy(userName);
//          cwProjectUser.setCreateTime(now);
//          cwProjectUser.setUpdateTime(now);
//          cwProjectUserMapper.insertCwProjectUser(cwProjectUser);
//      }
//
//      //导出人员
//      List<Long> exporUserList = addParojectVo.getExportUserList();
//      for (Long accountant : exporUserList) {
//          CwProjectUser cwProjectUser = new CwProjectUser();
//          cwProjectUser.setProjectId(id);
//          cwProjectUser.setUserId(accountant);
//          cwProjectUser.setUserFlag("4");
//          cwProjectUser.setCreateBy(userName);
//          cwProjectUser.setCreateTime(now);
//          cwProjectUser.setUpdateTime(now);
//          cwProjectUserMapper.insertCwProjectUser(cwProjectUser);
//      }
//      HashMap<String, Object> returnMap = new HashMap<>();

      return id;
  }

  /**
   * 终止项目
   * @param id
   */
  @Override
  public void closeProject(Long id) {
    //修改项目主表状态
    cwProjectMapper.closeProjectById(id);
    //修改待办通知表状态为禁用 因为终止项目所以相对应的所有待办也要为禁用
    String status = "1";
    topNotifyMapper.updateStatus(id,status);
  }

  /**
   * 修改项目
   * @param addParojectVo
   * @param userName
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public void updateProject(AddParojectVo addParojectVo,String userName){
      //2024.06.26修改项目时，修改项目主表的project_type_relevance_type_id
      Long projectTypeCode = addParojectVo.getProjectTypeCode();

//    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
      CwProject queryCwProject = cwProjectMapper.selectCwProjectById(addParojectVo.getId());
      if ("1".equals(queryCwProject.getPrestoreIncomeFlag()) && "0".equals(addParojectVo.getPrestoreIncomeFlag())) {
          //查询到是有预存的，要改为无预存cw_project_prestore_income
          cwProjectMapper.updateCwprojectPrestoreIncomeByProjectId(queryCwProject.getId());
      }
      Date now = new Date();
      //插入项目主表
    CwProject cwProject = new CwProject();
    cwProject.setId(addParojectVo.getId());
    cwProject.setCustName(addParojectVo.getCustName());
    cwProject.setProjectName(addParojectVo.getProjectName());
    cwProject.setIncomeCustName(addParojectVo.getIncomeCustName());
    cwProject.setUpdateBy(userName);
    cwProject.setUpdateTime(now);
    cwProject.setPrestoreIncomeFlag(addParojectVo.getPrestoreIncomeFlag());
    cwProject.setOaProjectDeployId(addParojectVo.getOaProjectDeployId());
    cwProject.setGenerateCertificateFlag(addParojectVo.getGenerateCertificateFlag());
    cwProject.setProjectTypeRelevanceTypeId(projectTypeCode);
      if ("1".equals(addParojectVo.getGenerateCertificateFlag())) {
          cwProject.setAccountSetsId(addParojectVo.getAccountSetsId());
          cwProject.setGuaranteeIncomeType(addParojectVo.getGuaranteeIncomeType());
          cwProject.setGuarantyPayee(addParojectVo.getGuarantyPayee());
      } else {
          cwProject.setAccountSetsId(null);
          cwProject.setGuaranteeIncomeType(null);
          cwProject.setGuarantyPayee(null);
      }
    cwProjectMapper.updateCwProject1(cwProject);
    Long id  = addParojectVo.getId();
    //插入返费公司与税率表
    List<CwProjectCust> cwProjectCusts = addParojectVo.getCwProjectCusts();
    //删除原来的返费公司
      int i = cwProjectCustMapper.deleteCustByProId(id);

      for (CwProjectCust cwProjectCust : cwProjectCusts) {

          if(!cwProjectCust.getCustName().isEmpty()){
              cwProjectCust.setProjectId(id);
              cwProjectCust.setCreateBy(userName);
              cwProjectCust.setCreateTime(now);
              cwProjectCust.setUpdateBy(userName);
              cwProjectCust.setUpdateTime(now);
              cwProjectCust.setStatus("0");
              cwProjectCustMapper.insertCwProjectCust(cwProjectCust);
          }

//      cwProjectCustMapper.updateProjectCust(cwProjectCust);

    }
    //插入角色与项目关联表
    //会计
//    cwProjectUserMapper.deleteByprojectId(id,"0");
//    List<Long> accountants = addParojectVo.getAccountants();
//    for (Long accountant : accountants) {
//      CwProjectUser cwProjectUser = new CwProjectUser();
//      cwProjectUser.setProjectId(id);
//      cwProjectUser.setUserId(accountant);
//      cwProjectUser.setUserFlag("0");
//      cwProjectUser.setCreateBy(userName);
//      cwProjectUser.setCreateTime(now);
//      cwProjectUser.setUpdateTime(now);
//      cwProjectUserMapper.insertCwProjectUser(cwProjectUser);
//    }
//    //出纳
//    cwProjectUserMapper.deleteByprojectId(id,"1");
//    List<Long> cashiers = addParojectVo.getCashiers();
//    for (Long accountant : cashiers) {
//      CwProjectUser cwProjectUser = new CwProjectUser();
//      cwProjectUser.setProjectId(id);
//      cwProjectUser.setUserId(accountant);
//      cwProjectUser.setUserFlag("1");
//      cwProjectUser.setCreateBy(userName);
//      cwProjectUser.setCreateTime(now);
//      cwProjectUser.setUpdateTime(now);
//      cwProjectUserMapper.insertCwProjectUser(cwProjectUser);
//    }
//    //业务
//    List<Long> salesmans = addParojectVo.getSalesmans();
//    cwProjectUserMapper.deleteByprojectId(id,"2");
//    for (Long accountant : salesmans) {
//      CwProjectUser cwProjectUser = new CwProjectUser();
//      cwProjectUser.setProjectId(id);
//      cwProjectUser.setUserId(accountant);
//      cwProjectUser.setUserFlag("2");
//      cwProjectUser.setCreateBy(userName);
//      cwProjectUser.setCreateTime(now);
//      cwProjectUser.setUpdateTime(now);
//      cwProjectUserMapper.insertCwProjectUser(cwProjectUser);
//    }
//
//      //查看人员
//      cwProjectUserMapper.deleteByprojectId(id,"3");
//      List<Long> checkUserList = addParojectVo.getCheckUserList();
//      for (Long accountant : checkUserList) {
//          CwProjectUser cwProjectUser = new CwProjectUser();
//          cwProjectUser.setProjectId(id);
//          cwProjectUser.setUserId(accountant);
//          cwProjectUser.setUserFlag("3");
//          cwProjectUser.setCreateBy(userName);
//          cwProjectUser.setCreateTime(now);
//          cwProjectUser.setUpdateTime(now);
//          cwProjectUserMapper.insertCwProjectUser(cwProjectUser);
//      }
//
//      //导出人员
//      cwProjectUserMapper.deleteByprojectId(id,"4");
//      List<Long> exporUserList = addParojectVo.getExportUserList();
//      for (Long accountant : exporUserList) {
//          CwProjectUser cwProjectUser = new CwProjectUser();
//          cwProjectUser.setProjectId(id);
//          cwProjectUser.setUserId(accountant);
//          cwProjectUser.setUserFlag("4");
//          cwProjectUser.setCreateBy(userName);
//          cwProjectUser.setCreateTime(now);
//          cwProjectUser.setUpdateTime(now);
//          cwProjectUserMapper.insertCwProjectUser(cwProjectUser);
//      }
  }

  @Override
  public Map getUserListName(CwProject cwProject) {
      CwProject cwProject1 = cwProjectMapper.selectCwProjectById(cwProject.getId());
      Long oaProjectDeployId = cwProject1.getOaProjectDeployId();
      List<AuthorizedFeatureDetailDTO> userList = newAuthorityService.getProjectRoleByOaProjectDeployIdAndModuleType(oaProjectDeployId, AuthModuleEnum.FINANCEPROJ.getCode());
      //会计
      List<String> accountantList = userList.stream().filter(t -> "1".equals(t.getFeatureUserFlag())).map(t -> t.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.toList());
      //出纳
      List<String> cashierList = userList.stream().filter(t -> "2".equals(t.getFeatureUserFlag())).map(t -> t.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.toList());
      //业务
      List<String> businessList = userList.stream().filter(t -> "3".equals(t.getFeatureUserFlag())).map(t -> t.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.toList());
    //查询所有出纳+会计
    ArrayList<String> cnAndKuaiji = new ArrayList<>();
    cnAndKuaiji.addAll(cashierList);
    cnAndKuaiji.addAll(accountantList);
    HashMap<String, Object> returnMap = new HashMap<>();
    returnMap.put("chunaList",cashierList);
    returnMap.put("kuaijiList",accountantList);
    returnMap.put("yewuList",businessList);
    returnMap.put("cnandkjList",cnAndKuaiji);
    return returnMap;
  }

    /**
     * 查询返费公司名称集合
     *
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    @Override
    public List<Map<String, Object>> getFeeCustLixt() {
        List<Map<String, Object>> returnList =  cwProjectFeeMapper.selectFeeList();
        return returnList;
    }

    @Override
    public List<SysUser> listAllUser() {
        return sysUserMapper.excludeAdmin();
    }

    /**
     * 获取权限控制后的下拉框数据
     *
     * @param loginUser 登录用户
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    @Override
    public Map<String, Object> getRoleSelectList(LoginUser loginUser) {
        HashMap<String, Object> returnMap = new HashMap<>();
        //2024.09.25获取当前用户能看到所有项目id
        List<Long> oaProjectDeployIdList = newAuthorityService.getNewAuthorityForModuleTypeByUserIdAndModuleType(loginUser.getUserId(), AuthModuleEnum.FINANCEPROJ.getCode());
        List<Long> projectId = cwProjectMapper.selectCwProjectListByOaProjectDeployIdList(oaProjectDeployIdList).stream().map(CwProject::getId).collect(Collectors.toList());
//        List<Long> projectId = this.getProjectId(loginUser,"0");
        List<Map<String, Object>> custNameXiaLa = new ArrayList<>();
        List<Map<String, Object>> incomeCustNameXiaLa = new ArrayList<>();
        if(null == projectId){
            custNameXiaLa = this.getCustNameXiaLa();
             incomeCustNameXiaLa = this.getIncomeCustNameXiaLa();
        } else if( projectId.size() > 0){
            custNameXiaLa = cwProjectMapper.getCustByProjectId(projectId);
            incomeCustNameXiaLa =  cwProjectMapper.getincomeCustByProjectId(projectId);
//             custNameXiaLa = this.getCustNameXiaLa();
//             incomeCustNameXiaLa = this.getIncomeCustNameXiaLa();
        }

        returnMap.put("custRoleName",custNameXiaLa);
        returnMap.put("incomeCustRoleName",incomeCustNameXiaLa);
        return returnMap;
    }

    /**
     * 批处理excel 压缩
     *
     * @param loginUser 登录用户
     * @return {@link String}
     * @throws IOException ioexception
     */
    @Override
    public String batchExcelZip(LoginUser loginUser) throws IOException {

        long ltime = System.currentTimeMillis();
        String fileZipName = "财务项目管理_"+loginUser.getUsername()+"_"+ltime+".zip";
        String absoluteFile = ExcelUtil.getAbsoluteFile(fileZipName);
        List<Map<String,Object>> projectIds = new ArrayList<>();
//            projectIds = this.getProjectId(loginUser, "0");
        //查询当前角色具有导出权限或者当前登录人参与过的数据id
        projectIds = this.getExportProjectId(loginUser,"0");
        List<String> fileNameList = new ArrayList<>();
        if(projectIds.size()>0){
            try {
//                if(projectIds.size()==0) {
//                    projectIds =  cwProjectMapper.getProjectId();
//                    fileNameList =  this.generateExportExcel(projectIds);
//                }else {
//
//                }
                fileNameList = this.generateExportExcel(projectIds);
                //压缩文件
                ZipUtil.zipFile(absoluteFile,fileNameList,"");
                //清除生成文件
                for (String s : fileNameList) {
                    FileUtils.deleteFile(s);
                }
            }finally {
                //清除生成文件
                for (String s : fileNameList) {
                    FileUtils.deleteFile(s);
                }
            }

        }else {
            fileZipName = "N";
        }

        return fileZipName;


    }

    @Override
    public Map<String, Object> checkLoginRole(LoginUser loginUser,CwProject cwProject) {

        HashMap<String, Object> returnMap = new HashMap<>();
        List<SysRole> roles = loginUser.getUser().getRoles();
        boolean b = false;
        //先判断登录人角色是否可以导出
        for (SysRole role : roles) {
            if(role.getRoleKey().equals("admin")||role.getRoleKey().equals("caiwuAdmin")||role.getRoleKey().equals("yewu")||role.getRoleKey().equals("kuaiji")||role.getRoleKey().equals("yewuAdmin")){
                b = true;
            }
        }
        //如果不在上述角色中则继续判断
        if(!b){
            //再通过当前登录人id和项目id以及导出权限的编号判断是否具有导出
            List<Long> userid =   cwProjectUserMapper.checkExport(cwProject.getId(),loginUser.getUserId(),"4");
            if(userid.size()>0){
                b = true;
            }else {
                b = false;
            }

        }


        returnMap.put("isOk",b);
        return returnMap;
    }

    @Override
    public Map<String, Object> checkExportProNum(LoginUser loginUser) {
        HashMap<String, Object> returnMap = new HashMap<>();
        List<SysRole> roles = loginUser.getUser().getRoles();
        boolean b = false;
        //先判断登录人角色是否可以导出
        for (SysRole role : roles) {
            if(role.getRoleKey().equals("admin")||role.getRoleKey().equals("caiwuAdmin")||role.getRoleKey().equals("chuna")||role.getRoleKey().equals("kuaiji")){
                b = true;
            }
        }
        //如果不在上述角色中则继续判断
        if(!b){
            //再通过当前登录人id和项目id以及导出权限的编号判断是否具有导出
            List<Long> userid =   cwProjectUserMapper.checkExportRoleNum(loginUser.getUserId(),"4");
            if(userid.size()>0){
                b = true;
            }else {
                b = false;
            }

        }

        returnMap.put("isOk",b);
        return returnMap;
    }

    @Override
    public List<Map<String, Object>> getServiceProvider() {
        List<Map<String, Object>> serviceProvider = cwProjectShowMapper.getServiceProvider();
        if (serviceProvider.size() != 0) {
            if (serviceProvider.get(0) == null) {
                serviceProvider.remove(0);
            }
        }
        return serviceProvider;
    }

    @Override
    public List<Map<String, Object>> getServiceProviderSecond() {
        List<Map<String, Object>> serviceProviderSecond = cwProjectShowMapper.getServiceProviderSecond();
        if (serviceProviderSecond.size() != 0) {
            if (serviceProviderSecond.get(0) == null) {
                serviceProviderSecond.remove(0);
            }
        }
        return serviceProviderSecond;
    }

    @Override
    public List<Map<String, Object>> getCustFeeListForLaw() {
        List<Map<String, Object>> custFeeListForLaw = cwProjectShowMapper.getCustFeeListForLaw();
        if (custFeeListForLaw.size() != 0) {
            if (custFeeListForLaw.get(0) == null) {
                custFeeListForLaw.remove(0);
            }
        }
        return custFeeListForLaw;
    }

    @Override
    public List<Map<String, Object>> getCustNameXiaLaDanBao() {
        return cwProjectShowMapper.getCustNameXiaLaDanBao();
    }

    @Override
    public String batchExcelZipNew(LoginUser loginUser) throws IOException {
        long ltime = System.currentTimeMillis();
        String fileZipName = "财务项目管理_"+loginUser.getUsername()+"_"+ltime+".zip";
        String absoluteFile = ExcelUtil.getAbsoluteFile(fileZipName);
        List<Map<String,Object>> projectIds = new ArrayList<>();
        //查询当前用户能看到的项目
        List<Long> oaProjectDeployIdList = newAuthorityService.getNewAuthorityForModuleTypeByUserIdAndModuleType(loginUser.getUserId(), AuthModuleEnum.FINANCEPROJ.getCode());
        //通过查到的项目找项目与项目类型
        if (oaProjectDeployIdList.size() > 0) {
            //projectType 对应关系 0 2 3 llyw ---> 非法催业务     1 ---> 法催业务
            projectIds = cwProjectMapper.selectCwProjectIdAndProjectTypeByOaProjectDeployIdListAndCwProjectFlag(oaProjectDeployIdList, "0").stream().filter(t -> t.get("projectType") != null).collect(Collectors.toList());
        }
        List<String> fileNameList = new ArrayList<>();
        if(projectIds.size() > 0){
            try {
                fileNameList = generateExportExcelNew(projectIds);
                //压缩文件
                ZipUtil.zipFile(absoluteFile, fileNameList, "");
                //清除生成文件
                for (String s : fileNameList) {
                    FileUtils.deleteFile(s);
                }
            } finally {
                //清除生成文件
                for (String s : fileNameList) {
                    FileUtils.deleteFile(s);
                }
            }

        } else {
            fileZipName = "N";
        }
        return fileZipName;
    }


    /**
     * 生成导出excel
     * 传进项目id
     * @param projectIds 项目id
     * @return {@link List}<{@link String}>
     * @throws IOException ioexception
     */
    public List<String> generateExportExcel(List<Map<String,Object>> projectIds) throws IOException {
        ArrayList<String> fileNameList = new ArrayList<>();

        for (Map<String, Object> map : projectIds) {

            Long projectId = Long.parseLong(map.get("id").toString()) ;
            String projectType = map.get("projectType").toString();
            if("0".equals(projectType) || "2".equals(projectType)) {
                //新导出方法
                ExcelCwProject excelCwProject =exportCwProjectMapper.selectExportCwProjectInfo(projectId);
                excelCwProject.setCustList(exportCwProjectMapper.selectExportCwProjectCustList(projectId));
                excelCwProject.setIncomeList(exportCwProjectMapper.selectExportCwProjectIncomeList(projectId));
                for (int i = 0; i < excelCwProject.getIncomeList().size(); i++) {
                    //找返费，因为返费与返费成员与费率表有联系
                    List<ExcelCwProjectFee> excelCwProjectFees = exportCwProjectMapper.selectExportCwProjectFeeList(projectId, excelCwProject.getIncomeList().get(i).getId());
                    //如果有返费，那么进行返费方案的统计   PS：财务项目管理四期只要有收入，肯定就有返费，所以以下情况只是为了保证现有项目不报错的问题
                    if (excelCwProjectFees.size() != 0) {
                        String schemeFlag = excelCwProjectFees.get(0).getSchemeFlag();
                        if (schemeFlag == null) {
                            schemeFlag = excelCwProjectFees.get(0).getCustRemark();
                        }
                        String finalSchemeFlag = schemeFlag;
                        List<ExcelCwProjectCust> collect = excelCwProject.getCustList().stream().filter(t -> finalSchemeFlag.equals(t.getSchemeFlag())).collect(Collectors.toList());
                        for (ExcelCwProjectCust excelCwProjectCust:collect) {
                            int schemeFlagUseSituation = excelCwProjectCust.getSchemeFlagUseSituation();
                            schemeFlagUseSituation++;
                            excelCwProjectCust.setSchemeFlagUseSituation(schemeFlagUseSituation);
                        }
                    }
                    excelCwProject.getIncomeList().get(i).setFeeList(excelCwProjectFees);
                    for (int j = 0; j < excelCwProject.getIncomeList().get(i).getFeeList().size(); j++) {
                        excelCwProject.getIncomeList().get(i).getFeeList().get(j).setPayList(exportCwProjectMapper.selectExportCwProjectPayList(projectId,excelCwProject.getIncomeList().get(i).getId(),excelCwProject.getIncomeList().get(i).getFeeList().get(j).getId()));
                    }
                }
                //财务项目管理四期，查询本项目所有角色和用户姓名
                List<ExcelCwProjectMember> excelCwProjectMemberList = exportCwProjectMapper.selectExportCwProjectMemberInfoByProjectId(projectId);
                List<ExcelCwProjectMember> projectView = excelCwProjectMemberList.stream().filter(t -> "查看权限".equals(t.getRole())).collect(Collectors.toList());
                List<ExcelCwProjectMember> projectExprot = excelCwProjectMemberList.stream().filter(t -> "导出权限".equals(t.getRole())).collect(Collectors.toList());
                if (projectView.size() == 0) {
                    ExcelCwProjectMember excelCwProjectMember = new ExcelCwProjectMember();
                    excelCwProjectMember.setRole("查看权限");
                    excelCwProjectMember.setNickName(StringUtils.EMPTY);
                    excelCwProjectMemberList.add(excelCwProjectMember);
                }
                if (projectExprot.size() == 0) {
                    ExcelCwProjectMember excelCwProjectMember = new ExcelCwProjectMember();
                    excelCwProjectMember.setRole("导出权限");
                    excelCwProjectMember.setNickName(StringUtils.EMPTY);
                    excelCwProjectMemberList.add(excelCwProjectMember);
                }
                excelCwProject.setMemberList(excelCwProjectMemberList);
                //财务项目管理四期，如果有预存收入，那么就给一个预存收入的标识
                if ("1".equals(excelCwProject.getPrestoreIncomeFlag())) {
                    Map<String, Object> map1 = cwProjectCustService.selectPrestoreIncomeListByProjectId(projectId);
                    List<CwProjectPrestoreIncome> prestoreIncomeList = (List<CwProjectPrestoreIncome>) map1.get("info");
                    excelCwProject.setPrestoreIncomeList(prestoreIncomeList);
                }
                String fileName = BatchExportExcel.exportCwProject(excelCwProject);
                fileNameList.add(fileName);
            }else {


                CwProject cwProject = new CwProject();
                cwProject.setId(projectId);
                Map<String, Object> data = cwProjectCustService.selectCwProjectLawDetileByProjectId(cwProject, 1);
                //新导出方法
                ExcelLawCwProject excelCwProject =exportCwProjectMapper.selectExportLawCwProjectInfo(projectId);
                excelCwProject.setCustList(exportCwProjectMapper.selectExportCwProjectCustList(projectId));
                List<CwProjectLawDetailView> detailList = (List<CwProjectLawDetailView>) data.get("detail_list_v2");
                List<Map<String, Object>> peopleList = (List<Map<String, Object>>) data.get("people_list");
                List<CwProjectLawDetailView> collect = detailList.stream().filter(t -> !"本期次合计".equals(t.getServiceProvider())).collect(Collectors.toList());
                collect.forEach(t -> {
                    if (t.getPhaseId() == null) {
                        t.setPhaseId(t.getProjectIncomeId());
                    }
                });
                excelCwProject.setIncomeList(collect);
                excelCwProject.setMemberList(peopleList);
                String s = BeachExportLawCwProject.exportLawCwProject(excelCwProject);
                fileNameList.add(s);
            }

        }
        return fileNameList;
    }

    /**
     * 生成导出excel 新
     * 传进项目id
     * @param projectIds 项目id 和 项目做哪些导出类型判断
     * projectType 对应关系 0 2 3 llyw ---> 非法催业务     1 ---> 法催业务
     */
    public List<String> generateExportExcelNew(List<Map<String,Object>> projectIds) throws IOException {
        ArrayList<String> fileNameList = new ArrayList<>();
        for (Map<String, Object> map : projectIds) {
            Long projectId = Long.parseLong(map.get("id").toString()) ;
            String projectType = map.get("projectType").toString();
            if ("1".equals(projectType)){
                CwProject cwProject = new CwProject();
                cwProject.setId(projectId);
                Map<String, Object> data = cwProjectCustService.selectCwProjectLawDetileByProjectId(cwProject, 1);
                //新导出方法
                Map<String, Object> map1 = cwProjectCustService.selectCwprojectDetailThree(cwProject);
                log.info("111当前财务项目id为：{}，项目名称为：{}", map1.get("id").toString(), map1.get("projectName").toString());
                ExcelLawCwProject excelCwProject = exportCwProjectMapper.selectExportLawCwProjectInfo(projectId);
                excelCwProject.setProjectName(map1.get("projectName").toString());
                excelCwProject.setCustName(map1.get("custName").toString());
                excelCwProject.setIncomeCustName(map1.get("incomeCustName").toString());
//                excelCwProject.setCustList(exportCwProjectMapper.selectExportCwProjectCustList(projectId));
                List<CwProjectOverDetailCompanyDto> feeList = (List<CwProjectOverDetailCompanyDto>) data.get("fee_list");
                List<ExcelCwProjectCust> excelCwProjectCusts = new ArrayList<>();
                for (CwProjectOverDetailCompanyDto cwProjectOverDetailCompanyDto : feeList) {
                    Long id = cwProjectOverDetailCompanyDto.getId();
                    String custName = cwProjectOverDetailCompanyDto.getCustName();
                    String rate = cwProjectOverDetailCompanyDto.getRate();
                    String taxRate = cwProjectOverDetailCompanyDto.getTaxRate();
                    String schemeFlag = cwProjectOverDetailCompanyDto.getSchemeFlag();
                    int schemeFlagUseSituation = cwProjectOverDetailCompanyDto.getSchemeFlagUseSituation();
                    ExcelCwProjectCust excelCwProjectCust = new ExcelCwProjectCust();
                    excelCwProjectCust.setId(id);
                    excelCwProjectCust.setProjectId(projectId);
                    excelCwProjectCust.setCustName(custName);
                    excelCwProjectCust.setRateStr(rate);
                    excelCwProjectCust.setTaxRateStr(taxRate);
                    excelCwProjectCust.setSchemeFlag(schemeFlag);
                    excelCwProjectCust.setSchemeFlagUseSituation(schemeFlagUseSituation);
                    excelCwProjectCusts.add(excelCwProjectCust);
                }
                excelCwProject.setCustList(excelCwProjectCusts);
                log.info("111要处理的直接导出Excel对象属性：{}", excelCwProject);
                List<CwProjectLawDetailView> detailList = (List<CwProjectLawDetailView>) data.get("detail_list_v2");
                List<Map<String, Object>> peopleList = (List<Map<String, Object>>) data.get("people_list");
                List<CwProjectLawDetailView> collect = detailList.stream().filter(t -> !"本期次合计".equals(t.getServiceProvider())).collect(Collectors.toList());
                collect.forEach(t -> {
                    if (t.getPhaseId() == null) {
                        t.setPhaseId(t.getProjectIncomeId());
                    }
                });
                excelCwProject.setIncomeList(collect);
                excelCwProject.setMemberList(peopleList);
                log.info("222要处理的直接导出Excel对象属性：{}", excelCwProject);
                String s = BeachExportLawCwProject.exportLawCwProject(excelCwProject);
                fileNameList.add(s);
            } else {
                CwProject cwProject = new CwProject();
                cwProject.setId(projectId);
                Map<String, Object> map2 = cwProjectCustService.selectCwprojectDetailThree(cwProject);
                log.info("当前财务项目id为：{}，项目名称为：{}", map2.get("id").toString(), map2.get("projectName").toString());
                //新导出方法
                ExcelCwProject excelCwProject = exportCwProjectMapper.selectExportCwProjectInfo(projectId);
                excelCwProject.setProjectName(map2.get("projectName").toString());
                excelCwProject.setCustName(map2.get("custName").toString());
                excelCwProject.setIncomeCustName(map2.get("incomeCustName").toString());
                Map<String, Object> data = cwProjectCustService.selectCwProjectOverListDetileByProjectId(cwProject, 1);
                List<CwProjectOverDetailCompanyDto> feeList = (List<CwProjectOverDetailCompanyDto>) data.get("fee_list");
                excelCwProject.setCustList(exportCwProjectMapper.selectExportCwProjectCustList(projectId));
                for (ExcelCwProjectCust ecc:excelCwProject.getCustList()) {
                    Long id = ecc.getId();
                    String s = feeList.stream().filter(t -> t.getId().equals(id)).findFirst().map(CwProjectOverDetailCompanyDto::getCustName).orElse("数据错误，没有找到对应的返费公司");
                    ecc.setCustName(s);
                }
                excelCwProject.setIncomeList(exportCwProjectMapper.selectExportCwProjectIncomeList(projectId));
                log.info("要处理的直接导出Excel对象属性：{}", excelCwProject);
                for (int i = 0; i < excelCwProject.getIncomeList().size(); i++) {
                    //找返费，因为返费与返费成员与费率表有联系
                    List<ExcelCwProjectFee> excelCwProjectFees = exportCwProjectMapper.selectExportCwProjectFeeList(projectId, excelCwProject.getIncomeList().get(i).getId());
                    log.info("查找的返费返费成员相关表：{}", excelCwProjectFees);
                    for (ExcelCwProjectFee ecf:excelCwProjectFees) {
                        Long custId = ecf.getCustId();
                        String s = feeList.stream().filter(t -> t.getId().equals(custId)).findFirst().map(CwProjectOverDetailCompanyDto::getCustName).orElse("数据错误，没有找到对应的返费公司");
                        if (custId == -999L) {
                            s = "暂不确定公司";
                        }
                        ecf.setFeeCustName(s);
                    }
                    //如果有返费，那么进行返费方案的统计   PS：财务项目管理四期只要有收入，肯定就有返费，所以以下情况只是为了保证现有项目不报错的问题
                    if (excelCwProjectFees.size() != 0) {
                        String schemeFlag = excelCwProjectFees.get(0).getSchemeFlag();
                        if (schemeFlag == null) {
                            schemeFlag = excelCwProjectFees.get(0).getCustRemark();
                        }
                        String finalSchemeFlag = schemeFlag;
                        log.info("当前的方案标识为：{}", finalSchemeFlag);
                        List<ExcelCwProjectCust> collect = excelCwProject.getCustList().stream().filter(t -> finalSchemeFlag.equals(t.getSchemeFlag())).collect(Collectors.toList());
                        for (ExcelCwProjectCust excelCwProjectCust:collect) {
                            int schemeFlagUseSituation = excelCwProjectCust.getSchemeFlagUseSituation();
                            schemeFlagUseSituation++;
                            excelCwProjectCust.setSchemeFlagUseSituation(schemeFlagUseSituation);
                        }
                    }
                    excelCwProject.getIncomeList().get(i).setFeeList(excelCwProjectFees);
                }
                //角色相关
                List<ExcelCwProjectMember> excelCwProjectMemberList = new ArrayList<>();
                Long oaProjectDeployId = (Long) data.get("oaProjectDeployId");
                List<AuthorizedFeatureDetailDTO> userList = newAuthorityService.getProjectRoleByOaProjectDeployIdAndModuleType(oaProjectDeployId, AuthModuleEnum.FINANCEPROJ.getCode());
                //会计
                List<Map<String, Object>> accountantList = userList.stream().filter(a -> "1".equals(a.getFeatureUserFlag())).map(a -> a.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().collect(Collectors.toList());
                if (accountantList.size() == 0) {
                    ExcelCwProjectMember excelCwProjectMember = new ExcelCwProjectMember();
                    excelCwProjectMember.setRole("会计");
                    excelCwProjectMember.setNickName(StringUtils.EMPTY);
                    excelCwProjectMemberList.add(excelCwProjectMember);
                } else {
                    String authorizedUserNickName = accountantList.stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.joining(","));
                    ExcelCwProjectMember excelCwProjectMember = new ExcelCwProjectMember();
                    excelCwProjectMember.setRole("会计");
                    excelCwProjectMember.setNickName(authorizedUserNickName);
                    excelCwProjectMemberList.add(excelCwProjectMember);
                }
                //业务
                List<Map<String, Object>> businessList = userList.stream().filter(a -> "3".equals(a.getFeatureUserFlag())).map(a -> a.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().collect(Collectors.toList());
                if (businessList.size() == 0) {
                    ExcelCwProjectMember excelCwProjectMember = new ExcelCwProjectMember();
                    excelCwProjectMember.setRole("业务");
                    excelCwProjectMember.setNickName(StringUtils.EMPTY);
                    excelCwProjectMemberList.add(excelCwProjectMember);
                } else {
                    String authorizedUserNickName = businessList.stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.joining(","));
                    ExcelCwProjectMember excelCwProjectMember = new ExcelCwProjectMember();
                    excelCwProjectMember.setRole("业务");
                    excelCwProjectMember.setNickName(authorizedUserNickName);
                    excelCwProjectMemberList.add(excelCwProjectMember);
                }
                //查看权限
                List<Map<String, Object>> selectList = userList.stream().filter(a -> "88".equals(a.getFeatureUserFlag())).map(a -> a.getFeatureUserList()).findFirst().orElse(new ArrayList<>()).stream().collect(Collectors.toList());
                if (selectList.size() == 0) {
                    ExcelCwProjectMember excelCwProjectMember = new ExcelCwProjectMember();
                    excelCwProjectMember.setRole("查看权限");
                    excelCwProjectMember.setNickName(StringUtils.EMPTY);
                    excelCwProjectMemberList.add(excelCwProjectMember);
                } else {
                    String authorizedUserNickName = selectList.stream().map(b -> b.get("authorizedUserNickName").toString()).collect(Collectors.joining(","));
                    ExcelCwProjectMember excelCwProjectMember = new ExcelCwProjectMember();
                    excelCwProjectMember.setRole("查看权限");
                    excelCwProjectMember.setNickName(authorizedUserNickName);
                    excelCwProjectMemberList.add(excelCwProjectMember);
                }
                excelCwProject.setMemberList(excelCwProjectMemberList);


                String fileName = BatchExportExcel.exportCwProject(excelCwProject);
                fileNameList.add(fileName);
            }
        }
        return fileNameList;
    }


    /**
     * 得到当前登录人所有的项目id
     *
     * @param loginUser 登录用户
     * @return {@link List}<{@link Long}>
     */
    public List<Long> getProjectId(LoginUser loginUser,String projectFlag){
        List<SysRole> roles = loginUser.getUser().getRoles();
        List<Long> projectIds = new ArrayList<>();

        boolean b = false;
        for (SysRole role : roles) {
            if(role.getRoleKey().equals("admin")||role.getRoleKey().equals("caiwuAdmin")||role.getRoleKey().equals("renshi")||role.getRoleKey().equals("yewuAdmin")){
                b = true;
            }
        }
        if(b){
            projectIds = null;
            return projectIds;
        }else {
            //获取userid通过当前登录人的id获取可以查看的项目id
            Long userId = loginUser.getUserId();
            projectIds  =   cwProjectUserMapper.getProjectIdByUserId(userId,projectFlag);
            return projectIds;
        }
    }

    /**
     * 获取当前用户参与过或有导出权限的项目id
     *
     * @param loginUser   登录用户
     * @param projectFlag 项目标志
     * @return {@link List}<{@link Long}>
     */
    public List<Map<String, Object>> getExportProjectId(LoginUser loginUser,String projectFlag){
        List<SysRole> roles = loginUser.getUser().getRoles();
        List<Map<String, Object>> projectIds = new ArrayList<>();

        boolean b = false;
        for (SysRole role : roles) {
            if(role.getRoleKey().equals("admin")||role.getRoleKey().equals("caiwuAdmin")||role.getRoleKey().equals("renshi")||role.getRoleKey().equals("yewuAdmin")){
                b = true;
            }
        }
        //如果是超级管理员 业务管理员 财务管理员或者人事就导出所有
        if(b){
            projectIds =  cwProjectMapper.getProjectId();
            return projectIds;
        }else {
            //获取userid   通过当前登录人的id获取参与和可以导出的项目id  接口参数分别为 用户id 项目状态 因为要查询除了查看权限外所有项目 所以直接！=3 就行最后一个参数可以不用传
            Long userId = loginUser.getUserId();
            projectIds  =   cwProjectUserMapper.getExportProjectIdByUserId(userId,projectFlag,"4");
            return projectIds;
        }
    }

    /**
     * 查询权限相关方法，获取到当前用户在财务项目管理当中可以看到的项目
     */
    public List<Map<String, Object>> getExportProjectIdNew(LoginUser loginUser, String projectFlag){
        List<SysRole> roles = loginUser.getUser().getRoles();
        List<Map<String, Object>> projectIds = new ArrayList<>();
        boolean b = false;
        for (SysRole role : roles) {
            if(role.getRoleKey().equals("admin")||role.getRoleKey().equals("caiwuAdmin")||role.getRoleKey().equals("renshi")||role.getRoleKey().equals("yewuAdmin")){
                b = true;
            }
        }
        //如果是超级管理员 业务管理员 财务管理员或者人事就导出所有
        if(b){
            projectIds =  cwProjectMapper.getProjectId();
            return projectIds;
        }else {
            //获取userid   通过当前登录人的id获取参与和可以导出的项目id  接口参数分别为 用户id 项目状态 因为要查询除了查看权限外所有项目 所以直接！=3 就行最后一个参数可以不用传
            Long userId = loginUser.getUserId();
            projectIds  =   cwProjectUserMapper.getExportProjectIdByUserId(userId,projectFlag,"4");
            return projectIds;
        }
    }
}
