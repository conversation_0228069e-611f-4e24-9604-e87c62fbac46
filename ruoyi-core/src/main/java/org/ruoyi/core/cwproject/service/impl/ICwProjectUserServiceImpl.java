package org.ruoyi.core.cwproject.service.impl;

import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.cwproject.domain.CwProjectUser;
import org.ruoyi.core.cwproject.mapper.CwProjectUserMapper;
import org.ruoyi.core.cwproject.service.ICwProjectUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 财务项目管理-成员Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-11-09
 */
@Service
public class ICwProjectUserServiceImpl implements ICwProjectUserService
{
    @Autowired
    private CwProjectUserMapper cwProjectUserMapper;

    /**
     * 查询财务项目管理-成员
     * 
     * @param id 财务项目管理-成员主键
     * @return 财务项目管理-成员
     */
    @Override
    public CwProjectUser selectCwProjectUserById(Long id)
    {
        return cwProjectUserMapper.selectCwProjectUserById(id);
    }

    /**
     * 查询财务项目管理-成员列表
     * 
     * @param cwProjectUser 财务项目管理-成员
     * @return 财务项目管理-成员
     */
    @Override
    public List<CwProjectUser> selectCwProjectUserList(CwProjectUser cwProjectUser)
    {
        return cwProjectUserMapper.selectCwProjectUserList(cwProjectUser);
    }

    /**
     * 新增财务项目管理-成员
     * 
     * @param cwProjectUser 财务项目管理-成员
     * @return 结果
     */
    @Override
    public int insertCwProjectUser(CwProjectUser cwProjectUser)
    {
        cwProjectUser.setCreateTime(DateUtils.getNowDate());
        return cwProjectUserMapper.insertCwProjectUser(cwProjectUser);
    }

    /**
     * 修改财务项目管理-成员
     * 
     * @param cwProjectUser 财务项目管理-成员
     * @return 结果
     */
    @Override
    public int updateCwProjectUser(CwProjectUser cwProjectUser)
    {
        cwProjectUser.setUpdateTime(DateUtils.getNowDate());
        return cwProjectUserMapper.updateCwProjectUser(cwProjectUser);
    }

    /**
     * 批量删除财务项目管理-成员
     * 
     * @param ids 需要删除的财务项目管理-成员主键
     * @return 结果
     */
    @Override
    public int deleteCwProjectUserByIds(Long[] ids)
    {
        return cwProjectUserMapper.deleteCwProjectUserByIds(ids);
    }

    /**
     * 删除财务项目管理-成员信息
     * 
     * @param id 财务项目管理-成员主键
     * @return 结果
     */
    @Override
    public int deleteCwProjectUserById(Long id)
    {
        return cwProjectUserMapper.deleteCwProjectUserById(id);
    }

    @Override
    public List<CwProjectUser> selectUserByProjectId(Long projectId) {
        List<CwProjectUser> cwProjectUsers = cwProjectUserMapper.selectUserByProjectId(projectId);
        return cwProjectUsers;
    }
}
