package org.ruoyi.core.cwproject.domain.dto;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 财务项目管理-完结项目归档查询详情-角色业务对象Fee CwProjectUserAndRoleDto
 *
 * <AUTHOR>
 * @date 2022-11-22
 */
@Getter
@Setter
@ToString
public class CwProjectUserAndRoleDto extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /** 角色 */
    @Excel(name = "角色")
    private String role;

    /** 姓名 */
    @Excel(name = "姓名")
    private String names;
}
