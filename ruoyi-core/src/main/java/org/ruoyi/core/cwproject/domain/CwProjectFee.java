package org.ruoyi.core.cwproject.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 财务项目管理-返费对象 cw_project_fee
 * 
 * <AUTHOR>
 * @date 2022-11-10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class CwProjectFee extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 项目管理表主键 */
    @Excel(name = "项目管理表主键")
    private Long projectId;

    @Excel(name = "项目管理表主键")
    private Long custId;

    /** 项目收入表主键 */
    @Excel(name = "项目收入表主键")
    private Long projectIncomeId;

    /** 出返费公司 */
    @Excel(name = "出返费公司")
    private String custName;

    /** 返费公司 */
    @Excel(name = "返费公司")
    private String feeCustName;

    /** 计算方式，0自动 1手动 */
    @Excel(name = "计算方式，0自动 1手动")
    private String calculateType;

    /** 返费金额 元 */
    @Excel(name = "返费金额 元")
    private BigDecimal feeAmt;

    /** 提成返费金额 元 */
    @Excel(name = "提成返费金额 元")
    private BigDecimal feeAmt2;

    /** 状态，0正常 1禁用 */
    @Excel(name = "状态，0正常 1禁用")
    private String status;

    /** 录入状态，0未录入 1已录入 2已确认 */
    @Excel(name = "录入状态，0未录入 1已录入 2已确认")
    private String feeFlag;

    /** 打款信息对象 */
    @Excel(name = "打款信息对象")
    private CwProjectPay cwProjectPay;

    private BigDecimal feeRound;

    private String suspendFlag;

    //该返费被哪个返费清除
    private Long suspendClearId;

    /** 联表的返费公司 */
    private String feeCustName1;

    /** 返费公司所属的方案 */
    private String schemeFlag;

    /** 费率 */
    private BigDecimal rate;

    /** 税率 */
    private BigDecimal taxRate;

    private BigDecimal shouldPayFeeAmt;

    private BigDecimal actuallyPayFeeAmt;

    private String custRemark;

    private BigDecimal currentFee;

    private BigDecimal jtfrAmt;

    private BigDecimal lawProfit;

    //替换标识（判断用）
    private String replaceFlag;

    //显示用返费公司id
    private Long revealFeeCompanyId;
}
