package org.ruoyi.core.cwproject.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 财务项目管理四期 - 编辑项目页面 - 普通项目 - 返费公司与费率 - 方案对象
 *
 * <AUTHOR>
 * @date 2023-03-21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class SchemeVo {
    private static final long serialVersionUID = 1L;

    private int useSituation;

    private boolean checkRepeatFlag;

    private List<CwProjectCustVo> cwProjectCusts;
}
