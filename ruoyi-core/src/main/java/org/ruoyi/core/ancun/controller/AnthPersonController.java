package org.ruoyi.core.ancun.controller;


import com.ancun.netsign.model.ApiRespBody;
import com.ancun.netsign.model.AuthInput;
import org.ruoyi.core.ancun.service.IAuthPersonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/anth/person")
public class AnthPersonController {

    @Autowired
    private IAuthPersonService authPersonService;

    /**
     * 个人实名认证网页版
     * https://preweb.asign.cn/platform/openDoc/docDetail?mid=personIdentifyUrl
     * @param input
     * @return
     */
    @PostMapping("/identifyUrl")
    public ApiRespBody identifyUrl(@RequestBody AuthInput input){
        return authPersonService.identifyUrl(input);
    }

    /**
     * 个人运营商三要素认证
     * https://preweb.asign.cn/platform/openDoc/docDetail?mid=mobile3
     * @param input
     * @return
     */
    @PostMapping("/mobile3")
    public ApiRespBody mobile3(@RequestBody AuthInput input){
        return authPersonService.mobile3(input);
    }

    /**
     * 个人银行卡四要素认证
     * https://preweb.asign.cn/platform/openDoc/docDetail?mid=bankCard4
     * @param input
     * @return
     */
    @PostMapping("/bankCard4")
    public ApiRespBody bankCard4(@RequestBody AuthInput input){
        return authPersonService.bankCard4(input);
    }

    /**
     * 个人人脸活体认证
     * https://preweb.asign.cn/platform/openDoc/docDetail?mid=personFace
     * @param input
     * @return
     */
    @PostMapping("/face")
    public ApiRespBody face(@RequestBody AuthInput input){
        return authPersonService.face(input);
    }

    /**
     * 个人人脸活体认证
     * https://preweb.asign.cn/platform/openDoc/docDetail?mid=personFace
     * @param input
     * @return
     */
    @PostMapping("/willFace")
    public ApiRespBody willFace(@RequestBody AuthInput input){
        return authPersonService.willFace(input);
    }

}
