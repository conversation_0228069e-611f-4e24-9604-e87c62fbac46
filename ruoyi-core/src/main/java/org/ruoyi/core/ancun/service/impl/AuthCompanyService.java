package org.ruoyi.core.ancun.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.ancun.netsign.model.ApiRespBody;
import com.ancun.netsign.model.AuthInput;
import com.ancun.netsign.model.AuthOutput;
import com.ancun.netsign.model.PublicAccountOutput;
import org.ruoyi.core.ancun.AncunClient;
import org.ruoyi.core.ancun.service.IAuthCompanyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class AuthCompanyService implements IAuthCompanyService {

    @Autowired
    private AncunClient ancunClient;

    @Override
    public ApiRespBody<AuthOutput> identifyUrl(AuthInput input){
        ApiRespBody<AuthOutput> apiRespBody = ancunClient.netSignClient.getCompanyIdentifyUrl(input);
        if (apiRespBody.success()) {
            System.out.println(JSONObject.toJSONString(apiRespBody.getData()));
        }
        return apiRespBody;
    }

    @Override
    public ApiRespBody<AuthOutput> mobile3(AuthInput input){
        ApiRespBody<AuthOutput> apiRespBody = ancunClient.netSignClient.companyAuthMobile3(input);
        if (apiRespBody.success()) {
            System.out.println(JSONObject.toJSONString(apiRespBody.getData()));
        }
        return apiRespBody;
    }

    @Override
    public ApiRespBody<AuthOutput> bankCard4(AuthInput input){
        ApiRespBody<AuthOutput> apiRespBody = ancunClient.netSignClient.companyAuthBankCard4(input);
        if (apiRespBody.success()) {
            System.out.println(JSONObject.toJSONString(apiRespBody.getData()));
        }
        return apiRespBody;
    }

    @Override
    public ApiRespBody<AuthOutput> ent3(AuthInput input){
        ApiRespBody<AuthOutput> apiRespBody = ancunClient.netSignClient.companyVerifyEnt3(input);
        if (apiRespBody.success()) {
            System.out.println(JSONObject.toJSONString(apiRespBody.getData()));
        }
        return apiRespBody;
    }

    @Override
    public ApiRespBody<AuthOutput> transfer(AuthInput input){
        ApiRespBody<AuthOutput> apiRespBody = ancunClient.netSignClient.companyPublicTransfer(input);
        if (apiRespBody.success()) {
            System.out.println(JSONObject.toJSONString(apiRespBody.getData()));
        }
        return apiRespBody;
    }

    @Override
    public ApiRespBody<AuthOutput> transferProcess(AuthInput input){
        ApiRespBody<AuthOutput> apiRespBody = ancunClient.netSignClient.transferProcess(input);
        if (apiRespBody.success()) {
            System.out.println(JSONObject.toJSONString(apiRespBody.getData()));
        }
        return apiRespBody;
    }

    @Override
    public ApiRespBody<Void> verifyTransferAmount(AuthInput input){
        ApiRespBody<Void> apiRespBody = ancunClient.netSignClient.companyVerifyTransferAmount(input);
        if (apiRespBody.success()) {
            System.out.println(JSONObject.toJSONString(apiRespBody.getData()));
        }
        return apiRespBody;
    }

    @Override
    public ApiRespBody<PublicAccountOutput> reverseTransfer(AuthInput input){
        ApiRespBody<PublicAccountOutput> apiRespBody = ancunClient.netSignClient.reverseTransfer(input);
        if (apiRespBody.success()) {
            System.out.println(JSONObject.toJSONString(apiRespBody.getData()));
        }
        return apiRespBody;
    }

    @Override
    public ApiRespBody<AuthOutput> reverseTransferProcess(AuthInput input){
        ApiRespBody<AuthOutput> apiRespBody = ancunClient.netSignClient.reverseTransferProcess(input);
        if (apiRespBody.success()) {
            System.out.println(JSONObject.toJSONString(apiRespBody.getData()));
        }
        return apiRespBody;
    }

    @Override
    public ApiRespBody<AuthOutput> authorization(AuthInput input){
        ApiRespBody<AuthOutput> apiRespBody = ancunClient.netSignClient.companyAuthorization(input);
        if (apiRespBody.success()) {
            System.out.println(JSONObject.toJSONString(apiRespBody.getData()));
        }
        return apiRespBody;
    }

    @Override
    public ApiRespBody<AuthOutput> authorizationUrl(AuthInput input){
        ApiRespBody<AuthOutput> apiRespBody = ancunClient.netSignClient.companyAuthorizationUrl(input);
        if (apiRespBody.success()) {
            System.out.println(JSONObject.toJSONString(apiRespBody.getData()));
        }
        return apiRespBody;
    }



}
