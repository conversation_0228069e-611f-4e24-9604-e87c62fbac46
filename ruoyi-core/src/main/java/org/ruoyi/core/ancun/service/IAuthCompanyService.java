package org.ruoyi.core.ancun.service;

import com.ancun.netsign.model.ApiRespBody;
import com.ancun.netsign.model.AuthInput;
import com.ancun.netsign.model.AuthOutput;
import com.ancun.netsign.model.PublicAccountOutput;

public interface IAuthCompanyService {

    public ApiRespBody<AuthOutput> identifyUrl(AuthInput input);

    public ApiRespBody<AuthOutput> mobile3(AuthInput input);

    public ApiRespBody<AuthOutput> bankCard4(AuthInput input);

    public ApiRespBody<AuthOutput> ent3(AuthInput input);

    public ApiRespBody<AuthOutput> transfer(AuthInput input);

    public ApiRespBody<AuthOutput> transferProcess(AuthInput input);

    public ApiRespBody<Void> verifyTransferAmount(AuthInput input);

    public ApiRespBody<PublicAccountOutput> reverseTransfer(AuthInput input);

    public ApiRespBody<AuthOutput> reverseTransferProcess(AuthInput input);

    public ApiRespBody<AuthOutput> authorization(AuthInput input);

    public ApiRespBody<AuthOutput> authorizationUrl(AuthInput input);

    public ApiRespBody<AuthOutput> authorizationResult(AuthInput input);
}
