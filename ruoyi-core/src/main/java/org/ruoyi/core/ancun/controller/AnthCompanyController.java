package org.ruoyi.core.ancun.controller;

import com.ancun.netsign.model.ApiRespBody;
import com.ancun.netsign.model.AuthInput;
import org.ruoyi.core.ancun.service.IAuthCompanyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/anth/company")
public class AnthCompanyController {

    @Autowired
    private IAuthCompanyService authCompanyService;

    /**
     * 企业实名认证网页版
     * https://preweb.asign.cn/platform/openDoc/docDetail?mid=companyIdentifyUrl
     * @param input
     * @return
     */
    @PostMapping("/identifyUrl")
    public ApiRespBody identifyUrl(@RequestBody AuthInput input){
        return authCompanyService.identifyUrl(input);
    }

}
