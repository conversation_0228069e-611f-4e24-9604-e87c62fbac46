package org.ruoyi.core.ancun.controller;

import com.ancun.netsign.model.ApiRespBody;
import com.ancun.netsign.model.AuthInput;
import org.ruoyi.core.ancun.service.IAuthCompanyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/anth/company")
public class AnthCompanyController {

    @Autowired
    private IAuthCompanyService authCompanyService;

    /**
     * 企业实名认证网页版
     * https://preweb.asign.cn/platform/openDoc/docDetail?mid=companyIdentifyUrl
     * @param input
     * @return
     */
    @PostMapping("/identifyUrl")
    public ApiRespBody identifyUrl(@RequestBody AuthInput input){
        return authCompanyService.identifyUrl(input);
    }

    /**
     * 企业法人运营商三要素认证
     * https://preweb.asign.cn/platform/openDoc/docDetail?mid=companyMobile3
     * @param input
     * @return
     */
    @PostMapping("/mobile3")
    public ApiRespBody mobile3(@RequestBody AuthInput input){
        return authCompanyService.mobile3(input);
    }


    /**
     * 企业法人银行卡四要素认证
     * https://preweb.asign.cn/platform/openDoc/docDetail?mid=companyBankCard4
     * @param input
     * @return
     */
    @PostMapping("/bankCard4")
    public ApiRespBody bankCard4(@RequestBody AuthInput input){
        return authCompanyService.bankCard4(input);
    }

    /**
     * 企业核身认证
     * https://preweb.asign.cn/platform/openDoc/docDetail?mid=companyEnt3
     * @param input
     * @return
     */
    @PostMapping("/ent3")
    public ApiRespBody ent3(@RequestBody AuthInput input){
        return authCompanyService.ent3(input);
    }

    /**
     * 发起打款认证
     * https://preweb.asign.cn/platform/openDoc/docDetail?mid=companyTransfer
     * @param input
     * @return
     */
    @PostMapping("/transfer")
    public ApiRespBody transfer(@RequestBody AuthInput input){
        return authCompanyService.transfer(input);
    }

    /**
     * 查询打款进度
     * https://preweb.asign.cn/platform/openDoc/docDetail?mid=transferProcess
     * @param input
     * @return
     */
    @PostMapping("/transferProcess")
    public ApiRespBody transferProcess(@RequestBody AuthInput input){
        return authCompanyService.transferProcess(input);
    }

    /**
     * 打款金额校验
     * https://preweb.asign.cn/platform/openDoc/docDetail?mid=verifyTransferAmount
     * @param input
     * @return
     */
    @PostMapping("/verifyTransferAmount")
    public ApiRespBody verifyTransferAmount(@RequestBody AuthInput input){
        return authCompanyService.verifyTransferAmount(input);
    }

    /**
     * 发起反向打款
     * https://preweb.asign.cn/platform/openDoc/docDetail?mid=verifyTransferAmount
     * @param input
     * @return
     */
    @PostMapping("/reverseTransfer")
    public ApiRespBody reverseTransfer(@RequestBody AuthInput input){
        return authCompanyService.reverseTransfer(input);
    }

    /**
     * 查询反向打款进度
     * https://preweb.asign.cn/platform/openDoc/docDetail?mid=reverseTransferProcess
     * @param input
     * @return
     */
    @PostMapping("/reverseTransferProcess")
    public ApiRespBody reverseTransferProcess(@RequestBody AuthInput input){
        return authCompanyService.reverseTransferProcess(input);
    }

    /**
     * 法人核身
     * https://preweb.asign.cn/platform/openDoc/docDetail?mid=authorization
     * @param input
     * @return
     */
    @PostMapping("/authorization")
    public ApiRespBody authorization(@RequestBody AuthInput input){
        return authCompanyService.authorization(input);
    }

    /**
     * 法人核身
     * https://preweb.asign.cn/platform/openDoc/docDetail?mid=authorizationUrl
     * @param input
     * @return
     */
    @PostMapping("/authorizationUrl")
    public ApiRespBody authorizationUrl(@RequestBody AuthInput input){
        return authCompanyService.authorizationUrl(input);
    }





}
