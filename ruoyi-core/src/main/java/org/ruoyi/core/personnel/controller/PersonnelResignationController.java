package org.ruoyi.core.personnel.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.personnel.domain.PersonnelResignation;
import org.ruoyi.core.personnel.domain.vo.PersonnelResignationVo;
import org.ruoyi.core.personnel.service.IPersonnelResignationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 人员离职Controller
 *
 * <AUTHOR>
 * @date 2024-01-29
 */
@RestController
@RequestMapping("/personnel/resignation")
public class PersonnelResignationController extends BaseController
{
    @Autowired
    private IPersonnelResignationService personnelResignationService;

    /**
     * 查询人员离职列表
     */
    //@PreAuthorize("@ss.hasPermi('personnel:resignation:list')")
    @GetMapping("/list")
    public TableDataInfo list(PersonnelResignationVo personnelResignation)
    {
        //startPage();
        List<PersonnelResignationVo> list = personnelResignationService.selectPersonnelResignationList(personnelResignation);
        return getDataTable(list);
    }

    /**
     * 导出人员离职列表
     */
   // @PreAuthorize("@ss.hasPermi('personnel:resignation:export')")
    @Log(title = "人员离职", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PersonnelResignationVo personnelResignation)
    {
        List<PersonnelResignationVo> list = personnelResignationService.exportList(personnelResignation);
        ExcelUtil<PersonnelResignationVo> util = new ExcelUtil<PersonnelResignationVo>(PersonnelResignationVo.class);
        util.exportExcel(response, list, "人员离职数据");
    }

    /**
     * 获取人员离职详细信息
     */
    //@PreAuthorize("@ss.hasPermi('personnel:resignation:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(personnelResignationService.selectPersonnelResignationById(id));
    }

    /**
     * 新增人员离职
     */
    //@PreAuthorize("@ss.hasPermi('personnel:resignation:add')")
    @Log(title = "人员离职", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PersonnelResignationVo personnelResignation)
    {
        return personnelResignationService.insertPersonnelResignation(personnelResignation);
    }

    /**
     * 修改人员离职
     */
    //@PreAuthorize("@ss.hasPermi('personnel:resignation:edit')")
    @Log(title = "人员离职", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PersonnelResignation personnelResignation)
    {
        return toAjax(personnelResignationService.updatePersonnelResignation(personnelResignation));
    }

    /**
     * 删除人员离职
     */
    //@PreAuthorize("@ss.hasPermi('personnel:resignation:remove')")
    @Log(title = "人员离职", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(personnelResignationService.deletePersonnelResignationByIds(ids));
    }

    /**
     * 审核通过入职
     */
    //@PreAuthorize("@ss.hasPermi('system:information:edit')")
    @Log(title = "审核通过离职", businessType = BusinessType.UPDATE)
    @PostMapping("/passResignation")
    public AjaxResult passResignationById(@RequestBody Long id)
    {
        return toAjax(personnelResignationService.passResignationById(id));
    }

    /**
     * 审核不通过入职
     */
    //@PreAuthorize("@ss.hasPermi('system:information:edit')")
    @Log(title = "审核不通过离职", businessType = BusinessType.UPDATE)
    @PostMapping("/unpassResignation")
    public AjaxResult unpassResignationById(@RequestBody Long id)
    {
        return toAjax(personnelResignationService.unpassResignationById(id));
    }

    /**
     * 审核不通过入职
     */
    //@PreAuthorize("@ss.hasPermi('system:information:edit')")
    @Log(title = "提交修改状态", businessType = BusinessType.UPDATE)
    @PostMapping("/commitResignation")
    public AjaxResult commitResignationProcess(@RequestBody Long id)
    {
        return toAjax(personnelResignationService.commitResignationProcess(id));
    }

}
