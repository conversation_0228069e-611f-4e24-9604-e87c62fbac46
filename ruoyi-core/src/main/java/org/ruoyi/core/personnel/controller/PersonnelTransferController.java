package org.ruoyi.core.personnel.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.personnel.domain.PersonnelTransfer;
import org.ruoyi.core.personnel.domain.vo.PersonnelTransferVo;
import org.ruoyi.core.personnel.service.IPersonnelTransferService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 人员调动Controller
 *
 * <AUTHOR>
 * @date 2024-01-19
 */
@RestController
@RequestMapping("/personnel/transfer")
public class PersonnelTransferController extends BaseController
{
    @Autowired
    private IPersonnelTransferService personnelTransferService;

    /**
     * 查询人员调动列表
     */
    //@PreAuthorize("@ss.hasPermi('personnel:transfer:list')")
    @GetMapping("/list")
    public TableDataInfo list(PersonnelTransfer personnelTransfer)
    {
        startPage();
        List<PersonnelTransfer> list = personnelTransferService.selectPersonnelTransferList(personnelTransfer);
        return getDataTable(list);
    }

    /**
     * 导出人员调动列表
     */
    //@PreAuthorize("@ss.hasPermi('personnel:transfer:export')")
    @Log(title = "人员调动", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PersonnelTransferVo personnelTransfer)
    {
        List<PersonnelTransferVo> list = personnelTransferService.exportList(personnelTransfer);
        ExcelUtil<PersonnelTransferVo> util = new ExcelUtil<PersonnelTransferVo>(PersonnelTransferVo.class);
        util.exportExcel(response, list, "人员调动数据");
    }

    /**
     * 获取人员调动详细信息
     */
    //@PreAuthorize("@ss.hasPermi('personnel:transfer:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(personnelTransferService.selectPersonnelTransferById(id));
    }

    /**
     * 新增人员调动
     */
    //@PreAuthorize("@ss.hasPermi('personnel:transfer:add')")
    @Log(title = "人员调动", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PersonnelTransferVo personnelTransfer)
    {
        return personnelTransferService.insertPersonnelTransfer(personnelTransfer);
    }

    /**
     * 修改人员调动
     */
    //@PreAuthorize("@ss.hasPermi('personnel:transfer:edit')")
    @Log(title = "人员调动", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PersonnelTransfer personnelTransfer)
    {
        return toAjax(personnelTransferService.updatePersonnelTransfer(personnelTransfer));
    }

    /**
     * 删除人员调动
     */
   // @PreAuthorize("@ss.hasPermi('personnel:transfer:remove')")
    @Log(title = "人员调动", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(personnelTransferService.deletePersonnelTransferByIds(ids));
    }

    @GetMapping("/getTransferHistoryList")
    public TableDataInfo getTransferHistoryList(PersonnelTransfer personnelTransfer)
    {
        startPage();
        List<PersonnelTransferVo> list = personnelTransferService.getTransferHistoryList(personnelTransfer);
        return getDataTable(list);
    }

    /**
     * 审核通过执行调动
     */
    //@PreAuthorize("@ss.hasPermi('system:information:edit')")
    @Log(title = "审核通过执行调动", businessType = BusinessType.UPDATE)
    @PostMapping("/passTransfer")
    public AjaxResult passTransferById(@RequestBody Long id)
    {
        return toAjax(personnelTransferService.passTransferById(id));
    }

    /**
     * 审核通过执行调动
     */
    //@PreAuthorize("@ss.hasPermi('system:information:edit')")
    @Log(title = "审核通过执行调动", businessType = BusinessType.UPDATE)
    @PostMapping("/unpassTransfer")
    public AjaxResult unpassTransferById(@RequestBody Long id)
    {
        return toAjax(personnelTransferService.unpassTransferById(id));
    }

    @Anonymous
    @PostMapping("/uploadFile")
    public AjaxResult uploadFile(@RequestParam("file") MultipartFile file) {
        return personnelTransferService.uploadFile(file);
    }

}
