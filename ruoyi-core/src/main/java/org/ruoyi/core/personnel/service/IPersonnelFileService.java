package org.ruoyi.core.personnel.service;

import org.ruoyi.core.personnel.domain.PersonnelFile;
import org.ruoyi.core.personnel.domain.vo.PersonnelFileVo;

import java.util.List;

/**
 * 人员文件Service接口
 *
 * <AUTHOR>
 * @date 2024-01-30
 */
public interface IPersonnelFileService
{
    /**
     * 查询人员文件
     *
     * @param id 人员文件主键
     * @return 人员文件
     */
    public PersonnelFile selectPersonnelFileById(Long id);

    /**
     * 查询人员文件列表
     *
     * @param personnelFile 人员文件
     * @return 人员文件集合
     */
    public List<PersonnelFile> selectPersonnelFileList(PersonnelFileVo personnelFile);

    /**
     * 新增人员文件
     *
     * @param personnelFile 人员文件
     * @return 结果
     */
    public int insertPersonnelFile(PersonnelFile personnelFile);

    public int insertPersonnelFiles( List<PersonnelFile> files);

    /**
     * 修改人员文件
     *
     * @param personnelFile 人员文件
     * @return 结果
     */
    public int updatePersonnelFile(PersonnelFile personnelFile);

    /**
     * 批量删除人员文件
     *
     * @param ids 需要删除的人员文件主键集合
     * @return 结果
     */
    public int deletePersonnelFileByIds(Long[] ids);

    /**
     * 删除人员文件信息
     *
     * @param id 人员文件主键
     * @return 结果
     */
    public int deletePersonnelFileById(Long id);

    public int deleteByCorrelationId(PersonnelFile personnelFile);

    public int correlationFile(PersonnelFileVo personnelFile);

}
