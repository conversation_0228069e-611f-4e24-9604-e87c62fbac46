package org.ruoyi.core.personnel.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import org.ruoyi.core.constant.UploadFeatureConstants;
import org.ruoyi.core.information.domain.vo.PageUtil;
import org.ruoyi.core.personnel.domain.*;
import org.ruoyi.core.personnel.domain.enums.PoliticalLandscapeEnum;
import org.ruoyi.core.personnel.domain.vo.PersonnelArchivesVo;
import org.ruoyi.core.personnel.domain.vo.PersonnelFileVo;
import org.ruoyi.core.personnel.domain.vo.PersonnelFormalVo;
import org.ruoyi.core.personnel.domain.vo.PersonnelOnboardingVo;
import org.ruoyi.core.personnel.mapper.PersonnelFormalMapper;
import org.ruoyi.core.personnel.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;
import static com.ruoyi.common.utils.SecurityUtils.getUsername;


/**
 * 人员转正Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-18
 */
@Service
public class PersonnelFormalServiceImpl implements IPersonnelFormalService
{
    @Autowired
    private PersonnelFormalMapper personnelFormalMapper;
    @Autowired
    private IPersonnelArchivesService personnelArchivesService;
    @Autowired
    private IPersonnelFileService personnelFileService;
    @Autowired
    private IPersonnelOnboardingService personnelOnboardingService;
    @Autowired
    private IPersonnelProcessService personnelProcessService;
    /**
     * 查询人员转正
     *
     * @param id 人员转正主键
     * @return 人员转正
     */
    @Override
    public PersonnelFormalVo selectPersonnelFormalById(Long id)
    {

        PersonnelFormalVo onboardingVo = personnelFormalMapper.selectPersonnelFormalById(id);
        PersonnelFileVo proFile = new PersonnelFileVo();
        proFile.setCorrelationId(id);
        proFile.setFileState("1");
        proFile.setFileType("7");
        List<PersonnelFile> proFiles = personnelFileService.selectPersonnelFileList(proFile);

        PersonnelFileVo becomeFile = new PersonnelFileVo();
        becomeFile.setCorrelationId(id);
        becomeFile.setFileState("1");
        becomeFile.setFileType("8");
        List<PersonnelFile> becomeFiles = personnelFileService.selectPersonnelFileList(becomeFile);

        onboardingVo.setFilesPro(proFiles);
        onboardingVo.setFilesBecome(becomeFiles);
        return onboardingVo;
    }

    /**
     * 查询人员转正列表
     *
     * @param personnelFormal 人员转正
     * @return 人员转正
     */
    @Override
    public List<PersonnelFormal> selectPersonnelFormalList(PersonnelFormal personnelFormal)
    {
        return personnelFormalMapper.selectPersonnelFormalList(personnelFormal);
    }

    /**
     * 新增人员转正
     *
     * @param personnelFormal 人员转正
     * @return 结果
     */
    @Override
    @Transactional
    public AjaxResult insertPersonnelFormal(PersonnelFormalVo personnelFormal)
    {
        //当有id时，为修改，为null时为新增
        if (personnelFormal.getId()==null){
            personnelFormal.setCreateBy(getUsername());
            personnelFormal.setFormalState("3");
            personnelFormal.setCreateTime(DateUtils.getNowDate());
            int count = personnelFormalMapper.getCountByCreateTime(DateUtils.getDate()) + 1;
            String createTimeNum = DateUtils.dateTimeNow("yyyyMMdd");
            personnelFormal.setFormalCode("ZZSQ" + createTimeNum + String.format("%03d", count));
            int data = personnelFormalMapper.insertPersonnelFormal(personnelFormal);

            if (personnelFormal.getBecomeFileIds() != null){
                PersonnelFileVo personnelFile = new PersonnelFileVo();
                personnelFile.setIds(personnelFormal.getBecomeFileIds());
                personnelFile.setCorrelationId(personnelFormal.getId());
                personnelFileService.correlationFile(personnelFile);
            }

            if (personnelFormal.getProfessionalFileIds() != null){
                PersonnelFileVo personnelFile = new PersonnelFileVo();
                personnelFile.setIds(personnelFormal.getProfessionalFileIds());
                personnelFile.setCorrelationId(personnelFormal.getId());
                personnelFileService.correlationFile(personnelFile);
            }

            return AjaxResult.success(personnelFormal);
        } else {
            //修改前先取消关联文件
            PersonnelFile proFile = new PersonnelFile();
            proFile.setFileType("7");
            proFile.setCorrelationId(personnelFormal.getId());
            personnelFileService.deleteByCorrelationId(proFile);

            PersonnelFile becomeFile = new PersonnelFile();
            becomeFile.setFileType("8");
            becomeFile.setCorrelationId(personnelFormal.getId());
            personnelFileService.deleteByCorrelationId(becomeFile);
            //关联最新的文件
//            if(personnelFormal.getFileIds() != null && !personnelFormal.getFileIds().isEmpty()){
//                PersonnelFileVo personnelFile = new PersonnelFileVo();
//                personnelFile.setIds(personnelFormal.getFileIds());
//                personnelFile.setCorrelationId(personnelFormal.getId());
//                personnelFileService.correlationFile(personnelFile);
//            }
            if (personnelFormal.getBecomeFileIds() != null){
                PersonnelFileVo personnelFile = new PersonnelFileVo();
                personnelFile.setIds(personnelFormal.getBecomeFileIds());
                personnelFile.setCorrelationId(personnelFormal.getId());
                personnelFileService.correlationFile(personnelFile);
            }

            if (personnelFormal.getProfessionalFileIds() != null){
                PersonnelFileVo personnelFile = new PersonnelFileVo();
                personnelFile.setIds(personnelFormal.getProfessionalFileIds());
                personnelFile.setCorrelationId(personnelFormal.getId());
                personnelFileService.correlationFile(personnelFile);
            }

            int i = personnelFormalMapper.updatePersonnelFormal(personnelFormal);
            return AjaxResult.success(personnelFormal);
        }
    }

    /**
     * 修改人员转正
     *
     * @param personnelFormal 人员转正
     * @return 结果
     */
    @Override
    public int updatePersonnelFormal(PersonnelFormalVo personnelFormal)
    {
        personnelFormal.setUpdateTime(DateUtils.getNowDate());
        return personnelFormalMapper.updatePersonnelFormal(personnelFormal);
    }

    /**
     * 批量删除人员转正
     *
     * @param ids 需要删除的人员转正主键
     * @return 结果
     */
    @Override
    public int deletePersonnelFormalByIds(Long[] ids)
    {
        return personnelFormalMapper.deletePersonnelFormalByIds(ids);
    }

    /**
     * 删除人员转正信息
     *
     * @param id 人员转正主键
     * @return 结果
     */
    @Override
    public int deletePersonnelFormalById(Long id)
    {
        return personnelFormalMapper.deletePersonnelFormalById(id);
    }
    /**
     * 查询当天人员转正次数
     *
     * @param createTime 人员转正主键
     * @return 结果
     */
    @Override
    public int getCountByCreateTime(String createTime) {
        return personnelFormalMapper.getCountByCreateTime(createTime);
    }

    /**
     * 查询人员未转正列表
     *
     * @param personnelOnboardingVo 人员档案
     * @return 人员档案集合
     */
    @Override
    public List<PersonnelFormalVo> getFormalList(PersonnelOnboardingVo personnelOnboardingVo){
        //数据范围
        LoginUser loginUser = getLoginUser();
        Map<String, List<Long>> dataRange = personnelArchivesService.getDataRange(loginUser);
        personnelOnboardingVo.setDeptIds(dataRange.get("deptIds"));
        personnelOnboardingVo.setUnitIds(dataRange.get("unitIds"));
        List<SysRole> roles = loginUser.getUser().getRoles();
        List<SysRole> oneself = roles.stream().filter(role ->
                ("6".equals(role.getDataScope()) || "1".equals(role.getDataScope())) && "3".equals(role.getRoleType()) && "0".equals(role.getDelFlag()) && "0".equals(role.getStatus())
        ).collect(Collectors.toList());
        if (!oneself.isEmpty()){
            personnelOnboardingVo.setCreateBy(loginUser.getUser().getUserName());
        }
        if(personnelOnboardingVo.getDeptIds().isEmpty() && personnelOnboardingVo.getUnitIds().isEmpty() && oneself.isEmpty()){
            return new ArrayList<>();
        }
        PageUtil.startPage();
        List<PersonnelFormalVo> formalList = personnelFormalMapper.getFormalList(personnelOnboardingVo);

        List<Long> Ids = formalList.stream().map(PersonnelFormalVo::getId).collect(Collectors.toList());
        PersonnelFileVo forFile = new PersonnelFileVo();
        forFile.setCorrelationIds(Ids);
        forFile.setFileState("1");
        forFile.setFileType("2");
        List<PersonnelFile> forFiles = personnelFileService.selectPersonnelFileList(forFile);
        Map<Long, List<PersonnelFile>> forFileMap = forFiles.stream().collect(Collectors.groupingBy(PersonnelFile::getCorrelationId));

        formalList.forEach(formal ->{
            formal.setFiles(forFileMap.get(formal.getId()));
        });
        return formalList;
    }

    @Override
    public int passFormalById(Long id){
        PersonnelFormalVo onboardingVo = selectPersonnelFormalById(id);
        //修改人员状态
        PersonnelArchives personnelArchives = new PersonnelArchives();
        personnelArchives.setPersonnelState("1");
        personnelArchives.setSysName(onboardingVo.getSysName());
        personnelArchives.setUpdateBy(getUsername());
        personnelArchives.setUpdateTime(DateUtils.getNowDate());
        personnelArchivesService.updatePersonnelStateBySysName(personnelArchives);
        return personnelFormalMapper.passFormalById(id);
    }

    @Override
    public int unpassFormalById(Long id){
        PersonnelProcess personnelProcess = new PersonnelProcess();
        personnelProcess.setCorrelationId(id);
        personnelProcess.setProcessType("2");
        personnelProcessService.abandonedProcess(personnelProcess);
        return personnelFormalMapper.unpassFormalById(id);
    }

    @Override
    public int commitFormalById(Long id){
        return personnelFormalMapper.commitFormalById(id);
    }

    @Override
    public AjaxResult uploadFile(MultipartFile file) {
        try {
            String name = file.getOriginalFilename();
            String url = FileUploadUtils.uploadOSS(UploadFeatureConstants.PERSONNEL_SYSTEM, file);
            PersonnelFile personnelFile = new PersonnelFile();
            personnelFile.setFileUrl(url);
            personnelFile.setFileName(name);
            personnelFile.setFileState("0");
            personnelFile.setFileType("2");
            personnelFile.setCreateTime(DateUtils.getNowDate());
            personnelFile.setCreateBy(getUsername());
            personnelFileService.insertPersonnelFile(personnelFile);
            return AjaxResult.success(personnelFile);
        } catch (Exception e) {
            return AjaxResult.error();
        }
    }

    @Override
    public AjaxResult uploadFilePro(MultipartFile file) {
        try {
            String name = file.getOriginalFilename();
            String url = FileUploadUtils.uploadOSS(UploadFeatureConstants.PERSONNEL_SYSTEM, file);
            PersonnelFile personnelFile = new PersonnelFile();
            personnelFile.setFileUrl(url);
            personnelFile.setFileName(name);
            personnelFile.setFileState("0");
            personnelFile.setFileType("7");
            personnelFile.setCreateTime(DateUtils.getNowDate());
            personnelFile.setCreateBy(getUsername());
            personnelFileService.insertPersonnelFile(personnelFile);
            return AjaxResult.success(personnelFile);
        } catch (Exception e) {
            return AjaxResult.error();
        }
    }

    @Override
    public AjaxResult uploadFileBecome(MultipartFile file) {
        try {
            String name = file.getOriginalFilename();
            String url = FileUploadUtils.uploadOSS(UploadFeatureConstants.PERSONNEL_SYSTEM, file);
            PersonnelFile personnelFile = new PersonnelFile();
            personnelFile.setFileUrl(url);
            personnelFile.setFileName(name);
            personnelFile.setFileState("0");
            personnelFile.setFileType("8");
            personnelFile.setCreateTime(DateUtils.getNowDate());
            personnelFile.setCreateBy(getUsername());
            personnelFileService.insertPersonnelFile(personnelFile);
            return AjaxResult.success(personnelFile);
        } catch (Exception e) {
            return AjaxResult.error();
        }
    }

    @Override
    public List<PersonnelFormalVo> exportList(PersonnelOnboardingVo personnelOnboardingVo){
        //数据范围
        LoginUser loginUser = getLoginUser();
        Map<String, List<Long>> dataRange = personnelArchivesService.getDataRange(loginUser);
        personnelOnboardingVo.setDeptIds(dataRange.get("deptIds"));
        personnelOnboardingVo.setUnitIds(dataRange.get("unitIds"));
        List<SysRole> roles = loginUser.getUser().getRoles();
        List<SysRole> oneself = roles.stream().filter(role ->
                ("6".equals(role.getDataScope()) || "1".equals(role.getDataScope())) && "3".equals(role.getRoleType()) && "0".equals(role.getDelFlag()) && "0".equals(role.getStatus())
        ).collect(Collectors.toList());
        if (!oneself.isEmpty()){
            personnelOnboardingVo.setCreateBy(loginUser.getUser().getUserName());
        }
        if(personnelOnboardingVo.getDeptIds().isEmpty() && personnelOnboardingVo.getUnitIds().isEmpty() && oneself.isEmpty()){
            return new ArrayList<>();
        }
        PageUtil.startPage();

        List<PersonnelFormalVo> formalList = personnelFormalMapper.getFormalList(personnelOnboardingVo);

        return formalList;
    }
}
