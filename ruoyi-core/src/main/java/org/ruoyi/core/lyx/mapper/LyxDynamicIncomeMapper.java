package org.ruoyi.core.lyx.mapper;

import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.lyx.domain.LyxDynamicIncome;

import java.util.List;
import java.util.Map;

/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-11
 */
public interface LyxDynamicIncomeMapper 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public LyxDynamicIncome selectLyxDynamicIncomeById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param lyxDynamicIncome 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<LyxDynamicIncome> selectLyxDynamicIncomeList(LyxDynamicIncome lyxDynamicIncome);

    /**
     * 新增【请填写功能名称】
     * 
     * @param lyxDynamicIncome 【请填写功能名称】
     * @return 结果
     */
    public int insertLyxDynamicIncome(LyxDynamicIncome lyxDynamicIncome);

    /**
     * 修改【请填写功能名称】
     * 
     * @param lyxDynamicIncome 【请填写功能名称】
     * @return 结果
     */
    public int updateLyxDynamicIncome(LyxDynamicIncome lyxDynamicIncome);

    /**
     * 删除【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteLyxDynamicIncomeById(Long id);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLyxDynamicIncomeByIds(Long[] ids);

    int deleteByDayCheck(@Param("dayCheckId") Long dayCheckId);

    List<LyxDynamicIncome> selectLyxdynamicIncomeByDayId(@Param("dayCheckId")Long dayCheckId);

    List<Map<String, Object>> queryIncomeDataByDayId(@Param("id") String id);

    List<LyxDynamicIncome> getDataByCheckId(@Param("id") Long id);
}
