package org.ruoyi.core.lyx.service.impl;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.financial.controller.JsonResult;
import com.ruoyi.financial.service.impl.FinancialOpenServiceImpl;
import org.ruoyi.core.lyx.domain.*;
import org.ruoyi.core.lyx.mapper.*;
import org.ruoyi.core.lyx.service.ILyxDayCheckService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;

/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-03-11
 */
@Service
public class LyxDayCheckServiceImpl implements ILyxDayCheckService
{
    @Autowired
    private LyxDayCheckMapper lyxDayCheckMapper;

    @Autowired
    private LyxDynamicIncomeMapper lyxDynamicIncomeMapper;

    @Autowired
    private LyxUploadFileMapper lyxUploadFileMapper;

    @Autowired
    private LyxRevenueItemDictMapper lyxRevenueItemDictMapper;
    @Autowired
    private LyxDayVoucharMapper lyxDayVoucharMapper;

    @Autowired
    private FinancialOpenServiceImpl financialOpenService;

    @Autowired
    private LyxDayVoucharDynamicMapper lyxDayVoucharDynamicMapper;

    @Autowired
    private LyxMeituanVoucharMapper lyxMeituanVoucharMapper;
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public LyxDayCheck selectLyxDayCheckById(Long id)
    {
        return lyxDayCheckMapper.selectLyxDayCheckById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param lyxDayCheck 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<LyxDayCheck> selectLyxDayCheckList(LyxDayCheck lyxDayCheck)
    {
        return lyxDayCheckMapper.selectLyxDayCheckList(lyxDayCheck);
    }

    public void exportData(){
        List<String> titleList = new ArrayList<>();
        titleList.add("时间");
    }

    /**
     * 新增【请填写功能名称】
     * 
     * @param lyxDayCheck 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertLyxDayCheck(LyxDayCheck lyxDayCheck)
    {
        lyxDayCheck.setCreateTime(DateUtils.getNowDate());
        return lyxDayCheckMapper.insertLyxDayCheck(lyxDayCheck);
    }

    /**
     * 修改【请填写功能名称】
     * 
     * @param lyxDayCheck 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateLyxDayCheck(LyxDayCheck lyxDayCheck)
    {
        lyxDayCheck.setUpdateTime(DateUtils.getNowDate());
        return lyxDayCheckMapper.updateLyxDayCheck(lyxDayCheck);
    }

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteLyxDayCheckByIds(Long[] ids)
    {
        return lyxDayCheckMapper.deleteLyxDayCheckByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteLyxDayCheckById(Long id)
    {
        return lyxDayCheckMapper.deleteLyxDayCheckById(id);
    }

    @Override
    public int inserOrUpdate(LyxDayCheckVO lyxDayCheckVO, LoginUser loginUser) {
        Date nowDate = DateUtils.getNowDate();
        LyxDayCheck lyxDayCheck = lyxDayCheckVO.getLyxDayCheck();
        List<LyxDynamicIncome> lyxDynamicIncomeList = lyxDayCheckVO.getLyxDynamicIncomeList();
        List<LyxUploadFile> lyxUploadFileList = lyxDayCheckVO.getLyxUploadFileList();
        int a = 0;
        //
        if(null == lyxDayCheck.getId() || "".equals(lyxDayCheck.getId())){
            //新增每日台账表
            lyxDayCheck.setCreateBy(loginUser.getUser().getNickName());
            lyxDayCheck.setCreateTime(nowDate);
            int i = lyxDayCheckMapper.insertLyxDayCheck(lyxDayCheck);
            //新增动态收入项
            int i2 = 0;
            if(lyxDynamicIncomeList.size()>0){
                for (LyxDynamicIncome lyxDynamicIncome : lyxDynamicIncomeList) {
                    lyxDynamicIncome.setDayCheckId(lyxDayCheck.getId());
                    lyxDynamicIncome.setCreateBy(loginUser.getUser().getNickName());
                    lyxDynamicIncome.setCreateTime(nowDate);
                    int a1 = lyxDynamicIncomeMapper.insertLyxDynamicIncome(lyxDynamicIncome);
                    i2 = i2+a1;
                }
            }
            int i3 = 0;
            if(lyxUploadFileList.size()>0){
                //新增附件
                for (LyxUploadFile lyxUploadFile : lyxUploadFileList) {
                    lyxUploadFile.setDayCheckId(lyxDayCheck.getId());
                    lyxUploadFile.setCreateBy(loginUser.getUser().getNickName());
                    lyxUploadFile.setCreateTime(nowDate);
                    int a2 = lyxUploadFileMapper.insertLyxUploadFile(lyxUploadFile);
                    i3 = i3+a2;
                }
            }
            a =a+i+i2+i3;
        }else {
            Long dayCheckId = lyxDayCheck.getId();
            //每日台账数据正常更新
            int i = lyxDayCheckMapper.updateLyxDayCheck(lyxDayCheck);
            int i2 = 0;
            //动态收入项内容得删除原本的再进行插入
            //删除原本的
            lyxDynamicIncomeMapper.deleteByDayCheck(dayCheckId);
            if(lyxDynamicIncomeList.size()>0){

                for (LyxDynamicIncome lyxDynamicIncome : lyxDynamicIncomeList) {
                    lyxDynamicIncome.setDayCheckId(lyxDayCheck.getId());
                    lyxDynamicIncome.setCreateBy(loginUser.getUser().getNickName());
                    lyxDynamicIncome.setCreateTime(nowDate);
                    int u1 = lyxDynamicIncomeMapper.insertLyxDynamicIncome(lyxDynamicIncome);
                    i2 = i2+u1;
                }

            }
            //附件涉及到删除文件，所以
            int i3 = 0;
            if(lyxUploadFileList.size()>0){
                lyxUploadFileMapper.deleteByDayCheck(dayCheckId);
                for (LyxUploadFile lyxUploadFile : lyxUploadFileList) {
                    lyxUploadFile.setDayCheckId(lyxDayCheck.getId());
                    lyxUploadFile.setCreateBy(loginUser.getUser().getNickName());
                    lyxUploadFile.setCreateTime(nowDate);
                    int u2 =  lyxUploadFileMapper.insertLyxUploadFile(lyxUploadFile);
                    i3 = i3+u2;
                }

            }
            a = a+i+i2+i3;

        }
        return a;
    }

    @Override
    public LyxDayCheckVO getDetailsById(Long id) {
        LyxDayCheckVO lyxDayCheckVO = new LyxDayCheckVO();
        LyxDayCheck lyxDayCheck = lyxDayCheckMapper.selectLyxDayCheckById(id);
        List<LyxDynamicIncome> lyxDynamicIncomeList = lyxDynamicIncomeMapper.selectLyxdynamicIncomeByDayId(id);
        List<LyxUploadFile> lyxUploadFileList = lyxUploadFileMapper.selectUploadFileByDayId(id);
        lyxDayCheckVO.setLyxDayCheck(lyxDayCheck);
        lyxDayCheckVO.setLyxDynamicIncomeList(lyxDynamicIncomeList);
        lyxDayCheckVO.setLyxUploadFileList(lyxUploadFileList);
        return lyxDayCheckVO;
    }

    @Override
    public void exportDataByMonth(HttpServletResponse response, LyxBillMonth lyxBillMonth) throws IOException {
        //查询所有动态收入项
//        List<String> strings = this.queryAllField();
        List<Map<String, Object>> rowDataList = this.queryData(lyxBillMonth.getId());
        Map<String, Object> map = rowDataList.get(0);
        Set<String> keySet = map.keySet();
        List<String> arrayList = new ArrayList<>(keySet);
        List<List<String>> headList = new ArrayList<>();
        arrayList.forEach(h -> headList.add(Collections.singletonList(h)));
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        String fileName = URLEncoder.encode(IdUtils.fastUUID(), "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        List<List<String>> a = a(rowDataList, headList);
        // 写入excel
        EasyExcelFactory.write(response.getOutputStream()).head(headList).sheet("月份台账").doWrite(a);

    }
    private List<List<String>>  a( List<Map<String, Object>> rowDataList , List<List<String>> headList ){
        List<List<String>> list = new ArrayList();
        for (Map<String, Object> map : rowDataList) {
            List<String> objects = new ArrayList<>();
            for (List<String> strings : headList) {
                String s = strings.get(0);
                objects.add(map.get(s).toString());
            }
            list.add(objects);
        }
        return list;
    }

    @Override
    public Map<String, Object> queryDayCheckData(LyxDayCheck lyxDayCheck) {
        HashMap<String, Object> returnMap = new HashMap<>();
        List<String> strings = this.queryAllField();
        Long billId = lyxDayCheck.getBillId();
        List<Map<String,Object>> dataList =   this.queryData1(billId);
        returnMap.put("fieldList",strings);
        returnMap.put("dataList",dataList);
        return returnMap;
    }

    @Override
    public List<LyxDynamicIncome> getLastDayCheckDynamic(LyxBillMonth lyxBillMonth) {

        LyxDayCheck lyxDayCheck = lyxDayCheckMapper.getFirstDataByMonthIdDesc(lyxBillMonth.getId());

        if(null == lyxDayCheck){
          return new ArrayList<>();
        }else {
            return  lyxDynamicIncomeMapper.getDataByCheckId(lyxDayCheck.getId());
        }


    }

    @Override
    public Map<String, Object> checkVoucharStatus(LyxDayVouchar lyxDayVouchar) {

        Map<String, Object> returnMap = new HashMap<>();
        boolean b = financialOpenService.checkCheckOut(Integer.valueOf(lyxDayVouchar.getVoucherId().toString()));
        returnMap.put("isok",b);
        return returnMap;
    }

    @Override
    public Map<String, Object> removeVouchar(LyxDayVouchar lyxDayVouchar) {

        Map<String, Object> returnMap = new HashMap<>();
        JsonResult jsonResult = financialOpenService.revokeVoucher(Integer.valueOf(lyxDayVouchar.getVoucherId().toString()));
        returnMap.put("isok",jsonResult.getCode());

        //1代表收款2代表美团提现
        if(lyxDayVouchar.getDayCheckId()==1){
            //删除凭证
            lyxDayVoucharMapper.deleteLyxDayVoucharById(lyxDayVouchar.getId());
            //删除凭证收入明细
            lyxDayVoucharDynamicMapper.deleteByDayId(lyxDayVouchar.getId());
        }else if(lyxDayVouchar.getDayCheckId()==2){
            lyxMeituanVoucharMapper.deleteLyxMeituanVoucharById(lyxDayVouchar.getId());
        }



        return returnMap;
    }

    @Override
    public Map<String, Object> checkDayDate(LyxDayCheckVO lyxDayCheck) {
        HashMap<String, Object> returnMap = new HashMap<>();
        List<LyxDayCheck> lyxDayChecks = lyxDayCheckMapper.selectLyxDayCheckBytime(lyxDayCheck.getDayTime());

        returnMap.put("num",lyxDayChecks.size());
        return returnMap;
    }

    /**
     * 获取所有的字段名
     * @return {@link List}<{@link String}>
     */
    public List<String> queryAllField(){
        LyxDayCheck lyxDayCheck = new LyxDayCheck();
        List<String> strings = lyxRevenueItemDictMapper.queryFildList();
        Map map = JSON.parseObject(JSON.toJSONString(lyxDayCheck), Map.class);
        Set set = map.keySet();
        boolean b = strings.addAll(set);

        return strings;
    }


    /**
     * 组装列表数据
     * @param monthId
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    public List<Map<String,Object>> queryData(Long monthId){
        List<Map<String, Object>> maps = lyxDayCheckMapper.exportData(monthId);
        List<String> strings = this.queryAllField();
        for (Map<String, Object> map : maps) {
            for (String string : strings) {
                map.put(string,"X");
            }
            //查询每条数据的动态收入项金额
            List<Map<String, Object>> incomeList =  lyxDynamicIncomeMapper.queryIncomeDataByDayId(map.get("id").toString());
            Map<String, Object> listToMap = this.listToMap(incomeList);
            map.putAll(listToMap);
        }
        return maps;
    }

    public List<Map<String,Object>> queryData1(Long monthId){
        List<Map<String, Object>> maps = lyxDayCheckMapper.queryDataByMonthId(monthId);
        List<String> strings = this.queryAllField();
        for (Map<String, Object> map : maps) {
            for (String string : strings) {
                map.put(string,0);
            }
            //查询每条数据的动态收入项金额
            List<Map<String, Object>> incomeList =  lyxDynamicIncomeMapper.queryIncomeDataByDayId(map.get("id").toString());
            Map<String, Object> listToMap = this.listToMap(incomeList);
            map.putAll(listToMap);

            //查询每条数据的动态收入项金额
            map.put("otherFieldLid",incomeList);

            List<LyxDayVouchar> voucharList = lyxDayVoucharMapper.selectDataByDayId(Long.valueOf(map.get("id").toString()));
            if(null == voucharList || voucharList.size()==0){
                map.put("isChenge",0);
            }else if(null != voucharList && voucharList.size()>0) {
                map.put("isChenge",1);
            }

        }
        return maps;
    }
    /**
     * listList<Map<String, Object>>转为map
     * @param dataList
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    public Map<String,Object> listToMap(List<Map<String, Object>> dataList){
        HashMap<String, Object> returnMap = new HashMap<>();
        for (Map<String, Object> map : dataList) {
            returnMap.put(map.get("name").toString(),map.get("avalues"));
        }
        return  returnMap;
    }
}
