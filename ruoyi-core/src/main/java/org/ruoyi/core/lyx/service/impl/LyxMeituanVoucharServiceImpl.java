package org.ruoyi.core.lyx.service.impl;

import java.util.List;

import org.ruoyi.core.lyx.domain.LyxMeituanVouchar;
import org.ruoyi.core.lyx.mapper.LyxMeituanVoucharMapper;
import org.ruoyi.core.lyx.service.ILyxMeituanVoucharService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-03-14
 */
@Service
public class LyxMeituanVoucharServiceImpl implements ILyxMeituanVoucharService
{
    @Autowired
    private LyxMeituanVoucharMapper lyxMeituanVoucharMapper;

    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public LyxMeituanVouchar selectLyxMeituanVoucharById(Long id)
    {
        return lyxMeituanVoucharMapper.selectLyxMeituanVoucharById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param lyxMeituanVouchar 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<LyxMeituanVouchar> selectLyxMeituanVoucharList(LyxMeituanVouchar lyxMeituanVouchar)
    {
        return lyxMeituanVoucharMapper.selectLyxMeituanVoucharList(lyxMeituanVouchar);
    }

    /**
     * 新增【请填写功能名称】
     * 
     * @param lyxMeituanVouchar 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertLyxMeituanVouchar(LyxMeituanVouchar lyxMeituanVouchar)
    {
        return lyxMeituanVoucharMapper.insertLyxMeituanVouchar(lyxMeituanVouchar);
    }

    /**
     * 修改【请填写功能名称】
     * 
     * @param lyxMeituanVouchar 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateLyxMeituanVouchar(LyxMeituanVouchar lyxMeituanVouchar)
    {
        return lyxMeituanVoucharMapper.updateLyxMeituanVouchar(lyxMeituanVouchar);
    }

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteLyxMeituanVoucharByIds(Long[] ids)
    {
        return lyxMeituanVoucharMapper.deleteLyxMeituanVoucharByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteLyxMeituanVoucharById(Long id)
    {
        return lyxMeituanVoucharMapper.deleteLyxMeituanVoucharById(id);
    }
}
