package org.ruoyi.core.lyx.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 lyx_meituan_vouchar
 * 
 * <AUTHOR>
 * @date 2024-03-14
 */
public class LyxMeituanVouchar extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 消费期间 */
    @Excel(name = "消费期间")
    private String consumptionTime;

    /** 凭证生成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "凭证生成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date voucharCreateTime;

    /** 摘要 */
    @Excel(name = "摘要")
    private String meituanAbstract;

    /** 银行存款-史总卡 */
    @Excel(name = "银行存款-史总卡")
    private BigDecimal yhckSzk;

    /** 财务费用-手续费 */
    @Excel(name = "财务费用-手续费")
    private BigDecimal cwfySxf;

    /** 应收账款-美团券 */
    @Excel(name = "应收账款-美团券")
    private BigDecimal yszkMtj;

    private Long voucherId;

    private Long withdrawId;
    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setConsumptionTime(String consumptionTime) 
    {
        this.consumptionTime = consumptionTime;
    }

    public String getConsumptionTime() 
    {
        return consumptionTime;
    }
    public void setVoucharCreateTime(Date voucharCreateTime) 
    {
        this.voucharCreateTime = voucharCreateTime;
    }

    public Date getVoucharCreateTime() 
    {
        return voucharCreateTime;
    }

    public void setYhckSzk(BigDecimal yhckSzk) 
    {
        this.yhckSzk = yhckSzk;
    }

    public BigDecimal getYhckSzk() 
    {
        return yhckSzk;
    }
    public void setCwfySxf(BigDecimal cwfySxf) 
    {
        this.cwfySxf = cwfySxf;
    }

    public BigDecimal getCwfySxf() 
    {
        return cwfySxf;
    }
    public void setYszkMtj(BigDecimal yszkMtj) 
    {
        this.yszkMtj = yszkMtj;
    }

    public BigDecimal getYszkMtj() 
    {
        return yszkMtj;
    }

    public String getMeituanAbstract() {
        return meituanAbstract;
    }

    public void setMeituanAbstract(String meituanAbstract) {
        this.meituanAbstract = meituanAbstract;
    }

    public Long getVoucherId() {
        return voucherId;
    }

    public void setVoucherId(Long voucherId) {
        this.voucherId = voucherId;
    }

    public Long getWithdrawId() {
        return withdrawId;
    }

    public void setWithdrawId(Long withdrawId) {
        this.withdrawId = withdrawId;
    }

    @Override
    public String toString() {
        return "LyxMeituanVouchar{" +
                "id=" + id +
                ", consumptionTime='" + consumptionTime + '\'' +
                ", voucharCreateTime=" + voucharCreateTime +
                ", meituanAbstract='" + meituanAbstract + '\'' +
                ", yhckSzk=" + yhckSzk +
                ", cwfySxf=" + cwfySxf +
                ", yszkMtj=" + yszkMtj +
                ", voucherId=" + voucherId +
                ", withdrawId=" + withdrawId +
                '}';
    }
}
