package org.ruoyi.core.lyx.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.lyx.domain.LyxDayVoucharDynamic;

/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-14
 */
public interface LyxDayVoucharDynamicMapper 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public LyxDayVoucharDynamic selectLyxDayVoucharDynamicById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param lyxDayVoucharDynamic 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<LyxDayVoucharDynamic> selectLyxDayVoucharDynamicList(LyxDayVoucharDynamic lyxDayVoucharDynamic);

    /**
     * 新增【请填写功能名称】
     * 
     * @param lyxDayVoucharDynamic 【请填写功能名称】
     * @return 结果
     */
    public int insertLyxDayVoucharDynamic(LyxDayVoucharDynamic lyxDayVoucharDynamic);

    /**
     * 修改【请填写功能名称】
     * 
     * @param lyxDayVoucharDynamic 【请填写功能名称】
     * @return 结果
     */
    public int updateLyxDayVoucharDynamic(LyxDayVoucharDynamic lyxDayVoucharDynamic);

    /**
     * 删除【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteLyxDayVoucharDynamicById(Long id);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLyxDayVoucharDynamicByIds(Long[] ids);

    public int batchInsert(List<LyxDayVoucharDynamic> list);

    List<String> queryFildList();

    List<Map<String, Object>> queryIncomeDataByDayId(@Param("id") String id);

    void deleteByDayId(@Param("id")Long id);
}
