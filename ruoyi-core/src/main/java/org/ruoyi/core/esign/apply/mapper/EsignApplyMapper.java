package org.ruoyi.core.esign.apply.mapper;

import java.util.List;
import org.ruoyi.core.esign.apply.domain.EsignApply;

/**
 * 富邦《担保明细清单》签章申请Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-06-26
 */
public interface EsignApplyMapper 
{
    /**
     * 查询富邦《担保明细清单》签章申请
     * 
     * @param id 富邦《担保明细清单》签章申请主键
     * @return 富邦《担保明细清单》签章申请
     */
    public EsignApply selectEsignApplyById(Long id);

    /**
     * 查询富邦《担保明细清单》签章申请列表
     * 
     * @param esignApply 富邦《担保明细清单》签章申请
     * @return 富邦《担保明细清单》签章申请集合
     */
    public List<EsignApply> selectEsignApplyList(EsignApply esignApply);

    /**
     * 新增富邦《担保明细清单》签章申请
     * 
     * @param esignApply 富邦《担保明细清单》签章申请
     * @return 结果
     */
    public int insertEsignApply(EsignApply esignApply);

    /**
     * 修改富邦《担保明细清单》签章申请
     * 
     * @param esignApply 富邦《担保明细清单》签章申请
     * @return 结果
     */
    public int updateEsignApply(EsignApply esignApply);

    /**
     * 删除富邦《担保明细清单》签章申请
     * 
     * @param id 富邦《担保明细清单》签章申请主键
     * @return 结果
     */
    public int deleteEsignApplyById(Long id);

    /**
     * 批量删除富邦《担保明细清单》签章申请
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEsignApplyByIds(Long[] ids);
    /**
     * 查询待数据校验
     * @return
     */
	public List<EsignApply> selectEsignApplyCheckList();

	/**
	 * 查询待签章
	 * @return
	 */
	public List<EsignApply> selectEsignApplyEsignList();
}
