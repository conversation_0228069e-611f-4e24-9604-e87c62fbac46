package org.ruoyi.core.badsystem.domain.tool;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@MappedTypes(List.class)  // 适配 List 类型
@MappedJdbcTypes(JdbcType.VARCHAR)  // 存储为字符串
public class StringToIntegerListTypeHandler extends BaseTypeHandler<List<Integer>> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<Integer> parameter, JdbcType jdbcType) throws SQLException {
        if (parameter != null && !parameter.isEmpty()) {
            String joined = parameter.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
            ps.setString(i, joined);
        } else {
            ps.setNull(i, jdbcType.TYPE_CODE); // 设置为 null 或适当的默认值
        }
    }

    @Override
    public List<Integer> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return parseValue(getStringValue(rs, columnName));
    }

    @Override
    public List<Integer> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return parseValue(getStringValue(rs, columnIndex));
    }

    @Override
    public List<Integer> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return parseValue(getStringValue(cs, columnIndex));
    }

    private String getStringValue(ResultSet rs, String columnName) throws SQLException {
        return rs.getString(columnName);
    }

    private String getStringValue(ResultSet rs, int columnIndex) throws SQLException {
        return rs.getString(columnIndex);
    }

    private String getStringValue(CallableStatement cs, int columnIndex) throws SQLException {
        return cs.getString(columnIndex);
    }

    private List<Integer> parseValue(String value) {
        if (value == null || value.trim().isEmpty()) {
            return Collections.emptyList(); // 返回空列表
        }

        return Arrays.stream(value.split(","))
                .map(this::parseInteger)
                .filter(Objects::nonNull) // 过滤掉解析失败的项
                .collect(Collectors.toList());
    }

    private Integer parseInteger(String str) {
        try {
            return Integer.valueOf(str);
        } catch (NumberFormatException e) {
            // 可以记录日志或抛出自定义异常
            return null; // 解析失败时返回 null
        }
    }
}
