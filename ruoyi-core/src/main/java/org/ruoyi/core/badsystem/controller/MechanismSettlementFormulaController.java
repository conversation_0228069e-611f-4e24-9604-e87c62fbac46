package org.ruoyi.core.badsystem.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.badsystem.domain.MechanismSettlementFormula;
import org.ruoyi.core.badsystem.service.IMechanismSettlementFormulaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 机构-结算公式明细Controller
 *
 * <AUTHOR>
 * @date 2024-12-04
 */
@RestController
@RequestMapping("/mechanism/settlement/formula")
public class MechanismSettlementFormulaController extends BaseController
{
    @Autowired
    private IMechanismSettlementFormulaService mechanismSettlementFormulaService;

    /**
     * 查询机构-结算公式明细列表
     */
    //@PreAuthorize("@ss.hasPermi('system:formula:list')")
    @GetMapping("/list")
    public TableDataInfo list(MechanismSettlementFormula mechanismSettlementFormula)
    {
        startPage();
        List<MechanismSettlementFormula> list = mechanismSettlementFormulaService.selectMechanismSettlementFormulaList(mechanismSettlementFormula);
        return getDataTable(list);
    }

    /**
     * 导出机构-结算公式明细列表
     */
    //@PreAuthorize("@ss.hasPermi('system:formula:export')")
    @Log(title = "机构-结算公式明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MechanismSettlementFormula mechanismSettlementFormula)
    {
        List<MechanismSettlementFormula> list = mechanismSettlementFormulaService.selectMechanismSettlementFormulaList(mechanismSettlementFormula);
        ExcelUtil<MechanismSettlementFormula> util = new ExcelUtil<MechanismSettlementFormula>(MechanismSettlementFormula.class);
        util.exportExcel(response, list, "机构-结算公式明细数据");
    }

    /**
     * 获取机构-结算公式明细详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:formula:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(mechanismSettlementFormulaService.selectMechanismSettlementFormulaById(id));
    }

    /**
     * 新增机构-结算公式明细
     */
    //@PreAuthorize("@ss.hasPermi('system:formula:add')")
    @Log(title = "机构-结算公式明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MechanismSettlementFormula mechanismSettlementFormula)
    {
        return toAjax(mechanismSettlementFormulaService.insertMechanismSettlementFormula(mechanismSettlementFormula));
    }

    /**
     * 修改机构-结算公式明细
     */
    //@PreAuthorize("@ss.hasPermi('system:formula:edit')")
    @Log(title = "机构-结算公式明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MechanismSettlementFormula mechanismSettlementFormula)
    {
        return toAjax(mechanismSettlementFormulaService.updateMechanismSettlementFormula(mechanismSettlementFormula));
    }

    /**
     * 删除机构-结算公式明细
     */
    //@PreAuthorize("@ss.hasPermi('system:formula:remove')")
    @Log(title = "机构-结算公式明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(mechanismSettlementFormulaService.deleteMechanismSettlementFormulaByIds(ids));
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<MechanismSettlementFormula> util = new ExcelUtil<MechanismSettlementFormula>(MechanismSettlementFormula.class);
        util.importTemplateExcel(response, "机构-结算公式");
    }

    /**
     * 结算账号明细导入
     *
     * @param file 导入文件
     * @throws Exception
     */
    @Log(title = "机构-结算公式导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        ExcelUtil<MechanismSettlementFormula> util = new ExcelUtil<MechanismSettlementFormula>(MechanismSettlementFormula.class);
        List<MechanismSettlementFormula> list = util.importExcel(file.getInputStream());
        for (MechanismSettlementFormula formula : list) {
            formula.setSettlementCycle(Arrays.asList(formula.getSettlementCycleString().split(",")));
        }
        return AjaxResult.success("导入成功",list);
    }
}
