package org.ruoyi.core.badsystem.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 不良系统-渠道业务对账单-财务收款单对象 bl_channel_business_reconciliation_collection
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class ChannelBusinessReconciliationCollection extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 业务对账单 id */
    //@Excel(name = "业务对账单 id")
    private Long businessReconciliationId;

    /** 日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date paymentDate;

    /** 收款金额 */
    @Excel(name = "收款金额")
    private BigDecimal amountCollected;

    /** 收款主体 */
    @Excel(name = "收款主体")
    private String payee;

    /** 付款主体 */
    @Excel(name = "付款主体")
    private String payment;

    @Excel(name = "备注")
    private String remark;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("businessReconciliationId", getBusinessReconciliationId())
            .append("paymentDate", getPaymentDate())
            .append("amountCollected", getAmountCollected())
            .append("payee", getPayee())
            .append("payment", getPayment())
            .append("remark", getRemark())
            .toString();
    }
}
