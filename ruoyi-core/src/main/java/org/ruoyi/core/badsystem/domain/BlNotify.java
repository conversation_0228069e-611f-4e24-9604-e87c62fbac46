package org.ruoyi.core.badsystem.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 不良提醒对象 bl_notify
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
public class BlNotify extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 通知模块 */
    @Excel(name = "通知模块")
    private String notifyModule;

    /** 相关url */
    @Excel(name = "相关url")
    private String url;

    /** 通知类型 0通知 1待办 */
    @Excel(name = "通知类型 0通知 1待办")
    private String notifyType;

    /** 通知内容 */
    @Excel(name = "通知内容")
    private String notifyMsg;

    /** 待处理人id */
    @Excel(name = "待处理人id")
    private Long disposeUser;

    /** 阅读状态 0未阅 1已阅 */
    @Excel(name = "阅读状态 0未阅 1已阅")
    private String viewFlag;

    /** 状态 0正常 1禁用 */
    @Excel(name = "状态 0正常 1禁用")
    private String status;

    /** 提醒正文 */
    @Excel(name = "提醒正文")
    private String remindText;

    /** 关联id */
    @Excel(name = "关联id")
    private Long correlationId;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setNotifyModule(String notifyModule)
    {
        this.notifyModule = notifyModule;
    }

    public String getNotifyModule()
    {
        return notifyModule;
    }
    public void setUrl(String url)
    {
        this.url = url;
    }

    public String getUrl()
    {
        return url;
    }
    public void setNotifyType(String notifyType)
    {
        this.notifyType = notifyType;
    }

    public String getNotifyType()
    {
        return notifyType;
    }
    public void setNotifyMsg(String notifyMsg)
    {
        this.notifyMsg = notifyMsg;
    }

    public String getNotifyMsg()
    {
        return notifyMsg;
    }
    public void setDisposeUser(Long disposeUser)
    {
        this.disposeUser = disposeUser;
    }

    public Long getDisposeUser()
    {
        return disposeUser;
    }
    public void setViewFlag(String viewFlag)
    {
        this.viewFlag = viewFlag;
    }

    public String getViewFlag()
    {
        return viewFlag;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setRemindText(String remindText)
    {
        this.remindText = remindText;
    }

    public String getRemindText()
    {
        return remindText;
    }
    public void setCorrelationId(Long correlationId)
    {
        this.correlationId = correlationId;
    }

    public Long getCorrelationId()
    {
        return correlationId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("notifyModule", getNotifyModule())
            .append("url", getUrl())
            .append("notifyType", getNotifyType())
            .append("notifyMsg", getNotifyMsg())
            .append("disposeUser", getDisposeUser())
            .append("viewFlag", getViewFlag())
            .append("status", getStatus())
            .append("remindText", getRemindText())
            .append("correlationId", getCorrelationId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
