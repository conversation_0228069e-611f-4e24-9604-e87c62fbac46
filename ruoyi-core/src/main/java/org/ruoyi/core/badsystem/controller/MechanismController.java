package org.ruoyi.core.badsystem.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.badsystem.domain.vo.MechanismImport;
import org.ruoyi.core.badsystem.domain.vo.MechanismVo;
import org.ruoyi.core.badsystem.service.IMechanismService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 机构Controller
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@RestController
@RequestMapping("/mechanism")
public class MechanismController extends BaseController
{
    @Autowired
    private IMechanismService mechanismService;

    /**
     * 查询机构列表
     */
    //@PreAuthorize("@ss.hasPermi('system:mechanism:list')")
    @GetMapping("/list")
    public TableDataInfo list(MechanismVo mechanism)
    {
        startPage();
        List<MechanismVo> list = mechanismService.selectMechanismList(mechanism);
        return getDataTable(list);
    }

    /**
     * 导出机构列表
     */
    //@PreAuthorize("@ss.hasPermi('system:mechanism:export')")
    @Log(title = "机构", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MechanismVo mechanism)
    {
        List<MechanismVo> list = mechanismService.selectMechanismList(mechanism);
        ExcelUtil<MechanismVo> util = new ExcelUtil<MechanismVo>(MechanismVo.class);
        util.exportExcel(response, list, "机构数据");
    }

    /**
     * 获取机构详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:mechanism:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(mechanismService.selectMechanismById(id));
    }

    /**
     * 新增机构
     */
    //@PreAuthorize("@ss.hasPermi('system:mechanism:add')")
    @Log(title = "机构", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MechanismVo mechanism)
    {
        return toAjax(mechanismService.insertMechanism(mechanism));
    }

    /**
     * 修改机构
     */
    //@PreAuthorize("@ss.hasPermi('system:mechanism:edit')")
    @Log(title = "机构", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MechanismVo mechanism)
    {
        return toAjax(mechanismService.updateMechanism(mechanism));
    }

    /**
     * 删除机构
     */
    //@PreAuthorize("@ss.hasPermi('system:mechanism:remove')")
    @Log(title = "机构", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(mechanismService.deleteMechanismByIds(ids));
    }

    /**
     * 修改机构
     */
    //@PreAuthorize("@ss.hasPermi('system:mechanism:edit')")
    @Log(title = "机构", businessType = BusinessType.UPDATE)
    @PutMapping("/updateCollaborationStatus")
    public AjaxResult updateCollaborationStatus(@RequestBody MechanismVo mechanism)
    {
        return toAjax(mechanismService.updateMechanismCollaborationStatus(mechanism));
    }

    @Log(title = "提交机构", businessType = BusinessType.UPDATE)
    @PutMapping("/processMechanismVo")
    public AjaxResult processMechanismVo(@RequestBody MechanismVo mechanism)
    {
        return toAjax(mechanismService.processMechanismVo(mechanism));
    }

    /**
     * 获取机构详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:mechanism:query')")
    @GetMapping(value = "/getByProcessId/{processId}")
    public AjaxResult selectMechanismByProcessId(@PathVariable("processId") String processId)
    {
        return AjaxResult.success(mechanismService.selectMechanismByProcessId(processId));
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<MechanismImport> util = new ExcelUtil<MechanismImport>(MechanismImport.class);
        util.importTemplateExcel(response, "机构导入模版");
    }

    @Log(title = "机构导入校验", businessType = BusinessType.IMPORT)
    @PostMapping("/importDataCheck")
    public AjaxResult importDataCheck(MultipartFile file) throws Exception {
        ExcelUtil<MechanismImport> util = new ExcelUtil<MechanismImport>(MechanismImport.class);
        List<MechanismImport> mechanismImportList = util.importExcel(file.getInputStream());
        Map<String,List<MechanismImport>> map = mechanismService.importDataCheck(mechanismImportList);
        return AjaxResult.success(map);
    }

    @Log(title = "机构导入数据", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(@RequestBody MechanismImport mechanismImport)  {
        String success = mechanismService.importData(mechanismImport.getSuccessList());
        return AjaxResult.success(success);
    }
}
