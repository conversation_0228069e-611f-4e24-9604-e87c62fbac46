package org.ruoyi.core.badsystem.mapper;

import org.ruoyi.core.badsystem.domain.FinancialSettlement;
import org.ruoyi.core.badsystem.domain.vo.FinancialSettlementVo;

import java.util.List;

/**
 * 不良系统-财务结算单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
public interface FinancialSettlementMapper
{
    /**
     * 查询不良系统-财务结算单
     *
     * @param id 不良系统-财务结算单主键
     * @return 不良系统-财务结算单
     */
    public FinancialSettlementVo selectFinancialSettlementById(Long id);

    /**
     * 查询不良系统-财务结算单列表
     *
     * @param financialSettlement 不良系统-财务结算单
     * @return 不良系统-财务结算单集合
     */
    public List<FinancialSettlementVo> selectFinancialSettlementList(FinancialSettlement financialSettlement);

    /**
     * 新增不良系统-财务结算单
     *
     * @param financialSettlement 不良系统-财务结算单
     * @return 结果
     */
    public int insertFinancialSettlement(FinancialSettlement financialSettlement);

    /**
     * 修改不良系统-财务结算单
     *
     * @param financialSettlement 不良系统-财务结算单
     * @return 结果
     */
    public int updateFinancialSettlement(FinancialSettlement financialSettlement);

    /**
     * 删除不良系统-财务结算单
     *
     * @param id 不良系统-财务结算单主键
     * @return 结果
     */
    public int deleteFinancialSettlementById(Long id);

    /**
     * 批量删除不良系统-财务结算单
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFinancialSettlementByIds(Long[] ids);

    /**
     * 获取当天创建次数
     */
    public int getCountByCreateTime(String createTime);

    /**
     * 查询不良系统-财务结算单 流程 id
     *
     * @param processId 不良系统-财务结算单主键
     * @return 不良系统-财务结算单
     */
    public FinancialSettlementVo selectFinancialSettlementByProcessId(String processId);
}
