package org.ruoyi.core.badsystem.mapper;

import org.ruoyi.core.badsystem.domain.ChannelBusinessReconciliationCollection;

import java.util.List;

/**
 * 不良系统-渠道业务对账单-财务收款单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface ChannelBusinessReconciliationCollectionMapper
{
    /**
     * 查询不良系统-渠道业务对账单-财务收款单
     *
     * @param id 不良系统-渠道业务对账单-财务收款单主键
     * @return 不良系统-渠道业务对账单-财务收款单
     */
    public ChannelBusinessReconciliationCollection selectChannelBusinessReconciliationCollectionById(Long id);

    /**
     * 查询不良系统-渠道业务对账单-财务收款单列表
     *
     * @param channelBusinessReconciliationCollection 不良系统-渠道业务对账单-财务收款单
     * @return 不良系统-渠道业务对账单-财务收款单集合
     */
    public List<ChannelBusinessReconciliationCollection> selectChannelBusinessReconciliationCollectionList(ChannelBusinessReconciliationCollection channelBusinessReconciliationCollection);

    /**
     * 新增不良系统-渠道业务对账单-财务收款单
     *
     * @param channelBusinessReconciliationCollection 不良系统-渠道业务对账单-财务收款单
     * @return 结果
     */
    public int insertChannelBusinessReconciliationCollection(ChannelBusinessReconciliationCollection channelBusinessReconciliationCollection);

    /**
     * 修改不良系统-渠道业务对账单-财务收款单
     *
     * @param channelBusinessReconciliationCollection 不良系统-渠道业务对账单-财务收款单
     * @return 结果
     */
    public int updateChannelBusinessReconciliationCollection(ChannelBusinessReconciliationCollection channelBusinessReconciliationCollection);

    /**
     * 删除不良系统-渠道业务对账单-财务收款单
     *
     * @param id 不良系统-渠道业务对账单-财务收款单主键
     * @return 结果
     */
    public int deleteChannelBusinessReconciliationCollectionById(Long id);

    /**
     * 批量删除不良系统-渠道业务对账单-财务收款单
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteChannelBusinessReconciliationCollectionByIds(Long[] ids);

    /**
     * 批量新增不良系统-渠道业务对账单-财务收款单
     *
     * @param channelBusinessReconciliationCollection 不良系统-渠道业务对账单-财务收款单
     * @return 结果
     */
    public int batchChannelBusinessReconciliationCollection(List<ChannelBusinessReconciliationCollection> channelBusinessReconciliationCollection);

}
