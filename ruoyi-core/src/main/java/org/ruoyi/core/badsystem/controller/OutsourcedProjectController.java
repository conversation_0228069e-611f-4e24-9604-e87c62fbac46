package org.ruoyi.core.badsystem.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.badsystem.domain.OutsourcedProject;
import org.ruoyi.core.badsystem.domain.vo.OutsourcedProjectVo;
import org.ruoyi.core.badsystem.service.IOutsourcedProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 不良系统-委外方案Controller
 *
 * <AUTHOR>
 * @date 2024-12-18
 */
@RestController
@RequestMapping("/outsourced/project")
public class OutsourcedProjectController extends BaseController
{
    @Autowired
    private IOutsourcedProjectService outsourcedProjectService;

    /**
     * 查询不良系统-委外方案列表
     */
    //@PreAuthorize("@ss.hasPermi('system:project:list')")
    @GetMapping("/list")
    public TableDataInfo list(OutsourcedProject outsourcedProject)
    {
        startPage();
        List<OutsourcedProjectVo> list = outsourcedProjectService.selectOutsourcedProjectList(outsourcedProject);
        return getDataTable(list);
    }

    /**
     * 导出不良系统-委外方案列表
     */
    //@PreAuthorize("@ss.hasPermi('system:project:export')")
    @Log(title = "不良系统-委外方案", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OutsourcedProject outsourcedProject)
    {
        List<OutsourcedProjectVo> list = outsourcedProjectService.selectOutsourcedProjectList(outsourcedProject);
        ExcelUtil<OutsourcedProjectVo> util = new ExcelUtil<OutsourcedProjectVo>(OutsourcedProjectVo.class);
        util.exportExcel(response, list, "不良系统-委外方案数据");
    }

    /**
     * 获取不良系统-委外方案详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:project:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(outsourcedProjectService.selectOutsourcedProjectById(id));
    }

    /**
     * 新增不良系统-委外方案
     */
    //@PreAuthorize("@ss.hasPermi('system:project:add')")
    @Log(title = "不良系统-委外方案", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OutsourcedProjectVo outsourcedProject)
    {
        return toAjax(outsourcedProjectService.insertOutsourcedProject(outsourcedProject));
    }

    /**
     * 修改不良系统-委外方案
     */
    //@PreAuthorize("@ss.hasPermi('system:project:edit')")
    @Log(title = "不良系统-委外方案", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OutsourcedProject outsourcedProject)
    {
        return toAjax(outsourcedProjectService.updateOutsourcedProject(outsourcedProject));
    }

    /**
     * 删除不良系统-委外方案
     */
    //@PreAuthorize("@ss.hasPermi('system:project:remove')")
    @Log(title = "不良系统-委外方案", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(outsourcedProjectService.deleteOutsourcedProjectByIds(ids));
    }


    /**
     * 获取不良系统-委外方案详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:project:query')")
    @GetMapping(value = "/getByProcessId/{processId}")
    public AjaxResult selectOutsourcedProjectByProcessId(@PathVariable("processId") String processId)
    {
        return AjaxResult.success(outsourcedProjectService.selectOutsourcedProjectByProcessId(processId));
    }
}
