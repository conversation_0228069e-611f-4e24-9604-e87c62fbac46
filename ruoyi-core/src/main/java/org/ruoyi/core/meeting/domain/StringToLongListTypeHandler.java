package org.ruoyi.core.meeting.domain;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class StringToLongListTypeHandler extends BaseTypeHandler<List<Long>> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<Long> parameter, JdbcType jdbcType) throws SQLException {
        if (parameter != null && !parameter.isEmpty()) {
            String joined = parameter.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
            ps.setString(i, joined);
        } else {
            ps.setString(i, ""); // 将空数组转换为空字符串
        }
    }
    @Override
    public List<Long> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return parseValue(getStringValue(rs, columnName));
    }
    @Override
    public List<Long> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return parseValue(getStringValue(rs, columnIndex));
    }
    @Override
    public List<Long> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return parseValue(getStringValue(cs, columnIndex));
    }
    private String getStringValue(ResultSet rs, String columnName) throws SQLException {
        return rs.getString(columnName);
    }
    private String getStringValue(ResultSet rs, int columnIndex) throws SQLException {
        return rs.getString(columnIndex);
    }
    private String getStringValue(CallableStatement cs, int columnIndex) throws SQLException {
        return cs.getString(columnIndex);
    }
    private List<Long> parseValue(String value) {
        if (value == null || value.trim().isEmpty()) {
            return Collections.emptyList(); // 返回空列表
        }
        return Arrays.stream(value.split(","))
                .map(this::parseLong)
                .filter(Objects::nonNull) // 过滤掉解析失败的项
                .collect(Collectors.toList());
    }
    private Long parseLong(String str) {
        try {
            return Long.valueOf(str);
        } catch (NumberFormatException e) {
            // 可以记录日志或抛出自定义异常
            return null; // 解析失败时返回 null
        }
    }
}
