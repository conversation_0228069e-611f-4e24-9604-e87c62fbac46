package org.ruoyi.core.notice.mapper;


import com.ruoyi.system.domain.SysCompany;
import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.notice.domain.TgNoticeMain;
import org.ruoyi.core.notice.domain.vo.TgIndexNoticeVo;
import org.ruoyi.core.xmglproject.domain.AuthDetailVo;

import java.util.List;

/**
 * 通知公告Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-11-11
 */
public interface TgNoticeMainMapper 
{
    /**
     * 查询通知公告
     * 
     * @param id 通知公告主键
     * @return 通知公告
     */
    public TgNoticeMain selectTgNoticeMainById(Long id);

    /**
     * 查询通知公告列表
     * 
     * @param tgNoticeMain 通知公告
     * @return 通知公告集合
     */
    public List<TgNoticeMain> selectTgNoticeMainList(TgNoticeMain tgNoticeMain);

    /**
     * 新增通知公告
     * 
     * @param tgNoticeMain 通知公告
     * @return 结果
     */
    public int insertTgNoticeMain(TgNoticeMain tgNoticeMain);

    /**
     * 修改通知公告
     * 
     * @param tgNoticeMain 通知公告
     * @return 结果
     */
    public int updateTgNoticeMain(TgNoticeMain tgNoticeMain);

    /**
     * 删除通知公告
     * 
     * @param tgNoticeMain 通知公告
     * @return 结果
     */
    public int deleteTgNoticeMainById(TgNoticeMain tgNoticeMain);

    /**
     * 批量删除通知公告
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTgNoticeMainByIds(Long[] ids);

    /**
     * 获取用户主岗的所属公司
     * @param userId
     * @return
     */
    SysCompany getUserHomePostCompany(String userId);

    /**
     * 获取首页面公告展示列表
     * @param tgNoticeMain
     * @return
     */
    List<TgIndexNoticeVo> selectIndexTgNoticeMainList(TgNoticeMain tgNoticeMain);

    /**
     * 查询用户有权限的公司
     * @param companyIdList
     * @return
     */
    List<SysCompany> selectCompanyListByCompanyIds(@Param("companyIdList")List<Long> companyIdList);
}
