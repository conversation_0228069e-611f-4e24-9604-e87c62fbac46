package org.ruoyi.core.notice.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Date;
import java.util.List;

/**
 * 通知公告对象 tg_notice_main
 * 
 * <AUTHOR>
 * @date 2024-11-11
 */
public class TgNoticeMain extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 通知名称 */
    @Excel(name = "通知名称")
    private String noticeName;

    /** 通知公告类型 */
    @Excel(name = "通知公告类型")
    private Long noticeType;

    /** 发布人 */
    @Excel(name = "发布人")
    private Long publisher;

    /** 发布人姓名 */
    private String publisherNickName;

    /** 发布公司 */
    @Excel(name = "发布公司")
    private Long publishCompany;

    /** 发布公司名称 */
    private String companyShortName;

    /** 内容 */
    @Excel(name = "内容")
    private String content;

    /** 发布状态(0未发布 1已发布) */
    @Excel(name = "发布状态(0未发布 1已发布)")
    private String publishStatus;

    /** 是否置顶(0否 1是) */
    @Excel(name = "是否置顶")
    private String isHeader;

    /** 是否重点(0否 1是) */
    @Excel(name = "是否重点")
    private String isEmphasis;

    /** 删除标识(0未删除 1已删除) */
    private String delFlag;

    /** 附件集合 */
    List<TgNoticesFile> noticesFileList;

    /** 公司id集合 */
    private List<Long> companyIdList;

    /** 创建人姓名 */
    private String createrNickName;

    /** 版本 */
    private Integer version;

    /** 分页参数 */
    private Integer pageSize;

    /** 分页参数 */
    private Integer pageNum;

    /** 登录用户id */
    private Long loginUserId;

    /** 公告类型数据集 */
    private String noticeDataName;

    /** 创建时间(条件筛选) */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    private Date sxCreateTime;

    /** 删除的附件 */
    private List<TgNoticesFile> deleteFiles;

    /** 新增的附件 */
    private List<TgNoticesFile> addFiles;

    public List<TgNoticesFile> getDeleteFiles() {
        return deleteFiles;
    }

    public void setDeleteFiles(List<TgNoticesFile> deleteFiles) {
        this.deleteFiles = deleteFiles;
    }

    public List<TgNoticesFile> getAddFiles() {
        return addFiles;
    }

    public void setAddFiles(List<TgNoticesFile> addFiles) {
        this.addFiles = addFiles;
    }

    public String getPublisherNickName() {
        return publisherNickName;
    }

    public void setPublisherNickName(String publisherNickName) {
        this.publisherNickName = publisherNickName;
    }

    public Date getSxCreateTime() {
        return sxCreateTime;
    }

    public void setSxCreateTime(Date sxCreateTime) {
        this.sxCreateTime = sxCreateTime;
    }

    public String getNoticeDataName() {
        return noticeDataName;
    }

    public void setNoticeDataName(String noticeDataName) {
        this.noticeDataName = noticeDataName;
    }

    public Long getLoginUserId() {
        return loginUserId;
    }

    public void setLoginUserId(Long loginUserId) {
        this.loginUserId = loginUserId;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public String getCompanyShortName() {
        return companyShortName;
    }

    public void setCompanyShortName(String companyShortName) {
        this.companyShortName = companyShortName;
    }

    public String getCreaterNickName() {
        return createrNickName;
    }

    public void setCreaterNickName(String createrNickName) {
        this.createrNickName = createrNickName;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public List<Long> getCompanyIdList() {
        return companyIdList;
    }

    public void setCompanyIdList(List<Long> companyIdList) {
        this.companyIdList = companyIdList;
    }

    public List<TgNoticesFile> getNoticesFileList() {
        return noticesFileList;
    }

    public void setNoticesFileList(List<TgNoticesFile> noticesFileList) {
        this.noticesFileList = noticesFileList;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setNoticeName(String noticeName) 
    {
        this.noticeName = noticeName;
    }

    public String getNoticeName() 
    {
        return noticeName;
    }
    public void setNoticeType(Long noticeType) 
    {
        this.noticeType = noticeType;
    }

    public Long getNoticeType() 
    {
        return noticeType;
    }

    public Long getPublisher() {
        return publisher;
    }

    public void setPublisher(Long publisher) {
        this.publisher = publisher;
    }

    public void setPublishCompany(Long publishCompany)
    {
        this.publishCompany = publishCompany;
    }

    public Long getPublishCompany()
    {
        return publishCompany;
    }
    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }
    public void setPublishStatus(String publishStatus) 
    {
        this.publishStatus = publishStatus;
    }

    public String getPublishStatus() 
    {
        return publishStatus;
    }
    public void setDelFlag(String delFlag)
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public String getIsHeader() {
        return isHeader;
    }

    public void setIsHeader(String isHeader) {
        this.isHeader = isHeader;
    }

    public String getIsEmphasis() {
        return isEmphasis;
    }

    public void setIsEmphasis(String isEmphasis) {
        this.isEmphasis = isEmphasis;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("noticeName", getNoticeName())
            .append("noticeType", getNoticeType())
            .append("publisher", getPublisher())
            .append("publishCompany", getPublishCompany())
            .append("content", getContent())
            .append("publishStatus", getPublishStatus())
            .append("isHeader", getIsHeader())
            .append("isEmphasis", getIsEmphasis())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
