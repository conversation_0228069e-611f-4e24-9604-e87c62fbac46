package org.ruoyi.core.notice.service;


import org.ruoyi.core.notice.domain.TgReadRelation;

import java.util.List;

/**
 * 公告-已读/未读Service接口
 * 
 * <AUTHOR>
 * @date 2024-11-11
 */
public interface ITgReadRelationService 
{
    /**
     * 查询公告-已读/未读
     * 
     * @param noticeId 公告-已读/未读主键
     * @return 公告-已读/未读
     */
    public TgReadRelation selectTgReadRelationByNoticeId(Long noticeId);

    /**
     * 查询公告-已读/未读列表
     * 
     * @param tgReadRelation 公告-已读/未读
     * @return 公告-已读/未读集合
     */
    public List<TgReadRelation> selectTgReadRelationList(TgReadRelation tgReadRelation);

    /**
     * 新增公告-已读/未读
     * 
     * @param tgReadRelation 公告-已读/未读
     * @return 结果
     */
    public int insertTgReadRelation(TgReadRelation tgReadRelation);

    /**
     * 修改公告-已读/未读
     * 
     * @param tgReadRelation 公告-已读/未读
     * @return 结果
     */
    public int updateTgReadRelation(TgReadRelation tgReadRelation);

    /**
     * 批量删除公告-已读/未读
     * 
     * @param noticeIds 需要删除的公告-已读/未读主键集合
     * @return 结果
     */
    public int deleteTgReadRelationByNoticeIds(Long[] noticeIds);

    /**
     * 删除公告-已读/未读信息
     * 
     * @param noticeId 公告-已读/未读主键
     * @return 结果
     */
    public int deleteTgReadRelationByNoticeId(Long noticeId);

    /**
     * 发布公告时，根据发布公司保存该公司下所有员工的未读公告数据
     * @param noticeId 公告id
     * @param publishCompanyId 公告所属公司id
     */
    void batchSave(Long noticeId, Long publishCompanyId);

    /**
     * 先发布公告，再授权的用户，同样可以看到所有当前公司下已发布的所有公告
     * @param companyIds
     * @param userIds
     * @return
     */
    int giveUserCompanyNoticeAuth(List<Long> companyIds, List<Long> userIds);
}