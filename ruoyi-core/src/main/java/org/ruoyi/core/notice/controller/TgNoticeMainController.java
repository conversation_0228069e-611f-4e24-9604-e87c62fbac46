package org.ruoyi.core.notice.controller;

import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.SysCompany;
import org.ruoyi.core.notice.domain.TgNoticeMain;
import org.ruoyi.core.notice.domain.TgReadDownloadHistory;
import org.ruoyi.core.notice.domain.vo.TgIndexNoticeVo;
import org.ruoyi.core.notice.service.ITgNoticeMainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.page.TableDataInfo;

import java.util.List;

/**
 * 通知公告Controller
 * 
 * <AUTHOR>
 * @date 2024-11-11
 */
@RestController
@RequestMapping("/noticeMain/notice")
public class TgNoticeMainController extends BaseController
{
    @Autowired
    private ITgNoticeMainService tgNoticeMainService;

    /**
     * 查询通知公告列表
     */
    // @PreAuthorize("@ss.hasPermi('noticeAnnouncement:management:list')")
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody TgNoticeMain tgNoticeMain)
    {
        List<TgNoticeMain> list = tgNoticeMainService.selectTgNoticeMainList(tgNoticeMain);
        return getDataTable(list);
    }

    /**
     * 导出通知公告列表
     */
    @Log(title = "通知公告", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TgNoticeMain tgNoticeMain)
    {
        List<TgNoticeMain> list = tgNoticeMainService.selectTgNoticeMainList(tgNoticeMain);
        ExcelUtil<TgNoticeMain> util = new ExcelUtil<TgNoticeMain>(TgNoticeMain.class);
        util.exportExcel(response, list, "通知公告数据");
    }

    /**
     * 获取通知公告详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(tgNoticeMainService.selectTgNoticeMainById(id));
    }

    /**
     * 新增通知公告
     */
    @Log(title = "通知公告", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TgNoticeMain tgNoticeMain)
    {
        return toAjax(tgNoticeMainService.insertTgNoticeMain(tgNoticeMain));
    }

    /**
     * 修改通知公告
     */
    @Log(title = "通知公告", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('noticeAnnouncement:management:update')")
    @PutMapping
    public AjaxResult edit(@RequestBody TgNoticeMain tgNoticeMain)
    {
        return toAjax(tgNoticeMainService.updateTgNoticeMain(tgNoticeMain));
    }

    /**
     * 删除通知公告
     */
    @Log(title = "通知公告", businessType = BusinessType.DELETE)
    @PreAuthorize("@ss.hasPermi('noticeAnnouncement:management:delete')")
	@DeleteMapping("/deleteNotice/{id}")
    public AjaxResult remove(@PathVariable("id") Long id)
    {
        return toAjax(tgNoticeMainService.deleteTgNoticeMainById(id));
    }

    /**
     * 获取用户的主岗位所属公司
     */
    @GetMapping("/getHomePostCompany")
    public AjaxResult getUserHomePostCompany(@RequestParam("userId") String userId){
        return AjaxResult.success(tgNoticeMainService.getUserHomePostCompany(userId));
    }

    /**
     * 发布/撤回公告
     */
    @PutMapping("/publishRevocation")
    public AjaxResult publishRevocationNotice(@RequestBody TgNoticeMain tgNoticeMain){
        return toAjax(tgNoticeMainService.publishRevocationNoticeMain(tgNoticeMain));
    }

    /**
     * 首页面公告列表
     */
    @PostMapping("/indexNotice")
    public TableDataInfo indexNoticesList(@RequestBody TgNoticeMain tgNoticeMain){
        return getDataTable(tgNoticeMainService.selectIndexNoticeList(tgNoticeMain));
    }

    /**
     * 导出首页面公告列表
     */
    //@Log(title = "通知公告", businessType = BusinessType.EXPORT)
    //@PostMapping("/indexExport")
    //public void indexExport(HttpServletResponse response, TgNoticeMain tgNoticeMain)
    //{
    //    List<TgIndexNoticeVo> list = tgNoticeMainService.selectIndexNoticeList(tgNoticeMain);
    //    ExcelUtil<TgIndexNoticeVo> util = new ExcelUtil<TgIndexNoticeVo>(TgIndexNoticeVo.class);
    //    util.exportExcel(response, list, "通知公告数据");
    //}

    /**
     * 查询用户有权限的公司
     */
    @GetMapping("/getAuthCompany")
    public List<SysCompany> getAuthCompany(@RequestParam("userId") Long userId){
        return tgNoticeMainService.selectAuthCompanyListByUserId(userId);
    }
}
