package org.ruoyi.core.OASettingUp.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.OASettingUp.domain.NoaWorkflowRemind;
import org.ruoyi.core.OASettingUp.service.INoaWorkflowRemindService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 流程提醒配置Controller
 *
 * <AUTHOR>
 * @date 2023-12-26
 *
 */
@RestController
@RequestMapping("/noa/OASettingUp")
public class NoaWorkflowRemindController extends BaseController
{
    @Autowired
    private INoaWorkflowRemindService noaWorkflowRemindService;

    /**
     * 查询流程提醒配置列表
     */
    @GetMapping("/list")
    public TableDataInfo list(NoaWorkflowRemind noaWorkflowRemind)
    {
        startPage();
        List<NoaWorkflowRemind> list = noaWorkflowRemindService.selectNoaWorkflowRemindList(noaWorkflowRemind);
        return getDataTable(list);
    }

    /**
     * 导出流程提醒配置列表
     */
    @Log(title = "OASettingUp", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, NoaWorkflowRemind noaWorkflowRemind)
    {
        List<NoaWorkflowRemind> list = noaWorkflowRemindService.selectNoaWorkflowRemindList(noaWorkflowRemind);
        ExcelUtil<NoaWorkflowRemind> util = new ExcelUtil<NoaWorkflowRemind>(NoaWorkflowRemind.class);
        util.exportExcel(response, list, "OASettingUp数据");
    }

    /**
     * 获取流程配置详细信息
     */
    @GetMapping
    public AjaxResult getInfo(NoaWorkflowRemind noaWorkflowRemind)
    {
        return AjaxResult.success(noaWorkflowRemindService.selectNoaWorkflowRemindByFlowIdAndNodeId(noaWorkflowRemind));
    }

    /**
     * 修改流程提醒配置信息
     */
    @Log(title = "OASettingUp", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody NoaWorkflowRemind noaWorkflowRemind)
    {
        return toAjax(noaWorkflowRemindService.updateNoaWorkflowRemind(noaWorkflowRemind));
    }

    /**
     * 删除流程提醒配置
     */
    @Log(title = "OASettingUp", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(noaWorkflowRemindService.deleteNoaWorkflowRemindByFlowId(ids));
    }

    /**
     * 新增流程提醒配置
     */
    @Log(title = "OASettingUp", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody NoaWorkflowRemind noaWorkflowRemind)
    {
        return toAjax(noaWorkflowRemindService.insertNoaWorkflowRemind(noaWorkflowRemind));
    }

    /**
     * 用户确认代办消息
     */
    @Log(title = "OASettingUp", businessType = BusinessType.INSERT)
    @GetMapping("/setRemindState")
    public AjaxResult setRemindState(String remindId)
    {
        return toAjax(noaWorkflowRemindService.setRemindState(remindId));
    }
}
