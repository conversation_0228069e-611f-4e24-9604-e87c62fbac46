package org.ruoyi.core.kaohe.mapper;

import org.ruoyi.core.kaohe.domain.AchievementEnter;
import org.ruoyi.core.kaohe.domain.vo.AchievementEnterVo;
import org.ruoyi.core.oasystem.domain.OaProjectDeploy;

import java.util.List;

/**
 * 项目业绩录入Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
public interface AchievementEnterMapper
{
    /**
     * 查询项目业绩录入
     *
     * @param id 项目业绩录入主键
     * @return 项目业绩录入
     */
    public AchievementEnter selectAchievementEnterById(Long id);


    public AchievementEnterVo getAchievementEnter(AchievementEnterVo achievementEnter);


    public AchievementEnterVo getProjectInfo(Long id);

    /**
     * 查询项目业绩录入列表
     *
     * @param achievementEnter 项目业绩录入
     * @return 项目业绩录入集合
     */
    public List<AchievementEnterVo> selectAchievementEnterList(AchievementEnterVo achievementEnter);

    public List<AchievementEnterVo> selectAchievementEnterSumList(AchievementEnterVo achievementEnter);

    public List<AchievementEnterVo> selectAchievementEnterSumListByYear(AchievementEnterVo achievementEnter);


    public AchievementEnterVo getAchievementEnterSum(AchievementEnterVo achievementEnter);

    public AchievementEnterVo getAchievementEnterSumByYear(AchievementEnterVo achievementEnter);

    /**
     * 新增项目业绩录入
     *
     * @param achievementEnter 项目业绩录入
     * @return 结果
     */
    public int insertAchievementEnter(AchievementEnter achievementEnter);
    public int batchInsertKhAchievementEnter(List<AchievementEnter> achievementEnter);

    /**
     * 修改项目业绩录入
     *
     * @param achievementEnter 项目业绩录入
     * @return 结果
     */
    public int updateAchievementEnter(AchievementEnter achievementEnter);

    /**
     * 删除项目业绩录入
     *
     * @param id 项目业绩录入主键
     * @return 结果
     */
    public int deleteAchievementEnterById(Long id);

    /**
     * 批量删除项目业绩录入
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAchievementEnterByIds(Long[] ids);

    public int updateAchievementEnterList(AchievementEnterVo achievementEnter);

    public List<OaProjectDeploy> selectOaProjectDeployList(OaProjectDeploy oaProjectDeploy);

    public List<AchievementEnterVo> getProcessId(AchievementEnterVo achievementEnter);
}
