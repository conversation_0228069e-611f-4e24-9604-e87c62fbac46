package org.ruoyi.core.kaohe.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginBody;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.kaohe.domain.vo.CheckConfigVo;
import org.ruoyi.core.kaohe.service.ICheckConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 考核配置Controller
 *
 * <AUTHOR>
 * @date 2024-07-29
 */
@RestController
@RequestMapping("/check/config")
public class CheckConfigController extends BaseController
{
    @Autowired
    private ICheckConfigService checkConfigService;

    /**
     * 查询考核配置列表
     */
    //@PreAuthorize("@ss.hasPermi('system:config:list')")
    @GetMapping("/list")
    public TableDataInfo list(CheckConfigVo checkConfig)
    {
        //startPage();
        List<CheckConfigVo> list = checkConfigService.selectCheckConfigList(checkConfig);
        return getDataTable(list);
    }

    /**
     * 导出考核配置列表
     */
    //@PreAuthorize("@ss.hasPermi('system:config:export')")
    @Log(title = "考核配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CheckConfigVo checkConfig)
    {
        List<CheckConfigVo> list = checkConfigService.selectCheckConfigList(checkConfig);
        ExcelUtil<CheckConfigVo> util = new ExcelUtil<CheckConfigVo>(CheckConfigVo.class);
        util.exportExcel(response, list, "考核配置数据");
    }

    /**
     * 获取考核配置详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:config:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(checkConfigService.selectCheckConfigById(id));
    }


    /**
     * 获取考核配置详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:config:query')")
    @GetMapping(value = "/getCheckConfig")
    public AjaxResult getCheckConfig(CheckConfigVo checkConfig)
    {
        return AjaxResult.success(checkConfigService.getCheckConfig(checkConfig));
    }

    /**
     * 新增考核配置
     */
    //@PreAuthorize("@ss.hasPermi('system:config:add')")
    @Log(title = "考核配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CheckConfigVo checkConfig)
    {
        return toAjax(checkConfigService.insertCheckConfig(checkConfig));
    }

    /**
     * 修改考核配置
     */
    //@PreAuthorize("@ss.hasPermi('system:config:edit')")
    @Log(title = "考核配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CheckConfigVo checkConfig)
    {
        return toAjax(checkConfigService.updateCheckConfig(checkConfig));
    }

    /**
     * 删除考核配置
     */
    //@PreAuthorize("@ss.hasPermi('system:config:remove')")
    @Log(title = "考核配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(checkConfigService.deleteCheckConfigByIds(ids));
    }


    @PostMapping("/checkPwd")
    public AjaxResult checkPwd(@RequestBody LoginBody loginBody)
    {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String msg = checkConfigService.checkPwd(loginBody.getPassword());
        return ajax;
    }

    /**
     * 新增考核配置
     */
    //@PreAuthorize("@ss.hasPermi('system:config:add')")
    @Log(title = "考核配置", businessType = BusinessType.INSERT)
    @PostMapping("/insertCheckConfigByTemplate")
    public AjaxResult insertCheckConfigByTemplate(@RequestBody CheckConfigVo checkConfig)
    {
        return AjaxResult.success(checkConfigService.insertCheckConfigByTemplate(checkConfig));
    }


    //@PreAuthorize("@ss.hasPermi('system:plan:add')")
    @Log(title = "考核配置流程", businessType = BusinessType.INSERT)
    @PostMapping("/insertCheckConfigProcess")
    public AjaxResult insertCheckConfigProcess(@RequestBody CheckConfigVo checkConfig)
    {
        return toAjax(checkConfigService.insertCheckConfigProcess(checkConfig));
    }


    @GetMapping("/selectCheckConfigProcessList")
    public TableDataInfo selectAnnualPlanProcessList(CheckConfigVo checkConfig)
    {
        List<CheckConfigVo> list = checkConfigService.selectCheckConfigProcessList(checkConfig);
        return getDataTable(list);
    }

    @Log(title = "通过考核录入", businessType = BusinessType.INSERT)
    @PutMapping("/passCheckConfigProcess")
    public AjaxResult passCheckConfigProcess(@RequestBody CheckConfigVo checkConfig)
    {
        return toAjax(checkConfigService.passCheckConfigProcess(checkConfig));
    }

    @Log(title = "废弃考核录入", businessType = BusinessType.INSERT)
    @PutMapping("/unpassCheckConfigProcess")
    public AjaxResult unpassCheckConfigProcess(@RequestBody CheckConfigVo checkConfig)
    {
        return toAjax(checkConfigService.unpassCheckConfigProcess(checkConfig));
    }
}
