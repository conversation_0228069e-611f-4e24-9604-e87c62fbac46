package org.ruoyi.core.kaohe.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import org.ruoyi.core.kaohe.domain.util.ExcelCheck;

import java.math.BigDecimal;

/**
 * 考核结果对象 kh_check_result
 *
 * <AUTHOR>
 * @date 2024-08-05
 */

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CheckResult extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 年度 */
    //@ExcelCheck(name = "年度")
    private String year;

    /** 所属类型  1.公司 2.部门 3.个人 */
    //@ExcelCheck(name = "所属类型  1.公司 2.部门 3.个人")
    private String type;

    /** 所属公司 */
    //@ExcelCheck(name = "所属公司")
    private Long companyId;

    /** 所属部门 */
    //@ExcelCheck(name = "所属部门")
    private Long deptId;

    /** 所属个人 */
    //@ExcelCheck(name = "所属个人")
    private Long userId;

    /** 季度 */
    //@ExcelCheck(name = "季度")
    private Integer quarter;

    /** 总项目指标(万元) */
    //@ExcelCheck(name = "总项目指标(万元)")
    private BigDecimal totalIndex;

    /** 分配项目指标(万元) */
    //@ExcelCheck(name = "分配项目指标(万元)")
    private BigDecimal distributionIndex;

    /** 自拓项目指标(万元) */
    //@ExcelCheck(name = "自拓项目指标(万元)")
    private BigDecimal extensionIndex;

    /** 自拓银行 */
    //@ExcelCheck(name = "自拓银行")
    private BigDecimal extensionBank;

    /** 绩效工资占比(%) */
    //@ExcelCheck(name = "绩效工资占比(%)")
    private BigDecimal achievementWagesProportion;

    /** 分配占比(%) */
    //@ExcelCheck(name = "分配占比(%)")
    private BigDecimal distributionProportion;

    /** 自拓占比(%) */
    //@ExcelCheck(name = "自拓占比(%)")
    private BigDecimal extensionProportion;

    /** 项目完成总业绩(万元) */
    //@ExcelCheck(name = "总项目完成业绩情况(万元)")
    private BigDecimal completeTotalIndex;

    /** 项目薪资总占比(%) */
    //@ExcelCheck(name = "项目薪资总占比(%)")
    private BigDecimal projectSalaryTotalProportion;

    /** 分配项目业绩完成情况(万元) */
    //@ExcelCheck(name = "分配项目业绩完成情况(万元)")
    private BigDecimal completeDistributionIndex;

    /** 项目薪资分配占比(%) */
    @ExcelCheck(name = "校准前分配指标薪资占比(%)")
    private BigDecimal projectSalaryDistributionProportion;

    /** 分配项目业绩偏差(万元) */
    //@ExcelCheck(name = "分配项目业绩偏差(万元)")
    private BigDecimal distributionIndexDeviation;

    /** 人事校准-分配项目 */
    //@ExcelCheck(name = "人事校准-分配项目")
    private BigDecimal calibrationDistributionIndex;

    /** 分配指标校准后完成状态  1.未完成 2.已完成*/
    @ExcelCheck(name = "校准后分配指标完成情况" ,sort = 10)
    private String calibrationDistributionIndexState;

    /** 分配指标校准后薪资占比(%) */
    @ExcelCheck(name = "本期分配指标校准占比(%)")
    private BigDecimal calibrationProjectSalaryDistributionProportion;

    /** 自拓项目业绩完成情况(万元) */
    //@ExcelCheck(name = "自拓项目业绩完成情况(万元)")
    private BigDecimal completeExtensionIndex;

    /** 项目薪资自拓占比(%) */
    @ExcelCheck(name = "校准前自拓项目指标薪资占比(%)")
    private BigDecimal projectSalaryExtensionProportion;

    /** 自拓项目业绩偏差(万元) */
    //@ExcelCheck(name = "自拓项目业绩偏差(万元)")
    private BigDecimal extensionIndexDeviation;

    /** 人事校准-自拓项目 */
    //@ExcelCheck(name = "人事校准-自拓项目")
    private BigDecimal calibrationExtensionIndex;

//    /** 自拓指标校准后完成状态 1.未完成 2.已完成-项目*/
//    @ExcelCheck(name = "自拓指标校准后完成状态")
//    private String calibrationExtensionIndexState;

    /** 自拓项目指标校准后薪资占比(%) */
    @ExcelCheck(name = "本期自拓项目指标校准占比(%)")
    private BigDecimal calibrationProjectSalaryExtensionProportion;

    /** 自拓银行业绩完成情况(家) */
    //@ExcelCheck(name = "自拓银行业绩完成情况(家)")
    private BigDecimal completeExtensionBank;

    /** 薪资自拓银行占比(%) */
    @ExcelCheck(name = "校准前自拓银行指标薪资占比")
    private BigDecimal bankSalaryExtensionProportion;

    /** 自拓银行完成情况偏差(家) */
    //@ExcelCheck(name = "自拓银行完成情况偏差(家)")
    private BigDecimal extensionBankDeviation;

    /** 人事校准-自拓银行(家) */
    @ExcelCheck(name = "本期自拓银行指标校准值")
    private BigDecimal calibrationExtensionBank;

    /** 自拓银行完成情况偏差(家) */
    //@ExcelCheck(name = "自拓银行完成情况偏差(家)")
    private BigDecimal calibrationExtensionBankDeviation;

    /** 自拓银行指标校准后完成状态 */
    //@ExcelCheck(name = "自拓银行指标校准后完成状态")
    private String calibrationExtensionIndexBankState;

    /** 分配项目指标校准后完成状态 */
    //@ExcelCheck(name = "自拓项目指标校准后完成状态")
    private String calibrationExtensionIndexProjectState;

    /** 自拓银行指标校准后薪资占比(%) */
    //@ExcelCheck(name = "自拓银行指标校准后薪资占比(%)")
    private BigDecimal calibrationProjectSalaryBankProportion;

    /** 校准项目薪资占比(%) */
    //@ExcelCheck(name = "校准项目薪资占比(%)")
    private BigDecimal calibrationProjectSalaryTotalProportion;

    /** 本年度累计项目业绩偏差分配项目 */
    //@ExcelCheck(name = "本年度累计项目业绩偏差分配项目")
    private BigDecimal distributionIndexDeviationYear;

    /** 本年度累计项目业绩偏差自拓项目  */
    //@ExcelCheck(name = "本年度累计项目业绩偏差自拓项目 ")
    private BigDecimal extensionIndexDeviationYear;

    /** 本年度累计项目业绩偏差自拓银行  */
    //@ExcelCheck(name = "本年度累计项目业绩偏差自拓银行 ")
    private BigDecimal extensionBankDeviationYear;

    /** 校准后年度累计项目业绩偏差分配项目 */
    //@ExcelCheck(name = "校准后年度累计项目业绩偏差分配项目")
    private BigDecimal distributionIndexDeviationCalibration;

    /** 校准后年度累计项目业绩偏差自拓项目  */
    //@ExcelCheck(name = "校准后年度累计项目业绩偏差自拓项目 ")
    private BigDecimal extensionIndexDeviationCalibration;

    /** 校准后年度累计项目业绩偏差自拓银行 */
    //@ExcelCheck(name = "校准后年度累计项目业绩偏差自拓银行")
    private BigDecimal extensionBankDeviationCalibration;

    /** 前三季度补充业绩偏差值累计后薪资占比-分配项目 */
    //@ExcelCheck(name = "前三季度补充业绩偏差值累计后薪资占比-分配项目")
    private BigDecimal distributionIndexDeviationProportionQ3;

    /** 前三季度补充业绩偏差值累计后薪资占比-自拓项目  */
    //@ExcelCheck(name = "前三季度补充业绩偏差值累计后薪资占比-自拓项目 ")
    private BigDecimal extensionIndexDeviationProportionQ3;

    /** 前三季度补充业绩偏差值累计后薪资占比-银行  */
    //@ExcelCheck(name = "前三季度补充业绩偏差值累计后薪资占比-银行 ")
    private BigDecimal extensionBankDeviationProportionQ3;

    /** 人事校准-年终 分配项目*/
    private BigDecimal calibrationDistributionIndexYearend;

    /** 人事校准-年终 自拓项目*/
    private BigDecimal calibrationExtensionIndexYearend;

    /** 人事校准-年终 自拓银行*/
    private BigDecimal calibrationExtensionBankYearend;

    /** 校准后年度累计项目业绩偏差分配项目-年终 */
    //@ExcelCheck(name = "校准后年度累计项目业绩偏差分配项目-年终")
    private BigDecimal distributionIndexDeviationCalibrationYearend;

    /** 校准后年度累计项目业绩偏差自拓项目-年终 */
    //@ExcelCheck(name = "校准后年度累计项目业绩偏差自拓项目-年终")
    private BigDecimal extensionIndexDeviationCalibrationYearend;

    /** 校准后年度累计项目业绩偏差自拓银行-年终 */
    //@ExcelCheck(name = "校准后年度累计项目业绩偏差自拓银行-年终")
    private Long extensionBankDeviationCalibrationYearend;

    /** 分配指标校准后薪资占比(%)-年终 */
    //@ExcelCheck(name = "分配指标校准后薪资占比(%)-年终")
    private BigDecimal calibrationProjectSalaryDistributionProportionYearend;

    /** 自拓项目指标校准后薪资占比(%)-年终 */
    //@ExcelCheck(name = "自拓项目指标校准后薪资占比(%)-年终")
    private BigDecimal calibrationProjectSalaryExtensionProportionYearend;

    /** 自拓银行指标校准后薪资占比(%)-年终 */
    //@ExcelCheck(name = "自拓银行指标校准后薪资占比(%)-年终")
    private BigDecimal calibrationProjectSalaryBankProportionYearend;

    /** 分配指标校准后完成状态-年终 */
    //@ExcelCheck(name = "分配指标校准后完成状态-年终")
    private String calibrationDistributionIndexStateYearend;

    /** 自拓银行指标校准后完成状态-年终 */
    //@ExcelCheck(name = "自拓银行指标校准后完成状态-年终")
    private String calibrationExtensionIndexBankStateYearend;

    /** 自拓项目指标校准后完成状态-年终 */
    //@ExcelCheck(name = "自拓项目指标校准后完成状态-年终")
    private String calibrationExtensionIndexProjectStateYearend;

    /** 补充至第四季度薪资占比-分配项目 */
    //@ExcelCheck(name = "补充至第四季度薪资占比-分配项目")
    private BigDecimal distributionIndexDeviationProportionQ4;

    /** 补充至第四季度薪资占比-自拓项目  */
    //@ExcelCheck(name = "补充至第四季度薪资占比-自拓项目 ")
    private BigDecimal extensionIndexDeviationProportionQ4;

    /** 补充至第四季度薪资占比-银行  */
    //@ExcelCheck(name = "补充至第四季度薪资占比-银行 ")
    private BigDecimal extensionBankDeviationProportionQ4;

    /** 确认状态 (1未确认，2确认中，3已确认) */
    private String confirmStatus;

    /** 状态 (1未提交，2审核中，3审核通过，4审核不通过) */
    //@ExcelCheck(name = "状态 (1未提交，2审核中，3审核通过，4审核不通过)")
    private String state;

}
