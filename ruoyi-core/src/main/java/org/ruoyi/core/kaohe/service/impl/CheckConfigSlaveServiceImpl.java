package org.ruoyi.core.kaohe.service.impl;

import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.kaohe.domain.CheckConfigSlave;
import org.ruoyi.core.kaohe.mapper.CheckConfigSlaveMapper;
import org.ruoyi.core.kaohe.service.ICheckConfigSlaveService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 考核配置从Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-29
 */
@Service
public class CheckConfigSlaveServiceImpl implements ICheckConfigSlaveService
{
    @Autowired
    private CheckConfigSlaveMapper checkConfigSlaveMapper;

    /**
     * 查询考核配置从
     *
     * @param id 考核配置从主键
     * @return 考核配置从
     */
    @Override
    public CheckConfigSlave selectCheckConfigSlaveById(Long id)
    {
        return checkConfigSlaveMapper.selectCheckConfigSlaveById(id);
    }

    /**
     * 查询考核配置从列表
     *
     * @param checkConfigSlave 考核配置从
     * @return 考核配置从
     */
    @Override
    public List<CheckConfigSlave> selectCheckConfigSlaveList(CheckConfigSlave checkConfigSlave)
    {
        return checkConfigSlaveMapper.selectCheckConfigSlaveList(checkConfigSlave);
    }

    /**
     * 新增考核配置从
     *
     * @param checkConfigSlave 考核配置从
     * @return 结果
     */
    @Override
    public int insertCheckConfigSlave(CheckConfigSlave checkConfigSlave)
    {
        checkConfigSlave.setCreateTime(DateUtils.getNowDate());
        return checkConfigSlaveMapper.insertCheckConfigSlave(checkConfigSlave);
    }

    @Override
    public int batchCheckConfigSlave(List<CheckConfigSlave> checkConfigSlaves){
        return checkConfigSlaveMapper.batchCheckConfigSlave(checkConfigSlaves);
    }

    @Override
    public int replaceCheckConfigSlave(List<CheckConfigSlave> checkConfigSlaves){
        return checkConfigSlaveMapper.replaceCheckConfigSlave(checkConfigSlaves);
    }

    /**
     * 修改考核配置从
     *
     * @param checkConfigSlave 考核配置从
     * @return 结果
     */
    @Override
    public int updateCheckConfigSlave(CheckConfigSlave checkConfigSlave)
    {
        checkConfigSlave.setUpdateTime(DateUtils.getNowDate());
        return checkConfigSlaveMapper.updateCheckConfigSlave(checkConfigSlave);
    }

    /**
     * 批量删除考核配置从
     *
     * @param ids 需要删除的考核配置从主键
     * @return 结果
     */
    @Override
    public int deleteCheckConfigSlaveByIds(Long[] ids)
    {
        return checkConfigSlaveMapper.deleteCheckConfigSlaveByIds(ids);
    }

    /**
     * 删除考核配置从信息
     *
     * @param id 考核配置从主键
     * @return 结果
     */
    @Override
    public int deleteCheckConfigSlaveById(Long id)
    {
        return checkConfigSlaveMapper.deleteCheckConfigSlaveById(id);
    }
}
