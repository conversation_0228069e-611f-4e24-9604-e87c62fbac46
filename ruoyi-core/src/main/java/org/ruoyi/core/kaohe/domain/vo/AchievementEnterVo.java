package org.ruoyi.core.kaohe.domain.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.ruoyi.core.kaohe.domain.AchievementEnter;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 项目业绩录入对象 kh_achievement_enter
 *
 * <AUTHOR>
 * @date 2024-07-24
 */

@Data
public class AchievementEnterVo extends AchievementEnter
{

   //private Long companyId;

   private String projectName;

   private String companyName;

   private String companyShortName;

   //业务责任人集合
   private List<Map<String,Object>> yewuList;


   @JSONField(serialize = false, deserialize = false)
   private Set<String> years;

   @JSONField(serialize = false, deserialize = false)
   private Set<Long> companyIds;

   @JSONField(serialize = false, deserialize = false)
   public Set<Long> projectIds;

   @JSONField(serialize = false, deserialize = false)
   private Set<Long> ids;

   private String processId;

   private Integer quarter;

   private List<AchievementEnterQuarter> achievementEnterQuarterList;

   private Set<Integer> checkResultQuarter;

   private Long oaApplyId;


   private List<Long> authorityCompanyIds;

   private List<Long> authorityProjectIds;
}
