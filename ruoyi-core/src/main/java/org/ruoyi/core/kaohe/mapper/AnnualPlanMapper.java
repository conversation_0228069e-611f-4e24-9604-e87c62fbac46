package org.ruoyi.core.kaohe.mapper;

import org.ruoyi.core.kaohe.domain.AnnualPlan;
import org.ruoyi.core.kaohe.domain.vo.AnnualPlanExportExcel;
import org.ruoyi.core.kaohe.domain.vo.AnnualPlanImportExcel;
import org.ruoyi.core.kaohe.domain.vo.AnnualPlanVo;

import java.util.List;
import java.util.Set;

/**
 * 年度计划Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
public interface AnnualPlanMapper
{
    /**
     * 查询年度计划
     *
     * @param id 年度计划主键
     * @return 年度计划
     */
    public AnnualPlan selectAnnualPlanById(Long id);

    /**
     * 查询年度计划列表
     *
     * @param annualPlan 年度计划
     * @return 年度计划集合
     */
    public List<AnnualPlanVo> selectAnnualPlanList(AnnualPlanVo annualPlan);

     public List<AnnualPlanVo> selectAnnualPlanTree(AnnualPlanVo annualPlan);

    /**
     * 新增年度计划
     *
     * @param annualPlan 年度计划
     * @return 结果
     */
    public int insertAnnualPlan(AnnualPlan annualPlan);



    public int insertAnnualPlanImportExcel(AnnualPlanImportExcel annualPlan);
    /**
     * 修改年度计划
     *
     * @param annualPlan 年度计划
     * @return 结果
     */
    public int updateAnnualPlan(AnnualPlan annualPlan);

    /**
     * 删除年度计划
     *
     * @param id 年度计划主键
     * @return 结果
     */
    public int deleteAnnualPlanById(Long id);

    /**
     * 批量删除年度计划
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAnnualPlanByIds(Long[] ids);

    public List<AnnualPlanVo> selectAnnualPlanListByCompanyShortName(Set<String> companyNames);
    public List<AnnualPlanExportExcel>  selectAnnualPlanExportExcel(AnnualPlanVo annualPlanVo);
    public List<AnnualPlanExportExcel>  selectAnnualPlanExportExcelUser(AnnualPlanVo annualPlanVo);

    public AnnualPlanVo getAnnualPlanVo(AnnualPlanVo annualPlanVo);

    public int updateAnnualPlanList(AnnualPlanVo annualPlan);


    public List<AnnualPlanVo> selectAnnualPlanProcessList(AnnualPlanVo annualPlan);
}
