package org.ruoyi.core.modules.fcdataquery.service;

import org.ruoyi.core.modules.fcdataquery.po.FundMarginDetailPo;
import org.ruoyi.core.modules.fcdataquery.po.FundMarginPo;
import org.ruoyi.core.modules.fcdataquery.vo.FundMarginDetailVo;
import org.ruoyi.core.modules.fcdataquery.vo.FundMarginVo;

import java.util.List;

/**
 * 运营部-资金方保证金Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-25
 */
public interface IFcFundMarginService
{

    /**
     * <AUTHOR>
     * @Description 运营部-资金方保证金统计
     * @Date 2024/10/23 9:25
     * @Param [startTime, endTime]
     * @return void
     **/
    public void fundMarginSts(String dateStat);

    /**
     * <AUTHOR>
     * @Description 查询运营部-资金方保证金列表
     * @Date 2024/10/14 14:11
     * @Param [fundMarginVo]
     * @return java.util.List<org.ruoyi.core.modules.fcdataquery.po.FundMarginPo>
     **/
    List<FundMarginPo> selectFundMarginList(FundMarginVo fundMarginVo);

    /**
     * <AUTHOR>
     * @Description 获取资金方保证金详情
     * @Date 2024/10/30 15:32
     * @Param [fundMarginDetailVo]
     * @return org.ruoyi.core.modules.fcdataquery.po.FundMarginDetailPo
     **/
    List<FundMarginDetailPo> getFundMarginDetail(FundMarginDetailVo fundMarginDetailVo);
}
