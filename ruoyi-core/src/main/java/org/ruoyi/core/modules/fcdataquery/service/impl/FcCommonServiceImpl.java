package org.ruoyi.core.modules.fcdataquery.service.impl;

import com.google.gson.Gson;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.AuthModuleEnum;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.service.INewAuthorityService;
import org.apache.commons.collections4.CollectionUtils;
import org.ruoyi.core.modules.fcdataquery.mapper.FcDataQueryMapper;
import org.ruoyi.core.modules.fcdataquery.po.AccountSetsInfoPo;
import org.ruoyi.core.modules.fcdataquery.service.IFcCommonService;
import org.ruoyi.core.modules.fcdataquery.vo.ParamsVo;
import org.ruoyi.core.oasystem.domain.ProjectCompanyRelevance;
import org.ruoyi.core.oasystem.mapper.ProjectCompanyRelevanceMapper;
import org.ruoyi.core.service.ISysSelectDataRefService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 运营部-平台技术服务费Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
@Service
public class FcCommonServiceImpl implements IFcCommonService
{

    @Autowired
    private ProjectCompanyRelevanceMapper projectCompanyRelevanceMapper;
    @Autowired
    private ISysSelectDataRefService sysSelectDataRefService;
    @Autowired
    private INewAuthorityService newAuthorityService;
    @Autowired
    private FcDataQueryMapper fcDataQueryMapper;


    @Override
    public List<Long> getProjectList(ParamsVo paramsVo) {
        //根据公司id获取项目id
        List<Long> projectIdList = new ArrayList<>();
        //动态查询
        if (paramsVo.getMoreSearch() != null && paramsVo.getMoreSearch().length() > 2){
            Gson gson = new Gson();
            // 将字符串转换为 Map<String, List<Long>>
            Map<String, List<Integer>> moreSearch = gson.fromJson(paramsVo.getMoreSearch(), Map.class);
            moreSearch.forEach((key, values) -> {
                if ("cust".equals(key)){
                    if (paramsVo.getGuaranteeIds() == null || paramsVo.getGuaranteeIds().isEmpty()) {
                        paramsVo.setGuaranteeIds(values);
                    } else {
                        paramsVo.getGuaranteeIds().addAll(values);
                    }
                } else if ("partner".equals(key)){
                    if (paramsVo.getAssetIds() == null || paramsVo.getAssetIds().isEmpty()) {
                        paramsVo.setAssetIds(values);
                    } else {
                        paramsVo.getAssetIds().addAll(values);
                    }
                } else if ("fund".equals(key)){
                    if (paramsVo.getFundIds() == null || paramsVo.getFundIds().isEmpty()) {
                        paramsVo.setFundIds(values);
                    } else {
                        paramsVo.getFundIds().addAll(values);
                    }
                } else {
                    if (paramsVo.getOthers() == null || paramsVo.getOthers().isEmpty()) {
                        paramsVo.setOthers(values);
                    } else {
                        paramsVo.getOthers().addAll(values);
                    }
                }
            });
        }
        //担保公司
        if(!CollectionUtils.isEmpty(paramsVo.getGuaranteeIds())){
            List<Long> projectIds = projectCompanyRelevanceMapper.getProjectIds(paramsVo.getGuaranteeIds());
            projectIdList.addAll(projectIds);
        }
        //资产方
        if(!CollectionUtils.isEmpty(paramsVo.getAssetIds())){
            List<Long> projectIds = projectCompanyRelevanceMapper.getProjectIds(paramsVo.getAssetIds());
            if(CollectionUtils.isEmpty(projectIdList)){
                projectIdList.addAll(projectIds);
            }else{
                projectIdList = (List<Long>)CollectionUtils.intersection(projectIdList, projectIds);
            }

        }
        //资金方
        if(!CollectionUtils.isEmpty(paramsVo.getFundIds())){
            List<Long> projectIds = projectCompanyRelevanceMapper.getProjectIds(paramsVo.getFundIds());
            if(CollectionUtils.isEmpty(projectIdList)){
                projectIdList.addAll(projectIds);
            }else{
                projectIdList = (List<Long>)CollectionUtils.intersection(projectIdList, projectIds);
            }
        }
        //其他公司
        if(!CollectionUtils.isEmpty(paramsVo.getOthers())){
            List<Long> projectIds = projectCompanyRelevanceMapper.getProjectIds(paramsVo.getOthers());
            if(CollectionUtils.isEmpty(projectIdList)){
                projectIdList.addAll(projectIds);
            }else{
                projectIdList = (List<Long>)CollectionUtils.intersection(projectIdList, projectIds);
            }
        }
        //判断项目id集合是否为空
        if(!CollectionUtils.isEmpty(paramsVo.getProjectIds())){
            if(CollectionUtils.isEmpty(projectIdList)){
                projectIdList.addAll(paramsVo.getProjectIds());
            }else{
                projectIdList = (List<Long>)CollectionUtils.intersection(projectIdList, paramsVo.getProjectIds());
            }
        }

        if((!CollectionUtils.isEmpty(paramsVo.getGuaranteeIds()) || !CollectionUtils.isEmpty(paramsVo.getAssetIds())
                ||!CollectionUtils.isEmpty(paramsVo.getFundIds()) || !CollectionUtils.isEmpty(paramsVo.getOthers())
                || !CollectionUtils.isEmpty(paramsVo.getProjectIds())) && CollectionUtils.isEmpty(projectIdList)){
            projectIdList.add(-1l);
        }

        //项目集合为空，则获取当前用户拥有的项目权限
        if(CollectionUtils.isEmpty(projectIdList)){
            //查询用户对应的项目信息
            LoginUser loginUser = SecurityUtils.getLoginUser();

            ProjectCompanyRelevance projectCompanyRelevance = new ProjectCompanyRelevance();
            projectCompanyRelevance.setUnitType("5");
            projectCompanyRelevance.setModuleTypeOfNewAuth("DATAREPORT");
            List<Map<String, Object>> mapList = sysSelectDataRefService.queryCompanyByProjectId(projectCompanyRelevance, loginUser);
            projectIdList = mapList.stream().map(e -> {
                return Long.parseLong(e.get("value").toString());
            }).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(projectIdList)){
                projectIdList = new ArrayList<>();
                projectIdList.add(-1l);
            }
        }

        return projectIdList;
    }

    @Override
    public List<Integer> getProjectIds(ParamsVo paramsVo){
        List<Long> projectList = getProjectList(paramsVo);
        return projectList.stream().map(e->Integer.parseInt(e.toString())).collect(Collectors.toList());
    }

    /**
     * <AUTHOR>
     * @Description 获取用户关联公司对应的账套id
     * @Date 2024/12/26 13:51
     * @Param []
     * @return java.util.List<java.lang.Long>
     **/
    @Override
    public List<Integer> getAccountSetsList(){
        //查询用户对应的账套信息
        Long userId = SecurityUtils.getUserId();
        List<Long> authorityCompanyIds = newAuthorityService.getNewAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType(userId, AuthModuleEnum.DATAREPORT.getCode());
        if(!CollectionUtils.isEmpty(authorityCompanyIds)){
            List<AccountSetsInfoPo> accountSetsInfos = fcDataQueryMapper.getAccountSetsByCompanyIds(authorityCompanyIds);
            return accountSetsInfos.stream().map(AccountSetsInfoPo::getId).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }
}
