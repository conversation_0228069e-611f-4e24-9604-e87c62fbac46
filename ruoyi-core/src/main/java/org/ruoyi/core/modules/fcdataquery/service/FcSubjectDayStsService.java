package org.ruoyi.core.modules.fcdataquery.service;

/**
 *@Authoer: huoruidong
 *@Description: 财务数据日统计
 *@Date: 2024/9/27 11:48
 **/
public interface FcSubjectDayStsService {

    /**
     * <AUTHOR>
     * @Description 科目日统计
     * @Date 2024/10/23 16:06
     * @Param [startTime, endTime]
     * @return void
     **/
    public void subjectDaySts(String startTime, String endTime);

    /**
     * <AUTHOR>
     * @Description 项目日统计
     * @Date 2024/10/23 16:06
     * @Param [startTime, endTime]
     * @return void
     **/
    public void projectDaySts(String startTime, String endTime, String stsType);

    /**
     * <AUTHOR>
     * @Description 删除科目日统计
     * @Date 2024/12/10 15:05
     * @Param [subjectId]
     * @return void
     **/
    public void delSubjectSts(Integer subjectId);
}
