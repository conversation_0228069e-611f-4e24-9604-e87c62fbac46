package org.ruoyi.core.modules.fcdataquery.service.impl;

import com.google.common.collect.Lists;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.financial.domain.FinancialAccountSets;
import com.ruoyi.financial.service.IFinancialAccountSetsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.ruoyi.core.modules.fcdataquery.domain.FcProjectSts;
import org.ruoyi.core.modules.fcdataquery.domain.FcSubjectSts;
import org.ruoyi.core.modules.fcdataquery.mapper.FcProjectStsMapper;
import org.ruoyi.core.modules.fcdataquery.mapper.FcSubjectStsMapper;
import org.ruoyi.core.modules.fcdataquery.po.ProjectIncomeDetailPo;
import org.ruoyi.core.modules.fcdataquery.po.ProjectIncomePo;
import org.ruoyi.core.modules.fcdataquery.service.IFcCommonService;
import org.ruoyi.core.modules.fcdataquery.service.IFcProjectIncomeService;
import org.ruoyi.core.modules.fcdataquery.vo.ParamsVo;
import org.ruoyi.core.modules.fcdataquery.vo.ProjectIncomeDetailVo;
import org.ruoyi.core.modules.fcdataquery.vo.ProjectIncomeVo;
import org.ruoyi.core.modules.fcdataquery.vo.ProjectStsVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 总裁办-项目收入Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-09-25
 */
@Slf4j
@Service
public class FcProjectIncomeServiceImpl implements IFcProjectIncomeService
{

    @Autowired
    private FcSubjectStsMapper fcSubjectStsMapper;
    @Autowired
    private FcProjectStsMapper fcProjectStsMapper;
    @Autowired
    private IFinancialAccountSetsService financialAccountSetsService;
    @Autowired
    private IFcCommonService fcCommonService;

    /**
     * <AUTHOR>
     * @Description 总裁办-项目收入统计
     * @Date 2024/10/23 9:26
     * @Param [startTime, endTime]
     * @return void
     **/
    @Override
    public void projectIncomeSts(String monthEnd){
        log.info("总裁办-项目收入统计开始："+monthEnd);
        String monthStr = monthEnd.substring(0, 7);

        List<FinancialAccountSets> accountSetsList = financialAccountSetsService.list();
        for (FinancialAccountSets accountSets:accountSetsList) {
            List<FcProjectSts> fcProjectStsList = new ArrayList<>();

            List<FcSubjectSts> subjectStsList = fcSubjectStsMapper.getSubjectStsGroupBy(accountSets.getId(), monthStr);
            Map<Integer, List<FcSubjectSts>> projectMap = subjectStsList.stream().filter(e->null != e.getProjectId()).collect(Collectors.groupingBy(FcSubjectSts::getProjectId));
            for (Integer projectId:projectMap.keySet()) {
                List<FcSubjectSts> childSubjectStsList = projectMap.get(projectId);

                List<FcSubjectSts> depositReceivedDatas = childSubjectStsList.stream().filter(e -> null != e.getSubjectName() && e.getSubjectName().contains("主营业务收入")).collect(Collectors.toList());
                BigDecimal depositReceivedSts = depositReceivedDatas.stream().filter(e -> null != e).map(FcSubjectSts::getCreditAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

                //计算收入
                FcProjectSts income = new FcProjectSts();
                income.setProjectId(projectId);
                income.setProjectType(childSubjectStsList.get(0).getProjectType());
                income.setProjectName(childSubjectStsList.get(0).getProjectName());
                income.setAccountSetsId(accountSets.getId());
                income.setAccountSetsName(accountSets.getCompanyName());
                income.setDataDay(monthEnd);
                income.setDataQuarter(childSubjectStsList.get(0).getDataQuarter());
                income.setDataMonth(childSubjectStsList.get(0).getDataMonth());
                income.setDataYear(childSubjectStsList.get(0).getDataYear());
                income.setStsIdentify("00");//项目收入-收入
                income.setChannelName("");
                income.setInitAmount(BigDecimal.ZERO);//期初金额
                //收入 = 主营业务收入-项目名称，取贷方发生额
                income.setTotalAmount(depositReceivedSts);//贷方发生额
                income.setCreditAmount(BigDecimal.ZERO);//贷方发生额
                income.setDebitAmount(BigDecimal.ZERO);//借方发生额
                fcProjectStsList.add(income);

                //查询主营业务成本对应的一级科目
                List<FcSubjectSts> mainBusinessCostDatas = childSubjectStsList.stream().filter(e -> null != e.getSubjectName() && e.getSubjectName().contains("主营业务成本")).collect(Collectors.toList());
                BigDecimal mainBusinessCostSts = BigDecimal.ZERO;
                if(!CollectionUtils.isEmpty(mainBusinessCostDatas)){
                    mainBusinessCostSts = mainBusinessCostDatas.stream().filter(e-> null != e).map(FcSubjectSts::getDebitAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                }

                //计算净毛利
                FcProjectSts netGrossProfit = new FcProjectSts();
                netGrossProfit.setProjectId(projectId);
                netGrossProfit.setProjectType(childSubjectStsList.get(0).getProjectType());
                netGrossProfit.setProjectName(childSubjectStsList.get(0).getProjectName());
                netGrossProfit.setAccountSetsId(accountSets.getId());
                netGrossProfit.setAccountSetsName(accountSets.getCompanyName());
                netGrossProfit.setDataDay(monthEnd);
                netGrossProfit.setDataQuarter(childSubjectStsList.get(0).getDataQuarter());
                netGrossProfit.setDataMonth(childSubjectStsList.get(0).getDataMonth());
                netGrossProfit.setDataYear(childSubjectStsList.get(0).getDataYear());
                netGrossProfit.setStsIdentify("02");//项目收入-净毛利
                netGrossProfit.setChannelName("");
                netGrossProfit.setInitAmount(BigDecimal.ZERO);//期初金额
                netGrossProfit.setTotalAmount(depositReceivedSts.subtract(mainBusinessCostSts));//贷方发生额
                netGrossProfit.setCreditAmount(BigDecimal.ZERO);//贷方发生额
                netGrossProfit.setDebitAmount(BigDecimal.ZERO);//借方发生额
                fcProjectStsList.add(netGrossProfit);
            }
            //等量拆分，分批插入
            List<List<FcProjectSts>> partition = Lists.partition(fcProjectStsList, 200);
            for (List<FcProjectSts> list:partition) {
                //批量新增项目日统计
                fcProjectStsMapper.batchInsertOrUpdateProjectSts(list);
            }
            fcProjectStsList.clear();
        }
        log.info("总裁办-项目收入统计结束："+monthEnd);
    }

    /**
     * <AUTHOR>
     * @Description 查询总裁办-项目收入列表
     * @Date 2024/10/14 14:32
     * @Param [projectIncomeVo]
     * @return java.util.List<org.ruoyi.core.modules.fcdataquery.po.ProjectIncomePo>
     **/
    @Override
    public List<ProjectIncomePo> selectProjectIncomeList(ProjectIncomeVo projectIncomeVo) {
        //请求参数处理
        reqParamsDeal(projectIncomeVo);
        //数据填充
        ProjectStsVo projectStsVo = new ProjectStsVo();
        BeanUtils.copyProperties(projectIncomeVo, projectStsVo);

        ParamsVo paramsVo = new ParamsVo();
        BeanUtils.copyProperties(projectIncomeVo, paramsVo);
        List<Long> projectList = fcCommonService.getProjectList(paramsVo);
        projectStsVo.setProjectIds(projectList);
        //查询项目统计数据
        List<FcProjectSts> fcProjectStsList = fcProjectStsMapper.selectFcProjectStsList(projectStsVo);
        //处理项目统计数据集合
        List<ProjectIncomePo> projectIncomeDetailPoList = dealProjectStsList(projectIncomeVo, fcProjectStsList);
        return projectIncomeDetailPoList;
    }

    /**
     * <AUTHOR>
     * @Description 获取项目收入明细
     * @Date 2024/10/30 16:07
     * @Param [projectIncomeDetailVo]
     * @return org.ruoyi.core.modules.fcdataquery.po.ProjectIncomeDetailPo
     **/
    @Override
    public ProjectIncomeDetailPo getProjectIncomeDetail(ProjectIncomeDetailVo projectIncomeDetailVo) {
        ProjectIncomeDetailPo projectIncomeDetailPo = new ProjectIncomeDetailPo();
        projectIncomeDetailPo.setProjectId(projectIncomeDetailVo.getProjectId());
        List<FcSubjectSts> subjectStsList = fcSubjectStsMapper.getSubjectStsByProjectId(projectIncomeDetailVo);

        List<FcSubjectSts> depositReceivedDatas = subjectStsList.stream().filter(e -> null != e.getSubjectName() && e.getSubjectName().contains("主营业务收入")).collect(Collectors.toList());
        projectIncomeDetailPo.setFee1(depositReceivedDatas.stream().filter(e-> null != e).map(FcSubjectSts::getCreditAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        if("02".equals(projectIncomeDetailVo.getStsIdentify())){
            //查询主营业务成本对应的一级科目
            List<FcSubjectSts> mainBusinessCostDatas = subjectStsList.stream().filter(e -> null != e.getSubjectName() && e.getSubjectName().contains("主营业务成本")).collect(Collectors.toList());
            projectIncomeDetailPo.setFee4(mainBusinessCostDatas.stream().filter(e-> null != e).map(FcSubjectSts::getDebitAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            projectIncomeDetailPo.setResultFee(projectIncomeDetailPo.getFee1().subtract(projectIncomeDetailPo.getFee4()));
        }
        return projectIncomeDetailPo;
    }

    /**
     * <AUTHOR>
     * @Description 请求参数处理
     * @Date 2024/10/28 15:10
     * @Param [projectIncomeVo]
     * @return void
     **/
    private void reqParamsDeal(ProjectIncomeVo projectIncomeVo){
        int timeRange = 11;
        //初始值
        if(StringUtils.isBlank(projectIncomeVo.getStartTime())){
            String yearMonthStr = DateUtils.dateForStr(new Date());
            if("month".equals(projectIncomeVo.getTimeType())){
                projectIncomeVo.setEndTime(yearMonthStr);
                projectIncomeVo.setStartTime(DateUtils.getNextMonth(projectIncomeVo.getEndTime(), -timeRange));
            }

            if("year".equals(projectIncomeVo.getTimeType())){
                projectIncomeVo.setEndTime(yearMonthStr.substring(0,4));
                projectIncomeVo.setStartTime(DateUtils.getNextYear(projectIncomeVo.getEndTime(), -timeRange));
            }
        }

        //日期格式转换
        projectIncomeVo.setStartTime(projectIncomeVo.getStartTime().replace(".", "-"));
        projectIncomeVo.setEndTime(projectIncomeVo.getEndTime().replace(".", "-"));
        //转换时间格式
        if("month".equals(projectIncomeVo.getTimeType())){
            Integer diffMon = DateUtils.calcMob(projectIncomeVo.getStartTime() + "-01", projectIncomeVo.getEndTime() + "-01");
            if(diffMon>timeRange){//月份差超过12个月取前12个月
                projectIncomeVo.setEndTime(DateUtils.getNextMonth(projectIncomeVo.getStartTime(), timeRange));
            }
        } else if("year".equals(projectIncomeVo.getTimeType())){
            int yearDiff = Integer.parseInt(projectIncomeVo.getEndTime())-Integer.parseInt(projectIncomeVo.getStartTime());
            if(yearDiff>timeRange){//年份差超过12年取前12年
                projectIncomeVo.setEndTime(DateUtils.getNextYear(projectIncomeVo.getStartTime(), timeRange));
            }
        }
    }

    /**
     * <AUTHOR>
     * @Description 处理项目统计集合数据
     * @Date 2024/10/29 17:17
     * @Param [projectIncomeVo, fcProjectStsList]
     * @return java.util.List<org.ruoyi.core.modules.fcdataquery.po.ProjectIncomePo>
     **/
    private List<ProjectIncomePo> dealProjectStsList(ProjectIncomeVo projectIncomeVo, List<FcProjectSts> fcProjectStsList){
        List<ProjectIncomePo> projectIncomeDetailPoList = new ArrayList<>();
        Map<Integer, List<FcProjectSts>> projectMap = fcProjectStsList.stream().filter(e->null != e.getProjectId()).collect(Collectors.groupingBy(FcProjectSts::getProjectId));
        for (Integer projectId: projectMap.keySet()) {
            //获取时间范围
            List<String> timeList = DateUtils.getTimeRange(projectIncomeVo.getStartTime(), projectIncomeVo.getEndTime(), projectIncomeVo.getTimeType());
            //项目对应的子集
            List<FcProjectSts> projectStsList = projectMap.get(projectId);

            ProjectIncomePo projectIncomeDetailPo = new ProjectIncomePo();
            projectIncomeDetailPo.setProjectId(projectId);//项目ID
            projectIncomeDetailPo.setProjectName(projectStsList.get(0).getProjectName());//项目名称

            Map<String, BigDecimal> amtMap = new TreeMap<>();
            if("month".equals(projectIncomeVo.getTimeType())){
                Map<String, List<FcProjectSts>> monthMap = projectStsList.stream().collect(Collectors.groupingBy(FcProjectSts::getDataMonth));
                //排除不存在的月份统计
                timeList.removeAll(monthMap.keySet());
                if(!CollectionUtils.isEmpty(timeList)){
                    for (String monthStr: timeList) {
                        String[] split = monthStr.split("-");
                        amtMap.put(split[0]+"年"+split[1]+"月", BigDecimal.ZERO.setScale(2));
                    }
                }
                //统计各月份数据
                for (String monthStr: monthMap.keySet()) {
                    List<FcProjectSts> monthProjectStsList = monthMap.get(monthStr);
                    BigDecimal sumTotalAmt = monthProjectStsList.stream().map(FcProjectSts::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);//金额和
                    String[] split = monthStr.split("-");
                    amtMap.put(split[0]+"年"+split[1]+"月", sumTotalAmt);
                }
            }

            if("year".equals(projectIncomeVo.getTimeType())){
                Map<String, List<FcProjectSts>> yearMap = projectStsList.stream().collect(Collectors.groupingBy(FcProjectSts::getDataYear));
                //排除不存在的年份统计
                timeList.removeAll(yearMap.keySet());
                if(!CollectionUtils.isEmpty(timeList)){
                    for (String yearStr: timeList) {
                        amtMap.put(yearStr, BigDecimal.ZERO.setScale(2));
                    }
                }
                for (String yearStr: yearMap.keySet()) {
                    List<FcProjectSts> yearProjectStsList = yearMap.get(yearStr);
                    BigDecimal sumTotalAmt = yearProjectStsList.stream().map(FcProjectSts::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);//金额和
                    amtMap.put(yearStr, sumTotalAmt);
                }
            }
            projectIncomeDetailPo.setAmtMap(amtMap);//金额map集合
            BigDecimal sumTotalAmt = projectStsList.stream().map(FcProjectSts::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);//金额和
            projectIncomeDetailPo.setTotalAmt(sumTotalAmt);//金额和
            projectIncomeDetailPoList.add(projectIncomeDetailPo);
        }
        return projectIncomeDetailPoList;
    }
}
