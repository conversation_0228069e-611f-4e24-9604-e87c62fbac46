package org.ruoyi.core.modules.fcdataquery.controller;

import com.github.pagehelper.PageInfo;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.PageUtils;
import org.ruoyi.core.modules.fcdataquery.po.ProjectIncomeDetailPo;
import org.ruoyi.core.modules.fcdataquery.po.ProjectIncomePo;
import org.ruoyi.core.modules.fcdataquery.service.IFcProjectIncomeService;
import org.ruoyi.core.modules.fcdataquery.vo.ProjectIncomeDetailVo;
import org.ruoyi.core.modules.fcdataquery.vo.ProjectIncomeVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目收入Controller
 * 
 * <AUTHOR>
 * @date 2024-09-25
 */
@RestController
@RequestMapping("/fc/projectIncome")
public class ProjectIncomeController extends BaseController
{
    @Autowired
    private IFcProjectIncomeService fcProjectIncomeService;

    /**
     * 查询财务收入列表
     */
    @PreAuthorize("@ss.hasAnyPermi('fc:projectIncome:list,fc:operation:projectIncome:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProjectIncomeVo projectIncomeVo, int pageNum, int pageSize)
    {
        //查询项目收入
        List<ProjectIncomePo> projectIncomePoList = fcProjectIncomeService.selectProjectIncomeList(projectIncomeVo);
        PageInfo<ProjectIncomePo> pageInfo = PageUtils.getPageInfo(pageNum, pageSize, projectIncomePoList);
        TableDataInfo tableDataInfo = getDataTable(pageInfo.getList());
        tableDataInfo.setTotal(pageInfo.getTotal());
        Map<Object, Object> map = new HashMap<>();
        map.put("sumTotalAmt", projectIncomePoList.stream().map(ProjectIncomePo::getTotalAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
        tableDataInfo.setMap(map);
        return tableDataInfo;
    }

    /**
     * <AUTHOR>
     * @Description 获取项目收入详情
     * @Date 2024/10/30 15:31
     * @Param [projectIncomeDetailVo]
     * @return org.ruoyi.core.modules.fcdataquery.po.ProjectIncomeDetailPo
     **/
    @PostMapping("/detail")
    public ProjectIncomeDetailPo projectIncomeDetail(@RequestBody ProjectIncomeDetailVo projectIncomeDetailVo){
        ProjectIncomeDetailPo projectIncomeDetailPo = fcProjectIncomeService.getProjectIncomeDetail(projectIncomeDetailVo);
        return projectIncomeDetailPo;
    }

    /**
     * 导出财务剔除明细列表
     */
    @PreAuthorize("@ss.hasAnyPermi('fc:projectIncome:export,fc:operation:projectIncome:export')")
    @Log(title = "财务收入明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public List<ProjectIncomePo> export(@RequestBody ProjectIncomeVo projectIncomeVo) {
        //查询项目收入
        return fcProjectIncomeService.selectProjectIncomeList(projectIncomeVo);
    }
}
