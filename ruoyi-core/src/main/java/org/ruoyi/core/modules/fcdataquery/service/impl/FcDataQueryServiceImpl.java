package org.ruoyi.core.modules.fcdataquery.service.impl;

import com.ruoyi.common.enums.AuthModuleEnum;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.service.INewAuthorityService;
import org.ruoyi.core.modules.fcdataquery.mapper.FcDataQueryMapper;
import org.ruoyi.core.modules.fcdataquery.po.AccountSetsInfoPo;
import org.ruoyi.core.modules.fcdataquery.po.BeneficiaryInfoPo;
import org.ruoyi.core.modules.fcdataquery.po.ChannelInfoPo;
import org.ruoyi.core.modules.fcdataquery.po.CompanyTypePo;
import org.ruoyi.core.modules.fcdataquery.service.IFcCommonService;
import org.ruoyi.core.modules.fcdataquery.service.IFcDataQueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 总裁办-公司净利润Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-09-25
 */
@Service
public class FcDataQueryServiceImpl implements IFcDataQueryService
{

    @Autowired
    private FcDataQueryMapper fcDataQueryMapper;
    @Autowired
    private INewAuthorityService newAuthorityService;


    /**
     * <AUTHOR>
     * @Description 总裁办-公司净利润统计
     * @Date 2024/10/23 9:26
     * @Param [startTime, endTime]
     * @return void
     **/
    @Override
    public List<CompanyTypePo> selectCompanyType(){
        List<CompanyTypePo> companyTypePos = fcDataQueryMapper.selectCompanyType();
        return companyTypePos;
    }

    /**
     * <AUTHOR>
     * @Description 根据公司类型获取公司id
     * @Date 2024/12/13 16:28
     * @Param [dictCode]
     * @return java.util.List<java.lang.Integer>
     **/
    @Override
    public List<Integer> getCompanyIdByType(List<Integer> dictCodes){
        return fcDataQueryMapper.getCompanyIdByType(dictCodes);
    }

    /**
     * <AUTHOR>
     * @Description 查询渠道信息
     * @Date 2024/12/26 9:37
     * @Param []
     * @return java.util.List<org.ruoyi.core.modules.fcdataquery.po.ChannelInfoPo>
     **/
    @Override
    public List<ChannelInfoPo> selectChannelInfo() {
        List<ChannelInfoPo> channelInfoPos = fcDataQueryMapper.selectChannelInfo();
        return channelInfoPos;
    }

    /**
     * <AUTHOR>
     * @Description 查询收款单位信息
     * @Date 2024/12/26 9:38
     * @Param [subjectName]
     * @return java.util.List<java.lang.String>
     **/
    @Override
    public List<BeneficiaryInfoPo> selectBeneficiaryInfo(Integer type) {
        String subjectName = type == 1 ? "应付账款2":"应付账款3";
        List<BeneficiaryInfoPo> beneficiaryInfoPoList = fcDataQueryMapper.selectBeneficiaryInfo(subjectName);
        return beneficiaryInfoPoList;
    }

    /**
     * <AUTHOR>
     * @Description 查询收款单位信息
     * @Date 2024/12/26 9:38
     * @Param [subjectName]
     * @return java.util.List<java.lang.String>
     **/
    @Override
    public List<AccountSetsInfoPo> selectAccountSets() {
        //查询用户对应的账套信息
        Long userId = SecurityUtils.getUserId();
        List<Long> authorityCompanyIds = newAuthorityService.getNewAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType(userId, AuthModuleEnum.DATAREPORT.getCode());
        List<AccountSetsInfoPo> accountSetsInfos = fcDataQueryMapper.getAccountSetsByCompanyIds(authorityCompanyIds);
        return accountSetsInfos;
    }

}
