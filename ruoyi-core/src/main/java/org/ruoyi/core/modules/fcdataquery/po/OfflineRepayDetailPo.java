package org.ruoyi.core.modules.fcdataquery.po;

import lombok.Data;

import java.math.BigDecimal;

/**
 *@Authoer: huoruidong
 *@Description: 线下还款明细PO类
 *@Date: 2023/7/20 10:30
 **/
@Data
public class OfflineRepayDetailPo {

    /**
     * 项目id
     **/
    private Integer projectId;

    /**
     * 项目名称
     **/
    private String projectName;

    /**
     * 记账日期
     **/
    private String dataDay;

    /**
     * 收到线下还款
     **/
    private BigDecimal receiveAmt;

    /**
     * 支付线下还款
     **/
    private BigDecimal payAmt;
}
