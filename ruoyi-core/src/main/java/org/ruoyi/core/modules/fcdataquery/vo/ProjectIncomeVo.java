package org.ruoyi.core.modules.fcdataquery.vo;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 项目收入VO类
 * @Date 2024/9/27 10:14
 * @Param
 * @return
 **/
@Data
public class ProjectIncomeVo extends BaseEntity {

    /** 数据标识 00-收入 01-毛利 02-净毛利 */
    private String stsIdentify;

    /** 项目ID */
    private List<Long> projectIds;

    /** 担保公司 */
    private List<Integer> guaranteeIds;

    /** 资产方 */
    private List<Integer> assetIds;

    /** 资金方 */
    private List<Integer> fundIds;

    /** 其他公司 */
    private List<Integer> others;

    /** 时间维度 day month year */
    private String timeType;

    /** 开始时间 */
    private String startTime;

    /** 结束时间 */
    private String endTime;

    private String moreSearch;
}
