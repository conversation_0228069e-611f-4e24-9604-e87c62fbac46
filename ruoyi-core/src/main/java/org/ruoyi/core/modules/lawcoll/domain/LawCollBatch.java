package org.ruoyi.core.modules.lawcoll.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 法催批次对象 law_coll_batch
 * 
 * <AUTHOR>
 * @date 2021-03-26
 */
public class LawCollBatch extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** null */
    private Integer pk;

    /** 批次号 */
    @Excel(name = "批次号")
    private String batchNo;

    /** 批次名称 */
    @Excel(name = "批次名称")
    private String batchName;

    /** 委托日期 */
    @Excel(name = "委托日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date wtrq;

    /** 申请人名称 */
    @Excel(name = "申请人名称")
    private String sqrmc;

    /** 贷款人简称 */
    @Excel(name = "贷款人简称")
    private String dkrjc;

    /** 融担公司简称 */
    @Excel(name = "融担公司简称")
    private String rdgsjc;

    public void setPk(Integer pk) 
    {
        this.pk = pk;
    }

    public String getBatchName() {
        return batchName;
    }

    public void setBatchName(String batchName) {
        this.batchName = batchName;
    }

    public Integer getPk()
    {
        return pk;
    }
    public void setBatchNo(String batchNo) 
    {
        this.batchNo = batchNo;
    }

    public String getBatchNo() 
    {
        return batchNo;
    }
    public void setWtrq(Date wtrq) 
    {
        this.wtrq = wtrq;
    }

    public Date getWtrq() 
    {
        return wtrq;
    }
    public void setSqrmc(String sqrmc) 
    {
        this.sqrmc = sqrmc;
    }

    public String getSqrmc() 
    {
        return sqrmc;
    }
    public void setDkrjc(String dkrjc) 
    {
        this.dkrjc = dkrjc;
    }

    public String getDkrjc() 
    {
        return dkrjc;
    }
    public void setRdgsjc(String rdgsjc) 
    {
        this.rdgsjc = rdgsjc;
    }

    public String getRdgsjc() 
    {
        return rdgsjc;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("pk", getPk())
            .append("batchNo", getBatchNo())
            .append("wtrq", getWtrq())
            .append("sqrmc", getSqrmc())
            .append("dkrjc", getDkrjc())
            .append("rdgsjc", getRdgsjc())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
