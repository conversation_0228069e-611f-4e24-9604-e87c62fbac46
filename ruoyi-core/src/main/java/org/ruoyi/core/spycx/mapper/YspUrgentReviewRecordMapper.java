package org.ruoyi.core.spycx.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.archivist.domain.vo.DaProcFormDataVo;
import org.ruoyi.core.spycx.domain.ProcFormDataDef;
import org.ruoyi.core.spycx.domain.YspUrgentReviewRecord;

/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-10-15
 */
public interface YspUrgentReviewRecordMapper 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public YspUrgentReviewRecord selectYspUrgentReviewRecordById(Long id);


    /**

     * @return {@link List}<{@link YspUrgentReviewRecord}>
     */
    public List<YspUrgentReviewRecord> selectYspUrgentReviewRecordList(YspUrgentReviewRecord yspUrgentReviewRecord);

    public List<YspUrgentReviewRecord> getDataByBusiness(YspUrgentReviewRecord yspUrgentReviewRecord);

    /**
     * 新增【请填写功能名称】
     * 
     * @param yspUrgentReviewRecord 【请填写功能名称】
     * @return 结果
     */
    public int insertYspUrgentReviewRecord(YspUrgentReviewRecord yspUrgentReviewRecord);

    /**
     * 修改【请填写功能名称】
     * 
     * @param yspUrgentReviewRecord 【请填写功能名称】
     * @return 结果
     */
    public int updateYspUrgentReviewRecord(YspUrgentReviewRecord yspUrgentReviewRecord);

    /**
     * 删除【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteYspUrgentReviewRecordById(Long id);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteYspUrgentReviewRecordByIds(Long[] ids);

    /**
     * businessId查询发起的流程信息
     * @param procFormData 单数据
     * @return
     */
    DaProcFormDataVo selectProcFormDataByBusinessId(DaProcFormDataVo procFormData);

    ProcFormDataDef getDataByBusindess(@Param("businessId") String businessId);

    int selectNumBybusIdAndNodeName(@Param("businessId") String businessId, @Param("taskNodeName") String taskNodeName);


    public int updateYspUrgentByTaskNoneName(YspUrgentReviewRecord yspUrgentReviewRecord);

    void updatenoticeStatus(@Param("ids") List<String> ids, @Param("noticeStatus") String noticeStatus);

    int updatenoticeStatusByid(@Param("id") Long id);
}
