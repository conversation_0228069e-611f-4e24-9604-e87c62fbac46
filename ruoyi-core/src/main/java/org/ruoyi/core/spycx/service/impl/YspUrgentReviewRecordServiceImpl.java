package org.ruoyi.core.spycx.service.impl;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

import com.fasterxml.jackson.databind.JsonNode;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.qiyeVX.AccessTokenUtils;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.system.mapper.SysUserMapper;
import org.activiti.bpmn.model.BpmnModel;
import org.activiti.bpmn.model.Process;
import org.activiti.bpmn.model.UserTask;
import org.activiti.engine.HistoryService;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.HistoricProcessInstance;
import org.ruoyi.core.archivist.domain.vo.DaProcFormDataVo;
import org.ruoyi.core.cwproject.domain.TopNotify;
import org.ruoyi.core.cwproject.mapper.TopNotifyMapper;
import org.ruoyi.core.oasystem.domain.vo.ProcFormDefVo;
import org.ruoyi.core.qiyeVX.domain.VxUser;
import org.ruoyi.core.qiyeVX.mapper.VXMapper;
import org.ruoyi.core.spycx.domain.ProcFormDataDef;
import org.ruoyi.core.spycx.domain.YspUrgentReviewRecord;
import org.ruoyi.core.spycx.mapper.YspUrgentReviewRecordMapper;
import org.ruoyi.core.spycx.service.IYspUrgentReviewRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-10-15
 */
@Service
public class YspUrgentReviewRecordServiceImpl implements IYspUrgentReviewRecordService
{
    @Autowired
    private YspUrgentReviewRecordMapper yspUrgentReviewRecordMapper;
    @Autowired
    private HistoryService historyService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private VXMapper vxMapper;

    @Autowired
    private AccessTokenUtils accessTokenUtils;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private TopNotifyMapper topNotifyMapper;
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public YspUrgentReviewRecord selectYspUrgentReviewRecordById(Long id)
    {
        return yspUrgentReviewRecordMapper.selectYspUrgentReviewRecordById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param yspUrgentReviewRecord 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<YspUrgentReviewRecord> selectYspUrgentReviewRecordList(YspUrgentReviewRecord yspUrgentReviewRecord)

    {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if(null != yspUrgentReviewRecord.getCreateTime() && !("".equals(yspUrgentReviewRecord.getCreateTime()))){
            yspUrgentReviewRecord.setBeginTime(sdf.format(yspUrgentReviewRecord.getCreateTime()));
        }
        if(null != yspUrgentReviewRecord.getUpdateTime() && !("".equals(yspUrgentReviewRecord.getUpdateTime()))){
            yspUrgentReviewRecord.setEndTime(sdf.format(yspUrgentReviewRecord.getUpdateTime()));
        }
        if(null != yspUrgentReviewRecord.getProcessType() && yspUrgentReviewRecord.getProcessType().equals("SP")){
            yspUrgentReviewRecord.setProcessType(null);
        }

        return yspUrgentReviewRecordMapper.selectYspUrgentReviewRecordList(yspUrgentReviewRecord);
    }

    /**
     * 新增【请填写功能名称】
     * 
     * @param yspUrgentReviewRecord 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertYspUrgentReviewRecord(YspUrgentReviewRecord yspUrgentReviewRecord, LoginUser loginUser) {
        String nickName = SecurityUtils.getLoginUser().getUser().getNickName();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = new Date();
        //根据业务id查询当前流程信息 获取当前流程节点和当前流程节点审批人

        Map<String, Object> currentFlow = this.getCurrentFlow(yspUrgentReviewRecord.getBusinessId());

        yspUrgentReviewRecord.setNoticeNodesNane(currentFlow.get("currentNode").toString());
        yspUrgentReviewRecord.setNoticeProcessUserName(currentFlow.get("assignee").toString());
        yspUrgentReviewRecord.setNoticeCheckStatus("0");
        yspUrgentReviewRecord.setNodesCheckStatus("0");
        yspUrgentReviewRecord.setUrgentReviewTime(sdf.format(date));
        yspUrgentReviewRecord.setUrgentReviewUser(nickName);

        ProcFormDataDef procFormData2 = yspUrgentReviewRecordMapper.getDataByBusindess(yspUrgentReviewRecord.getBusinessId());

        String assignee = procFormData2.getAssignee();

        SysUser sysUser = sysUserMapper.selectUserByUserName(assignee);
        //根据用户id查询要发送的企业微信账号
        List<Long> userList = new ArrayList<>();
        userList.add(sysUser.getUserId());
        if(userList.size()>0){

            String msg =
                            "<div class=\"gray\">"+procFormData2.getTheme()+"</div>" +
                            "<div class=\"gray\">流程模板："+procFormData2.getTemplateName()+"</div>" +
                            "<div class=\"gray\">"+yspUrgentReviewRecord.getUrgentReviewRemarks()+"</div>"+
                           "<div class=\"gray\"></div>"+
                            "<div class=\"gray\">催审人："+loginUser.getUser().getNickName()+"</div>" +
                            "<div class=\"gray\">催审时间："+sdf.format(date)+"</div>";
            String sendUser = "";
            List<VxUser>  vxUserList =  vxMapper.selectByUserId(userList);
            for (VxUser vxUser : vxUserList) {
                sendUser = sendUser+"|"+vxUser.getVxId();
            }
            try {
                accessTokenUtils.sendMsg(sendUser,msg,"9");
            } catch (IOException e) {
                e.printStackTrace();
            }

        }
        int i = yspUrgentReviewRecordMapper.insertYspUrgentReviewRecord(yspUrgentReviewRecord);
        TopNotify topNotify = new TopNotify();
        topNotify.setNotifyModule("预审批查询");
        topNotify.setNotifyType("1");
        topNotify.setNotifyMsg(yspUrgentReviewRecord.getUrgentReviewRemarks());
        topNotify.setUrl("/oaWork/approveProcessForm");
        topNotify.setProjectId(Long.parseLong("1"));
        topNotify.setIncomeId(Long.parseLong("1"));
        topNotify.setDisposeUser(sysUser.getUserId());
        topNotify.setViewFlag("0");
        topNotify.setYurrId(yspUrgentReviewRecord.getId());
        topNotify.setCreateBy(procFormData2.getNickName());
        topNotify.setCreateTime( DateUtils.getNowDate());
        topNotify.setUpdateTime( DateUtils.getNowDate());
        topNotify.setOaNotifyType("9");
        topNotify.setBusinessId(yspUrgentReviewRecord.getBusinessId());
        topNotify.setProcessId(yspUrgentReviewRecord.getBusinessId());
        topNotifyMapper.insertTopNotify(topNotify);


        return i;
    }

    public Map<String,Object> getCurrentFlow(String businessId){

        String userId = SecurityUtils.getUsername();
        DaProcFormDataVo procFormData = new DaProcFormDataVo();
        procFormData.setBusinessId(businessId);
        procFormData.setCreateBy(userId);
        procFormData.setWithProc("yes");
        DaProcFormDataVo procFormData1 = yspUrgentReviewRecordMapper.selectProcFormDataByBusinessId(procFormData);
        Map<String, Object> currentNodeInfo = new HashMap<>();
        String assignee = null;
//        ProcessInstance processInstance = processRuntime.processInstance(procFormData1.getInstanceId());
        org.activiti.engine.task.Task task = null;
        if (procFormData1.getInstanceId() != null) {
            task = taskService.createTaskQuery().processInstanceId(procFormData1.getInstanceId()).singleResult();
        }
        if (task != null) {
            String taskDefinitionKey = task.getTaskDefinitionKey();
            //通过taskId获取taskDefinitionKey
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(procFormData1.getInstanceId()).singleResult();
            //获取bpmnModel对象
            BpmnModel bpmnModel = repositoryService.getBpmnModel(historicProcessInstance.getProcessDefinitionId());
            //因为我们这里只定义了一个Process 所以获取集合中的第一个即可
            Process process = bpmnModel.getProcesses().get(0);
            //获取所有的FlowElement信息
            List<UserTask> userTaskList = process.findFlowElementsOfType(UserTask.class);
            for (UserTask userTask : userTaskList) {
                //获取当前任务节点Id
                String id = userTask.getId();
                if (id.equals(taskDefinitionKey)) {
                    String name = userTask.getName();
                    //获取当前处理人和岗位
                    currentNodeInfo.put("currentNode", name);
                    assignee = task.getAssignee();

                }
            }
            currentNodeInfo.put("assignee", assignee);
        }
        return currentNodeInfo;
    }
    /**
     * 修改【请填写功能名称】
     * 
     * @param yspUrgentReviewRecord 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateYspUrgentReviewRecord(YspUrgentReviewRecord yspUrgentReviewRecord)
    {
        return yspUrgentReviewRecordMapper.updateYspUrgentReviewRecord(yspUrgentReviewRecord);
    }

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteYspUrgentReviewRecordByIds(Long[] ids)
    {
        return yspUrgentReviewRecordMapper.deleteYspUrgentReviewRecordByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteYspUrgentReviewRecordById(Long id)
    {
        return yspUrgentReviewRecordMapper.deleteYspUrgentReviewRecordById(id);
    }

    @Override
    public int updateNoticeCheckStatusByid(YspUrgentReviewRecord yspUrgentReviewRecord) {

        return yspUrgentReviewRecordMapper.updatenoticeStatusByid(yspUrgentReviewRecord.getId());
    }
}
