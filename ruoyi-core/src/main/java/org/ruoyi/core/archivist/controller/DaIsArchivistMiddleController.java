package org.ruoyi.core.archivist.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import org.ruoyi.core.archivist.domain.DaIsArchivistMiddle;
import org.ruoyi.core.archivist.service.IDaIsArchivistMiddleService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 流程是否归档中间表Controller
 * 
 * <AUTHOR>
 * @date 2023-12-06
 */
@RestController
@RequestMapping("/archivist/archMiddle")
public class DaIsArchivistMiddleController extends BaseController
{
    @Autowired
    private IDaIsArchivistMiddleService daIsArchivistMiddleService;

    /**
     * 查询列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DaIsArchivistMiddle daIsArchivistMiddle)
    {
        startPage();
        List<DaIsArchivistMiddle> list = daIsArchivistMiddleService.selectDaIsArchivistMiddleList(daIsArchivistMiddle);
        return getDataTable(list);
    }

    /**
     * 导出列表
     */
    @Log(title = "流程是否归档中间", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DaIsArchivistMiddle daIsArchivistMiddle)
    {
        List<DaIsArchivistMiddle> list = daIsArchivistMiddleService.selectDaIsArchivistMiddleList(daIsArchivistMiddle);
        ExcelUtil<DaIsArchivistMiddle> util = new ExcelUtil<DaIsArchivistMiddle>(DaIsArchivistMiddle.class);
        util.exportExcel(response, list, "流程是否归档中间数据");
    }

    /**
     * 获取详细信息
     */
    @GetMapping(value = "/{flowId}")
    public AjaxResult getInfo(@PathVariable("flowId") String flowId)
    {
        return AjaxResult.success(daIsArchivistMiddleService.selectDaIsArchivistMiddleByFlowId(flowId));
    }

    /**
     * 新增
     */
    @Log(title = "流程是否归档中间", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DaIsArchivistMiddle daIsArchivistMiddle)
    {
        return toAjax(daIsArchivistMiddleService.insertDaIsArchivistMiddle(daIsArchivistMiddle));
    }

    /**
     * 修改
     */
    @Log(title = "流程是否归档中间", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DaIsArchivistMiddle daIsArchivistMiddle)
    {
        return toAjax(daIsArchivistMiddleService.updateDaIsArchivistMiddle(daIsArchivistMiddle));
    }

    /**
     * 删除
     */
    @Log(title = "流程是否归档中间", businessType = BusinessType.DELETE)
	@DeleteMapping("/{flowIds}")
    public AjaxResult remove(@PathVariable String[] flowIds)
    {
        return toAjax(daIsArchivistMiddleService.deleteDaIsArchivistMiddleByFlowIds(flowIds));
    }
}
