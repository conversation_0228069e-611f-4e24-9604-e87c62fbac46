package org.ruoyi.core.archivist.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.RepeatSubmit;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import org.ruoyi.core.archivist.domain.DaArchivistMain;
import org.ruoyi.core.archivist.service.IDaArchivistMainService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 档案详情Controller
 *
 * <AUTHOR>
 * @date 2023-11-28
 */
@RestController
@RequestMapping("/archivistMain/main")
public class DaArchivistMainController extends BaseController
{
    @Autowired
    private IDaArchivistMainService daArchivistMainService;

    /**
     * 查询档案详情列表
     */
    // @PreAuthorize("@ss.hasPermi('archivistMain:main:list')")
    @RepeatSubmit(interval = 1200,message = "数据正在处理中，请稍后！")
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody DaArchivistMain daArchivistMain)
    {
        List<DaArchivistMain> list = daArchivistMainService.selectDaArchivistMainList(daArchivistMain);
        return getDataTable(list);
    }

    /**
     * 导出档案详情列表
     */
    // @PreAuthorize("@ss.hasPermi('archivistMain:main:export')")
    @RepeatSubmit(interval = 8000,message = "数据正在导出，请勿重复提交！")
    @Log(title = "档案详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DaArchivistMain daArchivistMain)
    {
        List<DaArchivistMain> list = daArchivistMainService.selectDaArchivistMainList(daArchivistMain);
        ExcelUtil<DaArchivistMain> util = new ExcelUtil<DaArchivistMain>(DaArchivistMain.class);
        util.exportExcel(response, list, "档案详情数据");
    }

    /**
     * 获取档案详情详细信息
     */
    @PreAuthorize("@ss.hasPermi('fileManagement:contractFile:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return AjaxResult.success(daArchivistMainService.selectDaArchivistMainById(id));
    }

    /**
     * 新增档案详情
     */
    @PreAuthorize("@ss.hasPermi('archivistMain:main:add')")
    @RepeatSubmit(interval = 3000,message = "请勿重复提交！")
    @Log(title = "档案详情", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DaArchivistMain daArchivistMain)
    {
        return toAjax(daArchivistMainService.insertDaArchivistMain(daArchivistMain));
    }

    /**
     * 修改档案详情
     */
    // @PreAuthorize("@ss.hasPermi('archivistMain:main:edit')")
    @RepeatSubmit(interval = 3000,message = "请勿重复提交！")
    @Log(title = "档案详情", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DaArchivistMain daArchivistMain)
    {
        return toAjax(daArchivistMainService.updateDaArchivistMain(daArchivistMain));
    }

    /**
     * 删除档案详情
     */
    @PreAuthorize("@ss.hasPermi('archivistMain:main:remove')")
    @RepeatSubmit(interval = 3000,message = "请勿重复提交！")
    @Log(title = "档案详情", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(daArchivistMainService.deleteDaArchivistMainByIds(ids));
    }

    /**
     * 根据目录id查询档案
     */
    @GetMapping(value = "/getArchivistData/{catalogueId}")
    @RepeatSubmit(interval = 1200,message = "请勿重复提交！")
    public TableDataInfo getArchivistData(@PathVariable("catalogueId") Long catalogueId)
    {
        return getDataTable(daArchivistMainService.getArchivistData(catalogueId));
    }

    /**
     * 修复档案编号重复问题
     */
    @GetMapping(value = "/updateCode")
    public AjaxResult updateCode()
    {
        int i = daArchivistMainService.updateCode();
        return AjaxResult.success(i);
    }

    /**
     * 档案列表条件筛选
     * 公司和部门
     * @param pertainArchivist 档案库类别 HT:合同 JCXZ:基础行政
     */
    @GetMapping("/getCompanyAndDeptTree")
    public AjaxResult getCompanyAndDeptTree(@RequestParam("pertainArchivist") String pertainArchivist){
        return AjaxResult.success(daArchivistMainService.getCompanyAndDeptTreeList(pertainArchivist));
    }
}
