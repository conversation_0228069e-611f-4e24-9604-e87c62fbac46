package org.ruoyi.core.archivist.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 档案-目录中间对象 da_archivist_catalogue_middle
 * 
 * <AUTHOR>
 * @date 2023-11-28
 */
public class DaArchivistCatalogueMiddle extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 档案id */
    @Excel(name = "档案id")
    private String archivistId;

    /** 所属目录id */
    @Excel(name = "所属目录id")
    private Long catalogueId;

    public void setArchivistId(String archivistId) 
    {
        this.archivistId = archivistId;
    }

    public String getArchivistId() 
    {
        return archivistId;
    }
    public void setCatalogueId(Long catalogueId) 
    {
        this.catalogueId = catalogueId;
    }

    public Long getCatalogueId() 
    {
        return catalogueId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("archivistId", getArchivistId())
            .append("catalogueId", getCatalogueId())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .toString();
    }
}
