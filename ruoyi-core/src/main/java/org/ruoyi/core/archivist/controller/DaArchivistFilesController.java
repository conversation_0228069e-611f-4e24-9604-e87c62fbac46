package org.ruoyi.core.archivist.controller;

import java.io.*;
import java.net.URLEncoder;
import java.util.List;
import java.util.zip.Adler32;
import java.util.zip.CheckedOutputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.ListObjectsRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.OSSObjectSummary;
import com.aliyun.oss.model.ObjectListing;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.oss.OSSUtil;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.commons.io.FilenameUtils;
import org.ruoyi.core.archivist.domain.DaArchivistFiles;
import org.ruoyi.core.archivist.file.fileUtils;
import org.ruoyi.core.archivist.service.IDaArchivistFilesService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 * 
 * <AUTHOR>
 * @date 2023-12-08
 */
@RestController
@RequestMapping("/archivistFiles/file")
public class DaArchivistFilesController extends BaseController {
    @Autowired
    private IDaArchivistFilesService daArchivistFilesService;

    /**
     * 查询【请填写功能名称】列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DaArchivistFiles daArchivistFiles) {
        startPage();
        List<DaArchivistFiles> list = daArchivistFilesService.selectDaArchivistFilesList(daArchivistFiles);
        return getDataTable(list);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DaArchivistFiles daArchivistFiles) {
        List<DaArchivistFiles> list = daArchivistFilesService.selectDaArchivistFilesList(daArchivistFiles);
        ExcelUtil<DaArchivistFiles> util = new ExcelUtil<DaArchivistFiles>(DaArchivistFiles.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(daArchivistFilesService.selectDaArchivistFilesById(id));
    }

    /**
     * 新增
     */
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DaArchivistFiles daArchivistFiles) {
        return toAjax(daArchivistFilesService.insertDaArchivistFiles(daArchivistFiles));
    }

    /**
     * 修改【请填写功能名称】
     */
    @Log(title = "修改终稿扫描件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DaArchivistFiles daArchivistFiles) {
        return toAjax(daArchivistFilesService.updateDaArchivistFiles(daArchivistFiles));
    }

    /**
     * 删除
     */
    @Log(title = "删除终稿扫描件", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(daArchivistFilesService.deleteDaArchivistFilesByIds(ids));
    }

    /**
     * 新增终稿扫描件
     */
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping("/addFiles")
    public AjaxResult addFiles(@RequestBody DaArchivistFiles daArchivistFiles) {
        return toAjax(daArchivistFilesService.addDaArchivistFiles(daArchivistFiles));
    }

    @PostMapping("/downloadFileZip")
    public void getOssFile(HttpServletRequest request, HttpServletResponse response, @RequestBody DaArchivistFiles daArchivistFiles){
        fileUtils.getOssFile(request, response, daArchivistFiles);
    }
}
