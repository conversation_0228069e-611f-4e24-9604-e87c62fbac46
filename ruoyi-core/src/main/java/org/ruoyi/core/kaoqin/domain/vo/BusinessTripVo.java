package org.ruoyi.core.kaoqin.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import org.ruoyi.core.kaoqin.domain.BusinessTrip;
import org.ruoyi.core.kaoqin.domain.BusinessTripSlave;
import org.ruoyi.core.kaoqin.domain.KqFile;

import java.util.Date;
import java.util.List;

@Data
public class BusinessTripVo extends BusinessTrip {
    private List<Handover> companionsList;

    private List<BusinessTripSlave> businessTripSlaveList;

    /**
     * 附件
     */
    private List<KqFile> files;

    private List<Long> fileIds;

    private List<Long> ids;
    /**
     * 搜索条件
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTimeStart;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTimeEnd;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTimeStart;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTimeEnd;

    @Excel(name = "关联项目",sort = 9)
    private String projectName;

    private String handleState;

    private String refuseReason;

    @Excel(name = "申请人" ,sort = 1)
    private String applicantName;

    @Excel(name = "出差起始地点",sort = 3)
    private String setOut;

    @Excel(name = "出差到达地点",sort = 4)
    private String reach;

    @Excel(name = "出差起始时间",sort = 5)
    private String fullStartTime;

    @Excel(name = "出差起始",sort = 6)
    private String fullEndTime;

    private Long times;

    @Excel(name = "出差时长(天)",sort = 7)
    private String fullTimes;

    @Excel(name = "交通工具",sort = 8)
    private String vehicle;
    /**
     * 创建人userId
     */
    private Long createByUserId;

    private String createNickName;

    private List<Long> deptIds;

    private List<Long> unitIds;

    private List<String> createByList;
}
