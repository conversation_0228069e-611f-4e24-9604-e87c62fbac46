package org.ruoyi.core.kaoqin.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.kaoqin.domain.WorkOvertimeSlave;
import org.ruoyi.core.kaoqin.mapper.WorkOvertimeSlaveMapper;
import org.ruoyi.core.kaoqin.service.IWorkOvertimeSlaveService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 加班从Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-04
 */
@Service
public class WorkOvertimeSlaveServiceImpl implements IWorkOvertimeSlaveService
{
    @Autowired
    private WorkOvertimeSlaveMapper workOvertimeSlaveMapper;

    /**
     * 查询加班从
     *
     * @param id 加班从主键
     * @return 加班从
     */
    @Override
    public WorkOvertimeSlave selectWorkOvertimeSlaveById(Long id)
    {
        return workOvertimeSlaveMapper.selectWorkOvertimeSlaveById(id);
    }

    /**
     * 查询加班从列表
     *
     * @param workOvertimeSlave 加班从
     * @return 加班从
     */
    @Override
    public List<WorkOvertimeSlave> selectWorkOvertimeSlaveList(WorkOvertimeSlave workOvertimeSlave)
    {
        return workOvertimeSlaveMapper.selectWorkOvertimeSlaveList(workOvertimeSlave);
    }

    /**
     * 新增加班从
     *
     * @param workOvertimeSlave 加班从
     * @return 结果
     */
    @Override
    public int insertWorkOvertimeSlave(WorkOvertimeSlave workOvertimeSlave)
    {
        workOvertimeSlave.setCreateTime(DateUtils.getNowDate());
        return workOvertimeSlaveMapper.insertWorkOvertimeSlave(workOvertimeSlave);
    }

    @Override
    public int insertWorkOvertimeSlaveBatch(List<WorkOvertimeSlave> workOvertimeSlave){
        return workOvertimeSlaveMapper.insertWorkOvertimeSlaveBatch(workOvertimeSlave);
    }

    /**
     * 修改加班从
     *
     * @param workOvertimeSlave 加班从
     * @return 结果
     */
    @Override
    public int updateWorkOvertimeSlave(WorkOvertimeSlave workOvertimeSlave)
    {
        workOvertimeSlave.setUpdateTime(DateUtils.getNowDate());
        return workOvertimeSlaveMapper.updateWorkOvertimeSlave(workOvertimeSlave);
    }

    /**
     * 批量删除加班从
     *
     * @param ids 需要删除的加班从主键
     * @return 结果
     */
    @Override
    public int deleteWorkOvertimeSlaveByIds(Long[] ids)
    {
        return workOvertimeSlaveMapper.deleteWorkOvertimeSlaveByIds(ids);
    }

    /**
     * 删除加班从信息
     *
     * @param id 加班从主键
     * @return 结果
     */
    @Override
    public int deleteWorkOvertimeSlaveById(Long id)
    {
        return workOvertimeSlaveMapper.deleteWorkOvertimeSlaveById(id);
    }

    @Override
    public int deleteWorkOvertimeSlaveByOverTimeId(Long overTimeId){
        return workOvertimeSlaveMapper.deleteWorkOvertimeSlaveByOverTimeId(overTimeId);
    }
}
