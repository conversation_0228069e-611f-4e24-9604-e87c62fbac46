package org.ruoyi.core.kaoqin.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 月报提醒对象 kq_month_log_notify
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@Data
public class Notify extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 通知模块 */
    @Excel(name = "通知模块")
    private String notifyModule;

    /** 月份条件 */
    @Excel(name = "跳转url")
    private String url;

    /** 通知类型 0通知 1待办 */
    @Excel(name = "通知类型 0通知 1待办")
    private String notifyType;

    /** 通知内容 */
    @Excel(name = "通知内容")
    private String notifyMsg;

    /** 待处理人id */
    @Excel(name = "待处理人id")
    private Long disposeUser;

    /** 阅读状态 0未阅 1已阅 */
    @Excel(name = "阅读状态 0未阅 1已阅")
    private String viewFlag;

    /** 状态 0正常 1禁用 */
    @Excel(name = "状态 0正常 1禁用")
    private String status;

    /** 提醒正文 */
    @Excel(name = "提醒正文")
    private String remindText;

    /** 关联id */
    @Excel(name = "关联id")
    private Long correlationId;

    private String month;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("notifyModule", getNotifyModule())
            .append("url", getUrl())
            .append("notifyType", getNotifyType())
            .append("notifyMsg", getNotifyMsg())
            .append("disposeUser", getDisposeUser())
            .append("viewFlag", getViewFlag())
            .append("status", getStatus())
            .append("remindText", getRemindText())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
