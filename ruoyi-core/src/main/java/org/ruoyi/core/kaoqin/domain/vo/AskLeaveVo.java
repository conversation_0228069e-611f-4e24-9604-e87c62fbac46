package org.ruoyi.core.kaoqin.domain.vo;

import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.ruoyi.core.kaoqin.domain.AskLeave;
import org.ruoyi.core.kaoqin.domain.AskLeaveSlave;

/**
 * 请假对象 kq_ask_leave
 *
 * <AUTHOR>
 * @date 2024-03-11
 */
@Data
public class AskLeaveVo extends AskLeave
{
    private String nickName;
    /**
     * 查询条件某月
     */
    private String month;

    private List<AskLeaveSlave> askLeaveSlaveList;
    /**条件 */
    private List<Long> deptIds;
    /**条件 */
    private List<Long> unitIds;

    private List<String> dates;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    private String leaveType;
}
