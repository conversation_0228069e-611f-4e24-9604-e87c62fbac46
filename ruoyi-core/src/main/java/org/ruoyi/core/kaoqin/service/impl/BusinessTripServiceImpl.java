package org.ruoyi.core.kaoqin.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import org.checkerframework.checker.units.qual.K;
import org.ruoyi.core.constant.UploadFeatureConstants;
import org.ruoyi.core.information.domain.vo.PageUtil;
import org.ruoyi.core.kaoqin.domain.*;
import org.ruoyi.core.kaoqin.domain.vo.BusinessTripVo;
import org.ruoyi.core.kaoqin.domain.vo.Handover;
import org.ruoyi.core.kaoqin.mapper.BusinessTripMapper;
import org.ruoyi.core.kaoqin.service.IBusinessTripService;
import org.ruoyi.core.kaoqin.service.INotifyService;
import org.ruoyi.core.personnel.domain.vo.PersonnelArchivesVo;
import org.ruoyi.core.personnel.service.impl.PersonnelArchivesServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;
import static com.ruoyi.common.utils.SecurityUtils.getUsername;
import static org.ruoyi.core.kaoqin.service.impl.MonthLogMainServiceImpl.findSubordinates;

/**
 * 出差申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-28
 */
@Service
public class BusinessTripServiceImpl implements IBusinessTripService
{
    @Autowired
    private BusinessTripMapper businessTripMapper;
    @Autowired
    private BusinessTripSlaveServiceImpl businessTripSlaveService;
    @Autowired
    private KqFileServiceImpl kqFileService;
    @Autowired
    private INotifyService notifyService;
    @Autowired
    private PersonnelArchivesServiceImpl personnelArchivesService;
    /**
     * 查询出差申请
     *
     * @param id 出差申请主键
     * @return 出差申请
     */
    @Override
    public BusinessTripVo selectBusinessTripById(Long id)
    {
        BusinessTripVo businessTrip = businessTripMapper.selectBusinessTripById(id);
        if (businessTrip.getCompanions() != null && !businessTrip.getCompanions().isEmpty()){
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                List<Handover> handoverList = objectMapper.readValue(businessTrip.getCompanions(),  new TypeReference<List<Handover>>() {});
                businessTrip.setCompanionsList(handoverList);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return businessTrip;
    }

    /**
     * 查询出差申请列表
     *
     * @param businessTrip 出差申请
     * @return 出差申请
     */
    @Override
    public List<BusinessTripVo> selectBusinessTripList(BusinessTripVo businessTrip)
    {
        //数据范围
        LoginUser loginUser = getLoginUser();
        Map<String, List<Long>> dataRange = personnelArchivesService.getDataRange(loginUser);
        businessTrip.setDeptIds(dataRange.get("deptIds"));
        businessTrip.setUnitIds(dataRange.get("unitIds"));
        businessTrip.setCreateBy(loginUser.getUser().getUserName());
        PageUtil.startPage();
        List<BusinessTripVo> businessTripVos = businessTripMapper.selectBusinessTripList(businessTrip);
        businessTripVos.forEach(vo ->{
            if (vo.getCompanions() != null && !vo.getCompanions().isEmpty()){
                try {
                    ObjectMapper objectMapper = new ObjectMapper();
                    List<Handover> handoverList = objectMapper.readValue(vo.getCompanions(),  new TypeReference<List<Handover>>() {});
                    vo.setCompanionsList(handoverList);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
        return businessTripVos;
    }

    @Override
    public List<BusinessTripVo> selectBusinessTripListExport(BusinessTripVo businessTrip)
    {
        //数据范围
        LoginUser loginUser = getLoginUser();
        Map<String, List<Long>> dataRange = personnelArchivesService.getDataRangeFilterRoleKey(loginUser,"rz-");
        businessTrip.setDeptIds(dataRange.get("deptIds"));
        businessTrip.setUnitIds(dataRange.get("unitIds"));
        businessTrip.setCreateBy(loginUser.getUser().getUserName());
        //List<String> createByList = personnelArchivesService.getSubordinateList().stream().map(PersonnelArchivesVo::getSysName).collect(Collectors.toList());

        List<PersonnelArchivesVo> archivesVoList = personnelArchivesService.selectListOfMonthLog();
        Long userId = getLoginUser().getUser().getUserId();
        //查询登陆人 直属下属人员档案  以及下属的下属
        List<PersonnelArchivesVo> collect = archivesVoList.stream()
                .filter(vo -> vo != null && vo.getDirectSuperior() != null && vo.getDirectSuperior().equals(userId))
                .flatMap(vo -> Stream.concat(Stream.of(vo), findSubordinates(archivesVoList, vo.getId()).stream()))  //获取下属的下属
                .collect(Collectors.toList());
        //根据人员档案list,获取人员的登陆名 用来查询月报
        List<String> createByList = collect.stream().map(PersonnelArchivesVo::getSysName).collect(Collectors.toList());
        businessTrip.setCreateByList(createByList);

        List<BusinessTripVo> businessTripVos = businessTripMapper.selectBusinessTripListExport(businessTrip);
        businessTripVos.forEach(vo ->{
            if (vo.getCompanions() != null && !vo.getCompanions().isEmpty()){
                try {
                    ObjectMapper objectMapper = new ObjectMapper();
                    List<Handover> handoverList = objectMapper.readValue(vo.getCompanions(),  new TypeReference<List<Handover>>() {});
                    vo.setCompanionsList(handoverList);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                StringBuilder companions = new StringBuilder();
                vo.getCompanionsList().forEach(handover ->{
                    companions.append(handover.getNickName()).append(",");
                });
                vo.setCompanions(companions.toString());
            }
            if (!Objects.equals(vo.getTimes(), vo.getBusinessTripTimes())){
                vo.setFullTimes(vo.getTimes() + "\n" + "合计" + vo.getBusinessTripTimes() + "天");
            } else {
                vo.setFullTimes(vo.getTimes().toString());
            }


        });
        return businessTripVos;
    }

    /**
     * 新增出差申请
     *
     * @param businessTrip 出差申请
     * @return 结果
     */
    @Override
    public BusinessTripVo insertBusinessTrip(BusinessTripVo businessTrip)
    {
        businessTrip.setCreateBy(getLoginUser().getUsername());
        if (businessTrip.getCompanionsList() != null){
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                businessTrip.setCompanions(objectMapper.writeValueAsString(businessTrip.getCompanionsList()));
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
        }
        if (businessTrip.getId() == null){
            int count = getCountByCreateTime(DateUtils.getDate()) + 1;
            String createTimeNum = DateUtils.dateTimeNow("yyyyMMdd");
            businessTrip.setBusinessTripCode("CCSQ" + createTimeNum + String.format("%03d", count));

            List<BusinessTripSlave> slaves = businessTrip.getBusinessTripSlaveList();
            businessTripMapper.insertBusinessTrip(businessTrip);
            slaves.forEach( slave ->{
                slave.setMainId(businessTrip.getId());
            });
            businessTrip.setCreateBy(getLoginUser().getUsername());
            businessTripSlaveService.insertBusinessTripSlaveBatch(slaves);

            if (businessTrip.getFileIds() != null){
                KqFile kqFile = new KqFile();
                kqFile.setFileType("2");
                kqFile.setIds(businessTrip.getFileIds());
                kqFile.setCorrelationId(businessTrip.getId());
                kqFileService.correlationFile(kqFile);
            }
        } else {
            //审核不通过重新提交,生成新的数据
            if(businessTrip.getStatus() != null && "4".equals(businessTrip.getStatus())){
                //逻辑删除原来的数据
                BusinessTrip trip = businessTripMapper.selectBusinessTripById(businessTrip.getId());
                trip.setIsDelete("0");
                businessTripMapper.updateBusinessTrip(trip);

                businessTrip.setProcessId("");
                businessTrip.setStatus("1");
                businessTrip.setEffective("0");
                insertBusinessTrip(businessTrip);
            }

            businessTripSlaveService.deleteBusinessTripSlaveByMainId(businessTrip.getId());
            List<BusinessTripSlave> slaves =  businessTrip.getBusinessTripSlaveList();
            slaves.forEach(slave ->{
                slave.setMainId(businessTrip.getId());
            });
            KqFile file = new KqFile();
            file.setCorrelationId(businessTrip.getId());
            file.setFileType("2");
            kqFileService.deleteByCorrelationId(file);

            if (businessTrip.getFileIds() != null){
                KqFile kqFile = new KqFile();
                kqFile.setIds(businessTrip.getFileIds());
                kqFile.setFileType("2");
                kqFile.setCorrelationId(businessTrip.getId());
                kqFileService.correlationFile(kqFile);
            }

            businessTripSlaveService.insertBusinessTripSlaveBatch(slaves);
            businessTrip.setUpdateTime(DateUtils.getNowDate());
            businessTrip.setUpdateBy(getLoginUser().getUsername());
            businessTripMapper.updateBusinessTrip(businessTrip);
        }
        return businessTrip;
    }

    /**
     * 修改出差申请
     *
     * @param businessTrip 出差申请
     * @return 结果
     */
    @Override
    public int updateBusinessTrip(BusinessTrip businessTrip)
    {
        businessTrip.setUpdateTime(DateUtils.getNowDate());
        return businessTripMapper.updateBusinessTrip(businessTrip);
    }

    /**
     * 批量删除出差申请
     *
     * @param ids 需要删除的出差申请主键
     * @return 结果
     */
    @Override
    public int deleteBusinessTripByIds(Long[] ids)
    {
        return businessTripMapper.deleteBusinessTripByIds(ids);
    }

    /**
     * 删除出差申请信息
     *
     * @param id 出差申请主键
     * @return 结果
     */
    @Override
    public int deleteBusinessTripById(Long id)
    {
        return businessTripMapper.deleteBusinessTripById(id);
    }

    @Override
    public int getCountByCreateTime(String createTime){
        return businessTripMapper.getCountByCreateTime(createTime);
    }

    @Override
    public AjaxResult uploadFile(MultipartFile file) {
        try {
            String name = file.getOriginalFilename();
            String url = FileUploadUtils.uploadOSS(UploadFeatureConstants.KQ, file);
            KqFile kqFile = new KqFile();
            kqFile.setFileUrl(url);
            kqFile.setFileName(name);
            kqFile.setFileState("0");
            kqFile.setFileType("2");
            kqFile.setCreateTime(DateUtils.getNowDate());
            kqFile.setCreateBy(getUsername());
            kqFileService.insertKqFile(kqFile);
            return AjaxResult.success(kqFile);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    @Override
    public int processBusinessTrip(BusinessTrip businessTrip){
        return businessTripMapper.updateBusinessTrip(businessTrip);
    }

    @Override
    public int passBusinessTrip(BusinessTrip businessTrip){
        businessTrip.setStatus("3");
        businessTrip.setAuditCompletionTime(DateUtils.getNowDate());
        return businessTripMapper.updateBusinessTrip(businessTrip);
    }

    @Override
    public int unpassBusinessTrip(BusinessTrip businessTrip){
        businessTrip.setStatus("4");
        return businessTripMapper.updateBusinessTrip(businessTrip);
    }

    /**
     * 通过流程 id查询出差申请
     *
     * @param businessTrip 出差申请主键
     * @return 出差申请
     */
    public BusinessTrip selectBusinessTripByProcess(BusinessTripVo businessTrip){
        BusinessTripVo businessTripVo = businessTripMapper.selectBusinessTripList(businessTrip).stream().findFirst().orElse(null);
        if (businessTripVo.getCompanions() != null && !businessTripVo.getCompanions().isEmpty()){
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                List<Handover> handoverList = objectMapper.readValue(businessTripVo.getCompanions(),  new TypeReference<List<Handover>>() {});
                businessTripVo.setCompanionsList(handoverList);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return businessTripVo;
    }

    @Override
    public int voidBusinessTrip(BusinessTrip businessTrip) {
        businessTrip.setUpdateTime(DateUtils.getNowDate());
        businessTrip.setVoidTime(DateUtils.getNowDate());

        VoidHandle voidHandle = new VoidHandle();
        voidHandle.setCorrelationId(businessTrip.getId());
        voidHandle.setListState("1");
        voidHandle.setType("4");
        voidHandle.setCreateById(getLoginUser().getUserId());
        voidHandle.setCreateBy(getLoginUser().getUsername());
        notifyService.insertVoidHandle(voidHandle);

        return businessTripMapper.voidBusinessTrip(businessTrip);
    }

    @Override
    public BusinessTripVo selectBusinessTripByHandleId(Long id){
        BusinessTripVo businessTripVo = businessTripMapper.selectBusinessTripByHandleId(id);
        if (businessTripVo.getCompanions() != null && !businessTripVo.getCompanions().isEmpty()){
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                List<Handover> handoverList = objectMapper.readValue(businessTripVo.getCompanions(),  new TypeReference<List<Handover>>() {});
                businessTripVo.setCompanionsList(handoverList);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return businessTripVo;
    }
}
