package org.ruoyi.core.kaoqin.mapper;

import org.ruoyi.core.kaoqin.domain.DayLog;
import org.ruoyi.core.kaoqin.domain.MonthLogSlave;

import java.util.List;

/**
 * 月报从表Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-06
 */
public interface MonthLogSlaveMapper
{
    /**
     * 查询月报从表
     *
     * @param id 月报从表主键
     * @return 月报从表
     */
    public MonthLogSlave selectMonthLogSlaveById(Long id);

    /**
     * 查询月报从表列表
     *
     * @param monthLogSlave 月报从表
     * @return 月报从表集合
     */
    public List<MonthLogSlave> selectMonthLogSlaveList(MonthLogSlave monthLogSlave);

    /**
     * 新增月报从表
     *
     * @param monthLogSlave 月报从表
     * @return 结果
     */
    public int insertMonthLogSlave(MonthLogSlave monthLogSlave);

    /**
     * 修改月报从表
     *
     * @param monthLogSlave 月报从表
     * @return 结果
     */
    public int updateMonthLogSlave(MonthLogSlave monthLogSlave);

    /**
     * 删除月报从表
     *
     * @param id 月报从表主键
     * @return 结果
     */
    public int deleteMonthLogSlaveById(Long id);

    /**
     * 批量删除月报从表
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMonthLogSlaveByIds(Long[] ids);

    public int replaceDayLogBatch(List<MonthLogSlave> monthLogSlaveList);
}
