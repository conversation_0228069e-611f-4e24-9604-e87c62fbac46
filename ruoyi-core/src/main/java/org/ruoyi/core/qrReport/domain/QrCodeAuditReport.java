package org.ruoyi.core.qrReport.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.ruoyi.core.notice.domain.TgNoticesFile;

/**
 * 审计报告二维码生成对象 qr_code_audit_report
 * 
 * <AUTHOR>
 * @date 2024-12-06
 */
public class QrCodeAuditReport extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 被审计单位名称 */
    @Excel(name = "被审计单位名称")
    private String auditedUnitName;

    /** 事务所名称 */
    @Excel(name = "事务所名称")
    private String firmName;

    /** 委托项目 */
    @Excel(name = "委托项目")
    private String entrustedProject;

    /** 报告文号 */
    @Excel(name = "报告文号")
    private String documentNumber;

    /** 报告日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "报告日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date documentDate;

    /** 报告开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date beginTime;

    /** 报告结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /** 中国注册会计师1 */
    @Excel(name = "中国注册会计师1")
    private String signAccountant1;

    /** 中国注册会计师2 */
    @Excel(name = "中国注册会计师2")
    private String signAccountant2;

    /** 报备状态 */
    @Excel(name = "报备状态")
    private String reportStatus;

    /** 唯一标识 */
    private String uniqueFlag;

    /** 报备状态值 */
    private String reportStatusLabel;

    /** 二维码 */
    private TgNoticesFile qrAuditReportFile;

    /** 创建人姓名 */
    private String createrNickName;

    private Integer pageSize;

    private Integer pageNum;

    /** 记录表修改前json */
    private String oldJsonDate;

    /** 记录表修改后json */
    private String newJsonDate;

    public String getUniqueFlag() {
        return uniqueFlag;
    }

    public void setUniqueFlag(String uniqueFlag) {
        this.uniqueFlag = uniqueFlag;
    }

    public String getOldJsonDate() {
        return oldJsonDate;
    }

    public void setOldJsonDate(String oldJsonDate) {
        this.oldJsonDate = oldJsonDate;
    }

    public String getNewJsonDate() {
        return newJsonDate;
    }

    public void setNewJsonDate(String newJsonDate) {
        this.newJsonDate = newJsonDate;
    }

    public String getReportStatusLabel() {
        return reportStatusLabel;
    }

    public void setReportStatusLabel(String reportStatusLabel) {
        this.reportStatusLabel = reportStatusLabel;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public String getCreaterNickName() {
        return createrNickName;
    }

    public void setCreaterNickName(String createrNickName) {
        this.createrNickName = createrNickName;
    }

    public TgNoticesFile getQrAuditReportFile() {
        return qrAuditReportFile;
    }

    public void setQrAuditReportFile(TgNoticesFile qrAuditReportFile) {
        this.qrAuditReportFile = qrAuditReportFile;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setAuditedUnitName(String auditedUnitName) 
    {
        this.auditedUnitName = auditedUnitName;
    }

    public String getAuditedUnitName() 
    {
        return auditedUnitName;
    }
    public void setFirmName(String firmName) 
    {
        this.firmName = firmName;
    }

    public String getFirmName() 
    {
        return firmName;
    }
    public void setEntrustedProject(String entrustedProject) 
    {
        this.entrustedProject = entrustedProject;
    }

    public String getEntrustedProject() 
    {
        return entrustedProject;
    }
    public void setDocumentNumber(String documentNumber) 
    {
        this.documentNumber = documentNumber;
    }

    public String getDocumentNumber() 
    {
        return documentNumber;
    }
    public void setDocumentDate(Date documentDate) 
    {
        this.documentDate = documentDate;
    }

    public Date getDocumentDate() 
    {
        return documentDate;
    }
    public void setSignAccountant1(String signAccountant1) 
    {
        this.signAccountant1 = signAccountant1;
    }

    public String getSignAccountant1() 
    {
        return signAccountant1;
    }
    public void setSignAccountant2(String signAccountant2) 
    {
        this.signAccountant2 = signAccountant2;
    }

    public String getSignAccountant2() 
    {
        return signAccountant2;
    }
    public void setReportStatus(String reportStatus) 
    {
        this.reportStatus = reportStatus;
    }

    public String getReportStatus() 
    {
        return reportStatus;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("auditedUnitName", getAuditedUnitName())
            .append("firmName", getFirmName())
            .append("entrustedProject", getEntrustedProject())
            .append("documentNumber", getDocumentNumber())
            .append("documentDate", getDocumentDate())
            .append("signAccountant1", getSignAccountant1())
            .append("signAccountant2", getSignAccountant2())
            .append("reportStatus", getReportStatus())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .toString();
    }
}
