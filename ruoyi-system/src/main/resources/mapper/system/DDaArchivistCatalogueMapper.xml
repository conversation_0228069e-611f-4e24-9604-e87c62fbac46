<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.DDaArchivistCatalogueMapper">
    
    <resultMap type="DaArchivistCatalogue" id="DaArchivistCatalogueResult">
        <result property="id"    column="id"    />
        <result property="catalogueName"    column="catalogue_name"    />
        <result property="parentId"    column="parent_id"    />
        <result property="catalogueSystemCode"    column="catalogue_system_code"    />
        <result property="catalogueCode"    column="catalogue_code"    />
        <result property="orgId"    column="org_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="pertainArchivist"    column="pertain_archivist"    />
        <result property="orderNum"    column="order_num"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>


    <insert id="insertDaArchivistCatalogue" parameterType="DaArchivistCataloguevVO" useGeneratedKeys="true" keyProperty="id">
        insert into da_archivist_catalogue
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="catalogueName != null">catalogue_name,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="catalogueSystemCode != null">catalogue_system_code,</if>
            <if test="catalogueCode != null">catalogue_code,</if>
            <if test="orgId != null">org_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="pertainArchivist != null">pertain_archivist,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="catalogueName != null">#{catalogueName},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="catalogueSystemCode != null">#{catalogueSystemCode},</if>
            <if test="catalogueCode != null">#{catalogueCode},</if>
            <if test="orgId != null">#{orgId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="pertainArchivist != null">#{pertainArchivist},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDaArchivistCatalogue" parameterType="DaArchivistCataloguevVO">
        update da_archivist_catalogue
        <trim prefix="SET" suffixOverrides=",">
            <if test="catalogueName != null">catalogue_name = #{catalogueName},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="catalogueSystemCode != null">catalogue_system_code = #{catalogueSystemCode},</if>
            <if test="catalogueCode != null">catalogue_code = #{catalogueCode},</if>
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="pertainArchivist != null">pertain_archivist = #{pertainArchivist},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where org_id = #{orgId} and parent_id is null
    </update>

    <select id="getCountByCreateTime" resultType="int" parameterType="String">
        SELECT COUNT(id) FROM da_archivist_catalogue
        <where>
            <if test="createTime != null  and createTime != ''"> and create_time like concat('%', #{createTime}, '%')</if>
        </where>
    </select>

    <select id="selectDictDataList" resultType="com.ruoyi.common.core.domain.entity.SysDictData">
        select * from sys_dict_data where dict_type = #{archivistPertain}
    </select>

</mapper>