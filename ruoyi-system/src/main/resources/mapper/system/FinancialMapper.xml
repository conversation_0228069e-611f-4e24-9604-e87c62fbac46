<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.FinancialMapper">
    <resultMap id="BaseResultMap" type="com.ruoyi.system.domain.dto.FinancialUserAccountSetsNew">
        <!--@mbg.generated-->
        <result column="account_sets_id" jdbcType="INTEGER" property="accountSetsId" />
        <result column="user_id" jdbcType="INTEGER" property="userId" />
        <result column="role_type" jdbcType="VARCHAR" property="roleType" />
    </resultMap>

    <insert id="insertUserAccountSets">
        insert into financial_user_account_sets
        (account_sets_id, user_id, role_type,create_user)
        values
        <foreach collection="userAccountSets.userIds" item="item" separator=",">
            (#{userAccountSets.accountSetsId,jdbcType=INTEGER}, #{item,jdbcType=INTEGER}, #{userAccountSets.roleType,jdbcType=VARCHAR},#{userAccountSets.createUser,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
    
    <select id="queryAccountSetsIdByUnitId" resultType="java.lang.String">
        select id from financial_account_sets where platform_code = #{unitId,jdbcType = VARCHAR} limit 1
    </select>

    <delete id="deleteUserAccountSets">
        delete from financial_user_account_sets where account_sets_id = #{accountSetsId,jdbcType = VARCHAR}
        and user_id in
        <foreach collection="userList" item="userId" open="(" close=")" separator=",">
            #{userId,jdbcType=INTEGER}
        </foreach>
        <if test="roleType !=null and roleType !=''">
            and role_type = #{roleType,jdbcType=VARCHAR}
        </if>
    </delete>

</mapper>