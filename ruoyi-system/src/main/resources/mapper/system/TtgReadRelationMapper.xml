<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.TtgReadRelationMapper">

    <resultMap type="TgReadRelation" id="TgReadRelationResult">
        <result property="noticeId"    column="notice_id"    />
        <result property="userId"    column="user_id"    />
        <result property="readType"    column="read_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="noticeType"    column="notice_type"    />
        <result property="noticeName"    column="notice_name"    />
        <result property="createNickName"    column="nick_name"    />
        <result property="readTypeLabel"    column="readTypeLabel"    />
        <result property="isHeader"    column="is_header"    />
        <result property="isEmphasis"    column="is_emphasis"    />
        <result property="version"    column="version"    />
    </resultMap>

    <sql id="selectTgReadRelationVo">
        select notice_id, user_id, read_type, create_by, create_time, update_by, update_time from tg_read_relation
    </sql>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into tg_read_relation (notice_id, user_id, create_by, create_time) values
        <foreach collection="readRelationVoList" item="item" separator=",">
            (#{item.noticeId},#{item.userId},#{item.createBy},#{item.createTime})
        </foreach>
    </insert>

    <select id="selectAuthCompanyUsers" resultType="com.ruoyi.system.domain.vo.AauthDetailVo">
        SELECT
            am.id authMainId, am.third_id thirdId
        FROM
            auth_main am
        WHERE
            am.module_type = #{moduleType} and am.role_type = #{roleType}
          AND am.STATUS = '0'
          AND am.third_type = '1'
          AND am.permission_time >
              NOW( )  UNION ALL
        SELECT
            am.id authMainId, am.third_id thirdId
        FROM
            auth_detail ad
                left join auth_main am on ad.auth_main_id = am.id
        WHERE
            am.module_type = #{moduleType} and am.role_type = #{roleType} and ad.third_table_id = #{companyId}
          AND am.STATUS = '0'
          AND ad.STATUS = '0'
          AND am.third_type = '1'
          AND am.permission_time >
              NOW( )
    </select>

    <select id="selectLaunchedNoticesByCompanyId" resultType="com.ruoyi.system.domain.vo.TtgNoticeMainVo">
        select id, notice_name noticeName, publish_status publishStatus from tg_notice_main where publish_company in
        <foreach collection="companyIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and publish_status = '1' and del_flag = '0'
    </select>

    <delete id="deleteTgReadRelationByNoticeIdListAndUserId">
        delete from tg_read_relation where notice_id in
        <foreach collection="noticeIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and user_id = #{userId}
    </delete>

</mapper>