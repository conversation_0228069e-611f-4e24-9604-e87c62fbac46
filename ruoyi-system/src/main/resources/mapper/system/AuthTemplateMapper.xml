<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.AuthTemplateMapper">

    <resultMap type="AuthTemplate" id="AuthTemplateResult">
        <result property="id"    column="id"    />
        <result property="templateName"    column="template_name"    />
        <result property="templateExplain"    column="template_explain"    />
        <result property="templateType"    column="template_type"    />
        <result property="companyType"    column="company_type"    />
        <result property="functionType"    column="function_type"    />
        <result property="status"    column="status"    />
        <result property="source"    column="source"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="createUnit"    column="create_unit"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createName"    column="createName"    />
        <result property="useAuthFlag"    column="useAuthFlag"    />
        <result property="updateUnit"    column="update_unit"    />
        <result property="useAuthFlag"    column="useAuthFlag"    />
    </resultMap>

    <resultMap id="AuthTemplateAuthTemplateDetailResult" type="AuthTemplate" extends="AuthTemplateResult">
        <collection property="authTemplateDetailList" notNullColumn="sub_id" javaType="java.util.List" resultMap="AuthTemplateDetailResult" />
    </resultMap>

    <resultMap type="AuthTemplateDetail" id="AuthTemplateDetailResult">
        <result property="id"    column="sub_id"    />
        <result property="templateId"    column="sub_template_id"    />
        <result property="moduleType"    column="sub_module_type"    />
        <result property="permissionRule"    column="sub_permission_rule"    />
        <result property="status"    column="sub_status"    />
        <result property="createBy"    column="sub_create_by"    />
        <result property="createTime"    column="sub_create_time"    />
        <result property="createUnit"    column="sub_create_unit"    />
        <result property="updateBy"    column="sub_update_by"    />
        <result property="updateTime"    column="sub_update_time"    />
        <result property="updateUnit"    column="sub_update_unit"    />
    </resultMap>

    <resultMap type="AuthMain" id="AuthMainResult">
        <result property="id"    column="id"    />
        <result property="thirdType"    column="third_type"    />
        <result property="thirdId"    column="third_id"    />
        <result property="moduleType"    column="module_type"    />
        <result property="roleType"    column="role_type"    />
        <result property="permissionScope"    column="permission_scope"    />
        <result property="permissionType"    column="permission_type"    />
        <result property="permissionTime"    column="permission_time"    />
        <result property="status"    column="status"    />
        <result property="source"    column="source"    />
        <result property="createBy"    column="create_by"    />
        <result property="createId"    column="create_id"    />
        <result property="createDeptId"    column="create_dept_id"    />
        <result property="createUnitId"    column="create_unit_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateId"    column="update_id"    />
        <result property="updateDeptId"    column="update_dept_id"    />
        <result property="updateUnitId"    column="update_unit_id"    />
        <result property="createName"    column="createName"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAuthTemplateVo">
        select id, template_name, template_explain, template_type, company_type, function_type, status, source, create_by, create_time, create_unit, update_by, update_time, update_unit from auth_template
    </sql>

    <select id="selectAuthTemplateList" resultMap="AuthTemplateResult">
        SELECT
        temp.id,
        temp.template_name,
        temp.template_explain,
        temp.template_type,
        temp.company_type,
        temp.function_type,
        temp.STATUS,
        temp.create_by,
        temp.create_time,
        temp.create_unit,
        temp.update_by,
        temp.update_time,
        temp.update_unit,
        '0' useAuthFlag,
        us.nick_name createName
        FROM
        auth_template temp
        LEFT JOIN auth_detail det ON temp.id = det.third_table_id
        LEFT JOIN auth_main ma ON ma.id = det.auth_main_id
        LEFT JOIN sys_user us ON us.user_id = temp.create_by

        WHERE
        ma.third_id = #{userId} AND det.third_table_name = 'auth_template' AND ma.third_type = '1'
        AND temp.status = '0' and temp.source = 'SYS'
        <if test="temp.templateType != null "> and temp.template_type = #{temp.templateType}</if>
        <if test="temp.templateName != null"> and temp.template_name like concat('%', #{temp.templateName}, '%')</if>
        <if test="companyType != null and companyType.length > 0"> and temp.company_type in(
            <foreach item="item" index="index" collection="companyType" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="functionType != null and functionType.length > 0"> and temp.function_type in(
            <foreach item="item" index="index" collection="functionType" separator=",">
                #{item}
            </foreach>
            )
        </if>
        group by temp.id
        order by temp.create_time desc
    </select>

    <select id="selectAuthTemplateById" resultMap="AuthTemplateAuthTemplateDetailResult">
        select a.id, a.template_name, a.template_explain, a.template_type, a.company_type, a.function_type, a.status, a.create_by, a.create_time, a.create_unit, a.update_by, a.update_time, a.update_unit,
               b.id as sub_id, b.template_id as sub_template_id, b.module_type as sub_module_type, b.permission_rule as sub_permission_rule, b.status as sub_status, b.create_by as sub_create_by, b.create_time as sub_create_time, b.create_unit as sub_create_unit, b.update_by as sub_update_by, b.update_time as sub_update_time, b.update_unit as sub_update_unit
        from auth_template a
                 left join auth_template_detail b on b.template_id = a.id
        where a.id = #{id} and a.status = '0' and b.status = '0' and a.source = 'SYS'
    </select>

    <select id="checkTemplateName" resultType="java.lang.Integer">
        select count(id) from auth_template where template_name = #{templateName} and template_type = #{type} and status = '0' and source = 'SYS'
    </select>

    <select id="selectAuthTemplateListAll" resultMap="AuthTemplateResult">
        select temp.id, temp.template_name, temp.template_explain, '1' AS useAuthFlag, temp.template_type, temp.company_type, temp.function_type, temp.status, temp.create_by, temp.create_time, temp.create_unit, temp.update_by, temp.update_time, temp.update_unit, us.nick_name createName
        from auth_template temp
        left join sys_user us on us.user_id = temp.create_by
        <where>
            temp.status = '0' and temp.source = 'SYS'
            <if test="temp.templateType != null "> and temp.template_type = #{temp.templateType}</if>
            <if test="temp.templateName != null"> and temp.template_name like concat('%', #{temp.templateName}, '%')</if>
            <if test="companyType != null and companyType.length > 0"> and temp.company_type in(
                <foreach item="item" index="index" collection="companyType" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="functionType != null and functionType.length > 0"> and temp.function_type in(
                <foreach item="item" index="index" collection="functionType" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </where>
        order by temp.create_time desc
    </select>

    <select id="selectAuthUserByTempId" resultType="com.ruoyi.system.domain.AuthTemplate">
        SELECT
        ma.third_id authUserId,
        det.third_table_id id,
        us.nick_name createBy
        FROM
        auth_detail det
        LEFT JOIN auth_main ma ON ma.id = det.auth_main_id
        AND ma.third_type = '1'
        LEFT JOIN sys_user us ON us.user_id = ma.third_id
        WHERE
        det.third_table_id IN (
        <foreach item="item" index="index" collection="authTemp" separator=",">
            #{item.id}
        </foreach>)
        AND det.third_table_name = 'auth_template'
    </select>

    <select id="selectRelByTempId" resultType="java.lang.Integer">
        SELECT max(version) FROM auth_template_rel where template_id = #{id}
    </select>

    <select id="selectAuthMainByModuleAndUser" resultType="com.ruoyi.system.domain.AuthTemplate">
        SELECT
        de.third_table_id AS id,
        ma.third_id AS authUserId,
        '0' AS insertFlag
        FROM
        auth_main ma left join auth_detail de on de.auth_main_id = ma.id
        WHERE
        ma.third_type = '1'
        AND ma.module_type = #{tempType}
        AND de.third_table_name = #{tableName}
        AND de.third_table_id = #{id}
        AND ma.status = '0'
        AND de.status = '0'
        AND ma.third_id in (
        <foreach item="id" index="index" collection="userIds" separator=",">
            #{id}
        </foreach>
        )
    </select>

    <select id="selectTemplateById" resultType="com.ruoyi.system.domain.AuthTemplate">
        select id, template_name, template_explain, template_type, company_type, function_type, status, create_by, create_time, create_unit, update_by, update_time, update_unit from auth_template
        where id = #{id} and status = '0' and source = 'SYS' limit 1
    </select>

    <select id="selectProjectById" resultType="java.lang.String">
        SELECT
        project_name
        FROM
        oa_project_deploy
        WHERE
        id IN
        <foreach item="id" collection="projectIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectCompanyById" resultType="java.lang.String">
        SELECT
        company_short_name
        FROM
        sys_company
        WHERE
        id IN
        <foreach item="id" collection="companyIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectModuleAndTimeByUserId" resultType="com.ruoyi.system.domain.AuthMain">
        select ma.module_type moduleType, ma.permission_type permissionType,MAX(ma.permission_time) permissionTime from auth_main ma left join auth_detail de on de.auth_main_id = ma.id  where ma.third_type = '1' and ma.third_id = #{userId}
        and ma.status = '0' and (de.status = '0' or ma.permission_scope in ('PROJ0','UNIT0','DEPT0'))  and ma.permission_scope in
        <foreach item="scope" collection="scope" open="(" separator="," close=")">
            #{scope}
        </foreach>
        group by ma.module_type
    </select>

    <select id="selectModuleAndTimeAndRoleByUserId" resultType="com.ruoyi.system.domain.AuthMain">
        select ma.module_type moduleType,ma.role_type roleType,MAX(ma.permission_time) permissionTime from
        auth_main ma left join auth_detail de on de.auth_main_id = ma.id where ma.third_type = '1' and ma.third_id = #{userId} and ma.status = '0'
        and (de.status = '0' or ma.permission_scope in ('PROJ0','UNIT0','DEPT0')) and ma.permission_scope in
        <foreach item="scope" collection="scope" open="(" separator="," close=")">
            #{scope}
        </foreach>
        group by ma.module_type,ma.role_type
    </select>

    <select id="selectLoginUserPostByPostCode" resultType="java.lang.Integer">
        SELECT
            count(post.post_id)
        FROM
            sys_post post
                LEFT JOIN sys_user_post pouser ON post.post_id = pouser.post_id
        WHERE
            pouser.user_id = #{userId}
          AND post.post_code IN (#{ceo},#{xtCEO})
    </select>

    <insert id="insertAuthTemplate" parameterType="AuthTemplate" keyProperty="id" useGeneratedKeys="true">
        insert into auth_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="templateName != null and templateName != ''">template_name,</if>
            <if test="templateExplain != null">template_explain,</if>
            <if test="templateType != null and templateType != ''">template_type,</if>
            <if test="companyType != null">company_type,</if>
            <if test="functionType != null">function_type,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="source != null and source != ''">source,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createUnit != null">create_unit,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateUnit != null">update_unit,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="templateName != null and templateName != ''">#{templateName},</if>
            <if test="templateExplain != null">#{templateExplain},</if>
            <if test="templateType != null and templateType != ''">#{templateType},</if>
            <if test="companyType != null">#{companyType},</if>
            <if test="functionType != null">#{functionType},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="source != null and source != ''">#{source},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createUnit != null">#{createUnit},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateUnit != null">#{updateUnit},</if>
        </trim>
    </insert>

    <update id="updateAuthTemplate" parameterType="AuthTemplate">
        update auth_template
        <trim prefix="SET" suffixOverrides=",">
            <if test="templateName != null and templateName != ''">template_name = #{templateName},</if>
            <if test="templateExplain != null">template_explain = #{templateExplain},</if>
            <if test="companyType != null">company_type = #{companyType},</if>
            <if test="functionType != null">function_type = #{functionType},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="source != null and source != ''">source = #{source},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createUnit != null">create_unit = #{createUnit},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateUnit != null">update_unit = #{updateUnit},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAuthTemplateById">
        update auth_template set status = '1' where id = #{id} and source = 'SYS'
    </delete>

    <delete id="deleteAuthUseryTempId">
        delete from auth_template_user where permission_template_id = #{id} and source = 'SYS'
    </delete>

    <delete id="deleteUserDetailHis">
        delete from auth_detail where third_table_name = 'auth_template' and auth_main_id in
                                                                             (select id from auth_main where third_type = '1' and module_type = 'AUTHTEMPLATE' and STATUS = '0' and third_id = #{id})
                                  and third_table_id = #{tempId}
    </delete>

    <update id="deleteAuthTemplateDetailByTemplateId">
        update auth_template_detail set status = '1' where template_id = #{id}
    </update>



    <insert id="setAuthTempUser">
        insert into auth_template_user (permission_template_id,user_id,create_by,create_time,create_unit) values
        <foreach item="userIds" index="index" collection="userIds" separator=",">
            ( #{authTemplate.id}, #{userIds}, #{authTemplate.createBy}, #{authTemplate.createTime}, #{authTemplate.createUnit})
        </foreach>
    </insert>

    <insert id="batchAuthTemplateDetail">
        insert into auth_template_detail( template_id, module_type, permission_rule, status, create_by, create_time, create_unit, update_by, update_time, update_unit) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.templateId}, #{item.moduleType}, #{item.permissionRule}, #{item.status}, #{item.createBy}, #{item.createTime}, #{item.createUnit}, #{item.updateBy}, #{item.updateTime}, #{item.updateUnit})
        </foreach>
    </insert>

    <insert id="insertAuthTempUserDetail" parameterType="AuthDetail" useGeneratedKeys="true" keyProperty="id">
        insert into auth_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="authMainId != null">auth_main_id,</if>
            <if test="thirdTableName != null">third_table_name,</if>
            <if test="thirdTableAliasName != null">third_table_alias_name,</if>
            <if test="thirdTableId != null">third_table_id,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="createDeptId != null">create_dept_id,</if>
            <if test="createUnitId != null">create_unit_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateDeptId != null">update_dept_id,</if>
            <if test="updateUnitId != null">update_unit_id,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="authMainId != null">#{authMainId},</if>
            <if test="thirdTableName != null">#{thirdTableName},</if>
            <if test="thirdTableAliasName != null">#{thirdTableAliasName},</if>
            <if test="thirdTableId != null">#{thirdTableId},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createDeptId != null">#{createDeptId},</if>
            <if test="createUnitId != null">#{createUnitId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateDeptId != null">#{updateDeptId},</if>
            <if test="updateUnitId != null">#{updateUnitId},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <insert id="insertAuthTempUserDetailList">
        insert into auth_detail( auth_main_id, third_table_name, third_table_alias_name, third_table_id, status, create_by, create_id, create_dept_id, create_unit_id, create_time, update_by, update_id, update_dept_id, update_unit_id, update_time) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.authMainId}, #{item.thirdTableName}, #{item.thirdTableAliasName}, #{item.thirdTableId}, #{item.status}, #{item.createBy}, #{item.createId}, #{item.createDeptId}, #{item.createUnitId}, #{item.createTime}, #{item.updateBy}, #{item.updateId}, #{item.updateDeptId}, #{item.updateUnitId}, #{item.updateTime})
        </foreach>
    </insert>

    <insert id="insertAuthTemplateRel">
        insert into auth_template_rel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tempId != null">template_id,</if>
            <if test="authId != null">auth_id,</if>
            <if test="version != null">version,</if>
            <if test="authTemplate.companyType != null">company_type,</if>
            <if test="authTemplate.functionType != null">function_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tempId != null">#{tempId},</if>
            <if test="authId != null">#{authId},</if>
            <if test="version != null">#{version},</if>
            <if test="authTemplate.companyType != null">#{authTemplate.companyType},</if>
            <if test="authTemplate.functionType != null">#{authTemplate.functionType},</if>
        </trim>
    </insert>

    <insert id="insertAuthTempUserMain" parameterType="AuthMain" useGeneratedKeys="true" keyProperty="id">
        insert into auth_main
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="thirdType != null and thirdType != ''">third_type,</if>
            <if test="thirdId != null">third_id,</if>
            <if test="moduleType != null and moduleType != ''">module_type,</if>
            <if test="roleType != null and roleType != ''">role_type,</if>
            <if test="permissionScope != null and permissionScope != ''">permission_scope,</if>
            <if test="permissionType != null and permissionType != ''">permission_type,</if>
            <if test="permissionTime != null">permission_time,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createId != null">create_id,</if>
            <if test="createDeptId != null">create_dept_id,</if>
            <if test="createUnitId != null">create_unit_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateDeptId != null">update_dept_id,</if>
            <if test="updateUnitId != null">update_unit_id,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="thirdType != null and thirdType != ''">#{thirdType},</if>
            <if test="thirdId != null">#{thirdId},</if>
            <if test="moduleType != null and moduleType != ''">#{moduleType},</if>
            <if test="roleType != null and roleType != ''">#{roleType},</if>
            <if test="permissionScope != null and permissionScope != ''">#{permissionScope},</if>
            <if test="permissionType != null and permissionType != ''">#{permissionType},</if>
            <if test="permissionTime != null">#{permissionTime},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createDeptId != null">#{createDeptId},</if>
            <if test="createUnitId != null">#{createUnitId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateDeptId != null">#{updateDeptId},</if>
            <if test="updateUnitId != null">#{updateUnitId},</if>
            <if test="updateTime != null">#{updateTime},</if>

        </trim>
    </insert>

    <insert id="batchAuthTemplate" useGeneratedKeys="true" keyProperty="id">
        insert into auth_main (third_type,third_id,module_type,role_type,permission_scope,permission_type,permission_time,status,create_by,create_id,create_dept_id,create_unit_id,
        create_time,update_by,update_id,update_dept_id,update_unit_id,update_time) values
        <foreach item="item" index="index" collection="mainData" separator=",">
            ( #{item.thirdType}, #{item.thirdId}, #{item.moduleType}, #{item.roleType}, #{item.permissionScope}, #{item.permissionType}, #{item.permissionTime}, #{item.status}, #{item.createBy}, #{item.createId},
            #{item.createDeptId}, #{item.createUnitId},#{item.createTime}, #{item.updateBy},#{item.updateId}, #{item.updateDeptId},#{item.updateUnitId},#{item.updateTime})
        </foreach>
    </insert>

    <insert id="insertAgencyMsgs" parameterType="java.util.ArrayList">
        insert into agency_auth_record (principal_id,auth_dimensionality,auth_template_id,module_type,handle_type,third_type,third_id,role_type,third_table_name,
        third_table_id,permission_type,permission_time,status,create_by,create_id,create_dept_id,create_unit_id,create_time,update_by,update_id,update_dept_id,
        update_unit_id,update_time,auth_main_ids) values
        <foreach collection="agency" item="agency" separator="," >
            ( #{agency.principalId},#{agency.authDimensionality},#{agency.authTemplateId},#{agency.moduleType},#{agency.handleType},#{agency.thirdType},#{agency.thirdId},#{agency.roleType},
            #{agency.thirdTableName},#{agency.thirdTableId},#{agency.permissionType},#{agency.permissionTime},#{agency.status},#{agency.createBy},#{agency.createId},#{agency.createDeptId},
            #{agency.createUnitId},#{agency.createTime},#{agency.updateBy},#{agency.updateId},#{agency.updateDeptId},#{agency.updateUnitId},#{agency.updateTime},#{agency.authMainIds} )
        </foreach>
    </insert>

    <resultMap type="com.ruoyi.system.domain.AuthMain" id="AuthMainResult1">
        <result property="id"    column="id"    />
        <result property="thirdType"    column="third_type"    />
        <result property="thirdId"    column="third_id"    />
        <result property="moduleType"    column="module_type"    />
        <result property="roleType"    column="role_type"    />
        <result property="permissionScope"    column="permission_scope"    />
        <result property="permissionType"    column="permission_type"    />
        <result property="permissionTime"    column="permission_time"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createId"    column="create_id"    />
        <result property="createDeptId"    column="create_dept_id"    />
        <result property="createUnitId"    column="create_unit_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateId"    column="update_id"    />
        <result property="updateDeptId"    column="update_dept_id"    />
        <result property="updateUnitId"    column="update_unit_id"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAuthMainVo">
        SELECT id, third_type, third_id, module_type, role_type, permission_scope, permission_type, permission_time, status, create_by, create_id, create_dept_id, create_unit_id, create_time, update_by, update_id, update_dept_id, update_unit_id, update_time
        FROM auth_main
    </sql>

    <select id="selectAuthMainByIds" resultMap="AuthMainResult1">
        <include refid="selectAuthMainVo"/>
        WHERE id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="filtrationAgencyAuth" resultType="com.ruoyi.system.domain.AuthTemplateDetail">
        SELECT
            module_type
        FROM
            auth_main
        WHERE
            third_type = '3'
          AND third_id = #{userId}
          AND create_id = #{agencyUserId}
          AND STATUS = '0'
    </select>

</mapper>
