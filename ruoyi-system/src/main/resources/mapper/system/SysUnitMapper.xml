<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysUnitMapper">

    <resultMap type="SysUnit" id="SysUnitResult">
        <result property="unitId"    column="unit_id"    />
        <result property="unitCode"    column="unit_code"    />
        <result property="unitName"    column="unit_name"    />
        <result property="unitShortName"    column="unit_short_name"    />
        <result property="unitNo"    column="unit_no"    />
        <result property="linkman"    column="linkman"    />
        <result property="phone"    column="phone"    />
        <result property="email"    column="email"    />
        <result property="postcode"    column="postcode"    />
        <result property="businessAddress"    column="business_address"    />
        <result property="website"    column="website"    />
        <result property="registeredAddress"    column="registered_address"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSysUnitVo">
        select id as unit_id,company_code as unit_code,company_name as unit_name,company_short_name as unit_short_name,
               company_no as unit_no,linkman, phone, email,postcode, business_address, website, registered_address, status,
               create_by, create_time, update_by, update_time
        from sys_company
    </sql>

    <select id="selectSysUnitList" parameterType="SysUnit" resultMap="SysUnitResult">
        <include refid="selectSysUnitVo"/>
        <where>
            <if test="unitCode != null  and unitCode != ''"> and company_code like concat('%', #{unitCode}, '%')</if>
            <if test="unitName != null  and unitName != ''"> and company_name like concat('%', #{unitName}, '%')</if>
            <if test="unitShortName != null  and unitShortName != ''"> and company_short_name like concat('%', #{unitShortName}, '%')</if>
            <if test="unitNo != null  and unitNo != ''"> and company_no = #{unitNo}</if>
            <if test="linkman != null  and linkman != ''"> and linkman = #{linkman}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="email != null  and email != ''"> and email = #{email}</if>
            <if test="postcode != null  and postcode != ''"> and postcode = #{postcode}</if>
            <if test="businessAddress != null  and businessAddress != ''"> and business_address = #{businessAddress}</if>
            <if test="website != null  and website != ''"> and website = #{website}</if>
            <if test="registeredAddress != null  and registeredAddress != ''"> and registered_address = #{registeredAddress}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectSysUnitById" parameterType="Long" resultMap="SysUnitResult">
        <include refid="selectSysUnitVo"/>
        where id = #{unitId} and is_inside = '1'
    </select>

    <insert id="insertSysUnit" parameterType="SysUnit" useGeneratedKeys="true" keyProperty="unitId">
        insert into sys_unit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="unitCode != null">unit_code,</if>
            <if test="unitName != null">unit_name,</if>
            <if test="unitShortName != null">unit_short_name,</if>
            <if test="unitNo != null">unit_no,</if>
            <if test="linkman != null">linkman,</if>
            <if test="phone != null">phone,</if>
            <if test="email != null">email,</if>
            <if test="postcode != null">postcode,</if>
            <if test="businessAddress != null">business_address,</if>
            <if test="website != null">website,</if>
            <if test="registeredAddress != null">registered_address,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="unitCode != null">#{unitCode},</if>
            <if test="unitName != null">#{unitName},</if>
            <if test="unitShortName != null">#{unitShortName},</if>
            <if test="unitNo != null">#{unitNo},</if>
            <if test="linkman != null">#{linkman},</if>
            <if test="phone != null">#{phone},</if>
            <if test="email != null">#{email},</if>
            <if test="postcode != null">#{postcode},</if>
            <if test="businessAddress != null">#{businessAddress},</if>
            <if test="website != null">#{website},</if>
            <if test="registeredAddress != null">#{registeredAddress},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSysUnit" parameterType="SysUnit">
        update sys_unit
        <trim prefix="SET" suffixOverrides=",">
            <if test="unitCode != null">unit_code = #{unitCode},</if>
            <if test="unitName != null">unit_name = #{unitName},</if>
            <if test="unitShortName != null">unit_short_name = #{unitShortName},</if>
            <if test="unitNo != null">unit_no = #{unitNo},</if>
            <if test="linkman != null">linkman = #{linkman},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="email != null">email = #{email},</if>
            <if test="postcode != null">postcode = #{postcode},</if>
            <if test="businessAddress != null">business_address = #{businessAddress},</if>
            <if test="website != null">website = #{website},</if>
            <if test="registeredAddress != null">registered_address = #{registeredAddress},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where unit_id = #{unitId}
    </update>

    <delete id="deleteSysUnitById" parameterType="Long">
        delete from sys_unit where unit_id = #{unitId}
    </delete>

    <delete id="deleteSysUnitByIds" parameterType="String">
        delete from sys_unit where unit_id in
        <foreach item="unitId" collection="array" open="(" separator="," close=")">
            #{unitId}
        </foreach>
    </delete>


    <select id="checkUnitShortNameUnique" parameterType="String" resultMap="SysUnitResult">
		<include refid="selectSysUnitVo"/>
		 where company_short_name=#{unitShortName} and is_inside = '1' limit 1
	</select>

	<select id="checkUnitNameUnique" parameterType="String" resultMap="SysUnitResult">
		<include refid="selectSysUnitVo"/>
		 where company_name=#{unitName} and is_inside = '1' limit 1
	</select>
	<select id="checkUnitCodeUnique" parameterType="String" resultMap="SysUnitResult">
		<include refid="selectSysUnitVo"/>
		 where company_code=#{unitCode} and is_inside = '1' limit 1
	</select>

	<select id="selectUnitListEnable"  resultMap="SysUnitResult">
		select u.id as unit_id,u.company_name as unit_name, u.company_short_name as unit_short_name, u.status from sys_company u
		where  u.status = '0' and u.is_inside = '1'
	</select>
	<select id="selectUnitListAll"  resultMap="SysUnitResult">
		select u.id as unit_id,u.company_name as unit_name, u.company_short_name as unit_short_name, u.status from sys_company u where u.is_inside = '1'
	</select>
</mapper>
