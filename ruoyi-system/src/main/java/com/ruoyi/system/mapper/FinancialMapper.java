package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.dto.FinancialUserAccountSetsNew;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Description：智慧财务系统mapper
 * CreateTime：2024/6/11
 * Author：yu-qiang
 */
@Mapper
public interface FinancialMapper {

    /**
     * 增加财务系统账套用户权限
     * @param userAccountSets
     * @return
     */
    int insertUserAccountSets(@Param("userAccountSets") FinancialUserAccountSetsNew userAccountSets);

    /**
     * 通过公司ID查询账套主键ID
     * @param unitId
     * @return
     */
    String queryAccountSetsIdByUnitId(@Param("unitId") String unitId);

    /**
     * 通过账套ID+用户ID + 权限ID删除账套权限用户信息
     * @param accountSetsId
     * @param roleType
     * @return
     */
    int deleteUserAccountSets(@Param("accountSetsId") String accountSetsId, @Param("userList") List<Long> userList, @Param("roleType") String roleType);
}
