package com.ruoyi.system.mapper;

import com.ruoyi.common.core.domain.entity.SysRoleUnit;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色与全量公司关联表 数据层
 *
 * <AUTHOR>
 */
public interface SysRoleUnitMapper {
    /**
     * 通过角色ID删除角色和部门关联
     *
     * @param roleId 角色ID
     * @return 结果
     */
    public int deleteRoleUnitByRoleId(Long roleId);

    /**
     * 批量删除角色部门关联信息
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteRoleUnit(Long[] ids);

    /**
     * 查询全量公司使用数量
     *
     * @param unitId 部门ID
     * @return 结果
     */
    public int selectCountRoleUnitByUnitId(Long unitId);

    /**
     * 批量新增角色全量公司信息
     *
     * @return 结果
     */
    int insertRoleUnit(@Param("unitIds") Long[] unitIds, @Param("roleId") Long roleId);

    List<SysRoleUnit> selectUnitByRoleId(Long roleId);
}
