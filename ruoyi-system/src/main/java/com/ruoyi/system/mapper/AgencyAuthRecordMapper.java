package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.AgencyAuthRecord;

import java.util.List;
import java.util.Map;

/**
 * 代理权限操作记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-25
 */
public interface AgencyAuthRecordMapper
{
    /**
     * 查询代理权限操作记录
     *
     * @param id 代理权限操作记录主键
     * @return 代理权限操作记录
     */
    public AgencyAuthRecord selectAgencyAuthRecordById(Long id);

    /**
     * 查询代理权限操作记录列表
     *
     * @param agencyAuthRecord 代理权限操作记录
     * @return 代理权限操作记录集合
     */
    public List<AgencyAuthRecord> selectAgencyAuthRecordList(AgencyAuthRecord agencyAuthRecord);

    /**
     * 新增代理权限操作记录
     *
     * @param agencyAuthRecord 代理权限操作记录
     * @return 结果
     */
    public int insertAgencyAuthRecord(AgencyAuthRecord agencyAuthRecord);

    /**
     * 修改代理权限操作记录
     *
     * @param agencyAuthRecord 代理权限操作记录
     * @return 结果
     */
    public int updateAgencyAuthRecord(AgencyAuthRecord agencyAuthRecord);

    /**
     * 删除代理权限操作记录
     *
     * @param id 代理权限操作记录主键
     * @return 结果
     */
    public int deleteAgencyAuthRecordById(Long id);

    /**
     * 批量删除代理权限操作记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAgencyAuthRecordByIds(Long[] ids);

    //获取到所需要的各种信息
    public List<Map<String, Object>> selectAgencyAuthRecordList1(AgencyAuthRecord agencyAuthRecord);
}
