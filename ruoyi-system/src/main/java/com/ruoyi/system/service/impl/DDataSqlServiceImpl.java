package com.ruoyi.system.service.impl;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanValidators;
import com.ruoyi.system.domain.DDataSql;
import com.ruoyi.system.mapper.DDataSqlMapper;
import com.ruoyi.system.service.IDDataSqlService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.Validator;
import java.util.List;

/**
 * 外部系统平台数据查询sql配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-03-31
 */
@Service
public class DDataSqlServiceImpl implements IDDataSqlService 
{
    private static final Logger log = LoggerFactory.getLogger(SysUserServiceImpl.class);
    @Autowired
    protected Validator validator;
    @Autowired
    private DDataSqlMapper dDataSqlMapper;

    /**
     * 查询外部系统平台数据查询sql配置
     * 
     * @param id 外部系统平台数据查询sql配置主键
     * @return 外部系统平台数据查询sql配置
     */
    @Override
    public DDataSql selectDDataSqlById(Long id)
    {
        return dDataSqlMapper.selectDDataSqlById(id);
    }

    /**
     * 查询外部系统平台数据查询sql配置列表
     * 
     * @param dDataSql 外部系统平台数据查询sql配置
     * @return 外部系统平台数据查询sql配置
     */
    @Override
    public List<DDataSql> selectDDataSqlList(DDataSql dDataSql)
    {
        return dDataSqlMapper.selectDDataSqlList(dDataSql);
    }

    /**
     * 新增外部系统平台数据查询sql配置
     * 
     * @param dDataSql 外部系统平台数据查询sql配置
     * @return 结果
     */
    @Override
    public int insertDDataSql(DDataSql dDataSql)
    {
        dDataSql.setCreateTime(DateUtils.getNowDate());
        if(dDataSql.getUpdateTableFlag().length()==0 || Integer.parseInt(dDataSql.getUpdateTableFlag())==0 ||dDataSql.getUpdateTableFlag().equals("") ){
            dDataSql.setUpdateTableFlag("1");
        }

        dDataSql.setSqlAdditionalQuery(StringUtils.stringCast(dDataSql.getSqlAdditionalQuery()));
        dDataSql.setSqlBasicQuery(StringUtils.stringCast(dDataSql.getSqlBasicQuery()));
        dDataSql.setSqlField(StringUtils.stringCast(dDataSql.getSqlField()));
        dDataSql.setSqlFrom(StringUtils.stringCast(dDataSql.getSqlFrom()));

        return dDataSqlMapper.insertDDataSql(dDataSql);
    }

    /**
     * 修改外部系统平台数据查询sql配置
     * 
     * @param dDataSql 外部系统平台数据查询sql配置
     * @return 结果
     */
    @Override
    public int updateDDataSql(DDataSql dDataSql,String operName)
    {
        dDataSql.setUpdateTime(DateUtils.getNowDate());
        dDataSql.setUpdateBy(operName);
        dDataSql.setSqlAdditionalQuery(StringUtils.stringCast(dDataSql.getSqlAdditionalQuery()));
        dDataSql.setSqlBasicQuery(StringUtils.stringCast(dDataSql.getSqlBasicQuery()));
        dDataSql.setSqlField(StringUtils.stringCast(dDataSql.getSqlField()));
        dDataSql.setSqlFrom(StringUtils.stringCast(dDataSql.getSqlFrom()));
        return dDataSqlMapper.updateDDataSql(dDataSql);
    }

    /**
     * 批量删除外部系统平台数据查询sql配置
     * 
     * @param ids 需要删除的外部系统平台数据查询sql配置主键
     * @return 结果
     */
    @Override
    public int deleteDDataSqlByIds(Long[] ids)
    {
        return dDataSqlMapper.deleteDDataSqlByIds(ids);
    }

    /**
     * 删除外部系统平台数据查询sql配置信息
     * 
     * @param id 外部系统平台数据查询sql配置主键
     * @return 结果
     */
    @Override
    public int deleteDDataSqlById(Long id)
    {
        return dDataSqlMapper.deleteDDataSqlById(id);
    }

    @Override
    public String importDDataSql(List<DDataSql> dDataSqlList, boolean updateSupport, String operName) {
            if (StringUtils.isNull(dDataSqlList) || dDataSqlList.size() == 0)
            {
                throw new ServiceException("导入用户数据不能为空！");
            }
            int successNum = 0;
            int failureNum = 0;
            int mlIndex = 0;
            int updateNum = 0;
            StringBuilder successMsg = new StringBuilder();
            StringBuilder failureMsg = new StringBuilder();
            for (DDataSql dDataSql : dDataSqlList)
            {
                mlIndex++;
                try
                {
                    // 验证是否存在这条数据
                    DDataSql s = dDataSqlMapper.selectDDataSqlByAllCondition(dDataSql.getSqlCode());
                    if (StringUtils.isNull(s))
                    {
                        if(dDataSql.getStatus().length()== 0 || StringUtils.isEmpty( dDataSql.getStatus())){
                            dDataSql.setStatus("0");
                        }
                        if(dDataSql.getTaskStatus().length()== 0 || StringUtils.isEmpty( dDataSql.getTaskStatus())){
                            dDataSql.setTaskStatus("0");
                        }
                        BeanValidators.validateWithException(validator, dDataSql);

                        dDataSql.setCreateBy(operName);
                        this.insertDDataSql(dDataSql);
                        successNum++;
                    }
                    else if (updateSupport)
                    {
                        if(dDataSql.getStatus().length()== 0 ||StringUtils.isEmpty(dDataSql.getStatus())){
                            dDataSql.setStatus("0");
                        }
                        if(dDataSql.getTaskStatus().length()== 0 ||StringUtils.isEmpty(dDataSql.getTaskStatus()) ){
                            dDataSql.setTaskStatus("0");
                        }
                        BeanValidators.validateWithException(validator, dDataSql);
                        dDataSql.setId(s.getId());
                        this.updateDDataSql(dDataSql,operName);
                        updateNum++;
                    }
                    else
                    {
                        failureNum++;
                        failureMsg.append("<br/>第" +  mlIndex + "条数据已存在");
                    }
                }
                catch (Exception e)
                {
                    failureNum++;
                    String msg = "<br/>第"+  mlIndex +  " 条数据导入失败：";
                    failureMsg.append(msg + e.getMessage());
                    log.error(msg, e);
                }
            }
            if (failureNum > 0)
            {
                failureMsg.insert(0, "很抱歉，导入失败！应导入"+dDataSqlList.size()+"条，共导入 " + successNum + " 条数据，有 " + failureNum + " 条数据格式不正确，错误如下：");
                throw new ServiceException(failureMsg.toString());
            }
            else
            {
//            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
                successMsg.insert(0, "恭喜您，数据已全部导入成功！应导入"+dDataSqlList.size()+"条，共导入 " + successNum + " 条，更新"+updateNum+"条");
            }
            return successMsg.toString();

    }
}
