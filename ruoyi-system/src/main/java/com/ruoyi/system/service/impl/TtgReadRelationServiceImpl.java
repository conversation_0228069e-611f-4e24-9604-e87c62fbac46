package com.ruoyi.system.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.vo.TtgNoticeMainVo;
import com.ruoyi.system.domain.vo.TtgReadRelationVo;
import com.ruoyi.system.mapper.TtgReadRelationMapper;
import com.ruoyi.system.service.ITtgReadRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.SystemUtils.getUserName;

/**
 * 公告管理
 */
@Service
public class TtgReadRelationServiceImpl implements ITtgReadRelationService {

    @Autowired
    private TtgReadRelationMapper ttgReadRelationMapper;

    /**
     * 先发布公告，再授权的用户，同样可以看到所有当前公司下已发布的所有公告
     * @param companyIds
     * @param userIds
     * @return
     */
    @Override
    public int giveUserCompanyNoticeAuth(List<Long> companyIds, List<Long> userIds) {
        List<TtgReadRelationVo> readRelationVoList = new ArrayList<>();
        List<TtgNoticeMainVo> tgNoticeMainVoList = new ArrayList<>();
        //先根据公司id集合查询当前公司下有哪些已发布的公告
        if (!CollectionUtils.isEmpty(companyIds)){
            tgNoticeMainVoList = ttgReadRelationMapper.selectLaunchedNoticesByCompanyId(companyIds);
        }
        //已发布公告不为空
        if (!CollectionUtils.isEmpty(tgNoticeMainVoList)){
            List<Long> noticeIdList = tgNoticeMainVoList.stream().map(TtgNoticeMainVo::getId).collect(Collectors.toList());
            //根据公告id和用户id删除已读未读表中数据，重新插入数据，状态为未读
            if (!CollectionUtils.isEmpty(userIds)){
                for (Long userId : noticeIdList) {
                    ttgReadRelationMapper.deleteTgReadRelationByNoticeIdListAndUserId(noticeIdList,userId);
                }
            }
            //组装数据
            for (Long noticeId : noticeIdList) {
                for (Long userId : userIds) {
                    TtgReadRelationVo tgReadRelation = new TtgReadRelationVo();
                    tgReadRelation.setNoticeId(noticeId);
                    tgReadRelation.setCreateBy(getUserName());
                    tgReadRelation.setCreateTime(DateUtils.getNowDate());
                    tgReadRelation.setUserId(userId);
                    readRelationVoList.add(tgReadRelation);
                }
            }
            //数据批量入库
            ttgReadRelationMapper.batchInsert(readRelationVoList);
        }
        return 1;
    }
}
