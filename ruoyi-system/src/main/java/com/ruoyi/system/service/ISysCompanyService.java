package com.ruoyi.system.service;

import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.system.domain.SysCompany;
import com.ruoyi.system.domain.SysOperLog;
import com.ruoyi.system.domain.vo.CompanyBusinessTypeVo;
import com.ruoyi.system.domain.vo.CompanyLogVo;
import com.ruoyi.system.domain.vo.CompanyTypeVo;
import com.ruoyi.system.domain.vo.SysCompanyVo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 全量公司信息Service接口
 *
 * <AUTHOR>
 * @date 2024-04-16
 */
public interface ISysCompanyService
{
    /**
     * 查询全量公司信息
     *
     * @param id 全量公司信息主键
     * @return 全量公司信息
     */
    public SysCompanyVo selectSysCompanyById(Long id);

    /**
     * 查询全量公司信息列表
     *
     * @param sysCompany 全量公司信息
     * @return 全量公司信息集合
     */
    public List<SysCompanyVo> selectSysCompanyList(SysCompanyVo sysCompany);

    public List<SysCompanyVo> selectSysCompanyListHaveAuthority(SysCompanyVo sysCompany);

    /**
     * 新增全量公司信息
     *
     * @param sysCompany 全量公司信息
     * @return 结果
     */
    public Map<String,Object> insertSysCompany(SysCompanyVo sysCompany);

    /**
     * 修改全量公司信息
     *
     * @param sysCompany 全量公司信息
     * @return 结果
     */
    public int updateSysCompany(SysCompanyVo sysCompany);

    /**
     * 批量删除全量公司信息
     *
     * @param ids 需要删除的全量公司信息主键集合
     * @return 结果
     */
    public int deleteSysCompanyByIds(Long[] ids);

    /**
     * 删除全量公司信息信息
     *
     * @param id 全量公司信息主键
     * @return 结果
     */
    public int deleteSysCompanyById(Long id);

    public Object checkCompanyName(SysCompany sysCompany);

    /**
     * 添加公司类型
     *
     * @param data
     * @return
     */
    public Map<String,Object> insertCompanyType(SysDictData data);

    public List<CompanyTypeVo> getCompanyTypeList(SysDictData sysDictData);

    /**
     * 校验公司类型名称是否唯一
     * @param sysDictData
     * @return
     */
    public Object checkCompanyType(SysDictData sysDictData);
    /**
     * 添加公司支持类型
     * @param data
     * @return
     */
    public Long insertCompanyBusinessType(SysDictData data);
    /**
     * 查询公司支持类型
     * @param
     * @return
     */
    public List<CompanyBusinessTypeVo> getCompanyBusinessTypeList(SysDictData sysDictData);

    public Object checkCompanyBusinessType(SysDictData sysDictData);


    /**
     * 修改公司类型
     * @param data
     * @return
     */
    public int updateCompanyType(SysDictData data);

    public int updateCompanyBusinessType(SysDictData data);

    public List<CompanyLogVo> getCompanyLogVoList(SysOperLog sysOperLog);

     public void deleteCompanyType(Long dictCode);

     public void deleteCompanyBusinessType(Long dictCode);

     //根据传过来的公司id集合，找对应的公司
    List<SysCompanyVo> selectSysCompanyListForAuthority(SysCompanyVo sysCompany, List<Long> companyIdList);

    public List<SysCompanyVo> selectCompanyListByCompanyShortNames(Set<String> companyNames);

    Map<String, Object> getCompanyIsReference(SysCompanyVo sysCompany);

    public List<SysCompanyVo> selectSysCompanyListNewAuthority(SysCompanyVo sysCompany);

    List<SysCompanyVo> getCompanyBySelectType(List<String> companyTypeId);

    List<SysCompanyVo> selectSysCompanyListForAuthorityNew(SysCompanyVo sysCompany, List<Long> companyTypeCode, List<Long> companyIdList);

    /**人员调用时(调入公司)
     *
     * @param sysCompany
     * @return
     */
    public List<SysCompanyVo> selectSysCompanyListTransfer(SysCompanyVo sysCompany);
}
