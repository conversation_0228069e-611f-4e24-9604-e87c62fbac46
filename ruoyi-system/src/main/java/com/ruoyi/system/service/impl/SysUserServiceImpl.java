package com.ruoyi.system.service.impl;

import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.domain.entity.*;
import com.ruoyi.common.core.domain.entity.SysMenu;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.entity.SysUserPost;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.AuthModuleEnum;
import com.ruoyi.common.enums.AuthPermissionEnum;
import com.ruoyi.common.enums.FunctionNodeEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanValidators;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.system.domain.SysCompany;
import com.ruoyi.system.domain.SysPost;
import com.ruoyi.system.domain.SysUserRole;
import com.ruoyi.system.domain.vo.*;
import com.ruoyi.system.mapper.*;
import com.ruoyi.system.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.validation.Validator;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysUserServiceImpl implements ISysUserService
{
    private static final Logger log = LoggerFactory.getLogger(SysUserServiceImpl.class);

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private SysRoleMapper roleMapper;

    @Autowired
    private SysPostMapper postMapper;

    @Autowired
    private SysUserRoleMapper userRoleMapper;

    @Autowired
    private SysUserPostMapper userPostMapper;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    protected Validator validator;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private ISysMenuService iSysMenuService;

    @Autowired
    private ISysDeptService sysDeptService;

    @Autowired
    private ISysOperLogService sysOperLogService;

    @Autowired
    private ISysCompanyService sysCompanyService;

    @Override
    public List<SysUser> selectUserByUserIds(List<Long> userIdList) {
        List<SysUser> sysUsers = sysUserMapper.selectUserByUserId(userIdList);
        return sysUsers;
    }


    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    //@DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectUserList(SysUser user)
    {
        return userMapper.selectUserList(user);
    }

    @Override
    //@DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectUserListOfDayLog(SysUser user)
    {
        return userMapper.selectUserListOfDayLog(user);
    }


    @Override
    public List<SysUserAuth> listForPersonnel(SysUser user){
        return userMapper.listForPersonnel(user);
    }

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    //@DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectAllocatedList(SysUser user)
    {
        return userMapper.selectAllocatedList(user);
    }

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    //@DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectUnallocatedList(SysUser user)
    {
        return userMapper.selectUnallocatedList(user);
    }

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserByUserName(String userName)
    {
        return userMapper.selectUserByUserName(userName);
    }

    @Override
    public SysUser selectUserByUserNameOfTrue(String userName){
        return userMapper.selectUserByUserNameOfTrue(userName);
    }
    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserById(Long userId)
    {
        SysUser sysUser = userMapper.selectUserById(userId);
        return sysUser;
    }

    /**
     * 查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserRoleGroup(String userName)
    {
        List<SysRole> list = roleMapper.selectRolesByUserName(userName);
        if (CollectionUtils.isEmpty(list))
        {
            return StringUtils.EMPTY;
        }
        return list.stream().map(SysRole::getRoleName).collect(Collectors.joining(","));
    }

    /**
     * 查询用户所属岗位组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserPostGroup(String userName)
    {
        List<SysPost> list = postMapper.selectPostsByUserName(userName);
        if (CollectionUtils.isEmpty(list))
        {
            return StringUtils.EMPTY;
        }
        return list.stream().map(SysPost::getPostName).collect(Collectors.joining(","));
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param userName 用户名称
     * @return 结果
     */
    @Override
    public String checkUserNameUnique(String userName)
    {
        int count = userMapper.checkUserNameUnique(userName);
        if (count > 0)
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public String checkPhoneUnique(SysUser user)
    {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkPhoneUnique(user.getPhonenumber());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public String checkEmailUnique(SysUser user)
    {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkEmailUnique(user.getEmail());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    @Override
    public void checkUserAllowed(SysUser user)
    {
        if (StringUtils.isNotNull(user.getUserId()) && user.isAdmin())
        {
            throw new ServiceException("不允许操作超级管理员用户");
        }
    }

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    @Override
    public void checkUserDataScope(Long userId)
    {
        if (!SysUser.isAdmin(SecurityUtils.getUserId()))
        {
            SysUser user = new SysUser();
            user.setUserId(userId);
            List<SysUser> users = SpringUtils.getAopProxy(this).selectUserList(user);
            if (StringUtils.isEmpty(users))
            {
                throw new ServiceException("没有权限访问用户数据！");
            }
        }
    }

    /**
     * 新增保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertUser(SysUser user)
    {
        String message = "";
        int rows = 0;
        List<Long> postCollect = new ArrayList<>();
        try {
            //判断本次修改是否有重复岗位(同一岗位选择多次)
            List<SysUserPost> posts = user.getUserPostList();
            if (!CollectionUtils.isEmpty(posts)) {
                for (int i = 0; i < posts.size(); i++) {
                    Long postId = posts.get(i).getPostId();
                    if (postCollect.contains(postId)) {
                        throw new RuntimeException("岗位选择重复，请重新选择");
                    } else {
                        postCollect.add(postId);
                    }
                }
            }

            // 新增用户信息
            rows = userMapper.insertUser(user);
            // 新增用户岗位关联
            insertUserPost(user);
            // 新增用户与角色管理
            //insertUserRole(user);
        } finally {
            String operMessage = "新增用户【" + user.getNickName() + "】";
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.USERRE.getCode(), FunctionNodeEnum.USERMANAGE.getCode(), operMessage, 1, message,"");
        }
        return rows;
    }

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean registerUser(SysUser user)
    {
        return userMapper.insertUser(user) > 0;
    }

    /**
     * 修改保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateUser(SysUser user)
    {
        String message = "";
        try {
            List<Long> postCollect = new ArrayList<>();
            Long userId = user.getUserId();
            // 删除用户与角色关联
            // userRoleMapper.deleteUserRoleByUserId(userId);
            // 新增用户与角色管理
            //nsertUserRole(user);

            //判断本次修改是否有重复岗位(同一岗位选择多次)
            List<SysUserPost> posts = user.getUserPostList();
            if (!CollectionUtils.isEmpty(posts)) {
                for (int i = 0; i < posts.size(); i++) {
                    Long postId = posts.get(i).getPostId();
                    if (postCollect.contains(postId)) {
                        throw new RuntimeException("岗位选择重复，请重新选择");
                    } else {
                        postCollect.add(postId);
                    }
                }
            }

            // 删除用户与岗位关联
            userPostMapper.deleteUserPostByUserId(userId);
            // 新增用户与岗位管理
            insertUserPost(user);
            return userMapper.updateUser(user);
        } finally {
            String operMessage = "修改用户【" + user.getNickName() + "】的信息";
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.USERRE.getCode(), FunctionNodeEnum.USERMANAGE.getCode(), operMessage, 2, message,"");
        }
    }

    /**
     * 用户授权角色
     *
     * @param userId 用户ID
     * @param roleIds 角色组
     */
    @Override
    @Transactional
    public void insertUserAuth(Long userId, Long[] roleIds)
    {
        userRoleMapper.deleteUserRoleByUserId(userId);
        insertUserRole(userId, roleIds);
    }

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserStatus(SysUser user)
    {
        return userMapper.updateUser(user);
    }

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserProfile(SysUser user)
    {
        return userMapper.updateUser(user);
    }

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar 头像地址
     * @return 结果
     */
    @Override
    public boolean updateUserAvatar(String userName, String avatar)
    {
        return userMapper.updateUserAvatar(userName, avatar) > 0;
    }

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int resetPwd(SysUser user)
    {
        SysUser sysUser = userMapper.selectUserById(user.getUserId());
        int i = userMapper.updateUser(user);
        String operMessage = "重置用户【" + sysUser.getNickName() + "】的密码";
        sysOperLogService.insertOperLogMessage(AuthModuleEnum.USERRE.getCode(), FunctionNodeEnum.USERMANAGE.getCode(), operMessage, 2, "","");
        return i;
    }

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    @Override
    public int resetUserPwd(String userName, String password)
    {
        return userMapper.resetUserPwd(userName, password);
    }

    /**
     * 新增用户角色信息
     *
     * @param user 用户对象
     */
    public void insertUserRole(SysUser user)
    {
        Long[] roles = user.getRoleIds();
        if (StringUtils.isNotNull(roles))
        {
            // 新增用户与角色管理
            List<SysUserRole> list = new ArrayList<SysUserRole>();
            for (Long roleId : roles)
            {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(user.getUserId());
                ur.setRoleId(roleId);
                list.add(ur);
            }
            if (list.size() > 0)
            {
                userRoleMapper.batchUserRole(list);
            }
        }
    }

    /**
     * 新增用户岗位信息
     *
     * @param user 用户对象
     */
    public void insertUserPost(SysUser user)
    {
        // 新增用户与岗位管理
        List<SysUserPost> list = user.getUserPostList();
        if (list.size() > 0){
        	for (SysUserPost sysUserPost : list) {
        		sysUserPost.setUserId(user.getUserId());
			}
            userPostMapper.batchUserPost(list);
        }
    }

    /**
     * 新增用户角色信息
     *
     * @param userId 用户ID
     * @param roleIds 角色组
     */
    public void insertUserRole(Long userId, Long[] roleIds)
    {
        if (StringUtils.isNotNull(roleIds))
        {
            // 新增用户与角色管理
            List<SysUserRole> list = new ArrayList<SysUserRole>();
            for (Long roleId : roleIds)
            {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(userId);
                ur.setRoleId(roleId);
                list.add(ur);
            }
            if (list.size() > 0)
            {
                userRoleMapper.batchUserRole(list);
            }
        }
    }

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteUserById(Long userId)
    {
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 删除用户与岗位表
        userPostMapper.deleteUserPostByUserId(userId);
        return userMapper.deleteUserById(userId);
    }

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteUserByIds(Long[] userIds)
    {
        StringBuffer userNames = new StringBuffer("");
        String errorMsg = "";
        try {
            for (Long userId : userIds)
            {
                checkUserAllowed(new SysUser(userId));
                SysUser sysUser = selectUserById(userId);
                userNames.append("【" + sysUser.getNickName() + "】");
            }
            // 删除用户与角色关联
            userRoleMapper.deleteUserRole(userIds);
            // 删除用户与岗位关联
            userPostMapper.deleteUserPost(userIds);
            return userMapper.deleteUserByIds(userIds);
        } catch (Exception e) {
            e.printStackTrace();
            StackTraceElement stackTraceElement = e.getStackTrace()[0];;
            errorMsg = "错误信息:" + e.toString() + " at "
                    + stackTraceElement.getClassName() + "."
                    + stackTraceElement.getMethodName() + ":"
                    + stackTraceElement.getLineNumber();
            throw new RuntimeException(e);
        } finally {
            String operMessage = "删除用户" + userNames;
            sysOperLogService.insertOperLogMessage(AuthModuleEnum.USERRE.getCode(), FunctionNodeEnum.USERMANAGE.getCode(), operMessage, 3, errorMsg,"");
        }
    }

    /**
     * 导入用户数据
     *
     * @param userList 用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importUser(List<SysUser> userList, Boolean isUpdateSupport, String operName)
    {
        if (StringUtils.isNull(userList) || userList.size() == 0)
        {
            throw new ServiceException("导入用户数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        String password = configService.selectConfigByKey("sys.user.initPassword");
        for (SysUser user : userList)
        {
            try
            {
                // 验证是否存在这个用户
                SysUser u = userMapper.selectUserByUserName(user.getUserName());
                if (StringUtils.isNull(u))
                {
                    BeanValidators.validateWithException(validator, user);
                    user.setPassword(SecurityUtils.encryptPassword(password));
                    user.setCreateBy(operName);
                    this.insertUser(user);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    BeanValidators.validateWithException(validator, user);
                    user.setUpdateBy(operName);
                    this.updateUser(user);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账号 " + user.getUserName() + " 已存在");
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账号 " + user.getUserName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }


    public List<SysUser> selectUserListEnable()
    {
        return userMapper.selectUserListEnable();
    }
    public List<SysUser> selectUserListAll()
    {
        return userMapper.selectUserListAll();
    }

    @Override
    public List<SysUser> getUserListByDeptId(SysUser sysUser) {
        return userMapper.getUserListByDeptId(sysUser);
    }

    @Override
    public List<SysUser> selectUserListByDeptIds(Long[] deptIds) {
        return userMapper.selectUserListByDeptIds(deptIds);
    }

    @Override
    public Map<String, Object> getMenuList(Long userId) {

        HashMap<String, Object> returnMap = new HashMap<>();
        returnMap.put("data",false);
        returnMap.put("caiwu",false);
        List<SysMenu> sysMenus = iSysMenuService.selectMenuTreeByUserId(userId);
        for (SysMenu sysMenu : sysMenus) {
            if(null == sysMenu.getComponent() || "".equals(sysMenu.getComponent())){
            }else {
                if (sysMenu.getComponent().equals("homePage/dataSystem/index")) {
                    returnMap.put("data", true);
                }
            }

            if (null == sysMenu.getPath() || "".equals(sysMenu.getPath())){
            }else {
                if (sysMenu.getPath().contains("http://") && sysMenu.getMenuName().contains("财务")) {
                    returnMap.put("caiwu",true);
                }
            }
        }
        return returnMap;
    }

    @Override
    public List<SysUserAuth> getUserAuthorizationList(SysUser sysUser) {
        return userMapper.getUserAuthorizationList(sysUser);
    }

    @Override
    public List<SysUser> getUserListByPostId(SysUser sysUser, LoginUser loginUser) {
//        List<SysUser> userListByPostId = userMapper.getUserListByPostId(sysUser).stream().filter(t -> !t.getUserId().equals(loginUser.getUserId())).collect(Collectors.toList());
        List<SysUser> userListByPostId = userMapper.getUserListByPostId(sysUser);
        //sql去重后代码这里注释掉
//        List<SysUser> collect = userListByPostId.stream().collect(Collectors.collectingAndThen(Collectors
//                .toCollection(()->new TreeSet<>(Comparator.comparing(SysUser::getUserId))), ArrayList::new));
        return userListByPostId;
    }

    @Override
    public List<SysUser> selectUserListByPostId(Long[] postId) {
        return userMapper.selectUserListByPostId(postId);
    }

    @Override
    public String selectUserMainPost(String userName) {
        SysPost mainSysPost = postMapper.selectMainPostByUserName(userName);
        if (mainSysPost == null) {
            return StringUtils.EMPTY;
        } else if (mainSysPost.getPostName() == null || StringUtils.EMPTY.equals(mainSysPost.getPostName())){
            return StringUtils.EMPTY;
        } else {
            return mainSysPost.getPostName();
        }
    }

    /**
     * add by nieyi 根据岗位id查询已授权的用户列表
     * @param sysUserPostVo
     * @return
     */
    @Override
    public List<SysUser> queryAccreditUserList(SysUserPostVo sysUserPostVo) {
        return userMapper.selectAccreditUserList(sysUserPostVo);
    }

    /**
     * 通过用户ID数组查询用户信息
     * @param authUserIds   用户ID数组
     * @return    用户信息
     */
    @Override
    public List<SysUser> selectUserByIds(Long[] authUserIds) {
        return userMapper.selectUserByIds(authUserIds);
    }

    /**
     * 拼接部门信息
     * @param userPostList
     */
    @Override
    public List<SysUserPost> jointDept(List<SysUserPost> userPostList, List<SysPost> sysPosts) {
        for (SysUserPost sysUserPost : userPostList) {
            for (SysPost sysPost : sysPosts) {
                if (sysUserPost.getPostId() == sysPost.getPostId()){
                    String deptBreadcrumb = getDeptBreadcrumb(sysPost.getDept());
                    sysUserPost.setDeptName(deptBreadcrumb);
                    sysUserPost.setUnitName(sysUserPost.getUnitName());
                    break;
                }
            }
        }
        return userPostList;
    }

    /**
     * 拼接部门层级
     * @param sysDept
     * @return
     */
    public String getDeptBreadcrumb(SysDept sysDept) {
        if(sysDept != null && sysDept.getDept() != null ) {
            return getDeptBreadcrumb(sysDept.getDept()) + ">" + sysDept.getDeptName();
        }else if(sysDept != null && sysDept.getDeptName() != null){
            return sysDept.getDeptName();
        }else {
            return "";
        }
    }

    /**
     * 根据用户id查询其岗位所属公司的所有oa流程
     * @param userId
     * @return
     */
    @Override
    public List<ProClassificationVo> selectUserProcessListByUserId(Long userId) {
        if (userId == null ){
            userId = SecurityUtils.getLoginUser().getUserId();
        }
        //据用户id查询其所有岗位
        List<ProClassificationVo> treeList = new ArrayList<>();
        Set<Long> collect = this.getUserAuthCompanyListByUserId(userId);
        //去除null数据
        collect.removeAll(Collections.singleton(null));
        if (collect != null && collect.size() > 0){
            //查询所有的公司和公司下的第一层级部门
            List<ProClassificationVo> proClassificationVos = sysUserMapper.selectProcessClassificationByCompanysId(collect);
            //查询所有流程
            List<OaProcessTemplateVo> classificationVoList = sysUserMapper.selectClassification();
            //组装树形数据
            treeList = getTreeList(proClassificationVos);
            //循环根据树形列表的数据组装参数
            treeList.forEach(p -> {
                if (p.getFPiattaformas() != null && p.getFPiattaformas().size() > 0){
                    for (ProClassificationVo vos : p.getFPiattaformas()){
                        List<OaProcessTemplateVo> temList = new ArrayList<>();
                        for (OaProcessTemplateVo oaProcessTemplateVo : classificationVoList) {
                            String deptAncestors = "";
                            deptAncestors = vos.getAncestors() + "," + vos.getId();
                            String flowAncestors = "";
                            flowAncestors = oaProcessTemplateVo.getAncestors() + "," + oaProcessTemplateVo.getClassificationId();
                            if (flowAncestors.contains(deptAncestors) | vos.getId().toString().equals(oaProcessTemplateVo.getClassificationId().toString())){
                                temList.add(oaProcessTemplateVo);
                            }
                        }
                        vos.setOaProTemList(temList);
                    }
                }
            });
        }
        return treeList;
    }

    public List<ProClassificationVo> getTreeList(List<ProClassificationVo> proClassificationVos){
        // 父级，用于存放最终结果
        List<ProClassificationVo> fp = new ArrayList<>();
        // 筛选出的子集
        List<ProClassificationVo> fpson = new ArrayList<>();
        // 先提取出顶级目录和子集
        for (int i = 0; i < proClassificationVos.size(); i++) {
            if (proClassificationVos.get(i).getParentId().longValue() == 1 || "1".equals(proClassificationVos.get(i).getParentId())
                    && "0,1".equals(proClassificationVos.get(i).getAncestors()) && proClassificationVos.get(i).getIsCompany().equals("0")) {
                fp.add(proClassificationVos.get(i));
            } else {
                fpson.add(proClassificationVos.get(i));
            }
        }
        // 从顶级目录开始，递归穿插数据
        for (int i = 0; i < fp.size(); i++) {
            getChildData(fp.get(i), fpson);
        }
        return fp;
    }

    /**
     * @param fp    父集
     * @param fpson 子集
     */
    private void getChildData(ProClassificationVo fp, List<ProClassificationVo> fpson) {
        List<ProClassificationVo> stessoLive = new ArrayList<>();
        for (int j = 0; j < fpson.size(); j++) {
            // 如果是其子类，则存储在子类的list中，循环完统一set
            if (fpson.get(j).getParentId().equals(fp.getId())) {
                stessoLive.add(fpson.get(j));
                getChildData(fpson.get(j), fpson);
            }
        }
        // 设置子数据
        fp.setFPiattaformas(stessoLive);
    }

    /**
     * 获取指定部门(运营部)下的用户组织架构
     * @return
     */
    @Override
    public List<SysDept> getAssignDeptUserList( SysDept sysDept) {
        List<SysDept> assignList = new ArrayList<>();
        List<SysDept> sysDeptList2 = new ArrayList<>();
        if (sysDept.getDeptName() != null && !sysDept.getDeptName().equals("") ) {
            //获取前端传过来的要查询的部门名称，去模糊查询有哪些对的上的部门，比如要查询运营部岗位就传 运营
            List<SysDept> sysDeptList = sysDeptService.queryDeptInfo(sysDept.getDeptName());
            for (SysDept deptDO : sysDeptList) {
                //查询该部门所有用户对应的岗位
                List<SysUserPostDeptVo> userPostDeptVos = postMapper.selectPostListByDeptId(deptDO.getDeptId());
                deptDO.setUserPostDeptVos(userPostDeptVos);
                //获取该部门的祖级列表
                String ancestors = deptDO.getAncestors();
                String[] split = ancestors.split(",");
                long[] ids = Arrays.stream(split)
                        .mapToLong(Long::parseLong)
                        .toArray();
                List<SysDept> depts = sysDeptService.selectDeptByArrayIds(ids);
                List<Long> deptIds = sysDeptList2.stream().map(SysDept::getDeptId).collect(Collectors.toList());
                if (depts.size() > 0 && !CollectionUtils.isEmpty(depts)) {
                    for (SysDept dept : depts) {
                        if (!deptIds.contains(dept.getDeptId())){
                            sysDeptList2.add(dept);
                        }
                    }
                }
                sysDeptList2.add(deptDO);
            }
            //转换为树形结构列表
            assignList = sysDeptService.buildDeptTree(sysDeptList2);
            //取出数据，添加到另一个集合中(将那些没有该部门的公司添加进去，组成树形列表)
            List<Long> collect = assignList.stream().map(SysDept::getDeptId).collect(Collectors.toList());
            List<SysDept> otherDepts = sysDeptService.queryDeptInfoByIds(collect);
            for (SysDept otherDept : otherDepts) {
                assignList.add(otherDept);
            }
        }else {
            //如果不传deptType参数，默认查询各公司各部门下的各个用户信息
            List<SysDept> sysDeptList = sysDeptService.selectDeptList(sysDept);
            if (sysDeptList.size() > 0 && !CollectionUtils.isEmpty(sysDeptList)){
                sysDeptList.forEach( s->{
                    List<SysUserPostDeptVo> userPostDeptVos = postMapper.selectPostListByDeptId(s.getDeptId());
                    s.setUserPostDeptVos(userPostDeptVos);
                });
                assignList = sysDeptService.buildDeptTree(sysDeptList);
            }
        }
        return assignList;
    }

    /**
     * 根据用户id查询有查看权限的公司id集合
     * @param userId
     * @return
     */
    private  Set<Long> getUserAuthCompanyListByUserId(Long userId) {
        Set<Long> listIds = new HashSet<>();
        List<Map<String, Object>> resultList = new ArrayList<>();
        //查询自己有权限的公司如果是admin 则查询所有公司
        SysUser sysUser = userMapper.selectUserById(userId);
        List<SysRole> roles =sysUser.getRoles();
        List<Long> roleList = new ArrayList<>();
        boolean b = false;
        for (SysRole role : roles) {
            roleList.add(role.getRoleId());
            if(role.getRoleKey().equals("admin")){
                b = true;
                break;
            }
        }
        if(!b && (userMapper.queryAuthMainByUserId(SecurityUtils.getUserId(), AuthPermissionEnum.UNIT0.getCode()))>0 ){
            b = true;
        }
        if(b){
            //查询所有
            resultList=  userMapper.queryTabsAllCompany();
        }else {
            //查询所有角色的
            resultList=  userMapper.queryTabsCompanyByRole(roleList);
            resultList.addAll(userMapper.queryTabsCompanyByUserID(userId));
            resultList = resultList.stream()
                    .collect(Collectors.toMap(
                            map -> map.get("id"),
                            map -> map,
                            (existing, replacement) -> existing))
                    .values()
                    .stream()
                    .collect(Collectors.toList());
        }
        for (Map<String, Object> map : resultList) {
            Long companyId = Long.valueOf(map.get("id").toString());
            listIds.add(companyId);
        }
        return listIds;
    }
}
