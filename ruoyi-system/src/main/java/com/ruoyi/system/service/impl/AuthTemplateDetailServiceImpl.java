package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.service.IAuthTemplateDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.AuthTemplateDetailMapper;
import com.ruoyi.system.domain.AuthTemplateDetail;

/**
 * 权限模板明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-10
 */
@Service
public class AuthTemplateDetailServiceImpl implements IAuthTemplateDetailService
{
    @Autowired
    private AuthTemplateDetailMapper authTemplateDetailMapper;

    /**
     * 查询权限模板明细
     *
     * @param id 权限模板明细主键
     * @return 权限模板明细
     */
    @Override
    public AuthTemplateDetail selectAuthTemplateDetailById(String id)
    {
        return authTemplateDetailMapper.selectAuthTemplateDetailById(id);
    }

    /**
     * 查询权限模板明细列表
     *
     * @param authTemplateDetail 权限模板明细
     * @return 权限模板明细
     */
    @Override
    public List<AuthTemplateDetail> selectAuthTemplateDetailList(AuthTemplateDetail authTemplateDetail)
    {
        return authTemplateDetailMapper.selectAuthTemplateDetailList(authTemplateDetail);
    }

    /**
     * 新增权限模板明细
     *
     * @param authTemplateDetail 权限模板明细
     * @return 结果
     */
    @Override
    public int insertAuthTemplateDetail(AuthTemplateDetail authTemplateDetail)
    {
        authTemplateDetail.setCreateTime(DateUtils.getNowDate());
        return authTemplateDetailMapper.insertAuthTemplateDetail(authTemplateDetail);
    }

    /**
     * 修改权限模板明细
     *
     * @param authTemplateDetail 权限模板明细
     * @return 结果
     */
    @Override
    public int updateAuthTemplateDetail(AuthTemplateDetail authTemplateDetail)
    {
        authTemplateDetail.setUpdateTime(DateUtils.getNowDate());
        return authTemplateDetailMapper.updateAuthTemplateDetail(authTemplateDetail);
    }

    /**
     * 批量删除权限模板明细
     *
     * @param ids 需要删除的权限模板明细主键
     * @return 结果
     */
    @Override
    public int deleteAuthTemplateDetailByIds(String[] ids)
    {
        return authTemplateDetailMapper.deleteAuthTemplateDetailByIds(ids);
    }

    /**
     * 删除权限模板明细信息
     *
     * @param id 权限模板明细主键
     * @return 结果
     */
    @Override
    public int deleteAuthTemplateDetailById(String id)
    {
        return authTemplateDetailMapper.deleteAuthTemplateDetailById(id);
    }
}
