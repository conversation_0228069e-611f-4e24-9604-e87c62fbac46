package com.ruoyi.system.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.SystemSelectTypeComtype;
//import com.ruoyi.system.domain.vo.BusinessDataRecordVO;
import com.ruoyi.system.domain.vo.BusinessDataRecordVOT;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.SystemSelectTypeMapper;
import com.ruoyi.system.domain.SystemSelectType;
import com.ruoyi.system.service.ISystemSelectTypeService;

/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
public class SystemSelectTypeServiceImpl implements ISystemSelectTypeService 
{
    @Autowired
    private SystemSelectTypeMapper systemSelectTypeMapper;



    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public SystemSelectType selectSystemSelectTypeById(Long id)
    {
        SystemSelectType systemSelectType = systemSelectTypeMapper.selectSystemSelectTypeById(id);
        systemSelectType.setComtypes(systemSelectTypeMapper.queryTypeComTypes(id));
        return systemSelectType;
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param systemSelectType 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<SystemSelectType> selectSystemSelectTypeList(SystemSelectType systemSelectType)
    {

        List<SystemSelectType> systemSelectTypes = systemSelectTypeMapper.selectSystemSelectTypeList(systemSelectType);
        for (SystemSelectType selectType : systemSelectTypes) {
            selectType.setComtypes(systemSelectTypeMapper.queryComTypeData(selectType.getId()));
        }
        return systemSelectTypes;
    }

    /**
     * 新增【请填写功能名称】
     * 
     * @param systemSelectType 【请填写功能名称】
     * @return 结果
     */
    @Override
    public Map<String, Object> insertSystemSelectType(SystemSelectType systemSelectType, LoginUser loginUser)
    {
        Map<String, Object> returnMap = new HashMap<>();
        systemSelectType.setCreateTime(DateUtils.getNowDate());
       int i =  systemSelectTypeMapper.queryOnlyOne(systemSelectType.getModelCode(),systemSelectType.getSelectCode());
        int i2 = 0;
       if(i==0){
           i2 = systemSelectTypeMapper.insertSystemSelectType(systemSelectType);
           List<SystemSelectTypeComtype> comtypes = systemSelectType.getComtypes();

           for (SystemSelectTypeComtype comtype : comtypes) {
               comtype.setSstId(systemSelectType.getId());
           }
           //批量新增关联类型
           int i1 = systemSelectTypeMapper.batchInsert(comtypes);

           //编辑记录
           BusinessDataRecordVOT businessDataRecordVO = new BusinessDataRecordVOT();
           businessDataRecordVO.setApplyId(systemSelectType.getId());
           //6代表公司类型与下拉框关联
           businessDataRecordVO.setApplyType("6");
           //0代表新增
           businessDataRecordVO.setOperation("0");
           //新增只有新数据json有数据
           businessDataRecordVO.setOaApplyRecordsNewData(systemSelectType.getNewDataJson());
           businessDataRecordVO.setEditUserId(loginUser.getUserId());
           businessDataRecordVO.setEditTime(DateUtils.getNowDate());
           systemSelectTypeMapper.insertBusinessDataRecord(businessDataRecordVO);
           returnMap.put("code",200);
           returnMap.put("msg","新增成功！！");
       }else if(i>0) {
           returnMap.put("code",500);
           returnMap.put("msg","已存在此下拉框数据");
       }


        return returnMap;
    }

    /**
     * 修改【请填写功能名称】
     * 
     * @param systemSelectType 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateSystemSelectType(SystemSelectType systemSelectType, LoginUser loginUser)
    {
        systemSelectType.setUpdateTime(DateUtils.getNowDate());
        //删除原本关联的公司类型后再新增
        systemSelectTypeMapper.deleteTypeCpmTypeBySSTid(systemSelectType.getId());

        //重新添加
        List<SystemSelectTypeComtype> comtypes = systemSelectType.getComtypes();

        for (SystemSelectTypeComtype comtype : comtypes) {
            comtype.setSstId(systemSelectType.getId());
        }
        //批量新增关联类型
        int i1 = systemSelectTypeMapper.batchInsert(comtypes);

        int i = systemSelectTypeMapper.updateSystemSelectType(systemSelectType);
        //编辑记录
        BusinessDataRecordVOT businessDataRecordVO = new BusinessDataRecordVOT();
        businessDataRecordVO.setApplyId(systemSelectType.getId());
        //6代表公司类型与下拉框关联
        businessDataRecordVO.setApplyType("6");
        //0代表新增
        businessDataRecordVO.setOperation("1");
        //新增只有新数据json有数据
        businessDataRecordVO.setOaApplyRecordsNewData(systemSelectType.getNewDataJson());
        businessDataRecordVO.setOaApplyRecordsOldData(systemSelectType.getOldDataJson());
        businessDataRecordVO.setEditUserId(loginUser.getUserId());
        businessDataRecordVO.setEditTime(DateUtils.getNowDate());
        systemSelectTypeMapper.insertBusinessDataRecord(businessDataRecordVO);
        return i;
    }
    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteSystemSelectTypeByIds(Long[] ids)
    {
        return systemSelectTypeMapper.deleteSystemSelectTypeByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteSystemSelectTypeById(Long id)
    {
        return systemSelectTypeMapper.deleteSystemSelectTypeById(id);
    }
}
