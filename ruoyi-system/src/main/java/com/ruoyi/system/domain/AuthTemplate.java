package com.ruoyi.system.domain;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.entity.SysUser;
import lombok.Data;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import me.chanjar.weixin.cp.bean.WxCpAgent;

/**
 * 权限模板对象 auth_template
 *
 * <AUTHOR>
 * @date 2024-04-10
 */
@Data
public class AuthTemplate extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 模板名称 */
    @Excel(name = "模板名称")
    private String templateName;

    /** 模板说明 */
    @Excel(name = "模板说明")
    private String templateExplain;

    /** 生效状态：1 删除 0 生效 */
    @Excel(name = "生效状态：1 删除 0 生效")
    private String status;

    /** 来源(SYS:系统 XMGL:立项项目管理) */
    private String source;

    /** 创建人所在公司 */
    @Excel(name = "创建人所在公司")
    private Long createUnit;

    /** 修改人所在公司 */
    @Excel(name = "修改人所在公司")
    private Long updateUnit;

    /**
     * 模板类型：proj：项目  unit：公司  all：所有
     */
    private String templateType;

    /**
     * 公司分类(来源于数据字典)
     */
    private String companyType;

    /**
     * 公司分类数组
     */
    private String[] companyTypes;

    /**
     * 公司分类名称
     */
    private String companyTypeName;

    /**
     * 职能分类(来源于数据字典)
     */
    private String functionType;

    /**
     * 职能分类数组
     */
    private String[] functionTypes;

    /**
     * 职能分类名称
     */
    private String functionTypeName;

    /** 权限模板明细信息 */
    private List<AuthTemplateDetail> authTemplateDetailList;

    /**
     * 配置的权限模板详情数据
     */
    private List<AuthTempConfig> tempData;

    /**
     * 可查看和使用的用户id
     */
    private Long authUserId;

    /**
     * 是否新增标识
     */
    private String insertFlag;

    /**
     * 展示使用人配置标识  1:展示
     */
    private String useAuthFlag;

    /**
     * 操作状态: 0 增加  1 删除
     */
    private String operationFlag;

    /**
     * 可查看和使用的用户组
     */
    private Long[] authUserIds;

    private List<SysUser> authUser;

    /**
     * 权限到期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date authDate;

    /**
     * 权限保存的项目或公司ID
     */
    private Long[] authId;

    /**
     * 授权类型：proj：项目  unit：公司  all：所有
     */
    private String authType;

    /**
     * 创建人姓名
     */
    private String createName;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 代理用户ID
     */
    private Long agencyUserId;

    /**
     * 新增数据时，使用当时最新的授权id集合，用于区分不同的授权情况
     */
    private String authMainIds;
}
