package com.ruoyi.system.domain.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 新权限实体（页面接受参数用） - 查询用
 *
 * @Description
 * <AUTHOR>
 * @Date 2024/4/12 15:33
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class QueryNewAuthorityBO {
    private static final long serialVersionUID = 1L;

    //查询维度
    private String queryType;

    //查询名称
    private String queryName;

    //担保公司
    private Long custId;

    //标签页传入的code码
    private String queryCode;

    //查询时，列表展示的是用户还是岗位 1-用户 2-岗位 默认是1
    private String queryThirdType;

    //仅展示未对他人分配权限的项目0-关闭 1-开启。默认为0
    private String unassignedCompaniesFlag;

    //仅展示直属下级0-关闭 1-开启。默认为0
    private String subordinateFlag;

    //公司的类型
    private Long companyTypeCode;

    //当前分页
    private int pageNum;

    //分页大小
    private int pageSize;

    //要查询的用户id
    private Long queryUserId;
}
