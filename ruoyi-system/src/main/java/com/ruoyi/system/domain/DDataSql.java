package com.ruoyi.system.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 外部系统平台数据查询sql配置对象 d_data_sql
 * 
 * <AUTHOR>
 * @date 2022-03-31
 */
public class DDataSql extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** sql_查询编码 */
    @Excel(name = "sql_查询编码")
    private String sqlCode;

    /** 外部系统平台编码 */
    @Excel(name = "外部系统平台编码")
    private String platformNo;

    /** 担保公司编码 */
    @Excel(name = "担保公司编码")
    private String custNo;

    /** 合作方编码 */
    @Excel(name = "合作方编码")
    private String partnerNo;

    /** 资金方编码 */
    @Excel(name = "资金方编码")
    private String fundNo;

    /** 产品编码 */
    @Excel(name = "产品编码")
    private String productNo;

    /** sql查询字段 */
    @Excel(name = "sql查询字段")
    private String sqlField;

    /** sql查询表 */
    @Excel(name = "sql查询表")
    private String sqlFrom;

    /** 基本查询条件 */
    @Excel(name = "基本查询条件")
    private String sqlBasicQuery;

    /** 额外查询条件 */
    @Excel(name = "额外查询条件")
    private String sqlAdditionalQuery;

    /** 数据更新表 */
    @Excel(name = "数据更新表")
    private String updateTable;

    /** 数据更新标识（1插入3插入或更新） */
    @Excel(name = "数据更新标识",readConverterExp = "1=插入,3=插入或更新")
    private String updateTableFlag;

    /** 需要映射的字段 */
    @Excel(name = "需要映射的字段")
    private String mappingField;

    /** 插入或更新字段 */
    @Excel(name = "插入或更新字段")
    private String addUpdataField;

    /** 前置方法 以英文逗号隔开 */
    @Excel(name = "前置方法 以英文逗号隔开")
    private String beforeTask;

    /** 中置方法 以英文逗号隔开 */
    @Excel(name = "中置方法 以英文逗号隔开")
    private String ingTask;

    /** 后置方法 以英文逗号隔开 */
    @Excel(name = "后置方法 以英文逗号隔开")
    private String afterTask;


    /** 外部系统接口 */
    @Excel(name = "外部系统接口")
    private String externalSystemInterface;

    /** 请求方法 */
    @Excel(name = "请求方法")
    private String requestMethod;


    /** 定时任务状态（0正常 1停用） */
    @Excel(name = "定时任务状态",readConverterExp = "0=正常,1=停用" )
    private String taskStatus;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态",readConverterExp = "0=正常,1=停用")
    private String status;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String remark;


    @Override
    public String getRemark() {
        return remark;
    }

    @Override
    public void setRemark(String remark) {
        this.remark = remark;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setSqlCode(String sqlCode) 
    {
        this.sqlCode = sqlCode;
    }

    public String getSqlCode() 
    {
        return sqlCode;
    }
    public void setPlatformNo(String platformNo) 
    {
        this.platformNo = platformNo;
    }

    public String getPlatformNo() 
    {
        return platformNo;
    }
    public void setCustNo(String custNo) 
    {
        this.custNo = custNo;
    }

    public String getCustNo() 
    {
        return custNo;
    }
    public void setPartnerNo(String partnerNo) 
    {
        this.partnerNo = partnerNo;
    }

    public String getPartnerNo() 
    {
        return partnerNo;
    }
    public void setFundNo(String fundNo) 
    {
        this.fundNo = fundNo;
    }

    public String getFundNo() 
    {
        return fundNo;
    }
    public void setProductNo(String productNo) 
    {
        this.productNo = productNo;
    }

    public String getProductNo() 
    {
        return productNo;
    }
    public void setSqlField(String sqlField) 
    {
        this.sqlField = sqlField;
    }

    public String getSqlField() 
    {
        return sqlField;
    }
    public void setSqlFrom(String sqlFrom) 
    {
        this.sqlFrom = sqlFrom;
    }

    public String getSqlFrom() 
    {
        return sqlFrom;
    }
    public void setSqlBasicQuery(String sqlBasicQuery)
    {
        this.sqlBasicQuery = sqlBasicQuery;
    }

    public String getSqlBasicQuery()
    {
        return sqlBasicQuery;
    }
    public void setSqlAdditionalQuery(String sqlAdditionalQuery)
    {
        this.sqlAdditionalQuery = sqlAdditionalQuery;
    }

    public String getSqlAdditionalQuery()
    {
        return sqlAdditionalQuery;
    }

    public void setUpdateTable(String updateTable) 
    {
        this.updateTable = updateTable;
    }

    public void setMappingField(String mappingField)
    {
        this.mappingField = mappingField;
    }

    public String getMappingField()
    {
        return mappingField;
    }
    public String getUpdateTable() 
    {
        return updateTable;
    }
    public void setUpdateTableFlag(String updateTableFlag) 
    {
        this.updateTableFlag = updateTableFlag;
    }

    public String getUpdateTableFlag() 
    {
        return updateTableFlag;
    }
    public void setBeforeTask(String beforeTask) 
    {
        this.beforeTask = beforeTask;
    }
    public void setAddUpdataField(String addUpdataField)
    {
        this.addUpdataField = addUpdataField;
    }

    public String getAddUpdataField()
    {
        return addUpdataField;
    }
    public String getBeforeTask() 
    {
        return beforeTask;
    }
    public void setIngTask(String ingTask) 
    {
        this.ingTask = ingTask;
    }

    public String getIngTask() 
    {
        return ingTask;
    }
    public void setAfterTask(String afterTask) 
    {
        this.afterTask = afterTask;
    }

    public String getAfterTask() 
    {
        return afterTask;
    }
    public void setTaskStatus(String taskStatus) 
    {
        this.taskStatus = taskStatus;
    }

    public void setExternalSystemInterface(String externalSystemInterface)
    {
        this.externalSystemInterface = externalSystemInterface;
    }

    public String getExternalSystemInterface()
    {
        return externalSystemInterface;
    }
    public void setRequestMethod(String requestMethod)
    {
        this.requestMethod = requestMethod;
    }

    public String getRequestMethod()
    {
        return requestMethod;
    }
    public String getTaskStatus() 
    {
        return taskStatus;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("sqlCode", getSqlCode())
            .append("platformNo", getPlatformNo())
            .append("custNo", getCustNo())
            .append("partnerNo", getPartnerNo())
            .append("fundNo", getFundNo())
            .append("productNo", getProductNo())
            .append("sqlField", getSqlField())
            .append("sqlFrom", getSqlFrom())
            .append("sqlBasicQuery", getSqlBasicQuery())
            .append("sqlAdditionalQuery", getSqlAdditionalQuery())
            .append("updateTable", getUpdateTable())
            .append("updateTableFlag", getUpdateTableFlag())
            .append("mappingField", getMappingField())
            .append("addUpdataField", getAddUpdataField())
            .append("beforeTask", getBeforeTask())
            .append("ingTask", getIngTask())
            .append("afterTask", getAfterTask())
            .append("externalSystemInterface", getExternalSystemInterface())
            .append("requestMethod", getRequestMethod())
            .append("remark", getRemark())
            .append("taskStatus", getTaskStatus())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
