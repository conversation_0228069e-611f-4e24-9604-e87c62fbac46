package com.ruoyi.system.domain.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 取消所有授权 - 前端接收信息的BO
 *
 * @Description
 * <AUTHOR>
 * @Date 2024/4/23 14:51
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class CancelAllAuthorizationBO {
    private static final long serialVersionUID = 1L;

    //主键  公司维度查询就是公司id，项目维度查询就是项目id
    private Long id;

    //业务维度 1-项目 2-公司   all-所有
    private String businessType;

    //功能模块 功能模块的标签页
    private String businessCode;

    //取消授权用户id
    private Long unAuthorizedUserId;

    //取消类型
    //1-取消本项目的所有授权
    //2-取消在系统的所有授权
    private String unAuthorizedType;

    //被代理用户的id
    private Long principalId;
}
