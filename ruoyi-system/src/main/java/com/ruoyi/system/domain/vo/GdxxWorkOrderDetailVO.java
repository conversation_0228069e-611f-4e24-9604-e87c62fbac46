package com.ruoyi.system.domain.vo;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.system.domain.GdxxWorkOrderPersonnel;
import com.ruoyi.system.domain.GdxxWorkOrderFile;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class GdxxWorkOrderDetailVO {
    private Long id;
    private String workOrderTitle;
    private String workOrderType;
//    private String workOrderTypeName;
    private String requesterId; // 需求提出人ID
    private String requesterName;// 需求提出人姓名
    private String requesterDepartmentId;
    private String requesterDepartmentName;// 需求提出人部门名称
    private Date requirementSubmissionTime;
    private String acceptanceTime; //受理时间
    private String workOrderStatus;
//    private String workOrderStatusName;
    private String rndProgress;
//    private String rndProgressName;
    private String requirementBackground;
    private String requirementPurpose;
    private String requirementDescription;
    private String requirementPriority;
//    private String requirementPriorityName;
    private String expectedCompletionDate;
    private String requirementRemark;
    private String requirementImplementationSystem;
//    private String requirementImplementationSystemName;
    private String systemFunctionModule;
//    private String systemFunctionModuleName;
    private String projectRisk;
//    private String projectRiskName;
    private String externalStakeholderInfo;
    private String designScheduleStartDate;
    private String designScheduleEndDate;
    private String requirementScheduleStartDate;
    private String requirementScheduleEndDate;
    private String developmentScheduleStartDate;
    private String developmentScheduleEndDate;
    private String testingScheduleStartDate;
    private String testingScheduleEndDate;
    private String acceptanceTestingScheduleStartDate;
    private String acceptanceTestingScheduleEndDate;
    private String expectedGoLiveDate;

    private String currentExecutor; //执行人ID
    private String currentExecutorName;//执行人姓名
    // 关联表数据
    private List<GdxxWorkOrderPersonnel> personnelList;
    private List<GdxxWorkOrderFile> fileList;

    /** 创建时间 */
    private Date creationTime;
    /** 创建人的userName */
    private String creator;
    /** 创建人的nickName */
    private String creatorName;
    /** 修改时间 */
    private Date modificationTime;
    /** 修改人 */
    private String modifier;
    /** 修改人的nickName */
    private String modifierName;
}