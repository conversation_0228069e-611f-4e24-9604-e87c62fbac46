package com.ruoyi.system.domain.vo;

import com.ruoyi.system.domain.GdxxWorkOrderDynamic;
import com.ruoyi.system.domain.GdxxWorkOrderFile;
import lombok.Data;
import java.util.List;

@Data
public class GdxxWorkOrderDynamicVO extends GdxxWorkOrderDynamic {
    private static final long serialVersionUID = 1L;
    //创建人姓名
    private String creatorName;
    // 关联的文件列表
    private List<GdxxWorkOrderFile> fileList;
} 