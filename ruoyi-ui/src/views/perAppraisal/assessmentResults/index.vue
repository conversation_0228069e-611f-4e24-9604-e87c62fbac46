<template>
  <div class="p-5">
    <MyForm
      v-show="showSearch"
      v-model="queryParams"
      :columns="formColumns"
      @onSearchList="handleQuery"
      labelWidth="110px"
      class="myform"
    />
    <el-divider></el-divider>
    <div>
      系统说明<el-tooltip class="item" effect="light" placement="top-start">
        <div slot="content">
          <div class="text-black">
            <div>
              总项目指标/分配项目指标/自拓项目指标/分配银行指标：来源于您配置的<span
                class="font-black"
                >年度计划</span
              >
            </div>
            <div>
              总项目业绩/分配项目业绩/自拓项目业绩：来源于财务录入的<span
                class="font-black"
                >项目业绩</span
              >
            </div>
            <div>
              数据计算规则顺序：根据您配置年度计划，<span class="font-black"
                >用户配置 > 部门配置 > 公司配置</span
              >
            </div>
            <div class="my-3">
              <span class="font-black">例如1：</span
              >公司的一季度指标，维护了公司指标和考核配置占比：
            </div>
            <div>
              获取本公司的项目，根据项目的业绩类型(分配/自拓)，再根据年度计划中维护的指标，考核配置中维护的占比，计算本季度本公司薪资占比。
            </div>
            <div>薪资总占比：a + b</div>
            <div>
              薪资分配占比(a)：( 本季度公司<span class="font-black">分配</span
              >项目业绩(1月+2月+3月) / 本季度公司<span class="font-black"
                >分配</span
              >项目指标 ) * 考核配置<span class="font-black">分配</span>占比
            </div>
            <div>
              薪资自拓占比(b)：( 本季度公司<span class="font-black">自拓</span
              >项目业绩((1月+2月+3月) / 本季度公司<span class="font-black"
                >自拓</span
              >项目指标 ) * 考核配置<span class="font-black">自拓</span>占比
            </div>
            <div class="my-3">
              <span class="font-black">例如2：</span
              >公司的一季度指标，维护了部门指标和考核配置占比：
            </div>
            <div>
              获取本公司的项目，根据项目的业绩类型(分配/自拓)，再根据年度计划中维护的指标，考核配置中维护的占比，计算本季度本公司薪资占比。
            </div>
            <div>薪资总占比：a1 + b1</div>
            <div>
              薪资分配占比(a1)：( 本季度公司<span class="font-black">分配</span
              >项目业绩(1月+2月+3月) / 本季度公司<span class="font-black"
                >分配</span
              >项目指标 ) * 考核配置<span class="font-black">分配</span>占比
            </div>
            <div>
              薪资自拓占比(b1)：( 本季度公司<span class="font-black">自拓</span
              >项目业绩((1月+2月+3月) / 本季度公司<span class="font-black"
                >自拓</span
              >项目指标 ) * 考核配置<span class="font-black">自拓</span>占比
            </div>
            <div class="my-3">
              <span class="font-black">例如2：</span
              >公司的一季度指标，维护了用户指标和考核配置占比：
            </div>
            <div>
              获取本公司的项目，根据项目的业绩类型(分配/自拓)，再根据年度计划中维护的指标，考核配置中维护的占比，计算本季度本公司薪资占比。
            </div>
            <div>薪资总占比：a2 + b2</div>
            <div>
              薪资分配占比(a2)：( 本季度公司<span class="font-black">分配</span
              >项目业绩(1月+2月+3月) / 本季度公司<span class="font-black"
                >分配</span
              >项目指标 ) * 考核配置<span class="font-black">分配</span>占比
            </div>
            <div>
              薪资自拓占比(b2)：( 本季度公司<span class="font-black">自拓</span
              >项目业绩((1月+2月+3月) / 本季度公司<span class="font-black"
                >自拓</span
              >项目指标 ) * 考核配置<span class="font-black">自拓</span>占比
            </div>
            <div class="my-3">
              自拓银行占比计算公式：( 本季度<span class="font-black"
                >自拓银行数量</span
              >
              / 本季度<span class="font-black">自拓银行指标</span> ) *
              考核配置<span class="font-black">自拓</span>占比
            </div>
            <div class="mb-3 font-black">特殊情况</div>
            <div>中保的分配项目业绩为所有公司分配项目业绩的总和。</div>
          </div>
        </div>
        <span class="relative bottom-1"> <i class="el-icon-info"></i> </span>
      </el-tooltip>
    </div>

    <div class="flex mt-8">
      <div class="font-bold mr-2 leading-7">展示维度</div>
      <el-radio-group
        @input="handleQuery"
        v-model="queryParams.type"
        size="mini"
      >
        <el-radio-button
          v-for="(item, index) in dimensionList"
          :key="index"
          :label="item.value"
          >{{ item.label }}</el-radio-button
        >
      </el-radio-group>
      <el-button
        v-hasPermi="['perAppraisal:assessmentResults:export']"
        @click="handleExport"
        type="warning"
        size="mini"
        plain
        icon="el-icon-download"
        class="mx-5"
        >导出列表</el-button
      >
      <el-button
        v-hasPermi="['perAppraisal:assessmentResults:sure']"
        @click="
          $router.push({
            path: '/perAppraisalOther/assessmentResultsConfirmation',
          })
        "
        type="primary"
        size="mini"
        icon="el-icon-check"
        class="mx-5"
      >
        考核结果确认</el-button
      >
      <el-button
        v-hasPermi="['perAppraisal:assessmentResults:again']"
        @click="regenerate"
        type="primary"
        size="mini"
        icon="el-icon-refresh-right"
        class="mx-5"
      >
        重新生成考核结果</el-button
      >
    </div>
    <right-toolbar
      :showSearch.sync="showSearch"
      @queryTable="handleQuery"
      @handleClose="handleClose"
      :columns="columnsChange"
      :propsTransfer="propsTransfer"
    ></right-toolbar>

    <MyTable :columns="columns" :source="configList" v-loading="loading">
      <template #quarter="{ record }">
        <div>{{ quarterObj[record.quarter] }}</div>
      </template>
      <template #totalIndex="{ record }">
        <div :style="{ color: record.totalIndex ? '' : 'rgb(245, 108, 108)' }">
          {{ record.totalIndex || "请补充年度计划" }}
        </div>
      </template>
      <template #distributionIndex="{ record }">
        <div
          :style="{
            color: record.distributionIndex ? '' : 'rgb(245, 108, 108)',
          }"
        >
          {{ record.distributionIndex || "请补充年度计划" }}
        </div>
      </template>
      <template #extensionIndex="{ record }">
        <div
          :style="{ color: record.extensionIndex ? '' : 'rgb(245, 108, 108)' }"
        >
          {{ record.extensionIndex || "请补充年度计划" }}
        </div>
      </template>
      <template #extensionBank="{ record }">
        <div
          :style="{ color: record.extensionBank ? '' : 'rgb(245, 108, 108)' }"
        >
          {{ record.extensionBank || "未在年度计划中配置" }}
        </div>
      </template>
      <template #achievementWagesProportion="{ record }">
        <div
          :style="{
            color:
              record.achievementWagesProportion ||
              record.achievementWagesProportion == 0
                ? ''
                : 'rgb(245, 108, 108)',
          }"
        >
          {{
            record.achievementWagesProportion ||
            record.achievementWagesProportion == 0
              ? record.achievementWagesProportion
              : "请补充考核配置"
          }}
        </div>
      </template>
      <template #distributionProportion="{ record }">
        <div
          :style="{
            color:
              record.distributionProportion ||
              record.distributionProportion == 0
                ? ''
                : 'rgb(245, 108, 108)',
          }"
        >
          {{
            record.distributionProportion || record.distributionProportion == 0
              ? record.distributionProportion
              : "请补充考核配置"
          }}
        </div>
      </template>
      <template #extensionProportion="{ record }">
        <div
          :style="{
            color:
              record.extensionProportion || record.extensionProportion == 0
                ? ''
                : 'rgb(245, 108, 108)',
          }"
        >
          {{
            record.extensionProportion || record.extensionProportion == 0
              ? record.extensionProportion
              : "请补充考核配置"
          }}
        </div>
      </template>
      <template #completeExtensionBank="{ record }">
        <div
          :style="{
            color: record.completeExtensionBank ? '' : 'rgb(245, 108, 108)',
          }"
        >
          {{ record.completeExtensionBank || "未补充" }}
        </div>
      </template>
      <template #distributionIndexDeviation="{ record }">
        <div
          :style="{
            color: record.distributionIndexDeviationColor,
          }"
        >
          {{
            record.distributionIndexDeviation != undefined
              ? record.distributionIndexDeviation
              : "-"
          }}
        </div>
      </template>
      <template #calibrationProjectSalaryExtensionProportion="{ record }">
        <div>
          {{
            Math.max(
              record.calibrationProjectSalaryExtensionProportion || 0,
              record.extensionIndexDeviationProportionQ3 || 0
            )
          }}%
          <div
            style="color: red"
            v-show="
              record.extensionIndexDeviationProportionQ3 >
              record.calibrationProjectSalaryExtensionProportion
            "
          >
            (包含前三季度补充)
          </div>
        </div>
      </template>
      <template #extensionIndexDeviation="{ record }">
        <div
          :style="{
            color: record.extensionIndexDeviationColor,
          }"
        >
          {{
            record.extensionIndexDeviation != undefined
              ? record.extensionIndexDeviation
              : "-"
          }}
        </div>
      </template>
      <template #calibrationDistributionIndex="{ record }">
        <div
          :style="{
            color: record.calibrationDistributionIndexColor,
          }"
        >
          {{
            record.calibrationDistributionIndex !== null &&
            record.calibrationDistributionIndex !== undefined
              ? record.calibrationDistributionIndex
              : "-"
          }}
        </div>
      </template>
      <template #extensionBankDeviation="{ record }">
        <div
          :style="{
            color: record.extensionBankDeviationColor,
          }"
        >
          {{ record.extensionBankDeviation }}
        </div>
      </template>
      <template #calibrationDistributionIndexState="{ record }">
        <div>
          {{ distributionStateObj[record.calibrationDistributionIndexState] }}
        </div>
      </template>
      <template #operate="{ record }">
        <el-dropdown @command="handleCommand($event, record)" class="ml-2">
          <span class="el-dropdown-link mr-2" style="font-size: 14px">
            >>更多
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              command="calibration"
              :disabled="record.state == 3"
              >校准考核结果</el-dropdown-item
            >
            <el-dropdown-item command="supplement"
              >补充自拓银行完成情况</el-dropdown-item
            >
            <el-dropdown-item command="view">查看详情</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
    </MyTable>

    <pagination
      v-show="page.total > 0"
      :total="page.total"
      :page.sync="page.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="page.pageSize"
      @pagination="handleConfigList"
    />
    <DetailDialog
      v-model="openSupplement"
      :currentForm="currentForm"
      :type="queryParams.type"
      @on-submit-success="getList"
    />
    <RegenerateDetailDialog
      v-model="regenerateOpen"
      @on-submit-success="resetCheckResultSuccess"
      :queryParams="queryParams"
    />
  </div>
</template>

<script>
import { haveAuthorityCompanyList } from "@/api/businessInformation/companyInformation";
import { treeselect } from "@/api/system/dept";
import {
  listResult,
  checkResultList,
  replaceColumnsInit,
  resetCheckResult,
} from "@/api/perAppraisal/results";
import config from "./components/config";
import DetailDialog from "./components/DetailDialog.vue";
import RegenerateDetailDialog from "./components/RegenerateDetailDialog.vue";

import XEUtils from "xe-utils";
export default {
  name: "AssessmentResults",
  components: { DetailDialog, RegenerateDetailDialog },

  data() {
    return {
      ...config,
      showSearch: true,
      isView: false,
      queryParams: {
        year: [new Date().getFullYear() + ""],
        quarter: [String(Math.floor(new Date().getMonth() / 3) + 1)],
        type: "1",
      },
      page: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      configListAll: [],
      configList: [],
      openSupplement: false,
      currentForm: {},
      loading: false,
      regenerateOpen: false,
    };
  },

  created() {
    this.init();
  },
  methods: {
    async init() {
      await this.getCompanyList();
      await this.getDeptList();
      this.getForm();
      this.getColumnsInit();
      this.getList();
    },

    async getCompanyList() {
      const { rows } = await haveAuthorityCompanyList({ isInside: 1 });
      this.formColumnsInit[1].options = rows;
    },
    async getDeptList(value) {
      const { data } = await treeselect({ unitId: value?.join() });
      this.formColumnsInit[2].options = data;
    },
    getForm() {
      if (this.queryParams.type == 1) {
        this.queryParams.deptId = undefined;
        this.formColumns = this.formColumnsInit.filter(
          (item) => item.prop !== "deptId"
        );
      } else if (this.queryParams.type == 2) {
        this.queryParams.companyId = undefined;
        this.formColumns = this.formColumnsInit.filter(
          (item) => item.prop !== "companyId"
        );
      } else {
        this.formColumns = XEUtils.clone(this.formColumnsInit, true);
      }
    },
    async getColumnsInit() {
      this.columnsInit = {
        1: this.columnsInitCompany,
        2: this.columnsInitDept,
        3: this.columnsInitUser,
      }[this.queryParams.type];
      //发送请求获取列 如果没有则用默认值
      const { rows } = await checkResultList();
      rows.forEach((item) => {
        this.columnsAddInit.forEach((item1) => {
          if (item.columns == item1.columns) {
            this.$set(item, "label", item1.label);
          }
        });
      });
      this.columnsChange = rows;
      this.columns = this.columnsInit.concat(
        this.getColumnsChange(rows),
        this.columnsOperate
      );
    },
    getColumnsChange(rows) {
      const showColumns = rows
        .filter((item) => item.visible)
        .map((item) => item.columns);
      const columnsAddInit = this.columnsAddInit.filter((item) => {
        if (this.queryParams.type == 1) {
          return !["deptNameBelong", "companyShortNameBelong"].includes(
            item.columns
          );
        } else if (this.queryParams.type == 2) {
          return !["deptNameBelong"].includes(item.columns);
        } else if (this.queryParams.type == 3) {
          return !["companyShortNameBelong"].includes(item.columns);
        }
      });
      return columnsAddInit.filter((item) => {
        return showColumns.includes(item.columns);
      });
    },
    async getList() {
      this.loading = true;
      const { rows } = await listResult(this.getParams());
      this.configListAll = rows;
      this.loading = false;
      this.handleConfigList();
    },
    handleConfigList() {
      const start = (this.page.pageNum - 1) * this.page.pageSize;
      const end = start + this.page.pageSize;
      this.configList = this.configListAll.slice(start, end);
      this.page.total = this.configListAll.length;
      const toDecimal = [
        "totalIndex",
        "distributionIndex",
        "extensionIndex",
        "completeTotalIndex",
        "completeDistributionIndex",
        "calibrationDistributionIndex",
        "completeExtensionIndex",
        "calibrationExtensionIndex",
      ];
      const deviation = [
        "distributionIndexDeviation",
        "extensionIndexDeviation",
      ];
      const addUnit = [
        "achievementWagesProportion",
        "distributionProportion",
        "extensionProportion",
        "projectSalaryTotalProportion",
        "projectSalaryDistributionProportion",
        "calibrationProjectSalaryDistributionProportion",
        "bankSalaryExtensionProportion",
        "calibrationProjectSalaryBankProportion",
        "calibrationProjectSalaryTotalProportion",
        "projectSalaryExtensionProportion",
      ];
      this.configList = this.configList.map((item) => {
        item = Object.assign(item, item.checkResult);
        toDecimal.forEach((item1) => {
          item[item1] = item[item1] && Number(item[item1]).toFixed(6);
        });
        addUnit.forEach((item1) => {
          if (item[item1] != undefined && item[item1] != null) {
            item[item1] = item[item1] + "%";
          }
        });
        deviation.forEach((item1) => {
          if (item[item1]) {
            if (item[item1] > 0) {
              item[item1] = `+${Number(item[item1]).toFixed(6)}`;
              this.$set(item, item1 + "Color", "rgb(245, 108, 108)");
            } else {
              item[item1] = `${Number(item[item1]).toFixed(6)}`;
              this.$set(
                item,
                item1 + "Color",
                "rgba(191, 191, 0, 0.***************)"
              );
            }
          } else {
            item[item1] = item[item1] == 0 ? 0 : undefined;
            this.$set(item, item1 + "Color", "");
          }
        });
        if (item.extensionBankDeviation) {
          if (item.extensionBankDeviation > 0) {
            item.extensionBankDeviation = `+${item.extensionBankDeviation}`;
            this.$set(
              item,
              "extensionBankDeviationColor",
              "rgb(245, 108, 108)"
            );
          } else {
            this.$set(
              item,
              "extensionBankDeviationColor",
              "rgba(191, 191, 0, 0.***************)"
            );
          }
        } else {
          item.extensionBankDeviation =
            item.extensionBankDeviation == 0 ? 0 : undefined;
          this.$set(item, "extensionBankDeviationColor", "");
        }

        this.$set(
          item,
          "calibrationDistributionIndexColor",
          item.calibrationDistributionIndex &&
            item.calibrationDistributionIndex > 0
            ? "rgba(191, 191, 0, 0.***************)"
            : ""
        );
        if (
          item.calibrationExtensionIndexBankState &&
          item.calibrationExtensionIndexProjectState
        ) {
          let temp = "";
          if (
            item.calibrationExtensionIndexBankState.indexOf("已完成") != -1 &&
            item.calibrationExtensionIndexProjectState.indexOf("已完成") != -1
          ) {
            let partA =
              item.calibrationExtensionIndexProjectState.split("-")[1] || "";
            let partB =
              item.calibrationExtensionIndexBankState.split("-")[1] || "";
            let uniqueParts = new Set([partA, partB]);
            temp = `已完成-${Array.from(uniqueParts)
              .filter((item) => item !== "")
              .join(",")}`;
          } else if (
            item.calibrationExtensionIndexBankState.indexOf("已完成") != -1 ||
            item.calibrationExtensionIndexProjectState.indexOf("已完成") != -1
          ) {
            if (
              item.calibrationExtensionIndexBankState.indexOf("已完成") != -1
            ) {
              temp = item.calibrationExtensionIndexBankState;
            }
            if (
              item.calibrationExtensionIndexProjectState.indexOf("已完成") != -1
            ) {
              temp = item.calibrationExtensionIndexProjectState;
            }
          } else {
            temp = "未完成";
          }
          this.$set(item, "calibrationExtensionIndexState", temp);
        } else {
          this.$set(
            item,
            "calibrationExtensionIndexState",
            item.calibrationExtensionIndexBankState ||
              item.calibrationExtensionIndexProjectState
          );
        }
        delete item.checkResult;
        return item;
      });
    },
    getParams() {
      const params = XEUtils.clone(this.queryParams, true);
      const calibrationExtensionIndexBankState =
        params?.calibrationExtensionIndexBankState?.split(",");
      if (
        calibrationExtensionIndexBankState &&
        calibrationExtensionIndexBankState.length > 1
      ) {
        params.calibrationExtensionIndexBankState =
          calibrationExtensionIndexBankState[0];
        params.calibrationExtensionIndexProjectState =
          calibrationExtensionIndexBankState[1];
      }
      params.years = params.year?.map((item) => (item = Number(item))).join();
      params.quarters = params.quarter
        ?.map((item) => (item = Number(item)))
        .join();
      params.companyIds = params.companyId
        ?.map((item) => (item = Number(item)))
        .join();

      delete params.year;
      delete params.quarter;
      delete params.companyId;
      return params;
    },
    async handleClose() {
      this.columns = this.columnsInit.concat(
        this.getColumnsChange(this.columnsChange),
        this.columnsOperate
      );
      await replaceColumnsInit(this.columnsChange);
    },
    handleQuery(value) {
      this.page.pageNum = 1;
      if (value == "companyId") {
        // this.getDeptList(this.queryParams.companyId);
      }
      if (!this.queryParams.year.length) {
        this.$message.error("至少选择一个年度");
        this.configList = [];
        this.page.total = 0;
        return;
      }
      if (!this.queryParams.quarter.length) {
        this.$message.error("至少选择一个季度");
        this.configList = [];
        this.page.total = 0;
        return;
      }
      this.getForm();
      this.getColumnsInit();
      this.getList();
    },

    handleExport() {
      this.download("check/result/export", this.getParams(), `考核结果.xlsx`);
    },

    handleCommand(command, record) {
      const obj = {
        calibration: this.calibration,
        supplement: this.supplement,
        view: this.calibration,
      };
      if (command == "view") {
        this.isView = true;
      }
      if (command == "calibration") {
        this.isView = false;
      }
      obj[command](record);
    },
    regenerate() {
      this.regenerateOpen = true;
    },
    async resetCheckResultSuccess() {
      await resetCheckResult({
        quarters: this.queryParams.quarter?.map(
          (item) => (item = Number(item))
        ),
      });
      this.getList();
      this.$message.success("操作成功");
    },
    calibration(record) {
      const title = {
        1: `考核结果校准-公司`,
        2: `考核结果校准-部门`,
        3: `考核结果校准-用户`,
      }[this.queryParams.type];
      const id = {
        1: record.companyId,
        2: record.deptId,
        3: record.userId,
      }[this.queryParams.type];
      const proForm = JSON.stringify({
        type: this.queryParams.type,
        year: record.year,
        quarter: record.quarter,
        userId: record.userId,
        deptId: record.deptId,
        companyId: record.companyId,
        isView: this.isView,
      });
      this.$router.push({
        path: `/perAppraisalOther/assessmentResults/${id}`,
        query: {
          title,
          proForm,
        },
      });
    },
    supplement(record) {
      this.openSupplement = true;
      this.currentForm = { ...record };
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .wraps {
  .el-form-item__label {
    white-space: pre-wrap;
    text-align: center;
    line-height: 20px;
  }
}
::v-deep .el-table {
  .el-table__header-wrapper {
    table {
      thead {
        th {
          font-weight: bold;
          color: #333;
          // 换行
          .cell {
            white-space: pre-wrap;
          }
        }
      }
    }
  }
}
</style>
