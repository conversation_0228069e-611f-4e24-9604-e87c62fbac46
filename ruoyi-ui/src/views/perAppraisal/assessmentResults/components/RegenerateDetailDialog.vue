<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      title="警告"
      :visible.sync="innerValue"
      width="550px"
      @close="handleClose"
      @open="handleOpen"
    >
      <div>
        <div>
          <div v-for="(item, index) in myForm" :key="index">
            {{ `【${item.name}】【${item.year}】年度已生成考核结果但未确认` }}；
          </div>
          <div style="color: rgb(245, 108, 108)">
            执行重新计算考核结果操作,考核结果和校准数据将被清空,会根据当前配置的年度计划、考核配置、项目业绩重新进行计算，请您谨慎操作，输入登录密码后，点击【确定】按钮，执行本次操作
          </div>
        </div>
        <div class="text-center my-2">请输入登录密码，进行下一步操作</div>
        <div>
          <el-input
            class="block m-auto"
            style="width: 240px"
            placeholder="请输入登录密码"
            v-model.trim="password"
            show-password
            @keyup.enter.native="onSubmit"
          ></el-input>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <div class="flex justify-center">
          <el-button @click="innerValue = false" class="mr-3">取消</el-button>
          <el-button
            @click="onSubmit"
            :disabled="!Boolean(password)"
            type="primary"
            class="ml-3"
            >提交</el-button
          >
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { JSEncrypt } from "jsencrypt";
import { getPublicCode } from "@/api/login";
import { checkPwd } from "@/api/perAppraisal/assessmentConfiguration";
import { selectCheckResultNoAuditing } from "@/api/perAppraisal/results";
import XEUtils from "xe-utils";

import vModelMixin from "@/mixin/v-model";
export default {
  mixins: [vModelMixin],
  props: {
    queryParams: {
      type: Object,
      required: true,
      default: () => ({}),
    },
  },
  data() {
    return {
      myForm: [],
      password: "",
    };
  },

  mounted() {},
  methods: {
    async handleOpen() {
      const { rows } = await selectCheckResultNoAuditing({
        quarters: this.queryParams.quarter
          ?.map((item) => (item = Number(item)))
          .join(),
      });
      this.myForm = rows;
      this.myForm.forEach((item) => {
        if (item.type == 1) {
          item.name = item.companyShortName;
        } else if (item.type == 2) {
          item.name = item.deptName;
        } else {
          item.name = item.nickName;
        }
      });
    },
    async onSubmit() {
      if (!this.password) {
        this.$message.error("请输入密码");
        return;
      } else if (!this.myForm.length) {
        this.$message.error("暂无可重置的数据");
        return;
      }
      const res = await getPublicCode();
      var encrypt = new JSEncrypt();
      encrypt.setPublicKey(res);
      //对字段进行加密
      const data = {};
      data.password = encrypt.encrypt(this.password);
      await checkPwd(data);
      this.innerValue = false;
      this.$emit("on-submit-success");
    },

    handleClose() {
      this.password = "";
    },
  },
};
</script>
