export default {
  distributionStateObj: Object.freeze({
    1: "已完成",
    2: "未完成",
    3: "已完成-项目校准",
    4: "未完成-项目校准",
  }),

  quarterObj: Object.freeze({
    1: "一季度",
    2: "二季度",
    3: "三季度",
    4: "四季度",
  }),
  formColumns:[],
  formColumnsInit: [
    {
      label: "所属年度",
      prop: "year",
      type: "datePicker",
      dateType: "years",
      valueFormat: "yyyy",
      placeholder: "请选择所属年度",
      clearable: false,
    },

    {
      label: "公司名称",
      prop: "companyId",
      type: "select",
      filterable: true,
      multiple: true,
      options: [],
      dataProp: {
        value: "id",
        label: "companyName",
      },
      style: { width: "340px" },
      placeholder: "请选择/输入公司名称",
    },
    {
      label: "部门名称",
      prop: "deptId",
      type: "treeselect",
      filterable: true,
      options: [],
      placeholder: "请选择/输入部门名称",
    },

    {
      label: "所属季度",
      prop: "quarter",
      type: "select",
      filterable: true,
      multiple: true,
      options: [
        { value: "1", label: "第一季度" },
        { value: "2", label: "第二季度" },
        { value: "3", label: "第三季度" },
        { value: "4", label: "第四季度" },
      ],
      placeholder: "请选择季度",
      clearable: false,
    },
    {
      label: "分配指标\n校准后完成状态",
      prop: "calibrationDistributionIndexState",
      type: "select",
      options: [
        { value: "2", label: "未完成" },
        { value: "1", label: "已完成" },
        { value: "3", label: "已完成 - 项目校准" },
        { value: "4", label: "未完成 - 项目校准" },
      ],
      placeholder: "请选择分配指标校准后完成状态",
      class: "wraps",
    },
    {
      label: "自拓指标\n校准后完成状态",
      prop: "calibrationExtensionIndexBankState",
      type: "select",
      options: [
        { value: "未完成", label: "未完成" },
        { value: "已完成-项目", label: "已完成-项目" },
        { value: "已完成-银行", label: "已完成-银行" },
        { value: "已完成-项目,已完成-银行", label: "已完成-项目、银行" },
        { value: "已完成-项目校准", label: "已完成-项目校准" },
        { value: "已完成-银行校准", label: "已完成-银行校准" },
        {
          value: "已完成-项目校准,已完成-银行校准",
          label: "已完成-项目校准、银行校准",
        },
      ],
      placeholder: "请选择自拓指标校准后完成状态",
      class: "wraps",
    },
  ],
  dimensionList: Object.freeze([
    { label: "按公司", value: "1" },
    { label: "按部门", value: "2" },
    { label: "按用户", value: "3" },
  ]),
  propsTransfer: Object.freeze({
    key: "columns",
    label: "label",
  }),
  columns: [],
  columnsChange: [],
  columnsInit: [],
  columnsInitUser: Object.freeze([
    {
      label: "用户姓名",
      prop: "nickName",
      fixed: "left",
      minWidth: "150px",
    },
  ]),
  columnsInitDept: Object.freeze([
    {
      label: "部门名称",
      prop: "deptName",
      fixed: "left",
      minWidth: "150px",
    },
  ]),
  columnsInitCompany: Object.freeze([
    {
      label: "公司名称",
      prop: "companyShortName",
      fixed: "left",
      minWidth: "150px",
    },
  ]),
  columnsAddInit: [
    { label: "年度", prop: "year", columns: "year", minWidth: "100px" },
    { label: "季度", key: "quarter", columns: "quarter", minWidth: "100px" },
    {
      label: "所属部门",
      prop: "deptNameBelong",
      columns: "deptNameBelong",
      minWidth: "250px",
    },
    {
      label: "所属公司",
      prop: "companyShortNameBelong",
      columns: "companyShortNameBelong",
      minWidth: "250px",
    },

    {
      label: "总项目指标\n   (万元)",
      key: "totalIndex",
      columns: "totalIndex",
      minWidth: "130px",
    },
    {
      label: "分配项目指标\n    (万元)",
      key: "distributionIndex",
      columns: "distributionIndex",
      minWidth: "130px",
    },
    {
      label: "自拓项目指标\n     (万元)",
      key: "extensionIndex",
      columns: "extensionIndex",
      minWidth: "140px",
    },
    {
      label: "  自拓银行指标\n         (家)",
      key: "extensionBank",
      columns: "extensionBank",
      minWidth: "150px",
    },
    {
      label: "绩效工资占比\n      (%)",
      key: "achievementWagesProportion",
      columns: "achievementWagesProportion",
      minWidth: "140px",
    },
    {
      label: "分配占比(%)",
      key: "distributionProportion",
      columns: "distributionProportion",
      minWidth: "140px",
    },
    {
      label: "自拓占比(%)",
      key: "extensionProportion",
      columns: "extensionProportion",
      minWidth: "130px",
    },
    {
      label: "   总项目业绩\n完成情况(万元)",
      prop: "completeTotalIndex",
      columns: "completeTotalIndex",
      minWidth: "140px",
    },
    {
      label: " 项目薪资\n总占比(%)",
      prop: "projectSalaryTotalProportion",
      columns: "projectSalaryTotalProportion",
      minWidth: "140px",
    },
    {
      label: " 分配项目业绩\n完成情况(万元)",
      prop: "completeDistributionIndex",
      columns: "completeDistributionIndex",
      minWidth: "130px",
    },
    {
      label: "   项目薪资\n分配占比(%)",
      prop: "projectSalaryDistributionProportion",
      columns: "projectSalaryDistributionProportion",
      minWidth: "140px",
    },
    {
      label: "   分配项目\n业绩偏差(万元)",
      key: "distributionIndexDeviation",
      columns: "distributionIndexDeviation",
      minWidth: "140px",
    },
    {
      label: "人事校准-\n分配项目",
      key: "calibrationDistributionIndex",
      columns: "calibrationDistributionIndex",
      minWidth: "130px",
    },
    {
      label: "    分配指标\n校准后完成状态",
      key: "calibrationDistributionIndexState",
      columns: "calibrationDistributionIndexState",
      minWidth: "140px",
    },
    {
      label: "    分配指标\n校准后薪资占比",
      prop: "calibrationProjectSalaryDistributionProportion",
      columns: "calibrationProjectSalaryDistributionProportion",
      minWidth: "140px",
    },
    {
      label: "自拓项目业绩\n完成情况(万元)",
      prop: "completeExtensionIndex",
      columns: "completeExtensionIndex",
      minWidth: "130px",
    },
    {
      label: " 项目薪资\n自拓占比(%)",
      prop: "projectSalaryExtensionProportion",
      columns: "projectSalaryExtensionProportion",
      minWidth: "140px",
    },
    {
      label: "    自拓项目\n业绩偏差(万元)",
      key: "extensionIndexDeviation",
      columns: "extensionIndexDeviation",
      minWidth: "140px",
    },
    {
      label: "人事校准-\n自拓项目",
      prop: "calibrationExtensionIndex",
      columns: "calibrationExtensionIndex",
      minWidth: "130px",
    },

    {
      label: " 自拓项目指标\n校准后薪资占比",
      key: "calibrationProjectSalaryExtensionProportion",
      columns: "calibrationProjectSalaryExtensionProportion",
      minWidth: "160px",
    },
    {
      label: " 自拓银行业绩\n 完成情况(家)",
      key: "completeExtensionBank",
      columns: "completeExtensionBank",
      minWidth: "140px",
    },
    {
      label: "  薪资自拓\n银行占比(%)",
      prop: "bankSalaryExtensionProportion",
      columns: "bankSalaryExtensionProportion",
      minWidth: "130px",
    },
    {
      label: "自拓银行完成\n 情况偏差(家)",
      key: "extensionBankDeviation",
      columns: "extensionBankDeviation",
      minWidth: "140px",
    },
    {
      label: "  人事校准-\n自拓银行(家)",
      prop: "calibrationExtensionBank",
      columns: "calibrationExtensionBank",
      minWidth: "140px",
    },
    {
      label: " 自拓银行指标\n校准后薪资占比",
      prop: "calibrationProjectSalaryBankProportion",
      columns: "calibrationProjectSalaryBankProportion",
      minWidth: "130px",
    },
    {
      label: "   自拓指标\n校准后完成状态",
      prop: "calibrationExtensionIndexState",
      columns: "calibrationExtensionIndexState",
      minWidth: "140px",
    },
    {
      label: "   校准后\n薪资总占比",
      prop: "calibrationProjectSalaryTotalProportion",
      columns: "calibrationProjectSalaryTotalProportion",
      minWidth: "140px",
    },
  ],
  columnsOperate: Object.freeze([
    { label: "操作", key: "operate", width: "100px", fixed: "right" },
  ]),
  formColumnsDialog: [],
  formColumnsDialogUser: Object.freeze([
    { label: "用户姓名", value: "nickName" },
    { label: "所属公司", value: "companyShortName" },
    { label: "年度", value: "year" },
    { label: "季度", value: "quarter" },
    { label: "本季度自拓银行指标为", value: "extensionBank", unit: "家" },
    {
      label: "请填写自拓银行完成情况",
      value: "completeExtensionBank",
      unit: "家",
      type: "input",
    },
  ]),
  formColumnsDialogDept: Object.freeze([
    { label: "所属公司", value: "companyShortName" },
    { label: "所属部门", value: "deptName" },
    { label: "年度", value: "year" },
    { label: "季度", value: "quarter" },
    { label: "本季度自拓银行指标为", value: "extensionBank", unit: "家" },
    {
      label: "请填写自拓银行完成情况",
      value: "completeExtensionBank",
      unit: "家",
      type: "input",
    },
  ]),
  formColumnsDialogCompany: Object.freeze([
    { label: "所属公司", value: "companyShortName" },
    { label: "年度", value: "year" },
    { label: "季度", value: "quarter" },
    { label: "本季度自拓银行指标为", value: "extensionBank", unit: "家" },
    {
      label: "请填写自拓银行完成情况",
      value: "completeExtensionBank",
      unit: "家",
      type: "input",
    },
  ]),
};
