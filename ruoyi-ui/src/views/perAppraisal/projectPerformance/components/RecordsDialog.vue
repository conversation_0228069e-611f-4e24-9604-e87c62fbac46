<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      title="查看修改记录"
      :visible.sync="innerValue"
      width="1000px"
      @close="handleClose"
      @open="handleOpen"
    >
      <MyForm
        ref="form"
        v-model="queryParams"
        :columns="formColumnsRecordsSearch"
        @onSearchList="handleQuery"
      />
      <MyForm
        class="w-2/3 m-auto mt-2"
        v-model="myRecordForm"
        :columns="formColumnsRecords"
        formType="form"
      />
      <MyTable :columns="columnsRecords" :source="configList">
        <template #operate="{ record }">
          <el-button type="text" @click="goView(record)">查看详情</el-button>
          <el-button type="text" @click="viewProcessId(record)">查看流程</el-button>
        </template>
      </MyTable>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
      <span slot="footer" class="dialog-footer">
        <div class="flex justify-center">
          <el-button @click="innerValue = false" class="ml-3">取消</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { selectEditRecord2 } from "@/api/oa/voucharRules";
import XEUtils from "xe-utils";
import vModelMixin from "@/mixin/v-model";
import config from "./config";
export default {
  mixins: [vModelMixin],
  props: {
    recordForm: {
      type: Object,
      required: true,
      default: () => ({}),
    },
  },
  data() {
    return {
      ...config,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        applyType: 5,
        oaApplyId: "",
        times: [],
        nickName: "",
      },
      total: 0,
      configList: [],
      myRecordForm: {},
    };
  },

  mounted() {},
  methods: {
    handleOpen() {
      this.getList();
      this.myRecordForm = XEUtils.clone(this.recordForm, true);
    },
    async getList() {
      const params = XEUtils.clone(this.queryParams, true);
      params.oaApplyId = this.recordForm.id;
      params.createTime = params.times && params.times[0];
      params.updateTime = params.times && params.times[1];
      delete params.times;
      const { rows, total } = await selectEditRecord2(params);
      this.configList = rows;
      this.total = total;
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handleClose() {
      this.$refs.form.resetFields();
      this.times = [];
    },
    goView(row) {
      const form = {
        year: this.recordForm.year,
        projectName: this.recordForm.projectName,
        yewuList: this.recordForm.yewuList,
        companyName: this.recordForm.companyName,
        projectId: this.recordForm.projectId,
        oaApplyRecordsNewData: row.oaApplyRecordsNewData,
      };
      this.$router.push({
        path: `/perAppraisalOther/projectPerformance/${row.applyId}`,
        query: {
          title: "查看项目业绩修改记录详情",
          form: JSON.stringify(form),
        },
      });
    },
    viewProcessId(value) {
      this.$router.push({
        path: "/oaWork/processFormView",
        query: {
          oid: value.processId,
          businessId: value.processId,
        },
      });
    },
  },
};
</script>
