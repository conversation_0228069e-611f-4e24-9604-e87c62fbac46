export default {
  formColumns: [
    {
      label: "所属年度",
      prop: "year",
      type: "datePicker",
      dateType: "years",
      valueFormat: "yyyy",
      placeholder: "请选择所属年度",
      clearable:false
    },

    {
      label: "公司名称",
      prop: "companyId",
      type: "select",
      filterable: true,
      multiple: true,
      options: [],
      dataProp: {
        value: "id",
        label: "companyName",
      },
      style: { width: "340px" },
      placeholder: "请选择/输入公司名称",
    },

    {
      label: "所属季度",
      prop: "quarter",
      type: "select",
      filterable: true,
      multiple: true,
      clearable:false,
      options: [
        { value: "1", label: "第一季度" },
        { value: "2", label: "第二季度" },
        { value: "3", label: "第三季度" },
        { value: "4", label: "第四季度" },
      ],
      placeholder: "请选择季度",
    },
    {
      label: "确认状态",
      prop: "state",
      type: "select",
      options: [
        { value: "1", label: "未审核" },
        { value: "2", label: "审核中" },
        { value: "3", label: "审核通过" },
        { value: "4", label: "审核不通过" },
      ],
      placeholder: "请选择确认状态",
    },
  ],
  quarterObj: Object.freeze({
    1: "一季度",
    2: "二季度",
    3: "三季度",
    4: "四季度",
  }),
  generateStatusObj: Object.freeze({
    1: "未生成",
    2: "已生成",
  }),
  confirmStatusObj: Object.freeze({
    1: "未确认",
    2: "确认中",
    3: "已确认",
  }),
  stateObj: Object.freeze({
    1: "未提交",
    2: "审核中",
    3: "审核通过",
    4: "审核不通过",
  }),
  columns: Object.freeze([
    {
      label: "公司名称",
      prop: "name",
      minWidth: "250px",
    },
    { label: "年度", prop: "year", minWidth: "100px" },
    { label: "季度", key: "quarter", minWidth: "100px" },
    { label: "考核结果", prop: "generateStatus", minWidth: "100px" },
    { label: "生成时间", prop: "createTime", minWidth: "150px" },
    { label: "生成用户", prop: "generateName", minWidth: "100px" },
    { label: "确认状态", prop: "confirmStatus", minWidth: "100px" },
    { label: "审核状态", prop: "states", minWidth: "100px" },
    { label: "审核通过时间", prop: "stateUpdateTime", minWidth: "150px" },
    { label: "操作", key: "operate", minWidth: "180px", fixed: "right" },
  ]),
  columnsSelect: Object.freeze([
    {
      label: "公司名称",
      prop: "name",
      minWidth: "250px",
    },
    { label: "年度", prop: "year", minWidth: "100px" },
    { label: "季度", prop: "quarter", minWidth: "100px" },
    { label: "考核结果", prop: "generateStatus", minWidth: "100px" },
    { label: "生成时间", prop: "createTime", minWidth: "150px" },
    { label: "生成用户", prop: "generateName", minWidth: "100px" },
    { label: "近期修改规则时间", prop: "updateTime", minWidth: "150px" },
    { label: "修改规则用户", prop: "updateGenerateName", minWidth: "150px" },
  ]),
};
