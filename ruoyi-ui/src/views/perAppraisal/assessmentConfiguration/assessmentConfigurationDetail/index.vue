<template>
  <div
    class="p-5 page-component__scroll overflow-y-auto pb-12"
    style="height: calc(100vh - 100px)"
  >
    <div class="px-5 mb-3">
      <div style="font-size: 12px; color: #aaaaaa">
        说明：选择公司名称后，自动展示公司下的所有部门，可通过预制的模板快速增加配置考核占比。可单独增加公司下用户的考核占比配置。
      </div>
    </div>
    <MyForm
      v-model="proForm"
      :columns="formColumns"
      :rules="rules"
      ref="form"
      formType="form"
    />
    <el-divider></el-divider>
    <el-button
      v-show="title.indexOf('查看') == -1"
      @click="templateOpen = true"
      type="primary"
      size="mini"
      plain
      >选择配置模板</el-button
    >

    <MyTable :columns="columnsDept" :source="deptTable">
      <template #distributionProportion="{ record }">
        <el-input
          @input="changeInput(record, 'distributionProportion')"
          @blur="blurInput(record, 'distributionProportion')"
          v-model="record.distributionProportion"
          :disabled="title.indexOf('查看') != -1"
          class="w-1/2"
        ></el-input
      ></template>
      <template #extensionProportion="{ record }">
        <el-input
          @input="changeInput(record, 'extensionProportion')"
          @blur="blurInput(record, 'extensionProportion')"
          v-model="record.extensionProportion"
          :disabled="title.indexOf('查看') != -1"
          class="w-1/2"
        ></el-input>
      </template>
    </MyTable>
    <pagination
      v-show="totalDept > 10"
      :total="totalDept"
      :page.sync="queryParamsDept.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParamsDept.pageSize"
      @pagination="getListDept"
    />
    <el-divider class="mt-10"></el-divider>
    <MyTable :columns="columnsUser" :source="userTable">
      <template #distributionProportion="{ record }">
        <el-input
          @input="changeInput(record, 'distributionProportion')"
          @blur="blurInput(record, 'distributionProportion')"
          v-model="record.distributionProportion"
          :disabled="title.indexOf('查看') != -1"
          class="w-1/2"
        ></el-input
      ></template>
      <template #extensionProportion="{ record }">
        <el-input
          @input="changeInput(record, 'extensionProportion')"
          @blur="blurInput(record, 'extensionProportion')"
          v-model="record.extensionProportion"
          :disabled="title.indexOf('查看') != -1"
          class="w-1/2"
        ></el-input>
      </template>
    </MyTable>
    <pagination
      v-show="totalUser > 10"
      :total="totalUser"
      :page.sync="queryParamsUser.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParamsUser.pageSize"
      @pagination="getListUser"
    />

    <el-divider></el-divider>

    <el-form
      v-show="proForm.isPass"
      ref="form"
      class="my-2"
      :model="detailForm"
      :rules="rulesExplain"
      label-width="80px"
    >
      <el-form-item label="修改说明" prop="explain">
        <el-input
          v-model="detailForm.explain"
          placeholder="请输入修改说明"
          type="textarea"
          :autosize="{ minRows: 3 }"
        ></el-input>
      </el-form-item>
    </el-form>
    <InBody>
      <div
        class="text-center fixed bottom-0 bg-white z-10 pb-2"
        style="width: calc(100% - 260px); left: 260px"
      >
        <el-button
          @click="onSave"
          v-show="title.indexOf('查看') == -1 && !proForm.isPass"
          type="primary"
          >保存</el-button
        >

        <el-button @click="cancel">取消</el-button>
        <el-button @click="onSubmit" type="primary" v-show="proForm.isPass"
          >提交</el-button
        >
      </div>
    </InBody>
    <DetailDialog v-model="templateOpen" @on-submit="onSubmitTemplate" />

    <SelectCompany
      v-if="selectCompanyType"
      @close="closeCompany"
      @submit="submitCompany"
    />
  </div>
</template>

<script>
import DetailDialog from "./components/DetailDialog.vue";
import config from "./components/config";
import { queryDeptInfoList } from "@/api/system/dept";
import { getlistArchives } from "@/api/personnel/archives";
import {
  checkConfig,
  checkPutConfig,
  getCheckConfig,
  getCheckConfigEditFlow,
} from "@/api/perAppraisal/assessmentConfiguration";

import { floatAdd, decimal } from "@/utils";
import XEUtils from "xe-utils";

export default {
  name: "AssessmentConfigurationDetail",
  components: { DetailDialog },
  data() {
    return {
      ...config,
      id: this.$route.params.id,
      title: this.$route.query.title,
      proForm: JSON.parse(this.$route.query.form),
      templateOpen: false,
      deptTable: [],
      deptSlaveList: [],
      deptSlaveListInit: [],
      totalDept: 0,
      queryParamsDept: {
        pageNum: 1,
        pageSize: 10,
      },
      userTable: [],
      userSlaveList: [],
      userSlaveListInit: [],
      totalUser: 0,
      queryParamsUser: {
        pageNum: 1,
        pageSize: 10,
      },
      detailForm: {
        explain: "",
      },
      selectCompanyType: false,
    };
  },
  computed: {},

  created() {
    this.init();
  },
  methods: {
    init() {
      this.getForm();
    },
    async getForm() {
      if (this.title.indexOf("新增") != -1) {
        await this.getDeptUser();
      } else if (this.title.indexOf("复制") != -1) {
        const { data } = await getCheckConfig(this.proForm.id);
        this.deptSlaveList = data.deptSlaveList;
        this.userSlaveList = data.userSlaveList;
      } else if (this.title.indexOf("查看考核配置修改记录详情") != -1) {
        const oaApplyRecordsNewData = JSON.parse(
          this.proForm.oaApplyRecordsNewData
        );
        this.deptSlaveList = oaApplyRecordsNewData.deptSlaveList;
        this.userSlaveList = oaApplyRecordsNewData.userSlaveList;
      } else {
        const { data } = await getCheckConfig(this.id);
        this.deptSlaveList = data.deptSlaveList;
        this.userSlaveList = data.userSlaveList;
      }
      this.deptSlaveListInit = XEUtils.clone(this.deptSlaveList, true);
      this.userSlaveListInit = XEUtils.clone(this.userSlaveList, true);
      this.getList();
    },
    getList() {
      this.getListDept();
      this.getListUser();
    },
    getListDept() {
      const start =
        (this.queryParamsDept.pageNum - 1) * this.queryParamsDept.pageSize;
      const end = start + this.queryParamsDept.pageSize;
      this.deptTable = this.deptSlaveList.slice(start, end);
      this.totalDept = this.deptSlaveList.length;
    },
    getListUser() {
      const start =
        (this.queryParamsUser.pageNum - 1) * this.queryParamsUser.pageSize;
      const end = start + this.queryParamsUser.pageSize;
      this.userTable = this.userSlaveList.slice(start, end);
      this.totalUser = this.userSlaveList.length;
    },
    async getDeptUser() {
      await this.getDept();
      await this.getUser();
    },
    async getDept() {
      const { rows } = await queryDeptInfoList({
        unitId: this.proForm.companyId,
      });
      this.deptSlaveList = rows.map((item) => {
        return {
          correlationId: item.deptId,
          deptName: item.deptName,
          achievementWagesProportion: 0,
          distributionProportion: 0,
          extensionProportion: 0,
        };
      });
    },
    async getUser() {
      const { rows } = await getlistArchives({
        archivesCompany: this.proForm.companyId,
      });
      this.userSlaveList = rows.map((item) => {
        return {
          correlationId: item.userId,
          deptName: item.deptName,
          nickName: item.name,
          achievementWagesProportion: 0,
          distributionProportion: 0,
          extensionProportion: 0,
        };
      });
    },

    changeInput(value, itemValue) {
      value[itemValue] = decimal(value[itemValue], 2);
      this.getItemTotal(value, itemValue);
    },
    blurInput(value, itemValue) {
      value[itemValue] =
        value[itemValue] && Number(value[itemValue]) > 0
          ? Number(value[itemValue]).toFixed(2)
          : 0;
    },

    getItemTotal(value, itemValue) {
      value.achievementWagesProportion = floatAdd(
        value.distributionProportion,
        value.extensionProportion
      ).toFixed(2);
    },
    onSubmitTemplate(value) {
      this.deptSlaveList.forEach((item) => {
        value[0]?.slaveList.forEach((item1) => {
          const names =
            item.deptName.split(">")[item.deptName.split(">").length - 1];
          if (names == item1.deptName) {
            item.achievementWagesProportion = item1.achievementWagesProportion;
            item.distributionProportion = item1.distributionProportion;
            item.extensionProportion = item1.extensionProportion;
          }
        });
      });
      this.$message.success("操作成功");
      this.queryParamsDept.pageNum = 1;
      this.getListDept();
    },
    async onSave() {
      if (this.id.indexOf("Project") != -1) {
        await checkConfig({
          ...this.proForm,
          userSlaveList: this.userSlaveList,
          deptSlaveList: this.deptSlaveList,
        });
      } else {
        await checkPutConfig({
          ...this.proForm,
          id: Number(this.id),
          userSlaveList: this.userSlaveList,
          deptSlaveList: this.deptSlaveList,
        });
      }
      this.$message.success("保存成功");
      this.cancel();
    },
    async onSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.selectCompanyType = true;
        }
      });
    },
    submitCompany(e) {
      getCheckConfigEditFlow({ companyId: e }).then((res) => {
        if (res.code == 200) {
          this.selectCompanyType = false;
          const data = {
            oldData: {
              userSlaveList: this.userSlaveListInit,
              deptSlaveList: this.deptSlaveListInit,
            },
            newData: {
              userSlaveList: this.userSlaveList,
              deptSlaveList: this.deptSlaveList,
            },
            editInfo: this.detailForm.explain,
            companyShortName: this.proForm.companyShortName,
            year: this.proForm.year,
            id: this.id,
          };

          sessionStorage.setItem(
            "oa-assessConfigReviewTableFinish",
            JSON.stringify(data)
          );
          this.$router.push({
            path: "/oaWork/updateProcessForm",
            query: {
              templateId: res.templateId,
              classificationId: res.classificationId,
              companyId: res.companyId,
              assessConfigReviewTableFinish: true,
            },
          });
        }
      });
    },
    closeCompany() {
      this.selectCompanyType = false;
    },

    cancel() {
      const obj = { path: "/perAppraisal/assessmentConfiguration" };
      this.$tab.closeOpenPage(obj);
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .is-justify-space-between {
  justify-content: flex-start;
}
</style>

