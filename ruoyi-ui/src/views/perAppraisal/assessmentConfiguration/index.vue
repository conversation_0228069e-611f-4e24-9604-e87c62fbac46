<template>
  <div class="p-5">
    <MyForm
      v-model="queryParams"
      :columns="formColumns"
      @onSearchList="handleQuery"
    />
    <el-divider></el-divider>
    <div style="font-size: 10px; color: #aaaaaa" class="mb-2">
      <div>说明：考核配置以公司和年为维度配置</div>
      <div>修改考核配置时，已确认的考核结果不会受到影响</div>
    </div>
    <div class="flex mb-2 justify-between">
      <div class="flex">
        <el-button
          v-hasPermi="['perAppraisal:assessmentConfiguration:add']"
          type="primary"
          size="mini"
          plain
          icon="el-icon-plus"
          @click="openAssessDialog('add')"
          >增加考核配置</el-button
        >
        <el-button v-hasPermi="['perAppraisal:assessmentConfiguration:addBatch']" @click="openAssessBatchs" type="primary" size="mini" plain
          >批量增加考核配置</el-button
        >
        <el-button
          v-hasPermi="['perAppraisal:assessmentConfiguration:submit']"
          @click="
            $router.push({
              path: '/perAppraisalOther/assessmentConfigurationReview',
            })
          "
          type="primary"
          size="mini"
          plain
          icon="el-icon-check"
          >提交考核配置</el-button
        >
        <el-button
          v-hasPermi="['perAppraisal:assessmentConfiguration:addTem']"
          @click="
            $router.push({
              path: '/perAppraisalOther/templateList',
            })
          "
          type="primary"
          size="mini"
          >增加配置模板</el-button
        >
      </div>
    </div>
    <MyTable
      :columns="columns"
      :showIndex="true"
      :source="configList"
      :queryParams="queryParams"
    >
      <template #companyName="{ record }">
        <div>{{ record.companyName}}({{record.companyShortName}})</div>
      </template>
      <template #state="{ record }">
        <div>{{ statusObj[record.state] }}</div>
      </template>
      <template #operate="{ record }">
        <el-button
          @click="goUpdate(record, 'update')"
          type="text"
          v-show="['1', '4'].includes(record.state)"
          >修改</el-button
        >
        <el-button type="text" @click="goView(record, 'view')"
          >查看详情</el-button
        >
        <el-button type="text" @click="openAssessDialog('copy', record)"
          >复制成新配置</el-button
        >

        <el-button
          @click="handleDelete(record)"
          type="text"
          v-show="['1', '4'].includes(record.state)"
          style="color: #f56c6c"
          >删除</el-button
        >
        <el-dropdown
          @command="handleCommand($event, record)"
          class="ml-2"
          v-show="['2', '3'].includes(record.state)"
        >
          <span class="el-dropdown-link mr-2" style="font-size: 14px">
            >>更多
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="records">查看修改记录</el-dropdown-item>
            <el-dropdown-item command="process">查看最新流程</el-dropdown-item>
            <el-dropdown-item
              command="submitReview"
              v-show="['3'].includes(record.state)"
              >修改并提交审核</el-dropdown-item
            >
          </el-dropdown-menu>
        </el-dropdown>
      </template>
    </MyTable>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <DetailDialog
      v-model="openAssess"
      :openAssessType="openAssessType"
      :recordCurrent="recordCurrent"
    />
    <DetailDialogBatch v-model="openAssessBatch" @on-submit-success="getList" />
    <UpdateDetailDialog v-model="openUpdate" @on-submit-success="submitUpdate">
      <div v-show="type">
        <div>
          {{
            `【${this.recordCurrent.companyShortName}】【${this.recordCurrent.year}】年度已生成考核结果但未确认`
          }}
        </div>
        <div style="color: rgb(245, 108, 108)">
          修改后，考核结果和校准数据将被清空，再次点开考核结果界面时会重新计算，请您谨慎操作，确定请点击【提交】
        </div>
      </div>
      <div v-show="!type">
        <div>
          {{
            `您确认修改【${this.recordCurrent.companyShortName}】【${this.recordCurrent.year}】年度的考核配置吗？`
          }}
        </div>
        <div style="color: rgb(245, 108, 108)">
          本条配置已审核通过，修改内容需要重新走完审批流程才会生效。
        </div>
        <div style="color: rgb(245, 108, 108)">
          修改内容不会作用在已确认的考核结果上。
        </div>
      </div>
    </UpdateDetailDialog>
    <RecordsDialog v-model="openRecord" :recordForm="recordForm" />
  </div>
</template>

<script>
import XEUtils from "xe-utils";
import DetailDialog from "./components/DetailDialog.vue";
import DetailDialogBatch from "./components/DetailDialogBatch.vue";
import UpdateDetailDialog from "./components/UpdateDetailDialog.vue";
import RecordsDialog from "./components/RecordsDialog.vue";
import { haveAuthorityCompanyList } from "@/api/businessInformation/companyInformation";
import {
  checkConfigList,
  delCheckConfig,
  getExistence,
} from "@/api/perAppraisal/assessmentConfiguration";
import { resetCheckResult } from "@/api/perAppraisal/results";
import config from "./components/config";
export default {
  name: "AssessmentConfiguration",
  components: {
    DetailDialog,
    DetailDialogBatch,
    RecordsDialog,
    UpdateDetailDialog,
  },

  data() {
    return {
      ...config,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        year: [new Date().getFullYear() + ""],
      },
      total: 0,
      configList: [],
      openAssess: false,
      openAssessType: "add",
      openAssessBatch: false,
      openRecord: false,
      recordForm: {},
      openUpdate: false,
      recordCurrent: {},
      isPassUpdate: false,
      type: true, //本条配置已审批通过了，需要再次修改 ，修改时若该公司存在本年度某个季度已生成考核结果但未确认
    };
  },

  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.getCompanyList();
      this.getList();
      this.getOpen();
    },
    getOpen() {
      const openAssessBatch = localStorage.getItem("openAssessBatch");
      if (openAssessBatch === "true") {
        this.openAssessBatch = true;
      }
    },
    getParams() {
      const params = XEUtils.clone(this.queryParams, true);
      params.years = params.year?.map((item) => (item = Number(item))).join();
      params.companyIds = params.companyId
        ?.map((item) => (item = Number(item)))
        .join();
      delete params.year;
      delete params.companyId;
      return params;
    },
    async getCompanyList() {
      const { rows } = await haveAuthorityCompanyList({ isInside: 1 });
      this.formColumns[0].options = rows;
      this.formColumnsDialog[0].options = rows;
    },

    async getList() {
      const { rows, total } = await checkConfigList(this.getParams());
      this.configList = rows;
      this.total = total;
    },

    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    openAssessDialog(type, record) {
      this.openAssessType = type;
      this.recordCurrent = { ...record };
      this.openAssess = true;
    },
    openAssessBatchs() {
      this.openAssessBatch = true;
      localStorage.setItem("openAssessBatch", "true"); // 保存状态
    },
    handleCommand(command, record) {
      const obj = {
        records: this.records,
        process: this.process,
        submitReview: this.submitReview,
      };
      obj[command](record);
    },
    records(record) {
      this.recordForm = record;
      this.openRecord = true;
    },
    process(record) {
      this.$router.push({
        path: "/oaWork/processFormView",
        query: {
          oid: record.processId,
          businessId: record.processId,
        },
      });
    },
    submitReview(record) {
      this.goUpdate(record, "update", true);
    },

    handleDelete(row) {
      let text = `您确定删除${row.companyShortName}【${row.year}】年度的考核配置吗？`;
      this.$modal
        .confirm(text)
        .then(function () {
          return delCheckConfig(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    async submitUpdate() {
      if (this.type) {
        await resetCheckResult(this.recordCurrent);
      }
      this.goView(this.recordCurrent, "update", this.isPassUpdate);
    },
    async goUpdate(row, type, isPass = false) {
      this.recordCurrent = { ...row };
      this.isPassUpdate = isPass;
      if (isPass) {
        const { data } = await getExistence(row);
        this.type = data;
        this.openUpdate = true;
      } else {
        this.goView(row, type, isPass);
      }
    },
    goView(row, type, isPass = false) {
      const title = {
        update: "修改考核配置",
        view: "查看考核配置详情",
      }[type];
      const form = {
        year: row.year,
        companyName: row.companyName,
        companyShortName: row.companyShortName,
        projectId: row.projectId,
        isPass,
      };
      this.$router.push({
        path: `/perAppraisalOther/assessmentConfiguration/${row.id}`,
        query: {
          title,
          form: JSON.stringify(form),
        },
      });
    },
  },
};
</script>
