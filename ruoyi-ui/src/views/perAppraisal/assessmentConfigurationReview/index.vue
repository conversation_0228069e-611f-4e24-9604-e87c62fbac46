<template>
  <div class="p-5">
    <MyForm
      v-model="queryParams"
      :columns="formColumns"
      @onSearchList="handleQuery"
    />
    <el-divider></el-divider>
    <div style="font-size: 10px; color: #aaaaaa" class="mb-2">
      <div>说明：考核配置以公司和年为维度配置</div>
      <div>修改考核配置时，已确认的考核结果不会受到影响</div>
    </div>
    <div class="flex mb-2 justify-between">
      <div class="flex">
        <el-button @click="selectCompanyType=true" :disabled="!Boolean(multipleSelection.length)" type="primary" size="mini" plain icon="el-icon-check"
          >提交考核配置</el-button
        >
        <el-button @click="openSelect = true" type="primary" size="mini"
          >已选择({{ multipleSelection.length }})条</el-button
        >
      </div>
    </div>
    <MyTable
      ref="table"
      :columns="columns"
      :showCheckbox="true"
      :source="configList"
      @selection-change="handleSelectionChange"
      :disabledState="true"
    >
     <template #companyName="{ record }">
        <div>{{ record.companyName}}({{record.companyShortName}})</div>
      </template>
      <template #state="{ record }">
        <div>{{ statusObj[record.state] }}</div>
      </template>
      <template #operate="{ record }">
        <el-button type="text" @click="goView(record, 'view')"
          >查看详情</el-button
        >
      </template>
    </MyTable>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <TableSelect
      :columns="columnsTableSelect"
      :tableData="multipleSelection"
      v-model="openSelect"
      @on-submit-success-row="submitDelet"
    />
    <SelectCompany
      v-if="selectCompanyType"
      @close="closeCompany"
      @submit="submitCompany"
    />
  </div>
</template>

<script>
import XEUtils from "xe-utils";
import { haveAuthorityCompanyList } from "@/api/businessInformation/companyInformation";
import { checkConfigList ,getCheckConfigFlow} from "@/api/perAppraisal/assessmentConfiguration";
import config from "./components/config";
export default {
  name: "AssessmentConfigurationReview",

  data() {
    return {
      ...config,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        year: new Date().getFullYear() + "",
      },
      total: 0,
      configList: [],
      multipleSelection: [],
      openSelect: false,
      selectCompanyType: false,


    };
  },
  computed: {
    columnsTableSelect() {
      return this.columns.filter((item) => item.label != "操作");
    },
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.getCompanyList();
      this.getList();
    },
    getParams() {
      const params = XEUtils.clone(this.queryParams, true);
      params.companyIds = params.companyId
        ?.map((item) => (item = Number(item)))
        .join();
      delete params.companyId;
      return params;
    },
    async getCompanyList() {
      const { rows } = await haveAuthorityCompanyList({isInside:1});
      this.formColumns[0].options = rows;
    },

    async getList() {
      const { rows, total } = await checkConfigList(this.getParams());
      this.configList = rows;
      this.total = total;
    },

    handleQuery(value) {
      this.queryParams.pageNum = 1;
      this.getList();
      if (value == "year") {
        this.$refs.table.clearSelection();
        this.multipleSelection = [];
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    submitDelet(e) {
      e.forEach((row) => {
        this.$refs.table.toggleRowSelection(row, false);
      });
    },
    goView(row) {
      const title = "查看考核配置详情";
      const form = {
        year: row.year,
        companyName: row.companyName,
        companyShortName: row.companyShortName,
        projectId: row.projectId,
      };
      this.$router.push({
        path: `/perAppraisalOther/assessmentConfiguration/${row.id}`,
        query: {
          title,
          form: JSON.stringify(form),
        },
      });
    },
    submitCompany(e) {
      getCheckConfigFlow({ companyId: e }).then((res) => {
        if (res.code == 200) {
          this.selectCompanyType = false;
          const data = this.multipleSelection;
          sessionStorage.setItem(
            "oa-assessConfigReviewTable",
            JSON.stringify(data)
          );
          this.$router.push({
            path: "/oaWork/updateProcessForm",
            query: {
              templateId: res.templateId,
              classificationId: res.classificationId,
              companyId: res.companyId,
              assessConfigReviewTable: true,
            },
          });
        }
      });
    },
    closeCompany() {
      this.selectCompanyType = false;
      this.getList();
    },
  },
};
</script>
