<template>
  <div>
    <el-dialog
      title="审核"
      :visible.sync="dialogVisible"
      width="1000px"
      :before-close="handleClose"
    >
      <div style="display: flex; justify-content: space-around">
        <div>
          <span style="font-weight: bold">提交人</span>：{{
            detailData.editUserNickName
          }}
        </div>
        <div>
          <span style="font-weight: bold">提交身份人</span>：{{
            detailData.identity
          }}
        </div>
        <div>
          <span style="font-weight: bold">提交时间</span>：{{
            detailData.editTime
          }}
        </div>
      </div>
      <el-divider></el-divider>
      <div v-if="type == 1">
        <p style="font-weight: bold">修改前：</p>
        <span>
          公司：
          <span v-for="dict in projects" :key="dict.unitId">
            <span v-if="data1.companyNo == dict.unitId">
              {{ dict.unitShortName }}
            </span>
          </span>
        </span>
        <br />
        <span>流程模板：{{ data1.modelName }}</span>
        <br />
        <span>备注：{{ data1.remark }}</span>
        <br />
        <el-table
          v-for="(item, index) in data1.proList"
          :data="item.tableList"
          :key="index"
        >
          <el-table-column label="项目名称" align="center" prop="projectName">
            <template>{{ item.projectName }}</template>
          </el-table-column>

          <el-table-column label="收款人" align="center" prop="collName" />
          <el-table-column
            label="开户行"
            align="center"
            prop="collBankOfDeposit"
          />
          <el-table-column
            label="账号"
            align="center"
            prop="collAccountNumber"
          />
          <el-table-column
            label="收款人简称"
            align="center"
            prop="collAbbreviation"
          />
          <el-table-column label="付款人" align="center" prop="payName" />
          <el-table-column
            label="开户行"
            align="center"
            prop="payBankOfDeposit"
          />
          <el-table-column
            label="账号"
            align="center"
            prop="payAccountNumber"
          />
          <el-table-column
            label="付款人简称"
            align="center"
            prop="payAbbreviation"
          />
        </el-table>
        <p style="font-weight: bold">修改后：</p>
        <span>
          公司：
          <span v-for="dict in projects" :key="dict.unitId">
            <span v-if="data2.companyNo == dict.unitId">
              {{ dict.unitShortName }}
            </span>
          </span>
        </span>
        <br />
        <span>流程模板：{{ data2.modelName }}</span>
        <br />
        <span>备注：{{ data2.remark }}</span>
        <br />
        <el-table
          v-for="(item, index) in data2.proList"
          :data="item.tableList"
          :key="index"
        >
          <el-table-column label="项目名称" align="center" prop="projectName">
            <template>{{ item.projectName }}</template>
          </el-table-column>
          <el-table-column label="收款人" align="center" prop="collName" />
          <el-table-column
            label="开户行"
            align="center"
            prop="collBankOfDeposit"
          />
          <el-table-column
            label="账号"
            align="center"
            prop="collAccountNumber"
          />
          <el-table-column
            label="收款人简称"
            align="center"
            prop="collAbbreviation"
          />
          <el-table-column label="付款人" align="center" prop="payName" />
          <el-table-column
            label="开户行"
            align="center"
            prop="payBankOfDeposit"
          />
          <el-table-column
            label="账号"
            align="center"
            prop="payAccountNumber"
          />
          <el-table-column
            label="付款人简称"
            align="center"
            prop="payAbbreviation"
          />
        </el-table>
        <p style="font-weight: bold">修改说明：</p>
        {{ detailData.editInfo }}
      </div>
      <div v-if="type == 2">
        {{ detailData.editUserNickName }}申请删除本条记账凭证规则！
        <p style="font-weight: bold">删除原因说明：</p>
        {{ detailData.editInfo }}
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submit(0)">通过</el-button>
        <el-button
          type="primary"
          @click="dialogVisible2 = true"
          style="background-color: #ff9900; border: none"
          >驳回</el-button
        >
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="审核驳回"
      :visible.sync="dialogVisible2"
      width="800px"
      :before-close="handleClose2"
    >
      <p>请输入驳回原因</p>
      <el-input
        type="textarea"
        placeholder="请输入内容"
        v-model="textarea"
        maxlength="200"
        show-word-limit
      >
      </el-input>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submit(1)" :disabled="!textarea"
          >提 交</el-button
        >
        <el-button @click="dialogVisible2 = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getUnitListEnable } from "@/api/system/unit";

export default {
  props: {
    detailData: Object,
  },
  data() {
    return {
      dialogVisible: true,
      dialogVisible2: false,
      type: null,
      textarea: "",
      data1: null,
      data2: null,
      projects: [],
    };
  },
  mounted() {
    getUnitListEnable().then((response) => {
      this.projects = response.data;
    });
    this.type = this.detailData.applyType;
    this.data1 = JSON.parse(this.detailData.oaApplyRecordsOldData);
    this.data2 = JSON.parse(this.detailData.oaApplyRecordsNewData);
    console.log(this.data1);
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
    handleClose2() {
      this.dialogVisible2 = false;
    },
    submit(v) {
      this.$emit("submit", v, this.textarea);
    },
  },
};
</script>

<style lang="less" scoped>
</style>