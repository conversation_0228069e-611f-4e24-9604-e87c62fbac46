<template>
  <div class="p-5">
    <MyForm
      v-model="queryParams"
      :columns="formColumns"
      @onSearchList="handleQuery"
    />
    <el-divider></el-divider>

    <div class="flex mb-2 justify-between">
      <div class="flex">
        <el-button
          v-hasPermi="['oa:dataSet:add']"
          @click="add"
          type="primary"
          size="mini"
          plain
          icon="el-icon-plus"
          >新建</el-button
        >
      </div>
    </div>
    <MyTable
      ref="table"
      :columns="columns"
      :source="configList"
      :tree-props="{ children: 'fPiattaformas'}"
    >
      <template #status="{ record }">
        <el-switch
          @change="change(record)"
          v-model="record.status"
          active-color="#13ce66"
          inactive-color="#ff4949"
          active-value="0"
          inactive-value="1"
        >
        </el-switch>
      </template>
      <template #operate="{ record }">
        <el-button
          v-hasPermi="['oa:dataSet:update']"
          type="text"
          @click="update(record)"
          >修改</el-button
        >
        <el-button
          v-hasPermi="['oa:dataSet:add']"
          type="text"
          @click="add(record)"
          >新增</el-button
        >
        <el-button
          v-hasPermi="['oa:dataSet:delete']"
          type="text"
          @click="handleDelete(record)"
          style="color: #f56c6c"
          >删除</el-button
        >
      </template>
    </MyTable>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <DetailDialog v-model="open" :form="dialogForm" :title="title" :parentList="parentList" @on-submit-success='getList'/>
  </div>
</template>

<script>
import XEUtils from "xe-utils";
import {
  systemDataManage,
  deleteData,
  updateSystemDataManage,
} from "@/api/notice/dataSet";
import DetailDialog from "./components/DetailDialog.vue";
import {renameField} from "@/utils";
import config from "./components/config";
export default {
  name: "DataSet",
  components: { DetailDialog },
  data() {
    return {
      ...config,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dataCode: undefined,
        dataName: undefined,
        status: undefined,
      },
      total: 0,
      configList: [],
      parentList:[],
      open: false,
      dialogForm: {},
      title: "",
    };
  },

  created() {
    this.init();
  },
  methods: {
    init() {
      this.getList();
    },

    async getList() {
      const { rows, total } = await systemDataManage(this.queryParams);
      this.configList = rows;
      this.parentList=XEUtils.clone(rows,true)
      this.parentList.forEach(rootNode => renameField(rootNode, 'fPiattaformas', 'children'))
      this.parentList.forEach(rootNode => renameField(rootNode, 'dataName', 'label'))
      this.total = total;
    },

    handleQuery(value) {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    handleDelete(row) {
      const ids = row.id;
      const idNames = row.dataName;
      this.$modal
        .confirm(
          "您确定要删除数据集【" +
            idNames +
            "】吗?该数据集的下级数据将会一起删除"
        )
        .then(function () {
          return deleteData(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    async change(record) {
      await updateSystemDataManage({ ...record });
      this.$modal.msgSuccess("修改成功");
    },
    add(row) {
      this.title = "新增数据集";
      this.dialogForm = { parentId:row.id,status:0,firstDataCode:row.firstDataCode };
      this.open = true;
    },
    update(row) {
      this.title = "修改数据集";
      this.dialogForm = { ...row };
      this.open = true;
    },
  },
};
</script>
