<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :title="title"
      :visible.sync="innerValue"
      width="800px"
      @close="handleClose"
      @open="handleOpen"
    >
      <div>
        <MyForm
          ref="form"
          v-model="myForm"
          :columns="formColumnsDialog"
          formType="form"
          :rules="rules"
        >
        </MyForm>
      </div>
      <span slot="footer" class="dialog-footer">
        <div class="flex justify-center">
          <el-button type="primary" @click="onSubmit" class="mr-3"
            >确定</el-button
          >
          <el-button @click="innerValue = false" class="ml-3">取消</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import XEUtils from "xe-utils";
import {
  addSystemDataManage,
  updateSystemDataManage,
} from "@/api/notice/dataSet";
import vModelMixin from "@/mixin/v-model";
import config from "./config";

export default {
  mixins: [vModelMixin],
  props: {
    form: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    parentList: {
      type: Array,
      required: true,
      default: () => {
        return [];
      },
    },
    title: {
      type: String,
      required: true,
      default: "",
    },
  },
  data() {
    return {
      ...config,
      myForm: {
        parentId: undefined,
        sortNum: undefined,
        dataCode: undefined,
        dataName: undefined,
        status: '0',
      },
    };
  },
  watch: {},
  mounted() {},
  methods: {
    handleOpen() {
       this.$nextTick(() => {
        this.$refs["form"].clearValidate();
      });
      this.formColumnsDialog[0].options = this.parentList;
      this.myForm = XEUtils.clone(this.form, true);
      this.myForm.status=this.myForm.status||'0'
    },
    findFirstDataCode(parentId) {
      const findNode = (nodes) => {
        for (let node of nodes) {
          if (node.id === parentId) {
            return node.firstDataCode;
          }
          if (node.children && node.children.length > 0) {
            const result = findNode(node.children);
            if (result) return result;
          }
        }
        return null;
      };
      return findNode(this.parentList);
    },

    async onSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.myForm.firstDataCode = this.findFirstDataCode(this.myForm.parentId);
          if (this.title == "新增数据集") {
            await addSystemDataManage(this.myForm);
            this.$modal.msgSuccess("新增成功");
          }
          if (this.title == "修改数据集") {
            await updateSystemDataManage(this.myForm);
            this.$modal.msgSuccess("修改成功");
          }
          this.innerValue = false;
          this.$emit("on-submit-success");
        }
      });
    },

    handleClose() {
      // this.$refs.form.resetFields();
    },
  },
};
</script>
