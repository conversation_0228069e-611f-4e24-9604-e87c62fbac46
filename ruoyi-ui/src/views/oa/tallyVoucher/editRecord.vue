<template>
  <div>
    <el-dialog
      title="编辑记录"
      :visible.sync="dialogVisible"
      width="800px"
      :before-close="handleClose"
    >
      <el-table v-if="!changeEditType" :data="editList" style="width: 100%">
        <el-table-column prop="editTime" label="编辑日期" />
        <el-table-column prop="editUserNickName" label="编辑人员" />
        <el-table-column prop="name" label="操作">
          <template slot-scope="scope">
            <el-button type="text" @click="seeRecord(scope.row)"
              >查看编辑内容</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-table v-else :data="editList" style="width: 100%">
        <el-table-column prop="editTime" label="编辑日期" width="220" />
        <el-table-column prop="editUserNickName" label="编辑人员" />
        <el-table-column prop="editIdentity" label="编辑人身份" width="130">
          <template slot-scope="scope">
            {{ identity(scope.row.editIdentity) }}
          </template>
        </el-table-column>

        <el-table-column
          prop="editInfo"
          label="修改说明"
          show-overflow-tooltip=""
          width="200"
        />
        <el-table-column prop="checkUserNickName" label="审核人" />
        <el-table-column
          prop="checkUserNickName"
          label="审核人身份"
          width="130"
        >
          <template slot-scope="scope">
            {{ identity(scope.row.checkIdentity) }}
          </template>
        </el-table-column>
        <el-table-column prop="checkTime" label="审核时间" width="220" />
        <el-table-column prop="editUserNickName" label="审核状态">
          <template slot-scope="scope">
            <span v-if="scope.row.rejectFlag !== null">{{
              scope.row.rejectFlag == 0 ? "已通过" : "已驳回"
            }}</span>
            <span v-if="scope.row.rejectFlag === null">{{
              scope.row.checkStatus == 0 ? "待业务审核" : "待财务审核"
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="checkRejectInfo"
          label="驳回原因"
          width="200"
          show-overflow-tooltip=""
        />
        <el-table-column prop="name" fixed="right" label="操作" width="130">
          <template slot-scope="scope">
            <el-button type="text" @click="seeRecord(scope.row)"
              >查看编辑内容</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </span>
    </el-dialog>
    <preview v-if="previewType" @close="previewType = false" :detail="detail" />
  </div>
</template>

<script>
import preview from "./preview.vue";
export default {
  components: {
    preview,
  },
  props: {
    editList: Array,
    changeEditType: Boolean,
  },
  data() {
    return {
      previewType: false,
      dialogVisible: true,
      detail: null,
    };
  },
  methods: {},
  mounted() {},
  methods: {
    identity(e) {
      if (e == 0) {
        return "财务负责人";
      } else if (e == 1) {
        return "业务负责人";
      } else if (e == 2) {
        return "财务管理员";
      } else if (e == 3) {
        return "业务管理员";
      } else if (e == 9) {
        return "超级管理员";
      }
    },
    seeRecord(v) {
      this.detail = v;
      this.previewType = true;
    },
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="less" scoped>
</style>