<template>
  <div class="app-container" id="dictData">
    <div style="width: 100%;height:30px;">
      <span style="color:#9D9D9D;margin-left: 20px;">说明：配置OA流程中的各类选项中的信息</span>
    </div>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
      <el-table-column label="字典名称" align="center" prop="dictTypeName" />
      <el-table-column label="字典类型" align="center" prop="dictType">
        <template slot-scope="scope">
          <span v-show="scope.row.dictType==0">人员</span>
          <span v-show="scope.row.dictType==1">数值</span>
        </template>
      </el-table-column>
      <el-table-column label="启用状态" align="center" prop="isEnable">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.isEnable"
            active-color="#13ce66"
            inactive-color="#ff4949"
            active-value="Y"
            inactive-value="N"
            @change="updateinEnable(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="最后修改时间" align="center" prop="endUpdateTime" width="180">
        <!-- <span>{{ parseTime(scope.row.endUpdateTime, '{y}-{m}-{d}') }}</span> -->
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="updateDynamicList(scope.row)"
          >修改记录</el-button>
          <el-button
            size="mini"
            type="text"
            v-show="scope.row.isEnable == 'N'"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改【请填写功能名称】对话框 -->
    <el-dialog :visible.sync="open" width="30%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="字典名称" prop="dictName">
          <span style="color: red">*</span>
          <el-input style="width:55%" v-model="form.dictName" placeholder="请输入字典名称" />
        </el-form-item>
        <el-form-item label="字典类型" prop="dictType">
          <span style="color: red">*</span>
          <el-select
            @change="chengeDictType()"
            :disabled="seleceDisa"
            v-model="form.dictType"
            placeholder="请选择"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="启用状态" prop="isEnable">
          <el-radio v-model="form.isEnable" label="Y">正常</el-radio>
          <el-radio v-model="form.isEnable" label="N">停用</el-radio>
        </el-form-item>
      </el-form>
      <div v-show="form.dictType == '0'">
        <el-table :data="peopleDatas" style="width: 100%">
          <el-table-column type="index" width="50"></el-table-column>
          <el-table-column prop="dictName" label="人员" width="180"></el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="deletePeople(scope.$index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-button size="mini" @click="addPeople()">新增人员</el-button>
      </div>

      <div v-show="form.dictType == '1'">
        <el-table :data="numDictData" style="width: 100%">
          <el-table-column type="index" width="50"></el-table-column>
          <el-table-column prop="dictName" label="字典值" width="180"></el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="deleteNumDict(scope.$index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-button size="mini" @click="addNumDict()">增加字典值</el-button>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog title="增加字典值" :visible.sync="addDictNumDeilog" width="20%" append-to-body>
      <!--      <fm-generate-form :data="jsonData" ref="generateForm"> </fm-generate-form>-->
      <span style="font-weight: bold; margin-left: 35px">
        <span style="color: red">*</span>字典值：
        <el-input style="width:55%" v-model="dictNumData" placeholder="请输入" />
      </span>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitNumDict">确 定</el-button>
        <el-button @click="addDictNumDeilog=false">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="peopleDeilog" width="20%" append-to-body>
      <!--      <fm-generate-form :data="jsonData" ref="generateForm"> </fm-generate-form>-->
      <span style="font-weight: bold; margin-left: 35px">
        <span style="color: red">*</span>审核人：
      </span>
      <el-select
        v-model="form.approver"
        placeholder="请选择"
        size="small"
        filterable
        @change="getUserData($event)"
      >
        <el-option
          v-for="user in approvers"
          :key="user.userId"
          :label="user.nickName +'('+ user.userName+')'"
          :value="user.userId"
        />
      </el-select>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitPeople">确 定</el-button>
        <el-button @click="peopleDeilog=false">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog title="修改记录" :visible.sync="dinamicListdeilog" width="30%">
      <el-table :data="dynamicDataList">
        <el-table-column property="operationTime" label="时间"></el-table-column>
        <el-table-column property="operationContent" label="操作"></el-table-column>
        <el-table-column property="operationBy" label="用户"></el-table-column>
      </el-table>
      <span slot="footer">
        <el-button @click="dinamicListdeilog = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  listData,
  getData,
  delData,
  addData,
  updateData,
  changeenableStatus
} from "@/api/oa/data";
import {
  listDynamic,
  getDynamic,
  delDynamic,
  addDynamic,
  updateDynamic
} from "@/api/oa/dictDynamic";
import { start, getUsers } from "@/api/flow/flow";
export default {
  name: "DictData",
  data() {
    return {
      dynamicDataList: [],
      dinamicListdeilog: false,
      seleceDisa: false,
      numDictData: [],
      addDictNumDeilog: false,
      dictNumData: "",

      selectUser: {},
      approvers: [],
      peopleDeilog: false,
      peopleDatas: [{}],
      options: [
        {
          value: "0",
          label: "人员"
        },
        {
          value: "1",
          label: "数值"
        }
      ],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【请填写功能名称】表格数据
      dataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dictName: null,
        dictType: null,
        isEnable: null,
        endUpdateTime: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    chengeDictType() {
      this.peopleDatas = [];
      this.numDictData = [];
    },
    addNumDict() {
      this.addDictNumDeilog = true;
    },
    addPeople() {
      this.peopleDeilog = true;
      getUsers().then(response => {
        this.approvers = response.data;
      });
    },
    submitNumDict() {
      this.numDictData.push({ dictName: this.dictNumData });

      (this.addDictNumDeilog = false), (this.dictNumData = "");
    },
    submitPeople() {
      this.peopleDatas.push(this.selectUser);
      this.peopleDeilog = false;
      this.selectUser = {};
      console.log(this.peopleDatas);
    },
    deleteNumDict(index) {
      this.numDictData.splice(index, 1);
    },
    deletePeople(index) {
      this.peopleDatas.splice(index, 1);
    },
    //修改启用状态
    async updateinEnable(row) {
      let text = row.isEnable === "Y" ? "启用" : "停用";
      this.$modal
        .confirm(
          '确认要"' + text + '""' + row.dictTypeName + '"这个字典类型吗？'
        )
        .then(function() {
          return changeenableStatus(row.dictTypeIdent, row.isEnable);
        })
        .then(() => {
          this.$modal.msgSuccess(text + "成功");
        })
        .catch(function() {
          row.isEnable = row.isEnable === "Y" ? "N" : "Y";
        });
      // this.getList();
    },
    /** 查询【请填写功能名称】列表 */
    getList() {
      this.loading = true;
      listData(this.queryParams).then(response => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        dictName: null,
        dictType: null,
        isEnable: null,
        endUpdateTime: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.seleceDisa = false;
      this.form.isEnable = "Y";
      this.title = "添加【请填写功能名称】";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.seleceDisa = true;
      const id = row.dictTypeIdent;
      getData(id).then(response => {
        this.form.dictName = response.data.dictName;
        this.form.dictType = response.data.dictType;
        this.form.isEnable = response.data.isEnable;
        this.form.dictTypeIdent = response.data.dictTypeIdent;
        if (this.form.dictType == "0") {
          this.peopleDatas = response.data.oaDictDataList;
        } else if (this.form.dictType == "1") {
          this.numDictData = response.data.oaDictDataList;
        }
        // this.form = response.data;
        this.open = true;
        // this.title = "修改【请填写功能名称】";
      });
    },
    /** 提交按钮 */
    submitForm() {
      var dataForm = {
        dictName: this.form.dictName,
        dictType: this.form.dictType,
        isEnable: this.form.isEnable,
        dictTypeIdent: this.form.dictTypeIdent
      };

      if (this.form.dictType == "0") {
        dataForm.oaDictDataList = this.peopleDatas;
      } else if (this.form.dictType == "1") {
        dataForm.oaDictDataList = this.numDictData;
      }
      if (
        this.form.dictName != null &&
        this.form.dictType != "" &&
        dataForm.oaDictDataList != undefined &&
        JSON.stringify(dataForm.oaDictDataList) !== "[]"
      ) {
        if (this.seleceDisa == true) {
          updateData(dataForm).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
        } else if (this.seleceDisa == false) {
          addData(dataForm).then(response => {
            this.$modal.msgSuccess("新增成功");
            this.open = false;
            this.getList();
          });
        }
      } else {
        this.$message.error("请检查要提交的数据！！");
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.dictTypeIdent;
      this.$modal
        .confirm('是否确认删除字典类型为"' + row.dictTypeName + '"的数据项？')
        .then(function() {
          return delData(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/data/export",
        {
          ...this.queryParams
        },
        `data_${new Date().getTime()}.xlsx`
      );
    },
    getUserData(event) {
      const list = this.approvers.filter(item => item.userId === event);
      console.log(list[0]);
      this.selectUser.dictValue = list[0].userId;
      this.selectUser.dictName = list[0].nickName;
    },

    updateDynamicList(row) {
      this.dinamicListdeilog = true;
      var dynamic = {
        dictId: row.dictTypeIdent
      };
      listDynamic(dynamic).then(response => {
        this.dynamicDataList = response.rows;
      });
    }
  }
};
</script>
