<template>
  <div>
    <div class="top_auth" v-if="haveAuthList[0]&&haveAuthList[0].showFlag">
      您已指定 [授权代理人] ，由代理人替您进行通用授权工作
      <el-button style="margin-left: 16px" type="text" @click="toProxy(1)"
        >查看详情></el-button
      >
    </div>
    <div
      v-if="haveAuthList[1]&&haveAuthList[1].showFlag"
      class="top_auth"
      style="background: #fffbe6; display: flex; justify-content: space-between"
    >
      <span>
        您已被指定为 [授权代理人]
        ，可替被代理人执行通用授权的工作。请在右侧选择您当前作为授权人的身份
        <el-button @click="toProxy(2)" style="margin-left: 16px" type="text"
          >查看详情 ></el-button
        ></span
      >
      <span>
        当前授权身份：<el-select
          style="width: 180px"
          v-model="personId"
          clearable=""
          @change="changeAuthPerson"
        >
          <el-option
            v-for="item in authPersonList"
            :key="item.principalId"
            :label="item.createNickName + item.createUserStatus"
            :value="item.principalId"
          ></el-option
        ></el-select>
      </span>
    </div>
    <el-switch
      v-model="type"
      active-text="详细版"
      class="absolute right-5 z-10"
    >
    </el-switch>
    <Simple v-if="!type" :changeAuth="changeAuth" />
    <Detailed v-if="type" @changeAuthType="changeAuthType" />
  </div>
</template>

<script>
import { haveAgencyQuery } from "@/api/businessInformation/proxyAuth";
import Simple from "./components/simple";
import Detailed from "./components/detailed";

export default {
  name: "CurrencyCompany",
  components: { Simple, Detailed },
  data() {
    return {
      authPersonList: [],
      haveAuthList: [],
      personId: "",
      type: false,
      changeAuth: false,
    };
  },
  created() {
    this.init();
  },
  methods: {
    changeAuthType() {
      this.type = false;
      setTimeout(() => {
        this.$store.state.changeAuthType = !this.$store.state.changeAuthType;
        console.log(this.$store.state.changeAuthType);
      }, 0);
    },
    toProxy(e) {
      this.$router.push({
        path: "/businessInformation/proxyAuth",
        query: {
          type: e,
        },
      });
    },
    changeAuthPerson(e) {
      this.$store.state.principalId = e;
    },
    init() {
      haveAgencyQuery({ agencyType: 3 }).then((res) => {
        if (res.code == 200) {
          this.haveAuthList = res.data;
          if (
            this.haveAuthList[1].principalList &&
            this.haveAuthList[1].principalList.length > 0
          ) {
            this.authPersonList = this.haveAuthList[1].principalList;
            this.authPersonList.push({
            createNickName:'本人',
            principalId:null,
            createUserStatus:''
          })
            this.personId = this.authPersonList[0].principalId;
            this.$store.state.principalId = this.personId;
          }
        }
      });
    },
  },
};
</script>
<style lang="less" scoped>
.top_auth {
  width: 100%;
  height: 50px;
  line-height: 50px;
  background: #f2f2f2;
  padding: 0 16px;
}
</style>
