<template>
  <div class="p-5">
    <p class="mb-0">
      本页面展示您在智慧平台中已拥有数据权限的公司，它们来自于您的上级用户对您的授权，您也可对自己的下级人员进行授权
    </p>
    <p class="mb-0">
      例如对某个用户授与A公司的权限，则该用户将具有智慧平台中所有与该公司相关项目的权限
    </p>
    <p class="mb-0" v-if="!authType">
      按公司维度授权的优先级大于按项目维度授权，按所有维度授权的优先级大于按公司维度授权
    </p>
    <p class="mb-0" v-else>对公司的授权优先级大于对项目的授权</p>
    <p class="mb-0">
      用户只能为自己的下级用户授权，点击 [个人中心]
      可查看本人上下级关系，及已拥有权限
    </p>
    <div class="mt-3">
      <el-input
        class="mr-3"
        v-model="queryParams.queryName"
        style="width: 200px"
        placeholder="请输入公司名称"
      ></el-input>
      <el-button icon="el-icon-search" type="primary" @click="getList"
        >搜索</el-button
      >
      <el-button icon="el-icon-refresh" @click="reset">重置</el-button>
    </div>
    <div class="mt-3">
      <span v-if="!authType">
        <el-button
          size="mini"
          v-hasPermi="['currencyCompany:allAuth']"
          type="primary"
          @click="changeAuthType"
          >批量授权</el-button
        >
        <el-button
          v-hasPermi="['authTemplate:unit']"
          class="mr-12"
          size="mini"
          type="primary"
          @click="toTemp"
          >授权模板管理</el-button
        >
        <!-- <el-button class="mr-12" size="mini" type="primary">代理授权</el-button> -->
      </span>
      <span v-else>
        <el-button size="mini" @click="openSelect = true"
          >已选择{{ selectList.length }}个项目，查看已选</el-button
        >
        <el-button size="mini" type="primary" @click="addMostTemp"
          >选择完成，去授权</el-button
        >
        <el-button type="text" @click="allSelectType = !allSelectType"
          >全选/全不选</el-button
        >
        <el-button class="mr-12" type="text" @click="changeAuthType"
          >取消</el-button
        >
      </span>
      <span v-if="!authType">
        <el-switch class="mr-3" v-model="changeUserType" @change="getList">
        </el-switch
        >仅看直属下级
      </span>
      <el-switch class="mr-3 ml-8" v-model="changeType" @change="getList">
      </el-switch
      >未对他人分配权限的公司
    </div>
    <div class="mt-3" v-if="!authType">
      <div class="flex my-3">
        <div class="mr-3">公司类型:</div>
        <div class="flex-1 flex-wrap min-h-0 flex">
          <el-button
            type="text"
            class="relative"
            style="bottom: 7px"
            v-for="(item, index) in unitTypeList"
            :key="index"
            @click="changeUnitType(item)"
          >
            <span
              :style="{
                color:
                  item.dictCode == queryParams.companyTypeCode ? 'black' : '',
              }"
              >{{ item.dictLabel }}</span
            >
          </el-button>
        </div>
      </div>
    </div>
    <MyTable
      v-show="!authType && userList.length > 0"
      class="mt-3"
      :columns="dataColumns"
      :queryParams="queryParams"
      :source="dataList"
      :showIndex="true"
    >
      <template v-slot:h_authorizedUserList="">
        <div>
          授权用户
          <el-tooltip
            class="item"
            effect="dark"
            content="仅显示您和您已授权的下级用户，不属于您的下级用户不在此显示"
            placement="top"
          >
            <i class="el-icon-info"></i>
          </el-tooltip>
        </div>
      </template>
      <template #authorizedUserList="{ record }">
        <span
          class="user"
          @click="getUserData(item.authorizedUserId, item)"
          :style="{
            background:
              item.authorizedUserIsCurrentUserFlag != 1 ||
              item.authorizedUserHaveAllPermissionFlag == 1
                ? '#f2f2f2'
                : '',
          }"
          v-for="(item, i) in record.authorizedUserList"
          :key="i"
        >
          {{ item.authorizedUserId | user
          }}<span style="color: #cccccc">{{
            item.authorizedUserId | userStatus
          }}</span
          ><i
            @click.stop="delUser(record, item)"
            class="el-icon-close"
            v-if="
              item.authorizedUserIsCurrentUserFlag == 1 &&
              item.authorizedUserHaveAllPermissionFlag != 1
            "
          ></i
        ></span>
      </template>
      <template #opertion="{ record }">
        <el-button
          type="text"
          v-hasPermi="['currencyCompany:addPerson']"
          @click="addUser(record)"
          >+添加用户</el-button
        >
        <!-- <el-button type="text" @click="addUser(record)">按模板添加</el-button> -->
      </template>
    </MyTable>
    <!-- <MyTable
      v-show="authType && userList.length > 0"
      :showCheckbox="showCheckbox"
      :allSelectType="allSelectType"
      :isClearSelect="isClearSelect"
      @selectList="tableSelect"
      rowKey="id"
      class="mt-3"
      :columns="authDataColumns"
      :tableSelectList="selectList"
      :queryParams="queryParams"
      :source="dataList"
      :showIndex="true"
    >
    </MyTable> -->
    <el-table
      v-show="authType && userList.length > 0"
      :data="dataList"
      ref="select_table"
      row-key="id"
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column reserve-selection type="selection" width="55">
      </el-table-column>
      <el-table-column prop="responseName" label="公司名称"> </el-table-column>
      <el-table-column prop="responseAbbreviationName" label="简称">
      </el-table-column>
    </el-table>
    <pagination
      v-show="queryParams.total > 0"
      :total="queryParams.total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <TableSelect
      :columns="columnsSelect"
      :tableData="selectList"
      v-model="openSelect"
      @on-submit-success="submitDelet"
    />
    <AddingUsers
      :subordinateList="subordinateList"
      v-if="addingUsersType"
      @close="addingUsersType = false"
      @submit="submitAddUser"
    />
    <AuthMethod
      v-if="authMethodType"
      type="unit"
      @close="authMethodType = false"
      @submitMeth="submitMeth"
    />
    <SelectAuthTime
      @submit="submitDate"
      v-if="selectAuthTimeTpye"
      @close="selectAuthTimeTpye = false"
    />
    <UserDetail2
      :userId="userId"
      :authType="authTypeName"
      v-if="userDetailType"
      @close="userDetailType = false"
    />
  </div>
</template>
<script>
import { getDicts } from "@/api/system/dict/data";

import {
  newAuthority,
  getUserListAll,
  subordinate,
  newAuthTemp,
  queryCancelUser,
  cancelAuthorization,
} from "@/api/businessInformation/currencyCompany";
let that = "";
export default {
  data() {
    return {
      userId: "",
      authTypeName: "",
      userDetailType: false,
      unitTypeList: [],
      subordinateList: [],
      userList: [],
      selectAuthTimeTpye: false,
      allSelectType: false,
      showCheckbox: false,
      //批量授权状态
      authType: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        companyTypeCode: undefined,
        queryType: 2,
        queryName: "",
        queryCode: "ALL",
        queryThirdType: 1,
        unassignedCompaniesFlag: 0,
        subordinateFlag: 0,
      },
      dataList: [],
      changeUserType: false,
      authDataColumns: [
        {
          label: "公司名称",
          prop: "responseName",
          width: "200",
        },
        {
          label: "简称",
          prop: "responseAbbreviationName",
          width: "100",
        },
      ],
      dataColumns: [
        {
          label: "公司名称",
          prop: "responseName",
          width: "200",
        },
        {
          label: "简称",
          prop: "responseAbbreviationName",
          width: "100",
        },
        {
          label: "授权用户",
          prop: "authorizedUserList",
          key: "authorizedUserList",
          width: "300",
          isHSlot: true,
        },

        {
          label: "操作",
          key: "opertion",
          width: "200",
        },
      ],
      companyType: "1",
      changeType: false,
      authMethodType: false,
      addingUsersType: false,
      columnsSelect: [
        {
          label: "项目名称",
          prop: "responseName",
          minWidth: "300",
          showOverflowTooltipField: true,
        },
      ],
      tableDataSelect: [
        {
          projectName:
            "中保国信-360-富邦银行水水水水水水水水水水水水水水水水水水水水水水水水水水水水水水水水水水水啊啊啊啊啊啊啊啊啊",
          id: 1,
        },
        { projectName: "中保国信-360-北部湾 金科服务费", id: 2 },
        { projectName: "海南正堂-度小满-通商银行", id: 3 },
      ],
      openSelect: false,
      selectList: [],
      //添加用户选择授权人
      selectAuthUsers: [],
      //添加用户选择的模板
      selectAuthMethList: [],
      selectItemData: null,
      isClearSelect: false,
    };
  },
  created() {
    that = this;
    this.init();
  },
  watch: {
    "$store.state.changeAuthType": {
      handler(newval, oldval) {
        console.log(newval);
        this.changeAuthType();
      },
      deep: true,
    },
    "$store.state.principalId": {
      handler(newval, oldval) {
        console.log(newval);
        this.init();
      },
      deep: true,
    },
  },

  filters: {
    user(e) {
      let data = that.userList.find((item) => {
        return item.userId == e;
      });
      if (data) {
        return data.nickName;
      }
    },
    userStatus(e) {
      let data = that.userList.find((item) => {
        return item.userId == e;
      });
      if (data&&data.status == 0) {
        return "";
      } else {
        return "（已停用）";
      }
    },
  },
  methods: {
    getUserData(e, v) {
      console.log(e);
      if (v.authorizedUserHaveAllPermissionFlag == 1) {
        this.authTypeName = "所有";
      }
      if (
        !v.authorizedUserHaveAllPermissionFlag &&
        !v.authorizedUserHaveCompanyPermissionFlag
      ) {
        this.authTypeName = "项目";
      }
      if (v.authorizedUserHaveCompanyPermissionFlag == 1) {
        this.authTypeName = "公司";
      }
      let data = that.userList.find((item) => {
        return item.userId == e;
      });
      console.log(data);
      this.userId = data.userName;
      this.userDetailType = true;
    },
    changeUnitType(value) {
      this.queryParams.companyTypeCode = value.dictCode;
      this.getList();
    },
    getNickName(e) {
      let data = that.userList.find((item) => {
        return item.userId == e;
      });
      if (data) {
        return data.nickName;
      }
    },
    delUser(v, i) {
      console.log(v, i);
      let params = {
        id: v.id,
        authorizedType: 2,
        authorizedCode: "ALL",
        unAuthorizedUserId: i.authorizedUserId,
      };
      queryCancelUser({
        ...params,
        principalId: this.$store.state.principalId,
      }).then((res) => {
        if (res.msg == 0) {
          this.$confirm(
            `是否确认取消对用户[${this.getNickName(
              i.authorizedUserId
            )}]的授权？点击确定后，取消授权将立即生效！`,
            "取消授权",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }
          ).then(() => {
            this.cancelAuthorization(params);
          });
        } else if (res.msg > 0) {
          this.$confirm(
            ` [${this.getNickName(
              i.authorizedUserId
            )}]已经将本权限授权于其下级用户，取消后其下级用户也将失去此权限,点击确定后，取消授权将立即生效！ `,
            "取消授权",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }
          ).then(() => {
            this.cancelAuthorization(params);
          });
        }
      });
    },
    cancelAuthorization(e) {
      cancelAuthorization({
        ...e,
        principalId: this.$store.state.principalId,
      }).then((res) => {
        if (res.code == 200) {
          this.$message.success("操作成功");
          this.getList();
        }
      });
    },
    reset() {
      this.changeType = false;
      this.changeUserType = false;
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        queryType: 2,
        queryName: "",
        companyTypeCode: undefined,
        queryCode: "ALL",
        queryThirdType: 1,
        unassignedCompaniesFlag: 0,
        subordinateFlag: 0,
      };
      this.getList();
    },
    toTemp() {
      this.$router.push({
        path: "/businessInformationOther/authTemplate",
        query: {
          type: "unit",
        },
      });
    },
    submitDate(e) {
      let data = {
        id: this.selectAuthMethList[0].id,
        authDate: e ? this.$format(e, "yyyy-MM-dd 23:59:59") : null,
        authType: "unit",
        authId: !this.authType
          ? [this.selectItemData.id]
          : this.selectList.map((item) => item.id),
        authUserIds: this.selectAuthUsers.map((item) => item.userId),
        agencyUserId: this.$store.state.principalId,
      };
      console.log(data);
      newAuthTemp({ ...data }).then((res) => {
        if (res.code == 200) {
          this.$message.success("操作成功");
          this.getList();
          this.showCheckbox = false;
          this.allSelectType = false;
          this.$refs.select_table.clearSelection();
          this.dataColumns = [
            {
              label: "公司名称",
              prop: "responseName",
              width: "200",
            },
            {
              label: "简称",
              prop: "responseAbbreviationName",
              width: "100",
            },
            {
              label: "授权用户",
              prop: "authorizedUserList",
              key: "authorizedUserList",
              width: "300",
              isHSlot: true,
            },

            {
              label: "操作",
              key: "opertion",
              width: "200",
            },
          ];
          setTimeout(() => {
            this.authType = false;
          }, 50);
          this.selectAuthTimeTpye = false;
          this.authMethodType = false;
          this.addingUsersType = false;
        }
      });
    },
    submitMeth(e) {
      console.log(e);
      this.selectAuthMethList = e;
      this.selectAuthTimeTpye = true;
    },
    submitAddUser(e) {
      console.log(e);
      this.selectAuthUsers = e;
      this.authMethodType = true;
    },
    allSelect() {},
    handleSelectionChange(e) {
      this.selectList = [...e];
    },
    changeAuthType() {
      if (!this.authType) {
        this.showCheckbox = true;
        this.dataColumns = [
          {
            label: "公司名称",
            prop: "companyName",
            width: "200",
          },
          {
            label: "简称",
            prop: "company",
            width: "100",
          },
        ];
        setTimeout(() => {
          this.authType = true;
        }, 50);
      } else {
        this.showCheckbox = false;
        this.allSelectType = false;
        this.$refs.select_table.clearSelection();
        this.dataColumns = [
          {
            label: "公司名称",
            prop: "responseName",
            width: "200",
          },
          {
            label: "简称",
            prop: "responseAbbreviationName",
            width: "100",
          },
          {
            label: "授权用户",
            prop: "authorizedUserList",
            key: "authorizedUserList",
            width: "300",
            isHSlot: true,
          },

          {
            label: "操作",
            key: "opertion",
            width: "200",
          },
        ];
        setTimeout(() => {
          this.authType = false;
        }, 50);
      }
    },
    addMostTemp() {
      subordinate({ principalId: this.$store.state.principalId }).then(
        (res) => {
          if (res.code == 200) {
            if (
              res.data.subordinateList &&
              res.data.subordinateList.length > 0
            ) {
              this.subordinateList = res.data;
              this.addingUsersType = true;
            } else {
              this.$message.warning("您没有下级用户，无法添加用户给他人授权！");
            }
          }
        }
      );
    },
    addUser(e) {
      console.log(e);
      this.selectItemData = e;
      subordinate({ principalId: this.$store.state.principalId }).then(
        (res) => {
          if (res.code == 200) {
            if (
              res.data.subordinateList &&
              res.data.subordinateList.length > 0
            ) {
              this.subordinateList = res.data;
              this.addingUsersType = true;
            } else {
              this.$message.warning("您没有下级用户，无法添加用户给他人授权！");
            }
          }
        }
      );
    },
    init() {
      this.getDicts();
      this.getUser();
      this.getList();
    },
    getDicts() {
      getDicts("company_type").then((res) => {
        this.unitTypeList = res.data;
        this.unitTypeList.unshift({
          dictLabel: "全部",
          dictCode: undefined,
        });
      });
    },
    getUser() {
      getUserListAll().then((res) => {
        if (res.code == 200) {
          this.userList = res.data;
        }
      });
    },
    getList() {
      this.queryParams.subordinateFlag = this.changeUserType ? 1 : 0;
      this.queryParams.unassignedCompaniesFlag = this.changeType ? 1 : 0;
      newAuthority({
        ...this.queryParams,
        queryUserId: this.$store.state.principalId,
      }).then((res) => {
        if (res.code == 200) {
          this.dataList = res.rows;
          this.queryParams.total = res.total;
        }
      });
    },
    submitDelet(e) {
      let list = this.selectList.filter((item) => e.includes(item.id));
      this.selectList = this.selectList.filter((item) => !e.includes(item.id));

      console.log(list);
      this.$nextTick(() => {
        console.log(123);
        list.forEach((item) => {
          this.$refs.select_table.toggleRowSelection(item, false);
        });
      });
    },
  },
};
</script>
<style lang="less" scoped>
.user {
  display: inline-block;
  border: 1px solid #cccccc;
  padding: 0 10px;
  cursor: pointer;
  color: #409eff;
  border-radius: 4px;
  margin-right: 10px;
  margin-top: 3px;
  i {
    color: #666666;
    margin-left: 5px;
    cursor: pointer;
  }
}
</style>
