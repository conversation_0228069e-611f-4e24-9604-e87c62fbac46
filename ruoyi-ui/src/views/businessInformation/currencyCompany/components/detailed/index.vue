<template>
  <div class="p-5">
    <div>{{ tabsTitle[currentTab] }}</div>
    <el-tabs v-model="currentTab" type="card" class="mt-4">
      <el-tab-pane
        :key="index"
        v-if="
          item.UPCode == 'SummaryPermissions' ||
          item.UPCode == 'Oalaunch' ||
          item.UPCode == 'Financesys' ||
          item.UPCode == 'Financeproj' ||
          item.UPCode == 'Carboolmanger' ||
          item.UPCode == 'Projsetup' ||
          item.UPCode == 'Datareport' ||
          item.UPCode == 'Echarts' ||
          item.UPCode == 'License' ||
          item.UPCode == 'Datatop' ||
          item.UPCode == 'Archives' ||
          item.UPCode == 'Projname' ||
          item.UPCode == 'Performance' ||
          item.UPCode == 'Jginformation' ||
          item.UPCode == 'Sxinformation' ||
          item.UPCode == 'Notice' || item.UPCode=='Offsupply'
        "
        v-for="(item, index) in tabsList"
        :label="item.info"
        :name="item.UPCode"
      >
      </el-tab-pane>
    </el-tabs>
    <component
      :is="currentTab"
      :tabsList="tabsList"
      :code="currentTab"
      @authType="changeAuthType"
    ></component>
  </div>
</template>

<script>
import { getModuleType } from "@/api/businessInformation/authTemplate";

import SummaryPermissions from "./components/SummaryPermissions/index.vue";
import Oalaunch from "./components/Oalaunch/index.vue";
import Financesys from "./components/Financesys/index.vue";
import Financeproj from "./components/Financeproj/index.vue";
import Carboolmanger from "./components/Carboolmanger/index.vue";
import Projsetup from "./components/Projsetup/index.vue";
import Archives from "./components/Archives/index.vue";
import Information from "./components/Information/index.vue";
import Personnel from "./components/Personnel/index.vue";
import License from "./components/License/index.vue";
import Attendance from "./components/Attendance/index.vue";
import Payroll from "./components/Payroll/index.vue";
import Datareport from "./components/Datareport/index.vue";
import Echarts from "./components/Echarts/index.vue";
import Datatop from "./components/Datatop/index.vue";
import Projname from "./components/Projname/index.vue";
import Performance from "./components/Performance/index.vue";
import Jginformation from "./components/Jginformation/index.vue";
import Sxinformation from "./components/Sxinformation/index.vue";
import Offsupply from "./components/Offsupply/index.vue";
import Notice from "./components/Notice/index.vue";

export default {
  data() {
    const titleOne = "为每个功能模块设置详细的权限";
    const titleTwo = "管理用户为下属用户分配智慧平台各功能模块中的权限";
    return {
      tabsList: [],
      tabsTitle: Object.freeze({
        SummaryPermissions: titleOne,
        Oalaunch: titleOne,
        Financesys: titleOne,
        Financeproj: titleOne,
        Carboolmanger: titleOne,
        Projsetup: titleOne,
        Datareport: titleOne,
        Echarts: titleOne,
        Datatop: titleOne,
        Jginformation: titleTwo,
        Sxinformation: titleTwo,
        Archives: titleTwo,
      }),
      currentTab: "SummaryPermissions",
    };
  },
  components: {
    Notice,
    Performance,
    Sxinformation,
    Jginformation,
    SummaryPermissions,
    Archives,
    Oalaunch,
    Projname,
    Datatop,
    Echarts,
    Datareport,
    Payroll,
    Attendance,
    License,
    Personnel,
    Information,
    Projsetup,
    Carboolmanger,
    Financeproj,
    Financesys,
    Offsupply
  },
  created() {
    this.init();
  },
  methods: {
    changeAuthType() {
      this.$emit("changeAuthType");
    },
    init() {
      getModuleType("unit").then((res) => {
        if (res.code == 200) {
          this.tabsList = res.data;
          this.tabsList.unshift({
            UPCode: "SummaryPermissions",
            code: "SummaryPermissions",
            order: 0,
            info: "权限汇总",
            permissionType: "UNIT",
            status: "0",
          });
        }
      });
    },
  },
};
</script>
