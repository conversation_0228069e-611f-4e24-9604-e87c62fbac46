<template>
  <div>
    <p class="mb-0">
      本页面展示您在智慧财务系统拥有哪些公司（账套）的权限，它们来自于您的上级用户对您的授权
    </p>
    <p class="mb-0">您可以将自己已拥有的权限授权给自己的下级用户</p>

    <div class="mt-3">
      <el-input
        class="mr-3"
        style="width: 200px"
        placeholder="请输入公司名称"
      ></el-input>
      <el-button icon="el-icon-search" type="primary">搜索</el-button>
      <el-button icon="el-icon-refresh">重置</el-button>
    </div>
    <div class="mt-3">
      <!-- <span class="mr-6">
        <el-button
          size="mini"
          type="primary"
          v-hasPermi="['currencyProject:allAuth']"
          @click="changeAuthType"
          >批量授权</el-button
        >
      </span> -->
      <el-switch class="mr-3" v-model="changeType"> </el-switch
      >未对他人分配权限的公司
    </div>
    <MyTable
      class="mt-3"
      :columns="dataColumns"
      :queryParams="queryParams"
      :source="dataList"
      :showIndex="true"
    >
      <template v-slot:h_users="">
        <div>
          授权用户
          <el-tooltip
            class="item"
            effect="dark"
            content="仅显示您和您已授权的下级用户，不属于您的下级用户不在此显示"
            placement="top"
          >
            <i class="el-icon-info"></i>
          </el-tooltip>
        </div>
      </template>
      <template #users="{ record }">
        <span class="user"
          >张三<span style="color: #cccccc">（已停用）</span
          ><i class="el-icon-close"></i
        ></span>
      </template>

      <template #opertion="{ record }">
        <el-dropdown trigger="click">
          <span class="el-dropdown-link"> +添加用户 </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-button type="text">添加普通会计</el-button>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-button type="text">添加主管会计</el-button>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-button type="text">添加查看权限</el-button>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
    </MyTable>

    <pagination
      v-show="queryParams.total > 0"
      :total="queryParams.total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <AddingUsers
      v-if="addingUsersType"
      @close="addingUsersType = false"
      @submit="submitAddUser"
    />
    <AuthMethod
      v-if="authMethodType"
      @close="authMethodType = false"
      @submitMeth="submitMeth"
    />
    <SelectAuthTime
      v-if="selectAuthTimeTpye"
      @close="selectAuthTimeTpye = false"
    />
  </div>
</template>
<script>
export default {
  data() {
    return {
      selectAuthTimeTpye: false,
      allSelectType: false,
      showCheckbox: false,
      //批量授权状态
      authType: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      dataList: [{}],
      dataColumns: [
        {
          label: "账套所属公司",
          prop: "companyName",
          width: "200",
        },
        {
          label: "普通会计",
          key: "company",
          width: "100",
        },
        {
          label: "主管会计",
          key: "users",
          width: "300",
        },
        {
          label: "查看权限",
          key: "userCode",
          width: "100",
        },

        {
          label: "操作",
          key: "opertion",
          width: "200",
        },
      ],
      companyType: "1",
      changeType: false,
      authMethodType: false,
      addingUsersType: false,

      openSelect: false,
      selectList: [],
    };
  },
  created() {
    this.init();
  },
  methods: {
    toTemp() {
      this.$router.push({
        path: "/businessInformationOther/authTemplate",
        query: {
          type: "unit",
        },
      });
    },
    submitMeth(e) {
      this.selectAuthTimeTpye = true;
    },
    submitAddUser(e) {
      this.authMethodType = true;
    },
    allSelect() {},
    tableSelect(e) {
      console.log(e);
      this.selectList = [...e];
    },
    changeAuthType() {
      this.$emit("authType");
    },
    addUser(e) {
      console.log(e);
    },
    init() {
      this.getList();
    },
    getList() {},
    submitDelet(e) {
      console.log(e);
    },
  },
};
</script>
<style lang="less" scoped>
.user {
  display: inline-block;
  border: 1px solid #cccccc;
  padding: 0 10px;
  cursor: pointer;
  color: #409eff;
  border-radius: 4px;
  margin-right: 10px;
  i {
    color: #666666;
    margin-left: 5px;
    cursor: pointer;
  }
}
.demonstration {
  display: block;
}
</style>
