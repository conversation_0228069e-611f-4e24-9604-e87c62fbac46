<template>
  <div>
    <el-dialog
      append-to-body
      title="编辑内容"
      :before-close="handleClose"
      :visible.sync="innerValue"
      width="900px"
    >
      <div style="max-height: 80vh">
        <div class="data_content">
          <div class="table">
            <div class="left">
              <div style="background: #e4e4e4"></div>
              <div>产品名称</div>
              <div>产品编码</div>
              <div>系统</div>
              <div>项目名称</div>
              <div>说明</div>
              <div>是否映射成功</div>
              <div>项目是否已结束</div>
              <div>是否为企业客户</div>
              <div>是否通道简版数据</div>
              <div>是否有还款计划</div>
              <div>是否有还款计划实还更新数据</div>
              <div>是否有还款计划缩期情况</div>
              <div>是否有正常还款数据</div>
              <div>是否有提前还款数据</div>
              <div>是否有提前结清数据</div>
              <div>是否有代偿还款数据</div>
              <div>累计代偿还款数据</div>
              <div>累计还款数据</div>
              <div>追偿还款数据</div>
              <div>代偿时结清</div>
              <div>追偿还款是否按正常还款提供</div>
              <div>FPD10是否可用</div>
              <div>Vintage是否可用</div>
              <div>余额分布是否可用</div>
            </div>
            <div class="center">
              <div style="background: #e4e4e4">修改前</div>

              <el-tooltip
                class="item"
                effect="dark"
                :content="oldData.productName"
                placement="top-start"
                style="padding: 0 0 0 10px; margin: 0"
              >
                <div class="truncate ...">{{ oldData.productName }}</div>
              </el-tooltip>

              <el-tooltip
                class="item"
                effect="dark"
                :content="oldData.productNo"
                placement="top-start"
                style="padding: 0 0 0 10px; margin: 0"
              >
                <div class="truncate ...">{{ oldData.productNo }}</div>
              </el-tooltip>
              <div>{{ systemProductListObj[oldData.systemNo * 1] }}</div>

              <el-tooltip
                effect="dark"
                :content="oldData.projectName"
                placement="top"
              >
                <div style="overflow: hidden; text-overflow: ellipsis">
                  {{ oldData.projectName }}
                </div>
              </el-tooltip>
              <el-tooltip
                effect="dark"
                :content="oldData.description"
                placement="top"
              >
                <div style="overflow: hidden; text-overflow: ellipsis">
                  {{ oldData.description }}
                </div>
              </el-tooltip>
              <div>
                {{
                  oldData.isMapping && oldData.isMapping == "Y"
                    ? "是"
                    : !oldData.isMapping
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  oldData.isProjectFinish && oldData.isProjectFinish == "Y"
                    ? "是"
                    : !oldData.isProjectFinish
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  oldData.isProjectCompany && oldData.isProjectCompany == "Y"
                    ? "是"
                    : !oldData.isProjectCompany
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  oldData.isProjectTd && oldData.isProjectTd == "Y"
                    ? "是"
                    : !oldData.isProjectTd
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  oldData.isProjectPlan && oldData.isProjectPlan == "Y"
                    ? "有"
                    : !oldData.isProjectPlan
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  oldData.isProjectPlanUpdate &&
                  oldData.isProjectPlanUpdate == "Y"
                    ? "有"
                    : !oldData.isProjectPlanUpdate
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  oldData.isProjectPlanReset &&
                  oldData.isProjectPlanReset == "Y"
                    ? "有"
                    : !oldData.isProjectPlanReset
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  oldData.isProjectRepay1 && oldData.isProjectRepay1 == "Y"
                    ? "有"
                    : !oldData.isProjectRepay1
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  oldData.isProjectRepay4 && oldData.isProjectRepay4 == "Y"
                    ? "有"
                    : !oldData.isProjectRepay4
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  oldData.isProjectRepay5 && oldData.isProjectRepay5 == "Y"
                    ? "有"
                    : !oldData.isProjectRepay5
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  oldData.isProjectRepay7 && oldData.isProjectRepay7 == "Y"
                    ? "有"
                    : !oldData.isProjectRepay7
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  oldData.isProjectTotalRepay7 &&
                  oldData.isProjectTotalRepay7 == "Y"
                    ? "有"
                    : !oldData.isProjectTotalRepay7
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  oldData.isProjectTotalRepay &&
                  oldData.isProjectTotalRepay == "Y"
                    ? "有"
                    : !oldData.isProjectTotalRepay
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  oldData.isProjectRepay8 && oldData.isProjectRepay8 == "Y"
                    ? "有"
                    : !oldData.isProjectRepay8
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  oldData.isProjectRepay7Finish &&
                  oldData.isProjectRepay7Finish == "Y"
                    ? "是"
                    : !oldData.isProjectRepay7Finish
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  oldData.isProjectRepay8Normal &&
                  oldData.isProjectRepay8Normal == "Y"
                    ? "是"
                    : !oldData.isProjectRepay8Normal
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  oldData.isResultFpd10 && oldData.isResultFpd10 == "Y"
                    ? "是"
                    : !oldData.isResultFpd10
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  oldData.isResultVintage && oldData.isResultVintage == "Y"
                    ? "是"
                    : !oldData.isResultVintage
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  oldData.isResultBalanceDistribution &&
                  oldData.isResultBalanceDistribution == "Y"
                    ? "是"
                    : !oldData.isResultBalanceDistribution
                    ? ""
                    : "否"
                }}
              </div>
            </div>
            <div class="right">
              <div style="background: #e4e4e4">修改后</div>
            
              <el-tooltip
                class="item"
                effect="dark"
                :content="newData.productName"
                placement="top-start"
                style="padding: 0 0 0 10px; margin: 0"
              >
                <div class="truncate ...">{{ newData.productName }}</div>
              </el-tooltip>

              <el-tooltip
                class="item"
                effect="dark"
                :content="newData.productNo"
                placement="top-start"
                style="padding: 0 0 0 10px; margin: 0"
              >
                <div class="truncate ...">{{ newData.productNo }}</div>
              </el-tooltip>
              <div>{{ systemProductListObj[newData.systemNo * 1] }}</div>
              <el-tooltip
                effect="dark"
                :content="newData.projectName"
                placement="top"
              >
                <div style="overflow: hidden; text-overflow: ellipsis">
                  {{ newData.projectName }}
                </div>
              </el-tooltip>
              <el-tooltip
                effect="dark"
                :content="newData.description"
                placement="top"
              >
                <div style="overflow: hidden; text-overflow: ellipsis">
                  {{ newData.description }}
                </div>
              </el-tooltip>
              <div>
                {{
                  newData.isMapping && newData.isMapping == "Y"
                    ? "是"
                    : !newData.isMapping
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  newData.isProjectFinish && newData.isProjectFinish == "Y"
                    ? "是"
                    : !newData.isProjectFinish
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  newData.isProjectCompany && newData.isProjectCompany == "Y"
                    ? "是"
                    : !newData.isProjectCompany
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  newData.isProjectTd && newData.isProjectTd == "Y"
                    ? "是"
                    : !newData.isProjectTd
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  newData.isProjectPlan && newData.isProjectPlan == "Y"
                    ? "有"
                    : !newData.isProjectPlan
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  newData.isProjectPlanUpdate &&
                  newData.isProjectPlanUpdate == "Y"
                    ? "有"
                    : !newData.isProjectPlanUpdate
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  newData.isProjectPlanReset &&
                  newData.isProjectPlanReset == "Y"
                    ? "有"
                    : !newData.isProjectPlanReset
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  newData.isProjectRepay1 && newData.isProjectRepay1 == "Y"
                    ? "有"
                    : !newData.isProjectRepay1
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  newData.isProjectRepay4 && newData.isProjectRepay4 == "Y"
                    ? "有"
                    : !newData.isProjectRepay4
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  newData.isProjectRepay5 && newData.isProjectRepay5 == "Y"
                    ? "有"
                    : !newData.isProjectRepay5
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  newData.isProjectRepay7 && newData.isProjectRepay7 == "Y"
                    ? "有"
                    : !newData.isProjectRepay7
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  newData.isProjectTotalRepay7 &&
                  newData.isProjectTotalRepay7 == "Y"
                    ? "有"
                    : !newData.isProjectTotalRepay7
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  newData.isProjectTotalRepay &&
                  newData.isProjectTotalRepay == "Y"
                    ? "有"
                    : !newData.isProjectTotalRepay
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  newData.isProjectRepay8 && newData.isProjectRepay8 == "Y"
                    ? "有"
                    : !newData.isProjectRepay8
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  newData.isProjectRepay7Finish &&
                  newData.isProjectRepay7Finish == "Y"
                    ? "是"
                    : !newData.isProjectRepay7Finish
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  newData.isProjectRepay8Normal &&
                  newData.isProjectRepay8Normal == "Y"
                    ? "是"
                    : !newData.isProjectRepay8Normal
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  newData.isResultFpd10 && newData.isResultFpd10 == "Y"
                    ? "是"
                    : !newData.isResultFpd10
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  newData.isResultVintage && newData.isResultVintage == "Y"
                    ? "是"
                    : !newData.isResultVintage
                    ? ""
                    : "否"
                }}
              </div>
              <div>
                {{
                  newData.isResultBalanceDistribution &&
                  newData.isResultBalanceDistribution == "Y"
                    ? "是"
                    : !newData.isResultBalanceDistribution
                    ? ""
                    : "否"
                }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <div class="flex justify-center">
          <el-button @click="handleClose" class="ml-3">关闭</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>
  
  <script>
import { getDicts } from "@/api/system/dict/data";
import { arrToObj } from "@/utils";
export default {
  dicts: ["system_product"],
  props: {
    data: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      innerValue: true,
      oldData: null,
      newData: null,
      systemProductListObj: {},
    };
  },
  watch: {},
  mounted() {
    console.log(this.data);
    this.oldData = this.data.oaApplyRecordsOldData
      ? JSON.parse(this.data.oaApplyRecordsOldData)
      : {};
    this.newData = this.data.oaApplyRecordsNewData
      ? JSON.parse(this.data.oaApplyRecordsNewData)
      : {};
    this.getDic();
  },
  methods: {
    async getDic() {
      const { data } = await getDicts("system_product");

      this.systemProductListObj = arrToObj(data);
      console.log(this.systemProductListObj, "--");
    },
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>
<style lang="less" scoped>
.data_content {
  margin: 0 auto;
  .title2 {
    font-weight: bold;
    margin-bottom: 10px;
    margin-top: 16px;
  }
  .table {
    display: flex;
    justify-content: space-between;
    border: 1px solid #cccccc;
    border-bottom: none;
    .left,
    .center,
    .right {
      width: 35%;
      > div {
        height: 30px;
        width: 100%;
        border-right: 1px solid #ccc;
        text-align: center;
        line-height: 30px;
        border-bottom: 1px solid #ccc;
        text-align: left;
        padding-left: 10px;
      }
    }
    .left div {
      font-weight: bold;
    }
    .left {
      width: 30%;
    }
  }
}
</style>
  