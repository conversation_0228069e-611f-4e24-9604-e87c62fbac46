<template>
  <div>
    <div>
      <el-form
        ref="form"
        :rules="rules"
        :model="myForm"
        label-width="120px"
        class="w-1/2 m-auto"
      >
        <el-form-item label="公司名称:" prop="companyName">
          <el-input
            class="w-80"
            placeholder="请输入"
            maxlength="30"
            show-word-limit
            v-model.trim="myForm.companyName"
            clearable
          >
          </el-input>
          <div class="leading-6">
            请输入公司全称，即在工商部门注册的公司名称
          </div>
        </el-form-item>
        <el-form-item label="公司简称:" prop="companyShortName">
          <el-input
            class="w-80"
            placeholder="请输入"
            maxlength="15"
            show-word-limit
            v-model.trim="myForm.companyShortName"
            clearable
          >
          </el-input>
          <div class="leading-6">
            创建项目时，公司简称将作为项目名称的一部分
          </div>
          <div class="leading-6">
            每个项目名称由[担保公司]简称+[资产方]简称+[资金方]简称3部分组成
          </div>
        </el-form-item>
        <el-form-item label="公司编码:" prop="companyCode">
          <el-input
            class="w-80"
            placeholder="请输入"
            show-word-limit
            v-model.trim="myForm.companyCode"
            clearable
          >
          </el-input>
        </el-form-item>
        <el-form-item label="公司类型:" prop="companyTypeMappingList">
          <div>
            <div class="flex flex-wrap">
              <div
                v-for="(item, index) in myForm.companyTypeMappingList"
                :key="index"
                class="mr-3 mb-3 border border-solid rounded px-2 flex h-6 items-center relative top-2"
                :style="{
                  borderColor: item
                    ? unitTypeListColor[item.dictLabel] ||
                      unitTypeListColor['其他']
                    : '',
                  backgroundColor: item
                    ? unitTypeListBackColor[item.dictLabel] ||
                      unitTypeListBackColor['其他']
                    : '',
                }"
              >
                <div class="h-6 leading-6">{{ item && item.dictLabel }}</div>
                <div @click="deletCompany(item)" class="cursor-pointer">
                  <i class="el-icon-close"></i>
                </div>
              </div>
              <el-button
                style="height: 30px"
                class="relative top-1"
                type="primary"
                size="mini"
                icon="el-icon-plus"
                @click="openCompanyType = true"
                >添加</el-button
              >
            </div>
          </div>
          <div class="leading-6">
            可选择多个公司类型，如果找不到所需的公司类型，请联系技术部进行添加
          </div>
        </el-form-item>
        <el-form-item label="支持的产品分类:">
          <!-- <div>
            <div class="flex flex-wrap">
              <div
                v-for="(item, index) in myForm.companyBusinessTypeMappingList"
                :key="index"
                class="mr-3 mb-3 border border-solid rounded px-2 flex h-6 items-center relative top-2"
                style="border-color: #cccccc; backgroundcolor: #f2f2f2"
              >
                <div class="h-6 leading-6">{{ item && item.dictLabel }}</div>
                <div @click="deletBusiness(item)" class="cursor-pointer">
                  <i class="el-icon-close"></i>
                </div>
              </div>
              <el-button
                style="height: 30px"
                class="relative top-1"
                type="primary"
                size="mini"
                icon="el-icon-plus"
                @click="openBusinessType = true"
                >添加</el-button
              >
            </div>
          </div> -->
          <ProductCompanyClassification :myForm="myForm" />
          <div>
            可选择多个支持产品分类，如果找不到所需的产品分类，请联系技术部进行添加
          </div>
        </el-form-item>
        <!-- <el-form-item
          label="是否内部公司"
          prop="isInside"
          v-hasPermi="['companyInformation:inside']"
        >
          <el-select
            v-model="myForm.isInside"
            placeholder=""
            clearable
            @change="changeIsInside"
          >
            <el-option
              v-for="item in isInternalListEdit"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <div class="leading-6">
            内部公司可以用于创建组织架构，OA流程分类，财务模块账套等
          </div>
        </el-form-item> -->
        <el-form-item label="启用状态:" props="status">
          <el-switch
            v-model="myForm.status"
            active-text="启用"
            active-value="0"
            inactive-value="1"
          >
          </el-switch>
        </el-form-item>
      </el-form>
      <div
        style="color: red; padding-left: 120px"
        class="text-left w-1/2 m-auto"
      >
        <div class="leading-6" v-show="same.isShowCompanyName">
          已经存在相同公司名称的公司，建议优先编辑并使用旧的公司信息
        </div>
        <div class="leading-6" v-show="same.isShowCompanyShortName">
          已经存在相同公司简称的公司，建议优先编辑并使用旧的公司信息
        </div>
        <div class="leading-6" v-show="same.isShowCompanyCode">
          已经存在相同公司编码的公司，建议优先编辑并使用旧的公司信息
        </div>
      </div>
    </div>
    <div
      class="flex justify-center fixed bottom-0 bg-white z-10 w-full pb-2"
      style="left: 130px"
    >
      <el-button @click="onSubmit" type="primary" class="mr-3"
        >下一步</el-button
      >
      <!-- <div v-show="type == 'update'" class="mx-3">
        <el-button type="danger" @click="openDeletClick">删除</el-button>
      </div> -->
      <el-button @click="cancel" class="ml-3">关闭</el-button>
    </div>
    <DetailDialogCompanyType
      v-model="openCompanyType"
      @on-save-success="companyTypeCallBack"
      :selectCompany="myForm.companyTypeMappingList"
    />
    <DetailDialogBusinessType
      v-model="openBusinessType"
      @on-save-success="businessTypeCallBack"
      :selectBusiness="myForm.companyBusinessTypeMappingList"
    />
    <DetailDialogDelet
      v-if="openDelet"
      @close="openDelet = false"
      @on-save-success="deletCallBack"
      :projectCount="myForm.projectCount"
      :id="deletId"
    />
  </div>
</template>

<script>
import { getDeploy, getDataByTemplName } from "@/api/oa/deploy";

import XEUtils from "xe-utils";
import config from "@/views/businessInformation/companyInformation/components/config";
import DetailDialogCompanyType from "./components/DetailDialogCompanyType.vue";
import DetailDialogBusinessType from "./components/DetailDialogBusinessType.vue";
import DetailDialogDelet from "./components/DetailDialogDelet.vue";
import {
  addSystemCompany,
  updateSystemCompany,
  checkCompanyName,
} from "@/api/businessInformation/companyInformation";

export default {
  components: {
    DetailDialogCompanyType,
    DetailDialogBusinessType,
    DetailDialogDelet,
  },
  props: {
    form: {
      type: Object,
      required: true,
      default: () => ({}),
    },

    type: {
      type: [String, Number],
      required: true,
      default: "add",
    },
    id: {
      type: [String, Number],
      required: true,
      default: "",
    },
  },

  data() {
    return {
      oldForm: null,
      ...config,
      myForm: {},
      same: {
        isShowCompanyName: false,
        isShowCompanyShortName: false,
        isShowCompanyCode: false,
      },
      openCompanyType: false,
      openBusinessType: false,
      openDelet: false,
      deletId: "",
    };
  },
  watch: {
    form: {
      handler(val) {
        this.oldForm = XEUtils.clone(val, true);
        this.myForm = XEUtils.clone(val, true);

        this.handlerForm();
      },
      immediate: true,
    },
  },
  created() {
    this.init();
  },
  methods: {
    init() {},
    handlerForm() {
      if (this.type == "add") {
        this.$set(this.myForm, "isInside", "0");
        this.$set(this.myForm, "status", "0");
      }
      this.changeIsInside();
    },
    changeIsInside() {
      this.rules = this.rulesNoCode;
      //   this.myForm.isInside == 0 ? this.rulesNoCode : this.rulesCode;
      // this.$nextTick(() => {
      //   if (this.myForm.isInside == 0)
      //     this.$refs.form.clearValidate("companyCode");
      // });
    },
    companyTypeCallBack(value) {
      const companyTypeMappingList = this.myForm.companyTypeMappingList
        ? this.myForm.companyTypeMappingList.concat(value)
        : [...value];
      this.$set(this.myForm, "companyTypeMappingList", companyTypeMappingList);
      this.$refs.form.validateField("companyTypeMappingList");
    },
    deletCompany(value) {
      this.myForm.companyTypeMappingList =
        this.myForm.companyTypeMappingList.filter(
          (item) => item.dictLabel !== value.dictLabel
        );
      this.$refs.form.validateField("companyTypeMappingList");
    },
    businessTypeCallBack(value) {
      const companyBusinessTypeMappingList = this.myForm
        .companyBusinessTypeMappingList
        ? this.myForm.companyBusinessTypeMappingList.concat(value)
        : [...value];
      this.$set(
        this.myForm,
        "companyBusinessTypeMappingList",
        companyBusinessTypeMappingList
      );
      this.$refs.form.validateField("companyBusinessTypeMappingList");
    },
    deletBusiness(value) {
      this.myForm.companyBusinessTypeMappingList =
        this.myForm.companyBusinessTypeMappingList.filter(
          (item) => item.dictLabel !== value.dictLabel
        );
      this.$refs.form.validateField("companyBusinessTypeMappingList");
    },
    openDeletClick() {
      this.deletId = this.myForm.id;
      this.openDelet = true;
    },
    deletCallBack() {
      this.handlerParams();
      sessionStorage.setItem("delCompanyData", JSON.stringify(this.myForm));
      getDataByTemplName({
        templateName: "业务信息配置-删除外部公司信息申请",
      }).then((res) => {
        if (res.code == 200) {
          this.$router.push({
            path: "/oaWork/updateProcessForm",
            query: {
              templateId: res.templateId,
              classificationId: res.classificationId,
              companyId: res.companyId,
              delCompany: true,
            },
          });
        }
      });
      // this.cancel();
    },
    handlerParams() {
      this.myForm.companyTypeCodes = this.myForm.companyTypeMappingList.map(
        (item) => item.dictCode
      );
      if (this.myForm.companyBusinessTypeMappingList) {
        this.myForm.companyBusinessTypeCodes =
          this.myForm.companyBusinessTypeMappingList.map(
            (item) => item.dictCode
          );
      }
    },
    onSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          const { data } = await checkCompanyName(this.myForm);
          this.same = data;
          if (
            !this.same.isShowCompanyName &&
            !this.same.isShowCompanyShortName &&
            !this.same.isShowCompanyCode
          ) {
            this.handlerParams();
            console.log(this.myForm);
            if (this.type != "add") {
              this.oldForm.companyTypeCodes =
                this.oldForm.companyTypeMappingList.map(
                  (item) => item.dictCode
                );
              if (this.oldForm.companyBusinessTypeMappingList) {
                this.oldForm.companyBusinessTypeCodes =
                  this.oldForm.companyBusinessTypeMappingList.map(
                    (item) => item.dictCode
                  );
              }
              let data = {
                oldData: this.oldForm,
                newData: this.myForm,
              };
              sessionStorage.setItem("editCompanyData", JSON.stringify(data));
              getDataByTemplName({
                templateName: "业务信息配置-修改外部公司信息申请",
              }).then((res) => {
                if (res.code == 200) {
                  this.$router.push({
                    path: "/oaWork/updateProcessForm",
                    query: {
                      templateId: res.templateId,
                      classificationId: res.classificationId,
                      companyId: res.companyId,
                      editCompany: true,
                    },
                  });
                }
              });
            } else {
              sessionStorage.setItem(
                "addCompanyData",
                JSON.stringify(this.myForm)
              );
              getDataByTemplName({
                templateName: "业务信息配置-新增外部公司信息申请",
              }).then((res) => {
                if (res.code == 200) {
                  this.$router.push({
                    path: "/oaWork/updateProcessForm",
                    query: {
                      templateId: res.templateId,
                      classificationId: res.classificationId,
                      companyId: res.companyId,
                      addCompany: true,
                    },
                  });
                }
              });
            }
            this.$parent.addUpdateCallBack();
          }
        }
      });
    },

    cancel() {
      this.$parent.addUpdateCallBack();
    },
  },
};
</script>
