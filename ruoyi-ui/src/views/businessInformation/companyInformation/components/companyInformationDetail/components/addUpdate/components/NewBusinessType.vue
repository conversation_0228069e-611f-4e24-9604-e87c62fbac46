<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      title="创建新的公司类型"
      :visible.sync="innerValue"
      width="350px"
      @open="handlerOpen"
    >
      <div >
        <el-input placeholder="请输入" v-model="companyType"></el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="innerValue = false" class="ml-3">取消</el-button>
        <el-button @click="onSubmit" type="primary" class="ml-3" :disabled="!Boolean(companyType)">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>

import vModelMixin from "@/mixin/v-model";
import {
companyInsertCompanyType,
companyCheckCompanyType
} from "@/api/businessInformation/companyInformation";
export default {
  mixins: [vModelMixin],
  props: {
    uuId:String
  },
  data() {
    return {
      companyType:''

    };
  },
  watch: {},
  mounted() {},
  methods: {
    async handlerOpen(){
      this.companyType="";
    },
    async onSubmit(){
      const {data}= await companyCheckCompanyType({dictLabel:this.companyType});
      if(data.laberError){
        this.$message.error(data.laberError);
        return;
      }
       companyInsertCompanyType({dictLabel:this.companyType,addSource: "xmgl",addUuid:this.uuId?this.uuId:null}).then(res=>{
        this.innerValue=false;
      this.$emit('on-save-success',res.data);
       })
     
    }
 
  },
};
</script>

