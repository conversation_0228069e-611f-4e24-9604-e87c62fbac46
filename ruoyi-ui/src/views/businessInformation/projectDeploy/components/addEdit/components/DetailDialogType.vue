<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :title="title"
      :visible.sync="innerValue"
      width="350px"
      @open="handlerOpen"
    >
      <div>
        <el-checkbox-group v-model="checkList">
          <el-checkbox
            :disabled="mySelectCompany.includes(item.dictLabel)"
            v-for="(item, index) in typeList"
            :key="index"
            :label="item.dictValue"
            class="block my-4"
            >{{ item.dictLabel }}</el-checkbox
          >
            </el-checkbox-group>

      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="innerValue = false" class="ml-3">取消</el-button>
        <el-button type="primary" class="ml-3" @click="submit">添加</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getDicts } from "@/api/system/dict/data";
import { getCompanyBusinessTypeList } from "@/api/businessInformation/companyInformation";
import config from "../../config";

import vModelMixin from "@/mixin/v-model";
export default {
  mixins: [vModelMixin],
  props: {
    selectBusiness: {
      type: Array,
      required: true,
      default: () => [],
    },
    bussOrProject: {
      type: String,
      required: true,
      default: "",
    },
  },
  computed: {
    mySelectCompany() {
      return this.selectBusiness.map((item) => item.dictLabel);
    },
  },
  data() {
    return {
      ...config,
      typeList: [],
      checkList: [],
      title: "",
    };
  },
  watch: {},
  mounted() {},
  methods: {
    async handlerOpen() {
      this.checkList=[];
      if (this.bussOrProject == "project") {
        const { data } = await getDicts("project_type");
        this.typeList = data;
        this.title = "选择项目类型";
      } else if (this.bussOrProject == "business") {
        const { rows } = await getCompanyBusinessTypeList();
        this.typeList = rows;
        this.title = "选择业务类型";
      }
    },
    submit() {
      const data = this.typeList.filter(
        (item) => this.checkList.includes(item.dictValue) 
      );
      this.$emit("on-save-success", data);
      this.innerValue = false;
    },
  },
};
</script>

