<template>
  <div>
    <el-dialog
      title="编辑内容"
      :visible.sync="dialogVisible"
      width="1000px"
      :before-close="handleClose"
      @close="handleClose"
    >
      <ContrastDetailNewTable
        :data="datas"
        ref="ContrastDetailNewTable"
      ></ContrastDetailNewTable>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import XEUtils from "xe-utils";

export default {
  provide() {
    return {
      contrast: this.contrast,
      arraryToStringItem: this.arraryToStringItem,
      dictionary: {
        isEnable: { Y: "开启", N: "关闭" },
      },
    };
  },
  props: {
    detail: Object,
    companytypeList: Array,
    companyList: Object,
  },

  data() {
    return {
      dialogVisible: true,
      datas: {},
      contrast: {}, //对比表格映射的字段
      arraryToStringItem: {},
    };
  },
  mounted() {
    this.handleForm();
    this.getColoumn();
  },
  methods: {
    getColoumn() {
      let newObj = {};
      let oldObj = {};
      // 遍历 arr 数组
      this.$store.state.data.OPTION_MAP.select_name.forEach((item) => {
        let key = item.value + "List"; // 根据 value 构建匹配的键
        if (this.datas.newData[key]) {
          newObj[key] = item.label;
        }
        if (this.datas.oldData[key]) {
          oldObj[key] = item.label;
        }
      });
      let longerObj =
        Object.keys(newObj).length >= Object.keys(oldObj).length
          ? newObj
          : oldObj;
      this.contrast = {
        projectName: "项目名称",
        ...longerObj,
        otherUnitList: "其他公司",
        projectTypeList: "项目类型",
        businessTypeList: "产品分类",
        isEnable: "启用状态",
      };
      const arraryToStringItems = XEUtils.clone(longerObj, true);
      Object.keys(arraryToStringItems).forEach((item) => {
        arraryToStringItems[item] = "unitShortName";
      });
      this.arraryToStringItem = {
        ...arraryToStringItems,
        otherUnitList: "unitShortName",
        projectTypeList: "typeName",
        businessTypeList: "typeName",
      };
      this.$nextTick(() => {
        this.$refs.ContrastDetailNewTable.getRecordList(
          this.datas.newData,
          this.datas.oldData,
          this.contrast,
          this.arraryToStringItem
        );
      });
    },
    handleForm() {
      this.datas = XEUtils.clone(this.detail, true);
      let oldData = JSON.parse(this.datas.oaApplyRecordsOldData);
      let newData = JSON.parse(this.datas.oaApplyRecordsNewData);

      this.datas.oldData = {
        ...oldData,
        ...oldData.tableList,
      };
      this.datas.newData = {
        ...newData,
        ...newData.tableList,
      };
      this.datas.oldInfo = JSON.stringify(this.datas.oldData);
      this.datas.newInfo = JSON.stringify(this.datas.newData);
    },
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="less" scoped>
</style>