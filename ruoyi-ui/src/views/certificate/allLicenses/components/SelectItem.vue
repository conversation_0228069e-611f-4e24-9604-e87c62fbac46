<template>
  <div>
    <el-dialog
      :title="'已选择' + tableData.length + '条'"
      :visible.sync="dialogVisible"
      width="900px"
      :before-close="handleClose"
    >
      <el-table
        :data="tableData"
        style="width: 100%; margin-top: 16px; margin-left: 4px"
      >
        <el-table-column
            align="center"
            min-width="250"
            show-overflow-tooltip=""
            prop="licenseName"
            label="证照名称"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="see(scope.row)">{{
                scope.row.licenseName
              }}</el-button>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="issuingTime"
            label="发证日期"
            width="100"
          />
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="unitName"
            label="发证单位"
            width="100"
          />
          
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="pertainCompanyName"
            label="状态"
            width="140"
          >
            <template #default="{ row }">
                {{ dict.label.license_status[row.licenseStatus] }}
              
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="custodyName"
            label="证照保管人"
            width="120"
          />
        
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="version"
            label="证照版本"
            width="120"
          />
          
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          fixed="right"
          label="操作"
        >
          <template slot-scope="scope">
            <el-button @click="del(scope.row)" type="text" size="small"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </span>
    </el-dialog>
    <addItem
      :editData="editData"
      :seeType="seeType"
      v-if="addItemType"
      @close="addItemType = false"
    />
  </div>
</template>

<script>
import addItem from "./addItem.vue";

export default {
  props: {
    multipleSelection: Array,
  },
  components: {
    addItem,
  },
    dicts: ["license_status"],

  data() {
    return {
      dialogVisible: true,
      tableData: [],
      seeType: false,
      editData: null,
      addItemType: false,
    };
  },
  mounted() {
    this.tableData = JSON.parse(JSON.stringify(this.multipleSelection));
    console.log(this.tableData);
  },
  methods: {
    see(v) {
      this.seeType = true;
      this.editData = { ...v };
      this.addItemType = true;
    },
    submit() {
      this.$emit("confirm", this.tableData);
    },
    del(v) {
      this.tableData.map((item, index) => {
        if (item.id == v.id) {
          this.tableData.splice(index, 1);
        }
      });
    },
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style>
</style>