<template>
  <div>
    <div class="flex font-bold text-xl text-black p-2">
      <div>{{ tableData.projectName }}</div>
      <div class="ml-10">{{ tableData.year }}年度</div>
    </div>
    <el-descriptions class="margin-top" :column="1" border>
      <el-descriptions-item>
        <template slot="label"> 修改内容 </template>
        <ComparisonTable
          :columns="columns"
          :configList="configList"
          class="pb-5"
        />
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label"> 修改原因说明： </template>
        <el-input
          type="textarea"
          disabled
          :rows="3"
          v-model="tableData.editInfo"
        >
        </el-input>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>
export default {
  name: "Table",
  props: {
    tableData: {
      type: Object,
      required: true,
      default: () => ({}),
    },
  },
  data() {
    return {
      columns: Object.freeze([
        { label: "所属月份", prop: "months", width: "60px" },
        {
          label: "分配项目业绩修改前(万元)",
          prop: "distributionIndexBefore",
        },
        { label: "分配项目业绩修改后(万元)", prop: "distributionIndexAfter" },
        { label: "自拓项目业绩修改前(万元)", prop: "extensionIndexBefore" },
        { label: "自拓项目业绩修改后(万元)", prop: "extensionIndexAfter" },
      ]),
      configList: [
        {
          months: "一月",
          monthsIndex: "m1",
          distributionIndexBefore: "0",
          distributionIndexAfter: "0",
          extensionIndexBefore: "0",
          extensionIndexAfter: "0",
        },
        {
          months: "二月",
          monthsIndex: "m2",
          distributionIndexBefore: "0",
          distributionIndexAfter: "0",
          extensionIndexBefore: "0",
          extensionIndexAfter: "0",
        },
        {
          months: "三月",
          monthsIndex: "m3",
          distributionIndexBefore: "0",
          distributionIndexAfter: "0",
          extensionIndexBefore: "0",
          extensionIndexAfter: "0",
        },
        {
          months: "四月",
          monthsIndex: "m4",
          distributionIndexBefore: "0",
          distributionIndexAfter: "0",
          extensionIndexBefore: "0",
          extensionIndexAfter: "0",
        },
        {
          months: "五月",
          monthsIndex: "m5",
          distributionIndexBefore: "0",
          distributionIndexAfter: "0",
          extensionIndexBefore: "0",
          extensionIndexAfter: "0",
        },
        {
          months: "六月",
          monthsIndex: "m6",
          distributionIndexBefore: "0",
          distributionIndexAfter: "0",
          extensionIndexBefore: "0",
          extensionIndexAfter: "0",
        },
        {
          months: "七月",
          monthsIndex: "m7",
          distributionIndexBefore: "0",
          distributionIndexAfter: "0",
          extensionIndexBefore: "0",
          extensionIndexAfter: "0",
        },
        {
          months: "八月",
          monthsIndex: "m8",
          distributionIndexBefore: "0",
          distributionIndexAfter: "0",
          extensionIndexBefore: "0",
          extensionIndexAfter: "0",
        },
        {
          months: "九月",
          monthsIndex: "m9",
          distributionIndexBefore: "0",
          distributionIndexAfter: "0",
          extensionIndexBefore: "0",
          extensionIndexAfter: "0",
        },
        {
          months: "十月",
          monthsIndex: "m10",
          distributionIndexBefore: "0",
          distributionIndexAfter: "0",
          extensionIndexBefore: "0",
          extensionIndexAfter: "0",
        },
        {
          months: "十一月",
          monthsIndex: "m11",
          distributionIndexBefore: "0",
          distributionIndexAfter: "0",
          extensionIndexBefore: "0",
          extensionIndexAfter: "0",
        },
        {
          months: "十二月",
          monthsIndex: "m12",
          distributionIndexBefore: "0",
          distributionIndexAfter: "0",
          extensionIndexBefore: "0",
          extensionIndexAfter: "0",
        },
      ],
    };
  },

  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.configList.forEach((item) => {
        item.distributionIndexBefore =
          this.tableData.oldData[`${item.monthsIndex}DistributionIndex`];
        item.distributionIndexAfter =
          this.tableData.newData[`${item.monthsIndex}DistributionIndex`];
        item.extensionIndexBefore =
          this.tableData.oldData[`${item.monthsIndex}ExtensionIndex`];
        item.extensionIndexAfter =
          this.tableData.newData[`${item.monthsIndex}ExtensionIndex`];
        if (
          item.distributionIndexBefore != item.distributionIndexAfter ||
          item.extensionIndexBefore != item.extensionIndexAfter
        ) {
          item.diff = true;
        }
      });

      
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-descriptions-item__label {
  width: 200px;
}
</style>
