<template>
  <div class="content">
    <el-form ref="form" :model="form" label-width="130px">
      <div class="flex">
        <el-form-item label="员工姓名" class="form-item">
          <el-input v-model="form.name" disabled></el-input>
        </el-form-item>
        <el-form-item label="入职申请编号" class="form-item">
          <el-input v-model="form.onboardingCode" disabled></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item
          label="入职公司"
          class="form-item"
          prop="onboardingCompany"
        >
          <el-select
            v-model="form.onboardingCompany"
            placeholder="请选择入职公司"
            disabled
            style="width:400px"
          >
            <el-option
              v-for="dict in unitListEnableList"
              :key="dict.unitId"
              :label="dict.unitName"
              :value="dict.unitId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="入职部门" class="form-item">
          <el-input v-model="form.onboardingDeptName" disabled></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item label="入职岗位" class="form-item">
          <el-input v-model="form.onboardingPostName" disabled></el-input>
        </el-form-item>

        <el-form-item label="入职职级" class="form-item">
          <el-select
            v-model="form.onboardingRank"
            placeholder="请选择入职职级"
            disabled
          >
            <el-option
              v-for="dict in dict.type.entry_rank"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item label="身份证号码" class="form-item">
          <el-input v-model="form.idCard" disabled></el-input>
        </el-form-item>
        <el-form-item label="出生日期" class="form-item">
          <el-input v-model="form.birthday" disabled></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item label="手机号" class="form-item">
          <el-input v-model="form.phoneNum" disabled></el-input>
        </el-form-item>
        <el-form-item label="性别" class="form-item" prop="sex">
          <el-select v-model="form.sex" placeholder="请选择性别" disabled>
            <el-option
              v-for="dict in dict.type.sys_user_sex"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item label="籍贯" class="form-item">
          <el-input v-model="form.hometown" disabled></el-input>
        </el-form-item>
        <el-form-item label="现住址" class="form-item">
          <el-input v-model="form.currentAddress" disabled></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item label="入职日期" class="form-item">
          <el-input v-model="form.onboardingTime" disabled></el-input>
        </el-form-item>
        <el-form-item label="办公场地" class="form-item">
          <el-input v-model="form.officeSpace" disabled></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item label="参加工作时间" class="form-item">
          <el-input v-model="form.workTime" disabled></el-input>
        </el-form-item>
        <el-form-item label="人员类别" class="form-item">
          <el-select
            v-model="form.personnelType"
            placeholder="请选择人员类别"
            disabled
          >
            <el-option
              v-for="dict in dict.type.personnel_category"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item label="试用期期限(月)" class="form-item">
          <el-input v-model="form.probationMonths" disabled></el-input>
        </el-form-item>
        <el-form-item label="预计转正日期" class="form-item">
          <el-input v-model="form.formalTime" disabled></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item label="转正薪酬(元)" class="form-item">
          <el-input v-model="form.formalSalary" disabled></el-input>
        </el-form-item>
        <el-form-item label="试用期薪酬(元)" class="form-item">
          <el-input v-model="form.probationSalary" disabled></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item label="直属上级" class="form-item">
          <el-input v-model="form.leaderName" disabled></el-input>
        </el-form-item>
        <el-form-item label="政治面貌" class="form-item">
          <el-select
            v-model="form.politicalLandscape"
            placeholder="请选择政治面貌"
            disabled
          >
            <el-option
              v-for="dict in dict.type.political_outlook"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </div>
      <div class="flex">
         <el-form-item label="所在公司上级" class="form-item">
          <el-input v-model="form.localLeaderName" disabled></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item label="系统登录名" class="form-item">
          <el-input v-model="form.sysName" disabled></el-input>
        </el-form-item>
        <el-form-item label="系统初始密码" class="form-item">
          <el-input v-model="form.initialPassword" disabled></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item label="开户行" class="form-item">
          <el-input v-model="form.openingBank" disabled></el-input>
        </el-form-item>
        <el-form-item label="工资账号" class="form-item">
          <el-input v-model="form.salaryAccount" disabled></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item label="门禁卡申领" class="form-item">
          <el-select v-model="form.accessCard" placeholder="请选择门禁卡申领"  :disabled="disabled">
            <el-option
              v-for="dict in accessCardList"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
             
            />
          </el-select>
        </el-form-item>
        <el-form-item label="办公用品领用" class="form-item">
          <el-input
            v-model="form.officeSupplies"
             :disabled="disabled"
            placeholder="请输入办公用品领用"
          ></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item label="文件柜领用" class="form-item">
          <el-input
            v-model="form.fileCabinet"
             :disabled="disabled"
            placeholder="请输入文件柜领用"
          ></el-input>
        </el-form-item>
         <el-form-item label="文件柜编号" class="form-item">
          <el-input
            v-model="form.fileCabinetnum"
             :disabled="disabled"
            placeholder="请输入文件柜编号"
          ></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item label="名片印制" class="form-item">
          <el-input
            v-model="form.cardPrinting"
             :disabled="disabled"
            placeholder="请输入名片印制"
          ></el-input>
        </el-form-item>
          <el-form-item label="企业邮箱" class="form-item">
          <el-input
            v-model="form.enterpriseEmail"
             :disabled="disabled"
            placeholder="请输入企业邮箱"
          ></el-input>
        </el-form-item>
       
      </div>
      <div class="flex">
        <el-form-item label="阿里云账号" class="form-item">
          <el-input
            v-model="form.aliAccount"
             :disabled="disabled"
            placeholder="请输入阿里云账号"
          ></el-input>
        </el-form-item>
        <el-form-item label="考勤机录入" class="form-item">
          <el-select
            v-model="form.attendanceEntry"
             :disabled="disabled"
            placeholder="请选择考勤机录入"
          >
            <el-option
              v-for="dict in attendanceEntryList"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </div>
      <div class="flex-one">
        <el-form-item label="附件上传">
          <el-upload disabled :file-list="fileList" action="#"   :on-preview ="openFile">
            <el-button disabled
              ><i class="el-icon-upload2"></i>上传附件</el-button
            >
          </el-upload>
        </el-form-item>
      </div>
      <div class="flex-one">
        <el-form-item label="工作职责">
          <el-input
            type="textarea"
            v-model="form.duty"
            maxlength="600"
            disabled
          ></el-input>
        </el-form-item>
      </div>
      <div class="flex-one">
        <el-form-item label="备注">
          <el-input
            type="textarea"
            v-model="form.remark"
            maxlength="600"
            disabled
          ></el-input>
        </el-form-item>
      </div>
    </el-form>
      <el-image
      ref="previewImg"
      v-show="false"
      :src="photoUrl"
      :preview-src-list="imagePreviewUrls"
    />
  </div>
</template>

<script>
import { getUnitListEnable } from "@/api/system/unit";
import privew from "@/mixin/privew";


export default {
  dicts: [
    "entry_rank",
    "sys_user_sex",
    "personnel_category",
    "political_outlook",
  ],
    mixins: [privew],

  props: {
    form: {
      type: Object,
      required: true,
      default: () => ({
        file:[]
      }),
    },
     disabled: {
      type: Boolean,
      default:true
    },
  },
  data() {
    return {
      fileList: [],
      unitListEnableList: [],
      directSuperiorList: [],
      accessCardList: Object.freeze([
        { label: "是", value: "0" },
        { label: "否", value: "1" },
      ]),

      attendanceEntryList: Object.freeze([
        { label: "是", value: "0" },
        { label: "否", value: "1" },
      ]),
    };
  },
  watch: {
    "form.files": {
      handler(val) {
          if(this.fileList.length) return;
          if (val && val.length) {
          this.fileList = val.map((item) => {
            return {
              name: item.fileName,
              id: item.id,
              url: process.env.VUE_APP_BASE_API + item.fileUrl,
              downLoadUrl:item.fileUrl

            };
          });
        }
      },
      immediate: true,
    },
  },
  mounted() {
    this.init();
  },
  methods: {
    async init() {
      this.getUnitListEnable();
    },
      openFile(value){
      this.handlePreview(value);

    },
    async getUnitListEnable() {
      const { data } = await getUnitListEnable();
      this.unitListEnableList = data;
    },

    
  },
};
</script>

<style lang="less" scoped>
.content {
  padding-right: 20px;
  .flex {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    .form-item {
      width: 48%;
    }
  }
  .flex-one {
    width: 70%;
    display: flex;
    margin-bottom: 20px;
    ::v-deep .el-form-item {
      width: 100%;
    }
  }
}
</style>