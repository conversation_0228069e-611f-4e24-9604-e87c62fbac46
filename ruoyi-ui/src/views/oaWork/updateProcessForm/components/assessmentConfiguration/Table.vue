<template>
  <div>
    <div v-for="(item, index) in myTableData" :key="index">
      <div
        class="flex font-bold text-xl text-black py-4 pl-4"
        style="background: #f8f8f9; border: 1px solid rgba(224, 231, 237, 1)"
      >
        <div @click="changeShow(item)" class="flex cursor-pointer">
          <div>{{ item.companyShortName }}:</div>
          <i
            class="el-icon-caret-bottom relative top-1 mr-10"
            v-if="item.show"
          ></i>
          <i class="el-icon-caret-top relative top-1 mr-10" v-else></i>
          <div class="ml-10">{{ item.year }}年度</div>
        </div>
      </div>
      <div :ref="item.ref" class="overflow-hidden h-0">
        <div class="p-4">
          <div class="pl-1 mb-1 font-bold text-black">部门考核配置占比</div>
          <div>
            <MyTable
              :columns="columnsDept"
              :source="item.deptSlaveListShow"
              class="w-2/3"
            />
            <pagination
              v-show="item.totalDept > 10"
              :auto-scroll="false"
              :total="item.totalDept"
              :page.sync="item.pageNumDept"
              :page-sizes="[10, 20, 50, 100]"
              :limit.sync="item.pageSizeDept"
              @pagination="getListDept(item)"
            />
          </div>
          <el-divider class="my-10"></el-divider>

          <div class="pl-1 mb-1 font-bold text-black">用户考核配置占比</div>
          <div>
            <MyTable
              :columns="columnsUser"
              :source="item.userSlaveListShow"
              class="w-2/3"
            />
            <pagination
              v-show="item.totalUser > 10"
              :auto-scroll="false"
              :total="item.totalUser"
              :page.sync="item.pageNumUser"
              :page-sizes="[10, 20, 50, 100]"
              :limit.sync="item.pageSizeUser"
              @pagination="getListUser(item)"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import XEUtils from "xe-utils";
export default {
  name: "Table",
  props: {
    tableData: {
      type: Array,
      required: true,
      default: () => {
        return [];
      },
    },
  },

  data() {
    return {
      columnsDept: Object.freeze([
        {
          label: "部门",
          prop: "deptName",
          minWidth: "150px",
        },

        { label: "绩效工资占比(%)", prop: "achievementWagesProportion" , width: "180px",},
        { label: "分配占比(%)", prop: "distributionProportion" , width: "180px"},
        { label: "自拓占比(%)", prop: "extensionProportion" , width: "180px"},
      ]),
      columnsUser: Object.freeze([
        {
          label: "用户姓名",
          prop: "nickName",
          minWidth: "150px",
        },
        {
          label: "部门",
          prop: "deptName",
          minWidth: "150px",
        },

        { label: "绩效工资占比(%)", prop: "achievementWagesProportion", width: "150px" },
        { label: "分配占比(%)", prop: "distributionProportion" , width: "150px"},
        { label: "自拓占比(%)", prop: "extensionProportion", width: "150px" },
      ]),
      myTableData: [],
    };
  },

  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.getTable();
    },
    getTable() {
      this.myTableData = XEUtils.clone(this.tableData, true);
      this.handleTable();
    },
    handleTable() {
      this.myTableData.forEach((item) => {
        this.$set(item, "show", false);
        this.$set(item, "ref", `assess${item.companyId}`);
        this.$set(item, "totalDept", item.deptSlaveList.length);
        this.$set(item, "pageNumDept", 1);
        this.$set(item, "pageSizeDept", 10);
        this.getListDept(item);
        this.$set(item, "totalUser", item.userSlaveList.length);
        this.$set(item, "pageNumUser", 1);
        this.$set(item, "pageSizeUser", 10);
        this.getListUser(item);
      });
    },
    getListDept(item) {
      const start = (item.pageNumDept - 1) * item.pageSizeDept;
      const end = start + item.pageSizeDept;
      this.$set(
        item,
        "deptSlaveListShow",
        item.deptSlaveList.slice(start, end)
      );
    },
    getListUser(item) {
      const start = (item.pageNumUser - 1) * item.pageSizeUser;
      const end = start + item.pageSizeUser;
      this.$set(
        item,
        "userSlaveListShow",
        item.userSlaveList.slice(start, end)
      );
    },
    changeShow(item) {
      item.show = !item.show;
      const dom = this.$refs[item.ref][0];
      if (item.show) {
        dom.style.transition = "none";
        dom.style.height = "auto";
        const height = dom.offsetHeight;
        dom.style.height = 0;
        dom.offsetHeight;
        dom.style.transition = ".5s";
        dom.style.height = height + "px";
      } else {
        dom.style.transition = ".5s";
        dom.style.height = 0;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .pagination-container .el-pagination {
  left: 0;
}
</style>