export default {
  settlementStatusObj: Object.freeze({
    1: "未结清",
    2: "已结清",
  }),
  formColumns: [
    {
      label: "结算机构",
      prop: "settlementInstitutionId",
      type: "select",
      filterable: true,
      options: [],
      dataProp:{value:'id',label:'mechanismShortName'},
      placeholder: "请选择结算机构",
    },

    {
      label: "对账日",
      prop: "reconciliationDate",
      type: "datePicker",
    },
  ],
  columns: [
    { label: "结算机构", key: "settlementInstitutionName", minWidth: "200px" },

    { label: "对账日", prop: "reconciliationDate", minWidth: "150px" },
    { label: "结算状态", prop: "settlementStatusLabel", minWidth: "150px" },
    { label: "财务收款总额", prop: "totalAmountCollected", minWidth: "150px" },
    {
      label: "平台核销账单金额",
      prop: "totalRepaymentAmount",
      minWidth: "200px",
    },
    { label: "创建人", prop: "createByName", minWidth: "150px" },
    { label: "创建时间", prop: "createTime", minWidth: "200px" },

    { label: "操作", key: "operate", minWidth: "100" },
  ],
};
