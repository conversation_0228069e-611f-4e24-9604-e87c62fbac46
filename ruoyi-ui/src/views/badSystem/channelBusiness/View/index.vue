<template>
  <div class="p-5">
    <el-descriptions title="" :column="1" :colon="false" direction="vertical">
      <el-descriptions-item label="渠道业务对账单">{{
        myForm.reconciliationCode
      }}</el-descriptions-item>
    </el-descriptions>
    <el-divider></el-divider>
    <el-descriptions title="" :column="3" :colon="false" direction="vertical">
      <el-descriptions-item
        ><template slot="label">
          <span style="color: red">*</span> 结算机构 </template
        >{{ myForm.settlementInstitutionName }}</el-descriptions-item
      >
      <el-descriptions-item
        ><template slot="label">
          <span style="color: red">*</span> 对账日 </template
        >{{ myForm.reconciliationDate }}</el-descriptions-item
      >
      <el-descriptions-item label="校验差异状态">{{
        differentialStateObj[myForm.verifyDifferenceStatus]
      }}</el-descriptions-item>
    </el-descriptions>
    <el-divider></el-divider>
    <el-descriptions title="" :column="3" :colon="false" direction="vertical">
      <el-descriptions-item label="财务收款总额">{{
        myForm.totalAmountCollected
      }}</el-descriptions-item>
      <el-descriptions-item label="平台核销账单金额">{{
        myForm.totalRepaymentAmount
      }}</el-descriptions-item>
      <el-descriptions-item label="差额">{{
        myForm.differenceAmount
      }}</el-descriptions-item>
    </el-descriptions>
    <el-divider></el-divider>

    <el-descriptions title="" :column="3" :colon="false" direction="vertical">
      <el-descriptions-item label="审批状态">{{
        approveStatusObj[myForm.status]
      }}</el-descriptions-item>
      <el-descriptions-item label="结算状态">{{
        settlementObj[myForm.settlementStatus]
      }}</el-descriptions-item>
    </el-descriptions>
    <el-divider></el-divider>
    <el-tabs v-model="currentTab" type="card">
      <el-tab-pane label="财务收款单" name="financialReceipt"></el-tab-pane>
      <el-tab-pane
        label="委后还款明细"
        name="repaymentDetailsAfter"
      ></el-tab-pane>
    </el-tabs>
    <MyTable
      :showIndex="true"
      :columns="
        currentTab == 'financialReceipt' ? columnsFinance : columnsAfter
      "
      :source="
        currentTab == 'financialReceipt'
          ? myForm.collectionList
          : myForm.repaymentList
      "
    >
    </MyTable>
    <InBody>
      <div
        class="text-center fixed bottom-0 bg-white z-10 pb-2"
        style="width: calc(100% - 260px); left: 260px"
        v-show="!proccess"
      >
        <el-button
          @click="initiate"
          type="primary"
          v-show="
            myForm.status != 2 &&
            myForm.status != 3 &&
            myForm.verifyDifferenceStatus == '2' &&
            myForm.differenceAmount == '0元'
          "
          >发起审批流程</el-button
        >
        <!-- <el-button type="primary" v-show="myForm.status == 2">撤回</el-button> -->
        <el-button
          @click="cancel"
          type="primary"
          v-show="myForm.status != 2 && myForm.status != 3"
          >作废</el-button
        >
        <!-- <el-button
          @click="selectCompanyType = true"
          type="primary"
          v-show="myForm.status == 3"
          >重新发起</el-button
        > -->
        <el-button @click="cancels">取 消</el-button>
      </div>
    </InBody>
    <SelectCompany
      v-if="selectCompanyType"
      @close="closeCompany"
      @submit="submitCompany"
    />
  </div>
</template>

<script>
import XEUtils from "xe-utils";
import {
  getBusinessReconciliationById,
  getBlChannelBusinessFlowController,
  updateProcessChannelBusiness,
} from "@/api/badSystem/channelBusiness";

import config from "./components/config";
export default {
  name: "ChannelBusinessDetailView",
  props: {
    detailId: {
      type: [String, Number],
      default: "",
    },
    proccess: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      ...config,
      myForm: {
        collectionList: [],
        repaymentList: [],
      },
      currentTab: "financialReceipt",
      myDetailId: "",
      selectCompanyType: false,
    };
  },
  computed: {},
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.myDetailId = this.detailId || this.$route.params.id;
      this.getDetail();
    },
    async getDetail() {
      const { data } = await getBusinessReconciliationById(this.myDetailId);
      this.myForm = data;
      this.handlerForm();
    },
    handlerForm() {
      const addYuan = [
        "totalAmountCollected",
        "totalRepaymentAmount",
        "differenceAmount",
      ];
      addYuan.forEach((item) => {
        this.myForm[item] = this.myForm[item]
          ? this.myForm[item].toLocaleString("zh-CN") + "元"
          : "";
      });
      this.myForm.collectionList.forEach((item) => {
        item.amountCollected = item.amountCollected
          ? item.amountCollected.toLocaleString("zh-CN") + "元"
          : "";
      });
      this.myForm.repaymentList.forEach((item) => {
        item.repaymentAmount = item.repaymentAmount
          ? item.repaymentAmount.toLocaleString("zh-CN") + "元"
          : "";
      });
    },
    initiate() {
      if (this.myForm.processId && this.myForm.status == 6) {
        this.$router.push({
          path: "/oaWork/processFormView",
          query: {
            oid: this.myForm.processId,
            myActiviteType: true,
          },
        });
      } else if (this.myForm.processId) {
        this.$router.push({
          path: "/oaWork/processFormView",
          query: {
            oid: this.myForm.processId,
            businessId: this.myForm.processId,
            myActiviteType: true,
          },
        });
      } else {
        this.selectCompanyType = true;
      }
    },
    submitCompany(e) {
      getBlChannelBusinessFlowController({ companyId: e }).then((res) => {
        if (res.code == 200) {
          this.selectCompanyType = false;
          this.$router.push({
            path: "/oaWork/updateProcessForm",
            query: {
              templateId: res.templateId,
              classificationId: res.classificationId,
              companyId: res.companyId,
              channelBusinessDetailId: this.myDetailId,
            },
          });
        }
      });
    },
    closeCompany() {
      this.selectCompanyType = false;
    },

    async cancel() {
      const params = {
        id: this.myDetailId,
        status: "5",
      };
      await updateProcessChannelBusiness(params);
      this.$modal.msgSuccess("操作成功");
      this.$router.go(-1);
    },
    cancels() {
      this.$router.go(-1);
    },
  },
};
</script>
<style lang="less" scoped>
</style>
