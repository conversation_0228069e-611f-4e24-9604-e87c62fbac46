<template>
  <div class="p-5">
    <el-descriptions title="" :column="4" :colon="false" direction="vertical">
      <el-descriptions-item label="结算单编号">{{
        myForm.settlementDocumentCode
      }}</el-descriptions-item>
      <el-descriptions-item
        ><template slot="label">
          <span style="color: red">*</span> 结算主体 </template
        >{{ myForm.settlementEntityName }}</el-descriptions-item
      >
      <el-descriptions-item label="结算状态">{{
        caseStatusObj[myForm.settlementStatus]
      }}</el-descriptions-item>
      <el-descriptions-item label="审批状态">{{
        approveStatusObj[myForm.status]
      }}</el-descriptions-item>
    </el-descriptions>
    <el-divider></el-divider>

    <div class="text-lg font-bold my-2">业务对账单</div>
    <MyTable
      :columns="columnsAccountStatement"
      :source="myForm.financialSettlementReconciliationList"
      :showIndex="true"
      class="mb-5"
    >
    </MyTable>
    <el-descriptions title="" :column="3" :colon="false" direction="vertical">
      <el-descriptions-item label="本次结算金额">{{
        myForm.settlementAmount
      }}</el-descriptions-item>
      <el-descriptions-item label="财务收款总额">{{
        myForm.financialReceiptsAmount
      }}</el-descriptions-item>
      <el-descriptions-item label="剩余未结算金额">{{
        myForm.unsettledAmount
      }}</el-descriptions-item>
    </el-descriptions>
    <el-divider></el-divider>
    <div class="text-lg font-bold my-2">
      <span style="color: red">*</span> 付款明细
    </div>
    <MyTable
      :columns="columnsPayment"
      :source="myForm.financialSettlementPaymentList"
      :showIndex="true"
    >
      <template #h_paymentDate>
        <span style="color: red">*</span> 日期
      </template>
      <template #h_paymentAmountLabel>
        <span style="color: red">*</span> 金额
      </template>
    </MyTable>
    <InBody>
      <div
        class="text-center fixed bottom-0 bg-white z-10 pb-2"
        style="width: calc(100% - 260px); left: 260px"
        v-show="!proccess"
      >
        <el-button type="primary" v-show="myForm.status == 3" @click="payment"
          >已付款</el-button
        >
        <el-button
          type="primary"
          @click="initiate"
          v-show="
            myForm.status != 3 &&
            myForm.status != 2 &&
            myForm.settlementStatus == 1
          "
          >发起审批流程</el-button
        >

        <!-- <el-button type="primary" v-show="myForm.status==2">撤回</el-button> -->
        <!-- <el-button
          type="primary"
          v-show="myForm.status != 3 && myForm.status != 2"
          @click="selectCompanyType = true"
          >重新发起</el-button
        > -->

        <el-button type="primary" v-show="myForm.status == 3" @click="cancel"
          >作废</el-button
        >
        <el-button @click="cancels">取 消</el-button>
      </div>
    </InBody>
    <SelectCompany
      v-if="selectCompanyType"
      @close="closeCompany"
      @submit="submitCompany"
    />
  </div>
</template>

<script>
import XEUtils from "xe-utils";
import {
  getFinancialSettlementById,
  getBlFinancialSettlementFlowController,
  updateProcessSettlementFinancial,
  settlementPaidAlready,
} from "@/api/badSystem/financialSettlement";
import config from "./components/config";
export default {
  name: "FinancialSettlementDetailView",
  props: {
    detailId: {
      type: [String, Number],
      default: "",
    },
    proccess: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      ...config,
      myForm: {
        financialSettlementReconciliationList: [],
        financialSettlementPaymentList: [],
      },
      myDetailId: "",
      selectCompanyType: false,
    };
  },
  computed: {},
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.myDetailId = this.detailId || this.$route.params.id;
      this.getMyForm();
    },
    async getMyForm() {
      const { data } = await getFinancialSettlementById(this.myDetailId);
      this.myForm = data;
      this.handlerMyForm();
    },
    handlerMyForm() {
      this.myForm.financialSettlementReconciliationList.forEach((item) => {
        item.totalAmountCollectedLabel = item.totalAmountCollected
          ? item.totalAmountCollected.toLocaleString("zh-CN") + "元"
          : "";
      });
      this.myForm.financialSettlementPaymentList.forEach((item) => {
        item.paymentAmountLabel = item.paymentAmount
          ? item.paymentAmount.toLocaleString("zh-CN") + "元"
          : "";
        item.paymentStateLabel = this.paymentStateObj[item.paymentState];
      });
      this.myForm.settlementAmount = this.myForm.settlementAmount
        ? this.myForm.settlementAmount.toLocaleString("zh-CN") + "元"
        : "";
      this.myForm.financialReceiptsAmount = this.myForm.financialReceiptsAmount
        ? this.myForm.financialReceiptsAmount.toLocaleString("zh-CN") + "元"
        : "";
      this.myForm.unsettledAmount = this.myForm.unsettledAmount
        ? this.myForm.unsettledAmount.toLocaleString("zh-CN") + "元"
        : "";
    },
    initiate() {
      if (this.myForm.processId && this.myForm.status == 6) {
        this.$router.push({
          path: "/oaWork/processFormView",
          query: {
            oid: this.myForm.processId,
            myActiviteType: true,
          },
        });
      } else if (this.myForm.processId) {
        this.$router.push({
          path: "/oaWork/processFormView",
          query: {
            oid: this.myForm.processId,
            businessId: this.myForm.processId,
            myActiviteType: true,
          },
        });
      } else {
        this.selectCompanyType = true;
      }
    },
    submitCompany(e) {
      getBlFinancialSettlementFlowController({ companyId: e }).then((res) => {
        if (res.code == 200) {
          this.selectCompanyType = false;
          this.$router.push({
            path: "/oaWork/updateProcessForm",
            query: {
              templateId: res.templateId,
              classificationId: res.classificationId,
              companyId: res.companyId,
              financialSettlementDetailId: this.myDetailId,
            },
          });
        }
      });
    },
    closeCompany() {
      this.selectCompanyType = false;
    },
    async payment() {
      await settlementPaidAlready(this.myDetailId);
      this.$modal.msgSuccess("操作成功");
      this.$router.go(-1);
    },
    async cancel() {
      const params = {
        id: this.myDetailId,
        status: "5",
      };
      await updateProcessSettlementFinancial(params);
      this.$modal.msgSuccess("操作成功");
      this.$router.go(-1);
    },
    cancels() {
      this.$router.go(-1);
    },
  },
};
</script>
<style lang="less" scoped>
</style>
