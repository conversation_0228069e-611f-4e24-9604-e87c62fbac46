export default {
  paymentStatus: Object.freeze([
    { label: "已付", value: "1" },
    { label: "未付", value: "2" },
  ]),
  formColumnsDetail: [
    {
      label: "结算单编号",
      prop: "settlementDocumentCode",
      type: "input",
      placeholder: "请输入结算单编号",
      disabled: true,
      span: 8,
    },
    {
      label: "结算主体",
      prop: "settlementEntity",
      type: "select",
      placeholder: "请选择结算主体",
      options: [],
      dataProp:{value:'id',label:'mechanismShortName'},
      span: 8,
    },
    {
      label: "审批状态",
      prop: "status",
      type: "select",
      placeholder: "请选择审批状态",
      options: [
        { label: "未发起", value: '1' },
        { label: "审批中", value: '2' },
        { label: "通过", value: '3' },
        { label: "撤回", value: '4' },
        { label: "否决", value: '5' },
        { label: "暂存", value: '6' },
      ],
      disabled: true,
      span: 8,
    },
    {
      label: "",
      slotName: "businessReconciliation",
      type: "slot",
      span: 24,
    },
    {
      label: "本次结算金额",
      prop: "settlementAmountLabel",
      type: "divText",
      span: 8,
    },
    {
      label: "财务收款总额",
      prop: "financialReceiptsAmountLabel",
      type: "divText",
      span: 8,
    },
    {
      label: "剩余未结算金额",
      prop: "unsettledAmountLabel",
      type: "divText",
      span: 8,
    },
    {
      label: "",
      slotName: "paymentDetails",
      type: "slot",
      span: 24,
    },
  ],
  formColumnsDialog: [
    {
      label: "对账单号",
      prop: "reconciliationCode",
      type: "input",
      placeholder: "请输入对账单号",
    },
  ],
  columnsDialog: Object.freeze([
    { label: "对账单号",  prop:"reconciliationCode",sortable: "custom",minWidth: "250px" },
    { label: "对账日",  prop:"reconciliationDate",sortable: "custom",minWidth: "250px" },
    { label: "财务收款总额", prop: "totalAmountCollectedLabel",sortable: "custom", minWidth: "250px" },
    {
      label: "平台核销账单金额",
      prop: "totalRepaymentAmountLabel",
      sortable: "custom",
      minWidth: "250px",
    },
  ]),
  columnsDialogSelect: Object.freeze([
    { label: "对账单号",  prop:"reconciliationCode",minWidth: "250px" },
    { label: "对账日",  prop:"reconciliationDate",minWidth: "150px" },
    { label: "财务收款总额", prop: "totalAmountCollectedLabel", minWidth: "250px" },
    {
      label: "平台核销账单金额",
      prop: "totalRepaymentAmountLabel",
      minWidth: "250px",
    },
  ]),
  columnsAccountStatement: Object.freeze([
    {
      label: "对账单号",
      prop: "reconciliationCode",
      minWidth: "250px",
    },
    { label: "对账日", prop: "reconciliationDate", minWidth: "250px" },
    { label: "财务收款总额", prop: "totalAmountCollectedLabel", minWidth: "250px" },
  ]),
  columnsPayment: Object.freeze([
    {
      label: "日期",
      key: "paymentDate",
      isHSlot: true,
      minWidth: "250px",
    },
    { label: "金额", key: "paymentAmount", isHSlot: true, minWidth: "250px" },
    { label: "备注", key: "remark", minWidth: "250px" },
    { label: "付款状态", key: "paymentState", minWidth: "250px" },
    { label: "操作", key: "delete", minWidth: "250px" },
  ]),

  rules: Object.freeze({
    settlementEntity: [
      { required: true, message: "请选择结算主体", trigger: "change" },
    ],
    paymentDetails: [
      { required: true, message: "请添加付款明细", trigger: "change" },
    ],
  }),

  pickerOptions: Object.freeze({
    shortcuts: [
      {
        text: "今天",
        onClick(picker) {
          picker.$emit("pick", new Date());
        },
      },
      {
        text: "昨天",
        onClick(picker) {
          const date = new Date();
          date.setTime(date.getTime() - 3600 * 1000 * 24);
          picker.$emit("pick", date);
        },
      },
      {
        text: "一周前",
        onClick(picker) {
          const date = new Date();
          date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
          picker.$emit("pick", date);
        },
      },
    ],
  }),
};
