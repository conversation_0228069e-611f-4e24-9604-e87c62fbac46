<template>
  <div class="p-5">
    <MyForm
      v-model="queryParams"
      :columns="formColumns"
      @onSearchList="handleQuery"
      label-width="110px"
    />
    <el-divider></el-divider>

    <div class="flex mb-2 justify-between">
      <div class="flex">
        <el-button
          v-hasPermi="['bad:outsourecd:add']"
          @click="handleAdd"
          type="primary"
          size="mini"
          plain
          icon="el-icon-plus"
          >创建分案</el-button
        >
      </div>
    </div>
    <MyTable
      :columns="columns"
      :showIndex="true"
      :source="configList"
      :queryParams="queryParams"
    >
      <template #outsourcedProjectNumber="{ record }">
        <el-button v-if="checkPermi(['badSystem:outsourecd:view'])" type="text" @click="handleView(record)">{{
          record.outsourcedProjectNumber || "-"
        }}</el-button>
        <div v-else>{{ record.outsourcedProjectNumber || "-" }}</div>
      </template>
      <template #operate="{ record }">
        <el-button v-hasPermi="['bad:outsourecd:view']" type="text" @click="handleView(record)">查看</el-button>
      </template>
    </MyTable>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import XEUtils from "xe-utils";
import { newCompanySelectList } from "@/api/businessInformation/companyInformation";

import { getMechanismList } from "@/api/badSystem/organizationalManagement";
import { getOutsourcedProjectList } from "@/api/badSystem/outsourced";
import config from "./components/config";
import { checkPermi } from "@/utils/permission"; // 权限判断函数

export default {
  name: "Outsourced",

  data() {
    return {
      ...config,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      configList: [],
    };
  },
  watch: {},
  mounted() {
    this.init();
  },
  methods: {
    checkPermi,
    init() {
      this.getCompanyList();
      this.getOrganizational();
      this.getList();
    },
    async getCompanyList() {
      const response = await newCompanySelectList({
        selectCode: "partner",
        modelCode: "BADSYSTEM",
      });
      this.formColumns[2].options = response;
    },
    async getOrganizational() {
      const { rows } = await getMechanismList({collaborationStatus:1});
      this.formColumns[1].options = rows;
    },
    async getList() {
      const { rows, total } = await getOutsourcedProjectList(this.queryParams);
      this.configList = this.handleConfigList(rows);
      this.total = total;
    },
    handleConfigList(row) {
      const newRow = XEUtils.clone(row, true);
      const addYuan=["totalAmountMoney","realTotalAmountMoney","detailRepaymentAmount"];
      const addBi=["realTotalQuantity","detailQuantity"];
      const addUnit=["totalQuantity"];
      newRow.forEach((item) => {
        addYuan.forEach(item1=>{
          item[item1]=item[item1]?item[item1].toLocaleString("zh-CN")+'元':'';
        })
        addBi.forEach(item1=>{
          item[item1]=item[item1]?item[item1].toLocaleString("zh-CN")+'笔':'';
        })
        addUnit.forEach(item1=>{
          item[item1]=item[item1]?.toLocaleString("zh-CN");
        })
        item.caseStatusLabel = this.caseStatusObj[item.caseStatus];
        item.disposalModeLabel =
          this.disposalModeObj[item.disposalMode];
      });
      return newRow;
    },
    handleAdd() {
      this.$router.push({
        path: `/badSystemOther/outsourcedDetail/add`,
        query: {
          title: `创建分案`,
        },
      });
    },

    handleUpdate(row) {},
    handleView(row) {
      this.$router.push({
        path: `/badSystemOther/outsourcedDetailView/${row.id}`,
        query: {
          title: `委外分案${row.outsourcedProjectNumber}`,
        },
      });
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
  },
};
</script>
<style lang="scss" scoped>
</style>
