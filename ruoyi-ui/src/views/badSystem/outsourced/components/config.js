export default {

  disposalModeObj: Object.freeze({
    1: "催收/调解",
    2: "法诉",
    3: "保全",
  }),
  caseStatusObj: Object.freeze({
    1: "未分配",
    2: "已分配",
    3: "已撤案",
    4: "已召回",
    5: "对账中",
    6: "对账完成",
    7: "服务费用已结清",
    8: "作废",
  }),
  formColumns: [
    {
      label: "分案批次",
      prop: "outsourcedProjectNumber",
      type: "input",
      placeholder: "请输入分案批次",
    },
    {
      label: "受托机构",
      prop: "trusteeInstitutionId",
      type: "select",
      filterable: true,
      options: [],
      dataProp:{value:'id',label:'mechanismShortName'},
      placeholder: "请选择受托机构",
    },
    {
      label: "债权机构",
      prop: "creditorInstitutionsId",
      type: "select",
      filterable: true,
      options: [],
      dataProp:{value:'id',label:'companyShortName'},
      placeholder: "请选择债权机构",
    },
    {
      label: "委案开始日期",
      prop: "outsourcedStart",
      type: "datePicker",
    },
    {
      label: "委案截止日期",
      prop: "outsourcedEnd",
      type: "datePicker",
    },
  ],
  columns: [
    { label: "分案批次", key: "outsourcedProjectNumber", minWidth: "200px" },
    { label: "案件数量", prop: "totalQuantity", minWidth: "150px" },
    {
      label: "案件金额",
      prop: "totalAmountMoney",
      minWidth: "150px",
    },
    {
      label: "处置模式",
      prop: "disposalModeLabel",
      minWidth: "200px",
    },
    { label: "案件状态", prop: "caseStatusLabel", minWidth: "150px" },
    { label: "债权机构", prop: "creditorInstitutionsName", minWidth: "200px" },
    { label: "受托机构", prop: "trusteeInstitutionName", minWidth: "200px" },
    { label: "案件总额(实际)", prop: "realTotalAmountMoney", minWidth: "200px" },
    { label: "案件总数(实际)", prop: "realTotalQuantity", minWidth: "200px" },
    { label: "委后协助还款总额", prop: "detailRepaymentAmount", minWidth: "200px" },
    { label: "委后协助还款笔数", prop: "detailQuantity", minWidth: "200px" },
    { label: "创建人", prop: "createByName", minWidth: "150px" },
    { label: "创建时间", prop: "createTime", minWidth: "200px" },
    { label: "操作", key: "operate", minWidth: "100" },
  ],
};
