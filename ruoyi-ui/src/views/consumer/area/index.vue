<template>
  <div  style="width: 1000px;height:800px;" >
            <div id="main" style="width: 500px;height:400px; float:left"></div>
             <div id="cakeecharts" style="width: 500px;height:400px; float:left"></div>
             <div style="width: 1000px;height:400px; float:left ">
               <div style=" display: flex; justify-content:center;">
                  <el-select v-model="selectTime" placeholder="请选择时间" @change="selectChenge()" style="right:50px">
                      <el-option
                      v-for="item in options1"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                      </el-option>
                  </el-select>
                  <el-select v-model="weidu" placeholder="请选择维度" @change="selectChenge()">
                    <el-option
                    v-for="item in options2"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                    </el-option>
                 </el-select>
                </div>

             <div id="charts"  style="width: 1000px;height:350px; float:left"></div>
             </div>
   </div>



</template>

<script>
import * as echarts from 'echarts';
export default {
  name: 'index',
  data() {
    return {
       options1:[
            {
            value: '1',
            label: '日'
            },{
            value: '2',
            label: '月'
            },{
            value: '3',
            label: '周'
            },{
            value: '4',
             label: '年'
            }
            ],
       options2:[
            {
            value: '1',
            label: '男女'
            },{
            value: '2',
            label: '年龄分布'
            },{
            value: '3',
            label: '地区分布'
            },{
            value: '4',
             label: '风险等级分布'
            }
            ],
      selectTime:'1',
      weidu:'',
      data1:[]
    };
  },
  mounted() {
    this.myecharts()
    // this.secondecharts()
    this.thirdCakeEcharts()
  },
  methods: {
    selectChenge(){
      if(this.weidu==''){
          alert('请选择统计维度')
      }else{
        this.secondecharts()
      }
    },
    myecharts(){
      var myChart = this.$echarts.init(document.getElementById('main'));
        // 指定图表的配置项和数据
        var option = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                  type: 'shadow'
                }
              },
            toolbox: {
                  show: true,
                  feature: {
                      dataZoom: {
                      yAxisIndex: 'none'
                            },
                      dataView: { readOnly: false },
                    // 柱状图或者折线图展示
                    magicType: { type: ['line', 'bar'] },
                    restore: {},
                    saveAsImage: {},
                  }
                },
        legend: {},
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
          }
        ],
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: [
          {
            name: 'Direct',
            type: 'bar',
             stack: 'Ad',
            emphasis: {
              focus: 'series'
            },
            data: [320, 332, 301, 334, 390, 330, 320]
          },
          {
            name: 'Email',
            type: 'bar',
            stack: 'Ad',
            emphasis: {
              focus: 'series'
            },
            data: [120, 132, 101, 134, 90, 230, 210]
          },
          {
            name: 'Union Ads',
            type: 'bar',
            stack: 'Ad',
            emphasis: {
              focus: 'series'
            },
            data: [220, 182, 191, 234, 290, 330, 310]
          },
          {
            name: 'Video Ads',
            type: 'bar',
            stack: 'Ad',
            emphasis: {
              focus: 'series'
            },
            data: [150, 232, 201, 154, 190, 330, 410]
          },
          {
            name: 'Search Engine',
            type: 'bar',
             stack: 'Ad',
            data: [862, 1018, 964, 1026, 1679, 1600, 1570],
            emphasis: {
              focus: 'series'
            }
          },
          {
            name: 'Baidu',
            type: 'bar',
            barWidth: 5,
          stack: 'Ad',
            emphasis: {
              focus: 'series'
            },
            data: [620, 732, 701, 734, 1090, 1130, 1120]
          },
          {
            name: 'Google',
            type: 'bar',
             stack: 'Ad',
            emphasis: {
              focus: 'series'
            },
            data: [120, 132, 101, 134, 290, 230, 220]
          },
          {
            name: 'Bing',
            type: 'bar',
             stack: 'Ad',
            emphasis: {
              focus: 'series'
            },
            data: [60, 72, 71, 74, 190, 130, 110]
          },
          {
            name: 'Others',
            type: 'bar',
            stack: 'Ad',
            emphasis: {
              focus: 'series'
            },
            data: [62, 82, 91, 84, 109, 110, 120]
          }
        ]
        };
        // 使用刚指定的配置项和数据显示图表。
        myChart.setOption(option);
    },
    secondecharts(){
        var myChart = this.$echarts.init(document.getElementById('charts'));
          let base = +new Date(1999, 9, 0);
          let oneDay = 24 * 3600 * 1000;
          let oneweek = 7 * 24 * 3600 * 1000;
          let onemonth = 30 * 24 * 3600 * 1000;
          let oneyear = 12 * 30 * 24 * 3600 * 1000;
          let date = [];
          let data1 = [Math.random() * 300];
          let data2 = [Math.random() * 300];
          let data3 = [Math.random() * 300];
          let data4 = [Math.random() * 300];
          for (let i = 1; i < 2000; i++) {

            if(this.selectTime==1){
               var now = new Date((base += oneDay));
               date.push([now.getFullYear(), now.getMonth() + 1, now.getDate()].join('/'));
            }else if(this.selectTime==2){
               var now = new Date((base += onemonth));
              date.push([now.getFullYear(), now.getMonth() + 1].join('/'));
            }else if(this.selectTime==3){
               var now = new Date((base += oneweek));
              date.push([now.getFullYear(), now.getMonth() + 1, now.getDate()].join('/'));
              }else if(this.selectTime==4){
               var now = new Date((base += oneyear));
              date.push([now.getFullYear()].join('/'));
            }

            data1.push(Math.round((Math.random() - 0.5) * 20 + data1[i - 1]));
            data2.push(Math.round((Math.random() - 0.5) * 20 + data2[i - 1]));
            data3.push(Math.round((Math.random() - 0.5) * 20 + data3[i - 1]));
            data4.push(Math.round((Math.random() - 0.5) * 20 + data4[i - 1]));
          }
          console.log(date)
        var option = {
              tooltip: {
                    trigger: 'axis',
                    position: function (pt) {
                      return [pt[0], '10%'];
                    }
                  },
                   toolbox: {
                  show: true,
                  feature: {
                      dataZoom: {
                      yAxisIndex: 'none'
                            },
                      dataView: { readOnly: false },
                    // 柱状图或者折线图展示
                    magicType: { type: ['line', 'bar'] },
                    restore: {},
                    saveAsImage: {},
                  }
                },
                   legend: {},
                  title: {
                    left: 'center',
                    text: ''
                  },
                  xAxis: {

                    //X轴数据格式
                    type: 'category',
                    //留白策略
                    boundaryGap: false,
                    data: date
                  },
                  yAxis: {
                    type: 'value',
                    boundaryGap: ['2%', '100%']
                  },
                  dataZoom: [
                    //只能控制X
                    // {
                    //   type: 'inside',
                    //   start: 9,
                    //   end: 10
                    // },
                    // {
                    //   start: 9,
                    //   end: 10
                    // }
                    // 滚轮控制XY轴的时间轴    XY都有
                             {
                            type: 'slider',
                            show: true,
                            xAxisIndex: [0],
                            start: 1,
                            end: 50
                            },
                            {
                            type: 'slider',
                            show: true,
                            yAxisIndex: [0],
                            left: '93%',
                            start: 1,
                            end: 100
                            },
                            {
                            type: 'inside',
                            xAxisIndex: [0],
                            start: 1,
                            end: 10
                            }
                            // ,
                            // {
                            // type: 'inside',
                            // yAxisIndex: [0],
                            // start: 8,
                            // end: 10
                            // }
                    //XY都有时间轴 滚轮只能控制X
                            //  {
                            //   show: true,
                            //   start: 94,
                            //   end: 100
                            // },
                            // {
                            //   type: 'inside',
                            //   start: 94,
                            //   end: 100
                            // },
                            // {
                            //   show: true,
                            //   yAxisIndex: 0,
                            //   filterMode: 'empty',
                            //   // width: 30,
                            //   // height: '80%',
                            //   showDataShadow: false,
                            //   // left: '93%'
                            // }
                  ],
                  series: [
                    {
                      name: 'Fake Data',
                      type: 'bar',
                      stack: 'Ad',

                      emphasis: {
                        focus: 'series'
                      },
                      data: data1
                    },{
                      name: 'W Data',
                      type: 'bar',
                      stack: 'Ad',
                      emphasis: {
                        focus: 'series'
                      },
                      data: data2
                      },{
                      name: 'ooo Data',
                      type: 'bar',
                      stack: 'Ad',
                      emphasis: {
                        focus: 'series'
                      },
                      data: data3
                      },{
                      name: 'fize Data',
                      type: 'bar',
                      stack: 'Ad',
                      emphasis: {
                        focus: 'series'
                      },
                      data: data4
                      }
                  ]
          };
          myChart.setOption(option);
    },
    thirdCakeEcharts(){
          var myChart = this.$echarts.init(document.getElementById('cakeecharts'));
            var option = {
               title:{
                  left: 'center',
                  text: '饼状图'
               },
               //{a}系列名称 ，{b}（类目值），{c} 数值，{d}（无）
                tooltip: {
                  trigger: 'item',
                  formatter: "{b} : {c}({d}%)"
                },
                toolbox: {
                  show: true,
                  feature: {
                     dataView: { readOnly: false },
                     restore: {},
                  }
                },

                legend: {
                  top: '5%',
                  left: 'center'
                },
                series: [
                  {
                    name: 'Access From',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    avoidLabelOverlap: false,
                    label: {
                      show: false,
                      position: 'center'
                    },
                    emphasis: {
                      label: {
                        show: true,
                        fontSize: '40',
                        fontWeight: 'bold'
                      }
                    },
                    labelLine: {
                      show: false
                    },
                    data: [
                      { value: 1048, name: 'Search Engine' },
                      { value: 735, name: 'Direct' },
                      { value: 580, name: 'Email' },
                      { value: 484, name: 'Union Ads' },
                      { value: 300, name: 'Video Ads' }
                    ]
                  }
                ]
              };
          myChart.setOption(option);
    }
  },
};
</script>
