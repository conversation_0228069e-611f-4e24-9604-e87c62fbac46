<template>
  <div style="height: 100%">
    <div class="search">
      <div class="item">
        <span>信息检索</span
        ><el-tooltip
          class="item"
          effect="dark"
          content="包含界面的所有展示数据的内容，不包含资料文件或视频中的文字"
          placement="top-start"
          ><i class="el-icon-question relative right-1 cursor-pointer"></i>
        </el-tooltip>
        <el-input
          v-model="params.informationRetrieval"
          clearable=""
          placeholder="请输入关键字"
          style="width: 200px"
          @input="getList"
        ></el-input>
      </div>
      <div class="item">
        <span>目录名称</span>
        <el-input
          v-model="params.catalogueName"
          clearable=""
          placeholder="请输入目录名称"
          style="width: 200px"
        ></el-input>
      </div>
      <div class="item">
        <span>目录编号</span>
        <el-input
          v-model="params.catalogueCode"
          clearable=""
          placeholder="请输入目录编号"
          style="width: 200px"
        ></el-input>
      </div>
      <div class="item">
        <span>合作公司</span>
        <el-input
          v-model="params.cooperationCompanyName"
          clearable=""
          placeholder="请输入合作公司"
          style="width: 200px"
        ></el-input>
      </div>
      <div class="item">
        <span>合作项目</span>
        <el-input
          v-model="params.cooperationProjectName"
          clearable=""
          placeholder="请输入合作项目"
          style="width: 200px"
        ></el-input>
      </div>
      <div class="item">
        <span>所属公司</span>
        <el-select
          placeholder="请选择所属公司"
          clearable=""
          style="width: 200px"
          v-model="params.unitId"
        >
          <el-option
            v-for="item in projects"
            :key="item.id"
            :label="item.companyShortName"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </div>
      <div class="item">
        <span>所属部门</span>
        <el-select
          placeholder="请选择所属部门"
          clearable=""
          style="width: 200px"
          v-model="params.deptId"
          ref="selectUpResId"
        >
          <el-option
            hidden
            :value="params.deptId"
            :label="params.pertainDeptName"
          >
          </el-option>
          <el-tree
            :data="treeSelect"
            :props="defaultProps2"
            :expand-on-click-node="false"
            :check-on-click-node="true"
            @node-click="dehandleNodeClick"
          >
          </el-tree>
        </el-select>
      </div>
    </div>
    <div class="mt-2 ml-4">
      <el-button type="primary" icon="el-icon-search" @click="search"
        >搜 索</el-button
      >
      <el-button icon="el-icon-refresh" @click="reset">重 置</el-button>
    </div>
    <div class="solid"></div>
    <div class="content">
      <div class="left">
        <el-input
          v-model="filterText"
          placeholder="请输入目录名称"
          style="width: 210px"
        ></el-input>
        <el-tree
          class="filter-tree"
           :class="{ 'has-data': leftTreeList && leftTreeList.length > 0 }"
          :data="leftTreeList"
          :props="defaultProps"
          :default-expand-all="false"
          :filter-node-method="filterNode"
          @node-click="handleNodeClick"
          ref="tree"
        >
        </el-tree>
      </div>
      <div class="right">
        <div class="mb-5">
          您当前正在使用<span style="color: rgba(217, 0, 27, 0.803921568627451)"
            >监管报送</span
          >资料目录
        </div>
        <div class="header_btn" style="display: flex; align-items: center">
          <el-button
            v-hasPermi="['dataManagementSupervise:directoryMation:add']"
            style="border-color: #aed8ff; background: #e8f4ff; color: #3fa1ff"
            icon="el-icon-plus"
            type="primary"
            size="mini"
            @click="newData"
            >新建</el-button
          >
          <!-- <el-button
            style="border-color: #aed8ff; background: #e8f4ff; color: #3fa1ff"
            type="primary"
            size="mini"
            v-hasPermi="['department:auth']"
            @click="auth(0)"
            >部门授权</el-button
          >
          <el-button
            style="border-color: #aed8ff; background: #e8f4ff; color: #3fa1ff"
            type="primary"
            size="mini"
            v-hasPermi="['post:auth']"
            @click="auth(1)"
            >岗位授权</el-button
          >
          <el-button
            style="border-color: #aed8ff; background: #e8f4ff; color: #3fa1ff"
            type="primary"
            size="mini"
            v-hasPermi="['personnel:auth']"
            @click="auth(2)"
            >人员授权</el-button
          >
          <el-button @click="selectItemType = true" type="primary" size="mini"
            >已选择({{ multipleSelection.length }})条</el-button
          > -->
          <el-button
            style="
              border-color: #aed8ff;
              background: #e8f4ff;
              color: #3fa1ff;
              position: absolute;
              right: 0;
            "
            type="primary"
            size="mini"
            @click="reset"
            >返回至主目录</el-button
          >
        </div>
        <el-table
          :data="tableData"
          style="width: 100%; margin-top: 16px; margin-left: 4px"
        >
          <!-- <el-table-column
            align="center"
            prop="date"
            width="55"
            v-if="!params.parentId"
          > -->
          <!-- <template slot="header" slot-scope="scope">
              <img
                v-if="multipleSelection.length == 0"
                @click="selectionChange(scope.row, 'allact')"
                class="selsct"
                :src="require('@/assets/images/omo_none.png')"
                alt=""
              />
              <img
                v-show="tableData.length > 0 && allType"
                @click="selectionChange(scope.row, 'alldel')"
                class="selsct"
                :src="require('@/assets/images/omo_act.png')"
                alt=""
              />
              <img
                v-show="multipleSelection.length > 0 && !allType"
                @click="selectionChange(scope.row, 'allact')"
                class="selsct"
                :src="require('@/assets/images/omo_show.png')"
                alt=""
              />
            </template> -->
          <!-- <template slot-scope="scope">
              <img
                v-show="!scope.row.acttype"
                @click="selectionChange(scope.row, 'act')"
                class="selsct"
                :src="require('@/assets/images/omo_none.png')"
                alt=""
              />
              <img
                v-show="scope.row.acttype"
                @click="selectionChange(scope.row, 'del')"
                class="selsct"
                :src="require('@/assets/images/omo_act.png')"
                alt=""
              />
            </template>
          </el-table-column> -->
          <!-- <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="xh"
            label="序号"
            width="50"
          /> -->
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="catalogueCode"
            label="目录编号"
            width="180"
          >
            <template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(
                    row.catalogueCode,
                    params.informationRetrieval
                  )
                "
              ></span> </template
          ></el-table-column>
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="catalogueName"
            label="目录名称"
            width="380"
          >
            <template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(
                    row.catalogueName,
                    params.informationRetrieval
                  )
                "
              ></span> </template
          ></el-table-column>
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="orderNum"
            label="排序号"
            width="100"
            ><template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(row.orderNum, params.informationRetrieval)
                "
              ></span> </template
          ></el-table-column>
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="parentName"
            label="上级目录"
            width="140"
          >
            <template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(row.parentName, params.informationRetrieval)
                "
              ></span> </template
          ></el-table-column>
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="catalogueSystemCode"
            label="系统目录编号"
            width="140"
          >
            <template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(
                    row.catalogueSystemCode,
                    params.informationRetrieval
                  )
                "
              ></span> </template
          ></el-table-column>
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="cooperationCompany"
            label="合作公司"
            width="250"
          >
            <template #default="{ row }">
              <span
                v-show="row.cooperationCompany"
                v-html="
                  highlightKeyword(
                    row.cooperationCompanyName,
                    params.informationRetrieval
                  )
                "
              ></span>
              <span v-show="!row.cooperationCompany">-</span>
            </template></el-table-column
          >
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="cooperationProject"
            label="合作项目"
            width="380"
          >
            <template #default="{ row }">
              <span
                v-show="row.cooperationProject"
                v-html="
                  highlightKeyword(
                    row.cooperationProjectName,
                    params.informationRetrieval
                  )
                "
              ></span>
              <span v-show="!row.cooperationProject">-</span>
            </template></el-table-column
          >
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="isPublicLabel"
            label="公共资料库"
            width="150"
          >
            <template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(
                    row.isPublicLabel,
                    params.informationRetrieval
                  )
                "
              ></span> </template
          ></el-table-column>
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="orgName"
            label="所属公司"
            width="150"
          >
            <template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(row.orgName, params.informationRetrieval)
                "
              ></span> </template
          ></el-table-column>
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="deptName"
            label="所属部门"
            width="150"
          >
            <template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(row.deptName, params.informationRetrieval)
                "
              ></span> </template
          ></el-table-column>
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="createBy"
            label="创建人"
            min-width="100"
          >
            <template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(row.createBy, params.informationRetrieval)
                "
              ></span> </template
          ></el-table-column>
          <el-table-column
            align="center"
            show-overflow-tooltip=""
            prop="createTime"
            label="创建时间"
            width="200"
          >
            <template #default="{ row }">
              <span
                v-html="
                  highlightKeyword(row.createTime, params.informationRetrieval)
                "
              ></span> </template
          ></el-table-column>
          <el-table-column
            align="center"
            fixed="right"
            width="220"
            label="操作"
          >
            <template slot-scope="scope">
              <el-button
                v-hasPermi="['dataManagementSupervise:directoryMation:update']"
                @click="edit(scope.row)"
                type="text"
                size="small"
                >修改</el-button
              >
              <el-button @click="seeDetail(scope.row)" type="text" size="small"
                >查看详情</el-button
              >
              <el-button
                v-hasPermi="['dataManagementSupervise:directoryMation:delete']"
                @click="del(scope.row)"
                style="color: red"
                type="text"
                size="small"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="params.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :limit.sync="params.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
    <addItem
      :leftTreeList="leftTreeList"
      :editData="editData"
      @addItem="addItem"
      v-if="addItemType"
      :deTreeList="treeSelect"
      :seeType="seeType"
      @close="addItemType = false"
      :isHomeDirectory="isHomeDirectory"
      :cooperationCompanyList="cooperationCompanyList"
      :cooperationProjectList="cooperationProjectList"
    />
    <DepartmentAuthority
      @confimrDe="confimrDe"
      :auDeptIds="auDeptIds"
      :multipleSelection="multipleSelection"
      v-if="DepartmentAuthorityType"
      @close="DepartmentAuthorityType = false"
    />
    <PersonnelAuthority
      :auUserIds="auUserIds"
      :multipleSelection="multipleSelection"
      v-if="PersonnelAuthorityType"
      @confrimPer="confrimPer"
      @close="PersonnelAuthorityType = false"
    />
    <PostAuthority
      :auPostIds="auPostIds"
      @confirmPost="confirmPost"
      :multipleSelection="multipleSelection"
      v-if="PostAuthorityType"
      @close="PostAuthorityType = false"
    />
    <SelectItem
      :multipleSelection="multipleSelection"
      @confirm="confirmSelect"
      v-if="selectItemType"
      @close="selectItemType = false"
    />
  </div>
</template>

<script>
import Cookies from "js-cookie";
import SelectItem from "./SelectItem.vue";

import addItem from "./addItem.vue";
import DepartmentAuthority from "./DepartmentAuthority.vue";
import PersonnelAuthority from "./PersonnelAuthority.vue";
import PostAuthority from "./PostAuthority.vue";
import {
  treeselect,
  getTreeList,
  informationCatalogueList,
  informationDetail,
  delInformation,
  authority,
  informationCatalogue,
  informationCatalogueEdit,
  newAuthorityCompany,
} from "@/api/directoryMation/directoryMationSupervise";
import { ziliaogongsi, ziliaoxiangmu } from "@/api/form/formdesign";

import { highlightKeyword } from "@/utils/index.js";
export default {
  components: {
    addItem,
    DepartmentAuthority,
    PersonnelAuthority,
    PostAuthority,
    SelectItem,
  },
  data() {
    return {
      isHomeDirectory: false,
      defaultProps2: Object.freeze({
        children: "children",
        label: "label",
      }),
      DepartmentAuthorityType: false,
      PostAuthorityType: false,
      PersonnelAuthorityType: false,

      editData: null,
      treeSelect: [],
      addItemType: false,
      defaultProps: {
        children: "informationCatalogueVOList",
        label: "catalogueName",
      },
      projects: [],
      leftTreeList: [],
      multipleSelection: [],
      allType: false,
      selectItemType: false,
      moveItemType: false,
      total: 0,
      params: {
        informationRetrieval: "",
        catalogueName: "",
        catalogueCode: "",
        pageNum: 1,
        pageSize: 10,
        parentId: 0,
        deptId: "",
        pertainDeptName: "",
        unitId: "",
      },
      isPublicObj: Object.freeze({
        0: "否",
        1: "是",
      }),
      tableData: [],
      selectList: [],
      time: [],
      filterText: "",
      treeData: [],
      auUserIds: [],
      auPostIds: [],
      auDeptIds: [],
      seeType: false,
      highlightKeyword,
      cooperationCompanyList: [],
      cooperationProjectList: [],
      currentTree: {},
    };
  },
  watch: {
    multipleSelection(newval, oldval) {
      if (newval.length == 0) {
        this.allType = false;
      }
    },
    tableData(newval, oldval) {
      var flag = newval.every((item) => {
        return item.acttype;
      });
      this.allType = flag ? true : false;
    },
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  created() {},
  mounted() {
    this.getTreeList();
    this.getList();
    this.getCooperationCompany();
    this.getCooperationProject();
  },
  methods: {
    async getCooperationCompany() {
      const { informationCompany } = await ziliaogongsi();
      this.cooperationCompanyList = informationCompany;
    },
    async getCooperationProject() {
      const { informationProject } = await ziliaoxiangmu();
      this.cooperationProjectList = informationProject;
    },
    seeDetail(v) {
      this.isHomeDirectory = v.parentId == 0;
      informationDetail(v.id).then((res) => {
        if (res.code == 200) {
          this.editData = Object.assign(res.data, v);
          this.addItemType = true;
          this.seeType = true;
        }
      });
    },
    confirmPost(e) {
      console.log(e);
      let ids = e.map((item) => item.postId);
      let list = [];
      this.multipleSelection.forEach((item) => {
        list.push({
          billId: item.id,
          billType: 0,
          authorityType: 1,
          authorityIds: ids,
          impower: Cookies.get("username"),
          impowerTime: this.$format(
            new Date().getTime(),
            "yyyy-MM-dd HH:mm:ss"
          ),
        });
      });
      console.log(list);
      authority({ authority: list }).then((res) => {
        if (res.code == 200) {
          this.$message.success("授权成功");
          this.getList();
          this.getTreeList();
          this.PostAuthorityType = false;
        }
      });
    },
    confrimPer(e) {
      console.log(e);
      let ids = e.map((item) => item.userId);
      let list = [];
      this.multipleSelection.forEach((item) => {
        list.push({
          billId: item.id,
          billType: 0,
          authorityType: 2,
          authorityIds: ids,
          impower: Cookies.get("username"),
          impowerTime: this.$format(
            new Date().getTime(),
            "yyyy-MM-dd HH:mm:ss"
          ),
        });
      });
      console.log(list);
      authority({ authority: list }).then((res) => {
        if (res.code == 200) {
          this.$message.success("授权成功");
          this.getList();
          this.getTreeList();
          this.PersonnelAuthorityType = false;
        }
      });
    },
    confimrDe(e) {
      console.log(e);
      let ids = e.map((item) => item.id);
      let list = [];
      this.multipleSelection.forEach((item) => {
        list.push({
          billId: item.id,
          billType: 0,
          authorityType: 0,
          authorityIds: ids,
          impower: Cookies.get("username"),
          impowerTime: this.$format(
            new Date().getTime(),
            "yyyy-MM-dd HH:mm:ss"
          ),
        });
      });
      console.log(list);
      authority({ authority: list }).then((res) => {
        if (res.code == 200) {
          this.$message.success("授权成功");
          this.getList();
          this.getTreeList();
          this.DepartmentAuthorityType = false;
        }
      });
    },
    auth(v) {
      if (this.multipleSelection.length == 0) {
        this.$message.warning("请选择数据");
        return;
      }
      if (this.multipleSelection.length == 1) {
        informationDetail(this.multipleSelection[0].id).then((res) => {
          if (res.code == 200) {
            this.auDeptIds = res.data.auDeptIds;
            this.auPostIds = res.data.auPostIds;
            this.auUserIds = res.data.auUserIds;
            if (v == 0) {
              this.DepartmentAuthorityType = true;
            } else if (v == 1) {
              this.PostAuthorityType = true;
            } else {
              this.PersonnelAuthorityType = true;
            }
          }
        });
      } else {
        this.auDeptIds = [];
        this.auPostIds = [];
        this.auUserIds = [];
        if (v == 0) {
          this.DepartmentAuthorityType = true;
        } else if (v == 1) {
          this.PostAuthorityType = true;
        } else {
          this.PersonnelAuthorityType = true;
        }
      }
    },
    newData() {
      this.editData = {
        parentId: this.currentTree.parentId,
        huixianid:this.currentTree.id,
        catalogueName:this.currentTree.catalogueName,
        parentName: this.currentTree.parentName,
        orgId:this.currentTree.orgId,
        type:'add'
      };
      this.seeType = false;
      this.addItemType = true;
    },
    addItem(e) {
      console.log(e);
      if (e.id) {
        informationCatalogueEdit({ ...e }).then((res) => {
          if (res.code == 200) {
            this.$message.success("修改成功");
            this.addItemType = false;
            this.getList();
            this.getTreeList();
          }
        });
      } else {
        informationCatalogue({ ...e }).then((res) => {
          if (res.code == 200) {
            this.$message.success("新建成功");
            this.addItemType = false;
            this.getList();
            this.getTreeList();
          }
        });
      }
    },
    edit(v) {
      informationDetail(v.id).then((res) => {
        if (res.code == 200) {
          this.editData = Object.assign(res.data, v);
          this.seeType = false;
          this.addItemType = true;
        }
      });
    },
    del(v) {
      this.$confirm("此操作将永久删除该目录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          delInformation([v.id]).then((res) => {
            if (res.code == 200) {
              this.$message.success("操作成功");
              this.getTreeList();
              this.getList();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    success() {
      this.moveItemType = false;
      this.multipleSelection = [];
      this.getList();
    },
    confirmSelect(v) {
      this.multipleSelection = [...v];
      this.tableData.forEach((item) => {
        item.acttype = false;
      });
      this.getArrEqual(this.tableData, this.multipleSelection);
      this.selectItemType = false;
    },
    search() {
      this.params.pageNum = 1;
      this.getList();
    },
    collapseAll() {
      const arr = this.$refs.tree.store.root.childNodes;
      arr.forEach((item) => {
        if (Array.isArray(item)) {
          this.collapseAll(item);
        } else {
          item.expanded = false;
        }
      });
    },
    reset() {
      this.params = {
        catalogueName: "",
        catalogueCode: "",
        pageNum: 1,
        pageSize: 10,
        parentId: 0,
        deptId: "",
        pertainDeptName: "",
        unitId: "",
      };
      this.collapseAll();
      this.getList();
    },
    dehandleNodeClick(data) {
      console.log(data);
      this.params.pertainDeptName = data.label;
      this.params.deptId = data.id;
      this.$refs.selectUpResId.blur();
    },
    handleNodeClick(data) {
      console.log(data);
      this.multipleSelection = [];
      this.params.parentId = data.id;
      this.currentTree = data;
      this.getList();
    },
    getTreeList() {
      getTreeList().then((res) => {
        this.leftTreeList = res.data;
      });
      treeselect({ AuthModuleEnumCode: "JGINFORMATION" }).then((res) => {
        this.treeSelect = res.data;
      });
      newAuthorityCompany({ AuthModuleEnumCode: "JGINFORMATION" }).then(
        (response) => {
          this.projects = response.rows;
        }
      );
    },
    getList() {
      let params = {
        ...this.params,
      };
      informationCatalogueList({ ...params }).then((res) => {
        if (res.code == 200) {
          this.tableData = res.rows;
          this.tableData.forEach((item, index) => {
            item.acttype = false;
            item.xh = (this.params.pageNum - 1) * 10 + index + 1;
            item.isPublicLabel = this.isPublicObj[item.isPublic];
          });
          this.total = res.total;
          if (this.multipleSelection.length > 0) {
            this.getArrEqual(this.tableData, this.multipleSelection);
          }
        }
      });
    },
    getArrEqual(arr1, arr2) {
      for (let i = 0; i < arr2.length; i++) {
        for (let j = 0; j < arr1.length; j++) {
          if (arr1[j].id === arr2[i].id) {
            arr1[j].acttype = true;
          }
        }
      }
    },
    selectionChange(v, type) {
      if (this.multipleSelection.length == "1" && type == "act") {
        this.$modal.msgError("至多勾选一项");
        return;
      }
      var list = [...this.tableData];
      switch (type) {
        case "act":
          list.map((val, idx) => {
            if (val.id == v.id) {
              val.acttype = true;
            }
          });
          this.tableData = [];
          this.tableData = JSON.parse(JSON.stringify(list));
          this.multipleSelection.push(v);
          break;
        case "del":
          list.map((val, idx) => {
            if (val.id == v.id) {
              delete val.acttype;
            }
          });
          this.multipleSelection.map((val, idx) => {
            if (val.id == v.id) {
              this.multipleSelection.splice(idx, 1);
            }
          });
          this.tableData = [];
          this.tableData = JSON.parse(JSON.stringify(list));
          break;
        case "alldel":
          list.map((val, idx) => {
            delete val.acttype;
          });
          let arr = [...this.multipleSelection];
          for (let i = 0; i < arr.length; i++) {
            for (let j = 0; j < list.length; j++) {
              if (arr[i].id == list[j].id) {
                arr.splice(i, 1);
              }
            }
          }
          this.multipleSelection = [...arr];
          this.tableData = [];
          this.tableData = JSON.parse(JSON.stringify(list));
          break;
        case "allact":
          var flag = list.every((item) => {
            return item.acttype;
          });
          if (flag) {
            this.allType = true;
            list.map((val, idx) => {
              delete val.acttype;
            });
            let arr = [...this.multipleSelection];
            for (let i = 0; i < arr.length; i++) {
              for (let j = 0; j < list.length; j++) {
                if (arr[i].id == list[j].id) {
                  arr.splice(i, 1);
                }
              }
            }
            this.multipleSelection = [...arr];
            this.tableData = [];
            this.tableData = JSON.parse(JSON.stringify(list));
          } else {
            let datalist = [...list, ...this.multipleSelection];
            let obj = {};
            let peon = datalist.reduce((cur, next) => {
              obj[next.id] ? "" : (obj[next.id] = true && cur.push(next));
              return cur;
            }, []); //设置cur默认类型为数组，并且初始值为空的数组

            this.multipleSelection = [...peon];
            list.map((val, idx) => {
              val.acttype = true;
              // this.multipleSelection.push(val);
            });
            this.tableData = [];
            this.tableData = JSON.parse(JSON.stringify(list));
          }

          break;
      }
      console.log(this.multipleSelection, "---");
    },
    toDetail(v) {
      getDetail(v.id).then((res) => {});
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.catalogueName.indexOf(value) !== -1;
    },
  },
};
</script>

<style lang="less" scoped>
.content {
  display: flex;
  width: 100%;
  padding: 16px;
  .left {
    width: 250px;
    height: 650px;
    overflow-y: auto;
    border: 1px solid #ccc;
    flex-shrink: 0;
    padding: 16px;
    box-sizing: border-box;
    ::v-deep .el-tree {
      &.has-data {
        display: inline-block;
      }
    }
  }
  .right {
    width: calc(100% - 250px);
    padding-left: 12px;
    .el-button {
      height: 32px;
    }
  }
}
.search {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  .item {
    display: flex;
    align-items: center;
    margin-right: 16px;
    span {
      margin-right: 9px;
    }
  }
}
.el-button {
  height: 36px;
  margin-left: 4px;
  margin-right: 12px;
}
.solid {
  width: 100%;
  height: 1px;
  background: #f2f2f2;
  margin-top: 12px;
}
.selsct {
  width: 14px;
  cursor: pointer;
}
</style>
