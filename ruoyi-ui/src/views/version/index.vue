<template>
  <div style="padding: 20px">
    <div>
      <span style="font-weight: bold">上次修改人：</span
      >{{ versionData.updateBy
      }}<span style="margin-left: 30px; font-weight: bold">上次修改时间：</span
      >{{ versionData.updateTime }}
    </div>
    <div style="margin-top: 20px">
      <span style="font-weight: bold">当前版本号：</span
      ><el-input
        v-model="version"
        style="width: 250px"
        placeholder="请输入"
      ></el-input>
      <el-button type="primary" style="margin-left: 12px" @click="submit"
        >修 改</el-button
      >
    </div>
  </div>
</template>

<script>
import { getVersion, maintain } from "@/api/auditQrcode/index";
export default {
  data() {
    return {
      version: null,
      versionData: null,
    };
  },
  mounted() {
    this.getDetail();
  },
  methods: {
    getDetail() {
      getVersion().then((res) => {
        this.versionData = res.data;
        this.version = res.data.version;
      });
    },
    submit() {
      maintain({ id: this.versionData.id, version: this.version }).then(
        (res) => {
          this.$message.success("修改成功");
          this.getDetail();
        }
      );
    },
  },
};
</script>

<style>
</style>