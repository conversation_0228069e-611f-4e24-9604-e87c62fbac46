<template>
  <div class="app-container">
    <div v-show="configurationShow">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <span class="grid-content2">本功能用于设置收入预测报表中相关指标的计算因子，设置后实时生效</span><br/>
        </el-col>
      </el-row>
<!--      <el-divider/>-->
      <!--      <div style="width: 40%;height:60px; line-height: 45px;" v-hasPermi="['yybbsc:forecast:importStaOfAcc','yybbsc:forecast:importRepay','yybbsc:forecast:importCompensatory','yybbsc:forecast:export']">-->
      <!--        <el-button size="mini" type="primary" @click="importStaOfAcc()" v-hasPermi="['yybbsc:forecast:importStaOfAcc']">导入对账单</el-button>-->
      <!--        <el-button size="mini" type="primary" @click="importRepay()" v-hasPermi="['yybbsc:forecast:importRepay']">导入还款表</el-button>-->
      <!--        <el-button size="mini" type="primary" @click="importCompensatory()" v-hasPermi="['yybbsc:forecast:importCompensatory']">导入代偿明细表</el-button>-->
      <!--      </div>-->

      <div v-hasPermi="['yybbsc:forecast:list']" style="width: 1122px">
        <el-table v-loading="loading" :data="configurationVoList">
          <el-table-column label="参数项" align="center" prop="parameterName" width="280px"/>
          <el-table-column label="最后维护人" align="center" prop="lastUpdateUserName" width="280px">
            <template slot-scope="scope">
              <span v-if="scope.row.lastUpdateUserName === null">-</span>
              <span v-if="scope.row.lastUpdateUserName !== null">{{scope.row.lastUpdateUserName}}</span>
            </template>
          </el-table-column>
          <el-table-column label="最后维护日期" align="center" prop="updateTime" width="280px">
            <template slot-scope="scope">
              <span v-if="scope.row.updateTime === null">-</span>
              <span v-if="scope.row.updateTime !== null">{{scope.row.updateTime}}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="280px">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleList(scope.row.parameterCode)"
              >查看</el-button>
              <el-button
                size="mini"
                type="text"
                @click="handleEdit(scope.row.parameterCode)"
                v-hasPermi="['yybbsc:forecast:importStaOfAcc','yybbsc:forecast:importRepay','yybbsc:forecast:importCompensatory','yybbsc:forecast:export']"
              >编辑</el-button>
            </template>
          </el-table-column>
        </el-table>

      </div>

    </div>

    <technical-service-fee-configuration-list :style="{width: '100%'}" ref="bPage" :queryFromFirst="querySecondObj" @emitToParent="getBRes"
                                              v-show="technicalServiceFeeConfigurationListShow"></technical-service-fee-configuration-list>

    <technical-service-fee-configuration-edit :style="{width: '100%'}" ref="cPage" :queryFromFirst="querySecondObj" @emitToParent="getCRes"
                                              v-show="technicalServiceFeeConfigurationEditShow"></technical-service-fee-configuration-edit>

    <margin-cost-configuration-list :style="{width: '100%'}" ref="dPage" :queryFromFirst="querySecondObj" @emitToParent="getDRes"
                                    v-show="marginCostConfigurationListShow"></margin-cost-configuration-list>

    <margin-cost-configuration-edit :style="{width: '100%'}" ref="ePage" :queryFromFirst="querySecondObj" @emitToParent="getERes"
                                    v-show="marginCostConfigurationEditShow"></margin-cost-configuration-edit>

  </div>
</template>

<script>
import marginCostConfigurationList from '@/views/yybbsc/marginCostConfigurationList'
import technicalServiceFeeConfigurationList from '@/views/yybbsc/technicalServiceFeeConfigurationList'
import technicalServiceFeeConfigurationEdit from '@/views/yybbsc/technicalServiceFeeConfigurationEdit'
import marginCostConfigurationEdit from '@/views/yybbsc/marginCostConfigurationEdit'
import { listConfiguration } from '@/api/yybbsc/configuration'

export default {
  components: { marginCostConfigurationList, technicalServiceFeeConfigurationList, technicalServiceFeeConfigurationEdit, marginCostConfigurationEdit },
  name: "configuration",
  data() {
    return {
      //控制本页面是否显示第二页
      configurationShow: true,
      //控制本页面是否显示第三页
      technicalServiceFeeConfigurationEditShow: false,
      //控制本页面是否显示第四页
      marginCostConfigurationListShow: false,
      //控制本页面是否显示第五页
      marginCostConfigurationEditShow: false,
      //控制导入页面是否显示
      technicalServiceFeeConfigurationListShow: false,
      //第二个和第三个页面-importStaOfAcc、importRepay、importCompensatory传参对象
      querySecondObj: {
        productNo: null
      },
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      //月份下拉框参数
      monthDataList: [],
      // 渲染的参数
      configurationVoList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        loanMonth: null
      }
    }
  },
  created() {
    // this.queryParams.productNo = '0'
    this.getList()
  },
  methods: {
    /** 第五页返回的值 */
    getERes(data, index) {
      if (data === true) {
        this.getList()
        if (index === 0) {
          this.marginCostConfigurationEditShow = false
          this.configurationShow = true
        } else if (index === 1) {
          this.$refs.dPage.getList();
          this.marginCostConfigurationEditShow = false
          this.marginCostConfigurationListShow = true
        }
        // this.marginCostConfigurationEditShow = false
        // this.configurationShow = true
      }
    },
    /** 第四页返回的值 */
    getDRes(data) {
      if (data === true) {
        this.getList()
        this.marginCostConfigurationListShow = false
        this.configurationShow = true
      }
    },
    /** 第三页返回的值 */
    getCRes(data, index) {
      if (data === true) {
        this.getList()
        if (index === 0) {
          this.technicalServiceFeeConfigurationEditShow = false
          this.configurationShow = true
        } else if (index === 1) {
          this.$refs.bPage.getList();
          this.technicalServiceFeeConfigurationEditShow = false
          this.technicalServiceFeeConfigurationListShow = true
        }
      }
    },
    /** 第二页返回的值 */
    getBRes(data) {
      if (data === true) {
        this.getList()
        this.technicalServiceFeeConfigurationListShow = false
        this.configurationShow = true
      }
    },
    /** 进入页面首次查询列表 */
    getList() {
      // this.loading = true
      /** 首次查具体数据 */
      listConfiguration().then(response => {
        this.configurationVoList = response;
      })
      this.loading = false
    },
    /** 查看具体某一个参数项详情 */
    handleList(parameterCode) {
      if (parameterCode === '0') {
        this.technicalServiceFeeConfigurationListPage();
      }
      if (parameterCode === '1') {
        this.marginCostConfigurationListPage();
      }
    },
    /** 编辑具体某一个参数项详情 */
    handleEdit(parameterCode) {
      console.log('parameterCode', parameterCode)
      if (parameterCode === '0') {
        this.technicalServiceFeeConfigurationEditPage();
      }
      if (parameterCode === '1') {
        this.marginCostConfigurationEditPage();
      }
    },
    /** 技术服务费参数详情页面 */
    technicalServiceFeeConfigurationListPage() {
      this.configurationShow = false
      this.technicalServiceFeeConfigurationListShow = true
      this.$refs.bPage.getList();
      // this.$refs.bPage.buttonFlagIsFalse()
    },
    /** 技术服务费参数编辑页面 */
    technicalServiceFeeConfigurationEditPage() {
      this.configurationShow = false
      this.technicalServiceFeeConfigurationEditShow = true
      this.$refs.cPage.getList();
      // this.$refs.cPage.buttonFlagIsFalse()
    },
    /** 保证金成本参数详情页面 */
    marginCostConfigurationListPage() {
      this.configurationShow = false
      this.marginCostConfigurationListShow = true
      this.$refs.dPage.getList();
      // this.$refs.dPage.buttonFlagIsFalse()
    },
    /** 保证金成本参数编辑页面 */
    marginCostConfigurationEditPage() {
      this.configurationShow = false
      this.marginCostConfigurationEditShow = true
      this.$refs.ePage.getList();
      // this.$refs.cPage.buttonFlagIsFalse()
    }
  }
}
</script>
<style>
.grid-content2 {
  /* border-radius: 10px;
  height: 50px;
  line-height: 14px; */
  color: #cccccc;
  /* font-weight:bold; */
  font-size: 13px;
  text-align: center;
  /*margin-left: 24px;*/
}

.grid-content1 {
  /* border-radius: 10px;
  height: 50px;
  line-height: 14px; */
  color: #ff0000;
  /* font-weight:bold; */
  font-size: 13px;
  text-align: center;
  /*margin-left: 24px;*/
}
</style>
