<template>
  <div class="app-container">
    <div v-show="dayShow">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <span
            style="font-size: 13px; margin-left: 1px;  color:#cccccc;">本功能用于为财务部门自动生成《每日运营报表》</span><br>
          <span
            style="font-size: 13px; margin-left: 1px;  color:#cccccc;">请导入《每日放还款明细》文件，系统将根据规则汇总并生成《每日运营报表》</span>
        </el-col>
      </el-row>

      <div style="width: 40%;height:60px; line-height: 45px;"
           v-hasPermi="['yybbsc:day:importData','yybbsc:day:checkData']">
        <el-button size="mini" type="primary" @click="handleImport()" v-hasPermi="['yybbsc:day:importData']">
          导入每日放还款明细
        </el-button>
        <el-button size="mini" type="primary" @click="handleCheck()" v-hasPermi="['yybbsc:day:checkData']">数据核对
        </el-button>
      </div>

      <div>
        <span class="spancol" v-hasPermi="['yybbsc:day:list']">每日运营报表 </span>
        <el-button
          style="margin-left:20px"
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['yybbsc:day:export']"
        >导出
        </el-button>
        <br>
      </div>

      <div v-hasPermi="['yybbsc:day:list']">
        <el-table v-loading="loading" :data="dayList" :default-sort = "{prop: 'reconDate', order: 'descending'}" @sort-change="changeSort">
          <el-table-column label="业务日期" sortable="custom" align="center" prop="reconDate" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.reconDate, '{y}/{m}/{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="放款金额" align="center" prop="loanAmt" width="180px">
            <template slot-scope="scope">
              <span>{{ formaterMoney(scope.row.loanAmt) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="实还本金" align="center" prop="actPrintAmt" width="180px">
            <template slot-scope="scope">
              <span>{{ formaterMoney(scope.row.actPrintAmt) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="利息" align="center" prop="intAmt" width="180px">
            <template slot-scope="scope">
              <span>{{ formaterMoney(scope.row.intAmt) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="罚息" align="center" prop="ointAmt" width="180px">
            <template slot-scope="scope">
              <span>{{ formaterMoney(scope.row.ointAmt) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="复利" align="center" prop="flAmt" width="180px">
            <template slot-scope="scope">
              <span>{{ formaterMoney(scope.row.flAmt) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="提前还款违约金" align="center" prop="advDefineAmt" width="180px">
            <template slot-scope="scope">
              <span>{{ formaterMoney(scope.row.advDefineAmt) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="活动抵扣金额" align="center" prop="deductAmt" width="180px">
            <template slot-scope="scope">
              <span>{{ formaterMoney(scope.row.deductAmt) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="红线减免金额" align="center" prop="reduceAmt" width="180px">
            <template slot-scope="scope">
              <span>{{ formaterMoney(scope.row.reduceAmt) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="用户实还息费" align="center" prop="actIntAmt" width="180px">
            <template slot-scope="scope">
              <span>{{ formaterMoney(scope.row.actIntAmt) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="借条分润" align="center" prop="jtFrAmt" width="180px">
            <template slot-scope="scope">
              <span>{{ formaterMoney(scope.row.jtFrAmt) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="中保分账" align="center" prop="fzAmt" width="180px">
            <template slot-scope="scope">
              <span>{{ formaterMoney(scope.row.fzAmt) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="中保分润" align="center" prop="zbFrAmt" width="180px">
            <template slot-scope="scope">
              <span>{{ formaterMoney(scope.row.zbFrAmt) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="资金贷款余额" align="center" prop="fundBalanceAmt" width="180px">
            <template slot-scope="scope">
              <span>{{ formaterMoney(scope.row.fundBalanceAmt) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="客户贷款余额" align="center" prop="userBalanceAmt" width="180px">
            <template slot-scope="scope">
              <span>{{ formaterMoney(scope.row.userBalanceAmt) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="利润累计" align="center" prop="accumProfitAmt" width="180px">
            <template slot-scope="scope">
              <span>{{ formaterMoney(scope.row.accumProfitAmt) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="代偿本金" align="center" prop="compensatePrintAmt" width="180px">
            <template slot-scope="scope">
              <span>{{ formaterMoney(scope.row.compensatePrintAmt) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="代偿利息" align="center" prop="compensateIntAmt" width="180px">
            <template slot-scope="scope">
              <span>{{ formaterMoney(scope.row.compensateIntAmt) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="代偿罚息" align="center" prop="compensateOintAmt" width="180px">
            <template slot-scope="scope">
              <span>{{ formaterMoney(scope.row.compensateOintAmt) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="代偿总计" align="center" prop="compensateTotalAmt" width="180px">
            <template slot-scope="scope">
              <span>{{ formaterMoney(scope.row.compensateTotalAmt) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="代偿后还款本金" align="center" prop="compensateRepayPrintAmt" width="180px">
            <template slot-scope="scope">
              <span>{{ formaterMoney(scope.row.compensateRepayPrintAmt) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="代偿后还款总金额" align="center" prop="compensateRepayTotalAmt" width="180px">
            <template slot-scope="scope">
              <span>{{ formaterMoney(scope.row.compensateRepayTotalAmt) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true"    width="130px">
            <template slot-scope="scope"    >
              <span style="width: 90px;display: block;" >{{scope.row.remark }}</span>
              <el-button
                size="mini"
                type="text"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['yybbsc:day:updateStsOperateDay']"
              > 编辑
              </el-button>
              <el-button
                size="mini"
                type="text"
                @click="see(scope.row)"
                v-hasPermi="['yybbsc:day:see']"
              > 查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>

    </div>
    <!-- 添加或修改用户配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item  style="height: 160px"  label="备注">
              <el-input style="height: 160px"  v-model="form.remarks" type="textarea" placeholder="请输入备注信息，限500字"  maxlength="500" :autosize="{ minRows: 8, maxRows: 8}" ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">保 存</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 添加或修改用户配置对话框 -->
    <el-dialog :title="title" :visible.sync="openSee" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item  style="height: 160px"  label="备注">
              <el-input style="height: 160px"  v-model="form.remarks" type="textarea" placeholder="信息为空"  maxlength="500" :autosize="{ minRows: 8, maxRows: 8}"   readonly="readonly"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>

    <upload-index :style="{width: '100%'}" ref="bPage" :queryFromFirst="querySecondObj" @emitToParent='getBRes' v-show="detailShow"></upload-index>
    <check-index :style="{width: '100%'}" ref="cPage" :queryFromFirst="querySecondObj" @emitToParent='getCRes' v-show="checkShow"></check-index>
  </div>
</template>

<script>
import {listDay,selectStsOperateDayID,updateStsOperateDay} from "@/api/yybbsc/day";
import {getToken} from '@/utils/auth'
import uploadIndex from "@/views/yybbsc/upload/index"
import checkIndex from "@/views/yybbsc/check/index"
import {addUser, getUser, updateUser} from "@/api/system/user";

export default {
  components: {uploadIndex, checkIndex},
  // name: "Day",
  data() {
    return {
      //控制本页面是否显示第二页-uploadIndex内容
      dayShow: true,
      //控制本页面是否显示第三页-checkIndex内容
      checkShow: false,
      //控制导入页面是否显示
      detailShow: false,
      //第二个和第三个页面-uploadIndex、checkIndex传参对象
      querySecondObj: {
        productNo: null,
      },
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 每日运营统计表格数据
      dayList: [],


      // 表单参数
      form: {},
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openSee: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        reconDate: null,
        productNo: null,
        loanAmt: null,
        actPrintAmt: null,
        intAmt: null,
        ointAmt: null,
        flAmt: null,
        advDefineAmt: null,
        deductAmt: null,
        reduceAmt: null,
        actIntAmt: null,
        jtFrAmt: null,
        fzAmt: null,
        zbFrAmt: null,
        fundBalanceAmt: null,
        userBalanceAmt: null,
        accumProfitAmt: null,
        compensatePrintAmt: null,
        compensateIntAmt: null,
        compensateOintAmt: null,
        compensateTotalAmt: null,
        compensateRepayPrintAmt: null,
        compensateRepayTotalAmt: null,
        remarks: null,
        remark: null,
        id:null,
        orderByColumn : '',
        isAsc: 'descending'
      },

      // 表单校验
      rules: {
        remarks: [
          {required: true, message: "输入信息不能为空", trigger: "blur"},
          {min: 1, max: 500, message: '输入信息长度必须介于 1 和 500 之间', trigger: 'blur'}
        ],
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: {Authorization: "Bearer " + getToken()},


      }
    };
  },
  created() {
    this.queryParams.productNo = '0'
    this.getList();
  },
  methods: {
    // 从后台获取数据,重新排序
    changeSort (val) {
      console.log('val',val);

      // 改变排序规则，然后获取数据
      this.queryParams.orderByColumn = val.prop;
      this.queryParams.isAsc = val.order;
      this.getList()

    },
    /** 第三页返回的值 */
    getCRes(data) {
      if (data === true) {
        this.getList();
        this.checkShow = false;
        this.dayShow = true;
      }
    },
    /** 第二页返回的值 */
    getBRes(data) {
      if (data === true) {
        this.getList();
        this.detailShow = false;
        this.dayShow = true;
      }
    },
    /** 查询每日运营统计列表 */
    getList() {
      this.loading = true;
      listDay(this.queryParams).then(response => {
        this.dayList = response.rows;
        this.total = response.total;
      });
      this.loading = false;
    },
    /** 导入按钮操作 */
    handleImport() {
      this.dayShow = false;
      this.querySecondObj.productNo = '0';
      this.detailShow = true;
      this.$refs.bPage.getList();
    },
    /** 数据核对按钮操作 */
    handleCheck() {
      this.dayShow = false;
      this.querySecondObj.productNo = '0';
      this.checkShow = true;
      this.$refs.cPage.getList();
    },
    /** 格式化金额 */
    formaterMoney(data) {
      if (!data) return '0.00'
      if (data === '-') return '-'
      if (data === '您的业务流程有误，请再次核对！') return '数据错误！'
      // 将数据分割，保留两位小数
      data = data.toFixed(2)
      // 获取整数部分
      const intPart = Math.trunc(data)
      // 整数部分处理，增加,
      const intPartFormat = intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
      // 预定义小数部分
      let floatPart = '.00'
      // 将数据分割为小数部分和整数部分
      const newArr = data.toString().split('.')
      if (newArr.length === 2) { // 有小数部分
        floatPart = newArr[1].toString() // 取得小数部分
        if (1/intPart < 0 && intPart === 0) {
          return '-' + intPartFormat + '.' + floatPart
        }
        return intPartFormat + '.' + floatPart
      }
      if (1/intPart < 0 && intPart === 0) {
        return '-' + intPartFormat + '.' + floatPart
      }
      return intPartFormat + floatPart
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download('yybbsc/day/export', {
        ...this.queryParams
      }, `富邦每日运营报表_${new Date().getTime()}.xlsx`)
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      console.log(row);
        this.form.id  = row.id;
        this.form.remarks  = row.remarks;
        this.open = true;
        this.title = "录入备注信息";
      },
    /** 修改按钮操作 */
    see(row) {
      this.reset();
      console.log(row);
      this.form.id  = row.id;
      this.form.remarks  = row.remarks;
      this.openSee = true;
      this.title = "查看备注信息";
    },
    /** 提交按钮 */
    submitForm(){
       this.$refs["form"].validate(valid => {
         if (valid) {
           if (this.form.id != undefined) {
             updateStsOperateDay(this.form).then(response => {
               this.$modal.msgSuccess("保存成功");
               this.open = false;
               this.getList();
             });
           }
         }
       });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        reconDate: null,
        productNo: null,
        loanAmt: null,
        actPrintAmt: null,
        intAmt: null,
        ointAmt: null,
        flAmt: null,
        advDefineAmt: null,
        deductAmt: null,
        reduceAmt: null,
        actIntAmt: null,
        jtFrAmt: null,
        fzAmt: null,
        zbFrAmt: null,
        fundBalanceAmt: null,
        userBalanceAmt: null,
        accumProfitAmt: null,
        compensatePrintAmt: null,
        compensateIntAmt: null,
        compensateOintAmt: null,
        compensateTotalAmt: null,
        compensateRepayPrintAmt: null,
        compensateRepayTotalAmt: null,
        remarks: null,
      };
      this.resetForm("form");
    },
    },


};
</script>
<style>
.grid-content {
  /* border-radius: 10px;
  height: 50px;
  line-height: 14px; */
  color: #9D9D9D;
  /* font-weight:bold; */
  font-size: 15px;
  text-align: center;
  /*margin-left: 24px;*/
}

.spancol {
  color: #333333;
  font-weight: bold;
  font-size: 15px;
  display: inline-block;
  margin-left: 0;
  /* padding-top:10px; */
}

.spancol1 {
  margin-left: 10px;
}
</style>
