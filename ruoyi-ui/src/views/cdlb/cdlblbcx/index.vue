<!--绿本查询页面-->

<template>
  <div class="app-container">
    <div v-show="firstTime">
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
        label-width="100px"
        style="margin-bottom: 0px"
      >
        <el-form-item
          label="合同编号"
          prop="contractCode"
          style="margin-bottom: 16px"
        >
          <el-input
            v-model="queryParams.contractCode"
            placeholder="请输入"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item
          label="客户姓名"
          prop="clientName"
          style="margin-bottom: 16px"
        >
          <el-input
            v-model="queryParams.clientName"
            placeholder="请输入"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item
          label="身份证号码"
          prop="clientCardId"
          style="margin-bottom: 16px"
        >
          <el-input
            v-model="queryParams.clientCardId"
            placeholder="请输入"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item
          label="车贷借据编号"
          prop="loanNo"
          style="margin-bottom: 16px"
        >
          <el-input
            v-model="queryParams.loanNo"
            placeholder="请输入"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item
          label="绿本状态"
          prop="lbFlag"
          style="margin-bottom: 16px"
        >
          <el-select
            v-model="queryParams.lbFlag"
            clearable
            placeholder="全部"
            @keyup.enter.native="handleQuery"
          >
            <el-option
              v-for="item in optionslbFlags"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="所属担保公司"
          prop="custId"
          style="margin-bottom: 16px"
        >
          <el-select
            v-model="queryParams.custId"
            clearable
            placeholder="全部"
            @keyup.enter.native="handleQuery"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item style="margin-bottom: 16px">
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            element-loading-text="拼命加载中"
            v-loading.fullscreen.lock="fullscreenLoading"
            >搜索</el-button
          >
          <el-button
            icon="el-icon-refresh"
            size="mini"
            @click="resetQuery"
            element-loading-text="拼命加载中"
            v-loading.fullscreen.lock="fullscreenLoading"
            >重置</el-button
          >
        </el-form-item>
      </el-form>

      <div style="font-size: 13px; margin-left: 1px; color: #cccccc">
        共{{ total }}个绿本
      </div>
      <div style="height: 8px"></div>
      <el-table
        v-loading="loading"
        style="line-height: 70px"
        :span-method="objectSpanMethod"
        :data="infoList"
        @selection-change="handleSelectionChange"
      >
        <!--       <el-table-column type="selection" width="55" align="left" />-->
        <el-table-column label="序号" align="left" prop="id" />
        <el-table-column label="所属项目" align="left" prop="projectName" />
        <el-table-column label="所属担保公司" align="left" prop="custNameA" />
        <el-table-column label="绿本状态" align="left" prop="lbFlag">
          <template slot-scope="scope">
            <span>{{ changeStatus(scope.row.lbFlag) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="合同编号" align="left" prop="contractCode">
          <template slot-scope="scope">
            <span
              v-if="
                scope.row.contractCode != null || scope.row.contractCode != ''
              "
              >{{ scope.row.contractCode }}</span
            >
            <span
              v-if="
                scope.row.contractCode == null || scope.row.contractCode == ''
              "
              >-</span
            >
          </template>
        </el-table-column>

        <el-table-column label="客户名称" align="left" prop="clientName" />
        <el-table-column
          label="身份证号"
          align="left"
          prop="clientCardId"
          width="180px"
        />
        <el-table-column label="邮寄日期" align="left" prop="mailDate" />
        <el-table-column label="关联借据情况" align="left" prop="relevance" />
        <el-table-column
          label="车贷借据编号"
          align="left"
          prop="loanNo"
          :show-overflow-tooltip="true"
          width="180px"
        >
          <template slot-scope="scope">
            <a @click="handleUpdates(scope.row)">
              <span style="color: #1e90ff">{{ scope.row.loanNo }}</span>
            </a>
          </template>
        </el-table-column>
        <el-table-column
          label="借据日期"
          align="left"
          prop="applyTime"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.applyTime, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column label="借据金额" align="left" prop="loanAmt" />
        <el-table-column label="借据担保公司" align="left" prop="custName" />
        <!--        <el-table-column label="入库登记日期" align="left" prop="applyTime"/>-->
        <el-table-column
          label="入库登记日期"
          align="left"
          prop="registerTime"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.registerTime, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          class-name="small-padding fixed-width"
          style="width: 40%"
          width="90px"
        >
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="xvznzejiejv(scope.row)"
              >查看借据信息
            </el-button>
          </template>
        </el-table-column>
        <el-table-column
          label=""
          class-name="small-padding fixed-width"
          style="width: 40%"
          width="90px"
        >
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="handleDelete(scope.row)"
              >出入库记录
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.as"
        :limit.sync="queryParams.bs"
        @pagination="getList"
      />

      <!-- 添加或修改车贷绿本管理主对话框 -->
      <el-dialog
        :title="title"
        :visible.sync="open"
        width="700px"
        append-to-body
      >
        <span style="font-size: 13px; margin-left: 1px; color: #cccccc"
          >必须是项目人员才可办理绿本的入库、出库</span
        >
        <div style="height: 30px"></div>
        <el-form ref="form" :model="form" label-width="80px">
          <el-form-item
            label="项目名称："
            prop="projectName"
            label-width="270px"
          >
            <el-input
              v-model="form.projectName"
              placeholder="请输入项目名称"
              style="width: 207px"
            />
          </el-form-item>
          <el-form-item label="担保公司：" label-width="270px">
            <el-select v-model="form.custId" clearable placeholder="请选择">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <span style="font-size: 13px; margin-left: 270px; color: #cccccc"
            >可设置多人。只有该项目的项目经理可以申请绿本出入库</span
          >
          <el-form-item label="项目经理：" label-width="270px">
            <el-select v-model="form.pmID" multiple placeholder="请选择">
              <el-option
                v-for="item in options1"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <span style="font-size: 13px; margin-left: 270px; color: #cccccc"
            >可设置多人。绿本出库时需要由该项目的风险经理进行审核</span
          >
          <el-form-item label="风险经理：" label-width="270px">
            <el-select v-model="form.rmID" multiple placeholder="请选择">
              <el-option
                v-for="item in options2"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <span style="font-size: 13px; margin-left: 270px; color: #cccccc"
            >可设置多人。办理该项目的绿本入库、出库登记</span
          >
          <el-form-item label="综合管理部办公室主任：" label-width="270px">
            <el-select v-model="form.dmID" multiple placeholder="请选择">
              <el-option
                v-for="item in options3"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">修改</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>

      <!--    申请借据提示-->
      <el-dialog
        :title="title"
        :visible.sync="opendatas"
        width="400px"
        append-to-body
      >
        <el-table v-loading="loading" :data="opendatasList">
          <el-table-column label="车贷借据编号" align="left" prop="loanNo">
            <template slot-scope="scope">
              <a @click="handleUpdates(scope.row)">
                <span style="color: #1e90ff">{{ scope.row.loanNo }}</span>
              </a>
            </template>
          </el-table-column>
          <!--        <el-table-column label="操作" class-name="small-padding fixed-width" style="width: 40%" width="90px">
                    <template slot-scope="scope">
                      <el-button
                        size="mini"
                        type="text"
                        @click="shanChuXvanZe(scope.row)"
                      >删除
                      </el-button>
                    </template>
                  </el-table-column>-->
        </el-table>
        <div
          slot="footer"
          style="text-align: center; border-radius: 3px"
          class="dialog-footer"
        >
          <el-button @click="canceldanxvan">关 闭</el-button>
        </div>
      </el-dialog>

      <el-dialog
        :visible.sync="openjjxx"
        width="1000px"
        append-to-body
        :show-close="false"
        style="line-height: 0px"
      >
        <div style="height: 5px; margin-top: -26px">
          <span
            style="font-size: 16px; font-weight: bold; color: rgb(48, 49, 51)"
            >借据信息</span
          >
          <span style="font-size: 13px; margin-left: 1px; color: #cccccc"
            >以下为根据用户身份证号码在车贷系统中查询到的借据信息</span
          >
        </div>
        <el-divider></el-divider>
        <div
          style="
            height: 20px;
            margin-top: 0px;
            font-size: 14px;
            font-weight: bold;
            color: #333333;
          "
        >
          基本信息
        </div>
        <el-descriptions class="margin-top" :column="3">
          <el-descriptions-item
            style="font-size: 14px; color: #f8ac59"
            label="借款申请编号"
            >{{ chaxunjjxx.loanNo }}
          </el-descriptions-item>
          <el-descriptions-item label="合作机构编号">{{
            chaxunjjxx.partnerNo
          }}</el-descriptions-item>
          <el-descriptions-item label="资金方名称">{{
            chaxunjjxx.fundNo
          }}</el-descriptions-item>
          <el-descriptions-item label="担保公司">{{
            chaxunjjxx.custNo
          }}</el-descriptions-item>
          <el-descriptions-item label="借据状态">{{
            chaxunjjxx.loanStatus
          }}</el-descriptions-item>
        </el-descriptions>
        <el-divider></el-divider>
        <div
          style="
            height: 20px;
            margin-top: 0px;
            font-size: 14px;
            font-weight: bold;
            color: #333333;
          "
        >
          产品信息
        </div>
        <el-descriptions class="margin-top" :column="3">
          <el-descriptions-item label="产品编号">{{
            chaxunjjxx.productNo
          }}</el-descriptions-item>
          <el-descriptions-item label="产品名称">{{
            chaxunjjxx.productName
          }}</el-descriptions-item>
          <el-descriptions-item label="进件时间">{{
            chaxunjjxx.applyTime
          }}</el-descriptions-item>
          <el-descriptions-item label="借款用途">{{
            chaxunjjxx.loanUse
          }}</el-descriptions-item>
          <el-descriptions-item label="借款金额">{{
            chaxunjjxx.loanAmt
          }}</el-descriptions-item>
          <el-descriptions-item label="借款期限">{{
            chaxunjjxx.totalTerm
          }}</el-descriptions-item>
          <el-descriptions-item label="放款流水号">{{
            chaxunjjxx.loanReqNo
          }}</el-descriptions-item>
          <el-descriptions-item label="放款时间">{{
            chaxunjjxx.loanTime
          }}</el-descriptions-item>
          <el-descriptions-item label="到期时间">{{
            chaxunjjxx.dueDate
          }}</el-descriptions-item>
          <el-descriptions-item label="对账日期">{{
            chaxunjjxx.reconciliationTime
          }}</el-descriptions-item>
          <el-descriptions-item label="还款方式">{{
            chaxunjjxx.repayWay
          }}</el-descriptions-item>
          <el-descriptions-item label="在贷余额">{{
            chaxunjjxx.balanceAmt
          }}</el-descriptions-item>
        </el-descriptions>
        <el-divider></el-divider>

        <div
          style="
            height: 20px;
            margin-top: 0px;
            font-size: 14px;
            font-weight: bold;
            color: #333333;
          "
        >
          客户信息
        </div>
        <el-descriptions class="margin-top" :column="3">
          <el-descriptions-item label="姓名">{{
            chaxunjjxx.clientName
          }}</el-descriptions-item>
          <el-descriptions-item label="身份证号">{{
            chaxunjjxx.clientCardId
          }}</el-descriptions-item>
          <el-descriptions-item label="身份证地址">{{
            chaxunjjxx.clientCardAddress
          }}</el-descriptions-item>
        </el-descriptions>
        <el-divider></el-divider>
        <div
          style="
            height: 20px;
            margin-top: 0px;
            font-size: 14px;
            font-weight: bold;
            color: #333333;
          "
        >
          车辆信息
        </div>
        <el-descriptions class="margin-top" :column="3">
          <el-descriptions-item label="品牌">{{
            chaxunjjxx.carBrandName
          }}</el-descriptions-item>
          <el-descriptions-item label="车牌号">{{
            chaxunjjxx.carNo
          }}</el-descriptions-item>
          <el-descriptions-item label="车架号">{{
            chaxunjjxx.carVin
          }}</el-descriptions-item>
        </el-descriptions>
        <div slot="footer" class="dialog-footer">
          <el-button style="display: block; margin: 0 auto" @click="cancelimage"
            >关闭</el-button
          >
        </div>
      </el-dialog>
    </div>

    <second-index
      :style="{ width: '100%' }"
      ref="bPage"
      @emitToParent="getBRes"
      v-show="detailShow"
      :optionsData="toBParams"
    ></second-index>
  </div>
</template>

<script>
import {
  addProject,
  delProject,
  getProject,
  listProject,
  updateProject,
} from "@/api/cdlb/project";
import {
  cshlist,
  getcdInfo,
  getcdInfostate,
  leftlistsqa,
  listcdleftInfo,
  listcdleftInfoLvbenchaxun,
  leftlistLvbenchaxunchuku,
} from "@/api/cdlb/info";
import { listloaninfo, getloaninfo } from "@/api/cdlb/loanInfo";
import secondIndex from "@/views/cdlb/cdlbProject/churukjl";

export default {
  components: { secondIndex },
  name: "Cdlblbcx",
  // props，接收从A传递来的参数对象，optionsData和A页面中的保持一直
  props: {
    optionsData: {
      type: Object,
    },
  },
  data() {
    return {
      toBParams: { paramsOne: "", paramsTwo: "" }, //A页面向B页面传递的参数
      paramsOne: "",
      paramsTwo: "",

      firstTime: true,
      //第二页面详细信息是否显示
      detailShow: false,
      //第三页面详细信息是否显示
      detailShow1: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 车贷绿本管理主表格数据
      projectList: [],
      infoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openjjxx: false,
      // 查询参数
      queryParams: {
        /* pageNum: 1,
         pageSize: 10,*/
        as: 1,
        bs: 20,
        lbFlag: null,
        projectName: null,
        custId: null,
        custName: null,
        inApplyCount: null,
        inOkCount: null,
        outApplyCount: null,
        outAuditCount: null,
        outOkCount: null,
        status: null,
        createTime: null,
        pm: null,
        rm: null,
        dm: null,
        updateTime: null,
        loanNos: null,
      },
      // 表单参数
      form: {},
      /*  待提交       待入库        入库完成                       待审核        待出库     出库完成
      10入库录入   11入库申请    12入库登记    13入库完成         20出库申请    21出库审核    22出库登记     23出库完成*/
      optionszt: [
        { value: "", label: "全部" },
        { value: "11", label: "待入库" },
        { value: "12", label: "已入库" },
        { value: "20", label: "待审核" },
        { value: "21", label: "待出库" },
      ],
      options: [],
      options1: [],
      options2: [],
      options3: [],
      optionslbFlags: [
        { value: "10", label: "待提交" },
        { value: "11", label: "待入库" },
        { value: "12", label: "已入库" },
        { value: "20", label: "待审核" },
        { value: "21", label: "待出库" },
        { value: "22", label: "已出库" },
      ],
      value: [],
      valuelbFlags: [],
      value1: [],
      value2: [],
      value3: [],
      //车贷id
      cdlbId: "",
      cddata: {
        projectName: null,
        custName: null,
        pm: null,
        rm: null,
        dm: null,
      },
      fullscreenLoading: false,
      chaxunjjxx: {
        /** 主键 */
        id: null,
        reconciliationTime: null,
        /** 是否绑定绿本信息 Y是 N否 */
        cdlbBinding: null,
        /**已绑定的绿本信息表ID,未绑定时默认为0 */
        cdlbId: null,
        /** 外部系统平台编码 */
        platformNo: null,
        /** 担保公司编码 */
        custNo: null,
        /** 合作方编码 */
        partnerNo: null,
        /** 资金方编码 */
        fundNo: null,
        /** 产品编号 */
        productNo: null,
        /** 借据申请编号 */
        loanNo: null,
        /** 客户姓名 */
        clientName: null,
        /** 身份证号码 */
        clientCardId: null,
        /** 身份证地址 */
        clientCardAddress: null,
        /** 借据状态 */
        loanStatus: null,
        /** 进件时间 */
        applyTime: null,
        /** 借款金额（元） */
        loanAmt: null,
        /** 在贷余额（元） */
        balanceAmt: null,
        /** 借款期限（期数） */
        totalTerm: null,
        /** 放款时间 */
        loanTime: null,
        /** 到期日期 */
        dueDate: null,
        /** 放款流水号 */
        loanReqNo: null,
        /** 还款方式 */
        repayWay: null,
        /** 借款用途 */
        loanUse: null,
        /** 车辆品牌 */
        carBrandName: null,
        /** 车牌号 */
        carNo: null,
        /** 车架号 */
        carVin: null,
        /** 状态，0正常 1禁用 */
        status: null,

        merageArr1: [],
      },
      merageArr1: [],
      meragePos1: 0,
      merageArr2: [],
      meragePos2: 0,

      valuelb: null,
      //向上一个页面返回的结果-告诉第二个页面展示和隐藏
      BRes: false,
      //按钮是否置灰 false-灰色，true-蓝
      buttonFlag: false,
      // 表单校验
      rules: {
        projectName: [
          { required: true, message: "项目名称不能为空", trigger: "blur" },
          {
            min: 1,
            max: 20,
            message: "项目名称长度必须介于 1 和 20 之间",
            trigger: "blur",
          },
        ],
        /*custId: [
          { required: true, message: "担保公司id不能为空", trigger: "blur" }
        ],*/
      },

      opendatas: false,
      opendatasList: [],
    };
  },
  watch: {},

  created() {
    /* this.getList();*/
    this.initData();
  },
  methods: {
    // 初始化参数
    initData() {
      cshlist().then((response) => {
        /*     console.log(response);*/
        this.options = response.data.param1;
      });

      //绿本查询
      listcdleftInfoLvbenchaxun(this.queryParams).then((response) => {
        /*    console.log(response)*/
        this.infoList = response.rows;
        /* let set = new Set([]);
        response.rows.forEach( r =>{
          set.add( r.id)
        })
*/
        this.total = response.total;
        this.merage(response.rows);
        this.loading = false;
      });
      //绿本查询
      /*    listcdleftInfoLvbenchaxun().then(response => {

      /!*    console.log(response)*!/
          this.infoList = response.rows;
          let set = new Set([]);
          response.rows.forEach( r =>{
            set.add( r.id)
          })

          this.total = set.size;
          this.merage(response.rows);
         this.loading = false
        });*/
    },

    chaxunxuangqing(row) {
      listloaninfo(row.id).then((response) => {
        this.projectList = response.rows;
        let set = new Set([]);
        response.rows.forEach((r) => {
          set.add(r.id);
        });

        this.total = set.size;
      });
    },

    /** 第二页返回的值 */
    getBRes(data) {
      if (data === true) {
        this.detailShow = false;
        this.firstTime = true;
      }
    },

    // 取消按钮
    cancelimage() {
      this.openjjxx = false;
    },
    /*  /!*  待提交       待入库        入库完成                       待审核        待出库     出库完成
         10入库录入   11入库申请    12入库登记    13入库完成         20出库申请    21出库审核    22出库登记     23出库完成*!/
      optionszt: [{value:"",label:"全部"},{value:"11",label:"待入库"},{value:"12",label:"已入库"},{value:"20",label:"待审核"},{value:"21",label:"待出库"}],*/
    changeStatus(row) {
      if (row == 10) {
        return "待提交";
      }
      if (row == 11) {
        return "待入库";
      }
      if (row == 12) {
        return "已入库";
      }
      if (row == 19) {
        return "入库驳回";
      }

      if (row == 20) {
        return "待审核";
      }
      if (row == 21) {
        return "待出库";
      }
      if (row == 22) {
        return "已出库";
      }
      if (row == 28) {
        return "审核驳回";
      }
      if (row == 29) {
        return "出库驳回";
      }

      // [{value:"10",label:"待提交"},{value:"11",label:"待入库"},{value:"12",label:"已入库"},{value:"20",label:"待审核"},{value:"21",label:"待出库"},{value:"22",label:"已出库"}],
    },
    // 要合并的数组的方法
    merage(tableData) {
      this.merageInit1();
      for (var i = 0; i < tableData.length; i++) {
        if (i === 0) {
          // 第一行正常显示 必须存在
          this.merageArr1.push(1);
          this.meragePos1 = 0;
        } else {
          /* console.log("merage",tableData[i].id);*/
          // 判断当前元素与上一个元素是否相同 根据是否合并的id
          if (
            typeof tableData[i].id !== "undefined" &&
            tableData[i].id != null &&
            tableData[i].id !== "" &&
            tableData[i].id === tableData[i - 1].id
          ) {
            this.merageArr1[this.meragePos1] += 1;
            this.merageArr1.push(0);
          } else {
            this.merageArr1.push(1);
            this.meragePos1 = i;
          }

          /*if ((typeof tableData[i].projectFeeId !== 'undefined' && tableData[i].projectFeeId != null && tableData[i].projectFeeId !== '') && tableData[i].projectFeeId === tableData[i - 1].projectFeeId) {
            this.merageArr2[this.meragePos2] += 1;
            this.merageArr2.push(0);
          } else {
            this.merageArr2.push(1);
            this.meragePos2 = i;
          }*/
        }
      }
    },
    // 要合并的数组的方法
    merageB(tableData) {
      this.merageInit2();
      for (var i = 0; i < tableData.length; i++) {
        if (i === 0) {
          // 第一行正常显示 必须存在
          /*    this.merageArr1.push(1);
              this.meragePos1 = 0;*/
          this.merageArr2.push(1);
          this.meragePos2 = 0;
        } else {
          // 判断当前元素与上一个元素是否相同 根据是否合并的id
          if (
            typeof tableData[i].id !== "undefined" &&
            tableData[i].id != null &&
            tableData[i].id !== "" &&
            tableData[i].id === tableData[i - 1].id
          ) {
            this.merageArr2[this.meragePos2] += 1;
            this.merageArr2.push(0);
          } else {
            this.merageArr2.push(1);
            this.meragePos2 = i;
          }

          /*if ((typeof tableData[i].projectFeeId !== 'undefined' && tableData[i].projectFeeId != null && tableData[i].projectFeeId !== '') && tableData[i].projectFeeId === tableData[i - 1].projectFeeId) {
            this.merageArr2[this.meragePos2] += 1;
            this.merageArr2.push(0);
          } else {
            this.merageArr2.push(1);
            this.meragePos2 = i;
          }*/
        }
      }
    },
    merageInit2() {
      this.merageArr2 = [];
      this.meragePos2 = 0;
    },
    merageInit1() {
      this.merageArr1 = [];
      this.meragePos1 = 0;
    },
    objectSpanMethodB({ row, column, rowIndex, columnIndex }) {
      if (
        columnIndex === 0 ||
        columnIndex === 1 ||
        columnIndex === 2 ||
        columnIndex === 3 ||
        columnIndex === 4 ||
        columnIndex === 5 ||
        columnIndex === 11
      ) {
        const _row = this.merageArr2[rowIndex];
        const _col = _row > 0 ? 1 : 0; //如果被合并了_row=0则它这个列需要取消
        return {
          rowspan: _row,
          colspan: _col,
        };
      }
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (
        columnIndex === 0 ||
        columnIndex === 1 ||
        columnIndex === 2 ||
        columnIndex === 3 ||
        columnIndex === 4 ||
        columnIndex === 5 ||
        columnIndex === 6 ||
        columnIndex === 7 ||
        columnIndex === 8 ||
        columnIndex === 13 ||
        columnIndex === 14 ||
        columnIndex === 15
      ) {
        const _row = this.merageArr1[rowIndex];
        const _col = _row > 0 ? 1 : 0; //如果被合并了_row=0则它这个列需要取消
        return {
          rowspan: _row,
          colspan: _col,
        };
      }
    },
    xiangmuxq() {
      this.BRes = true;
      this.$emit("emitToParent", this.BRes);
    },

    /** 查询车贷绿本管理主列表 */
    getList() {
      /*     this.loading = true;
      listcdleftInfoLvbenchaxun(this.queryParams).then(response => {
        this.infoList = response.rows;
     /!*   let set = new Set([]);
        response.rows.forEach( r =>{
          set.add( r.id)
        })*!/

        this.total =  response.total
        this.loading = false;
      });
*/
      // this.fullscreenLoading = true;
      if (this.queryParams.lbFlag == null || this.queryParams.lbFlag < 20) {
        // alert(1)
        // this.loading = true;
        listcdleftInfoLvbenchaxun(this.queryParams).then((response) => {
          this.infoList = response.rows;
          /*    let set = new Set([]);
              response.rows.forEach( r =>{
                set.add( r.id)
              })*/

          this.total = response.total;
          this.merage(this.infoList);
          this.fullscreenLoading = false;
          this.loading = false;
        });
      } else {
        // alert(2)
        // this.loading = true;
        leftlistLvbenchaxunchuku(this.queryParams).then((response) => {
          this.infoList = response.rows;
          /*  let set = new Set([]);
            response.rows.forEach( r =>{
              set.add( r.id)
            })
  */
          this.total = response.total;
          this.merage(this.infoList);
          this.fullscreenLoading = false;
          this.loading = false;
        });
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.openjjxx = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectName: null,
        pm: null,
        rm: null,
        dm: null,
        pmID: null,
        rmID: null,
        dmID: null,
        custID: null,
        custId: null,
        custName: null,
        inApplyCount: null,
        inOkCount: null,
        outApplyCount: null,
        outAuditCount: null,
        outOkCount: null,
        dictValue: null,
        status: "0",
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        rmName: null,
        zrName: null,
        pmName: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      // alert(this.queryParams.lbFlag)
      // this.fullscreenLoading = true;
      this.loading = true;
      this.queryParams.as = 1;
      if (this.queryParams.lbFlag == null || this.queryParams.lbFlag < 20) {
        // alert(1)
        listcdleftInfoLvbenchaxun(this.queryParams).then((response) => {
          this.infoList = response.rows;
          /*    let set = new Set([]);
        response.rows.forEach( r =>{
          set.add( r.id)
        })*/

          this.total = response.total;
          this.merage(this.infoList);
          this.loading = false;
        });
      } else {
        // alert(2)
        leftlistLvbenchaxunchuku(this.queryParams).then((response) => {
          this.infoList = response.rows;
          /*  let set = new Set([]);
          response.rows.forEach( r =>{
            set.add( r.id)
          })
*/
          this.total = response.total;
          this.merage(this.infoList);
          this.loading = false;
        });
      }
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      getProject(this.paramsOne).then((response) => {
        this.form = response.data;
      });
      cshlist().then((response) => {
        this.options = response.data.param1;
        this.options1 = response.data.param2;
        this.options2 = response.data.param3;
        this.options3 = response.data.param4;
      });
      this.open = true;
      this.title = "修改车贷项目";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getProject(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改车贷绿本管理主";
      });
    },
    /** 修改按钮操作 */
    handleUpdates(row) {
      //  console.log(" console.log(row)",row)

      if (row.loanId != null) {
        this.reset();
        const id = row.loanId;
        getloaninfo(id).then((response) => {
          //   console.log(response)

          this.chaxunjjxx = response.data;
        });
        this.openjjxx = true;
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (
            this.form.projectName == null ||
            this.form.projectName == "" ||
            this.form.projectName.length > 21
          ) {
            this.$modal.msgWarning("请输入20以内的汉字");
            throw new Error();
          }
          var re = /[^\u4e00-\u9fa5]/;
          if (re.test(this.form.projectName)) {
            this.$modal.msgWarning("请输入20以内的汉字");
            throw new Error();
          }

          if (this.form.custId == null || this.form.custId == "") {
            this.$modal.msgWarning("担保公司不能为空");
            throw new Error();
          }

          if (this.form.pmID.length == 0) {
            this.$modal.msgWarning("项目经理不能为空");
            throw new Error();
          }
          if (this.form.rmID.length == 0) {
            this.$modal.msgWarning("风险经理不能为空");
            throw new Error();
          }
          if (this.form.dmID.length == 0) {
            this.$modal.msgWarning("综合管理部办公室主任不能为空");
            throw new Error();
          }
          // if (this.form.id != null) {
          this.openssa(this.form);
          //   } else {
          //     //this.dupdateData();
          //     this.openss();
          //   }
        }
      });
    },

    poenImage() {},
    /** 新增 */
    addData(row) {
      this.$modal
        .confirm("创建成功后，项目经理即可以对此项目发起绿本入库申请")
        .then(function () {})
        .then(() => {
          this.$refs["form"].validate((valid) => {
            if (valid) {
              if (this.form.id != null) {
                updateProject(this.form).then((response) => {
                  this.$modal.msgSuccess("修改项目成功");
                  this.open = false;
                  this.getList();
                });
              } else {
                addProject(this.form).then((response) => {
                  this.$modal.msgSuccess("创建项目成功");
                  this.open = false;
                  this.getList();
                });
              }
            }
          });
        })
        .catch(() => {});
    },

    openss() {
      const h = this.$createElement;
      this.$msgbox({
        title: "是否创建新的项目？",
        message: h("p", null, [
          h("dev", null, "创建成功后，项目经理即可以对此项目发起绿本入库申请 "),
        ]),
        showCancelButton: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }).then((action) => {
        /*addProject(this.form).then(response => {
          this.$modal.msgSuccess("创建项目成功");
          this.open = false;
          this.getList();
        });*/
      });
    },
    openssa(row) {
      const h = this.$createElement;
      this.$msgbox({
        title: "",
        message: h("p", null, [h("dev", null, "是否保存修改？")]),
        showCancelButton: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }).then((action) => {
        updateProject(row).then((response) => {
          this.$modal.msgSuccess("修改项目成功");
          this.open = false;
          this.initData();
        });
      });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      this.firstTime = false;
      //给第二个页面传数据
      this.toBParams.paramsOne = row.id;
      // console.log("row.id",row.id)
      this.$refs.bPage.initData();
      this.detailShow = true;
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "core/project/export",
        {
          ...this.queryParams,
        },
        `project_${new Date().getTime()}.xlsx`
      );
    },

    //选择借据   多个借据编号
    xvznzejiejv(row) {
      let a = this.infoList.filter((f) => {
        if (f.id == row.id) {
          return f;
        }
      });
      this.xzjj = false;

      this.opendatasList = a;
      /*  this.xzjj=true*/
      this.templateSelection = "";
      this.opendatas = true;
      /*  const   cdlbLoanInfo ={
          cdlbId : null
        }
        cdlbLoanInfo.cdlbId = row.id;
        listloaninfo(cdlbLoanInfo).then(response =>{
          this.opendatasList =response.rows;
          if ( response.total>1){
            this.xzjj=true
          }
          this.opendatas = true
          console.log(response)
        })*/
    },

    //取 消
    canceldanxvan() {
      this.opendatas = false;
    },
  },
};
</script>
