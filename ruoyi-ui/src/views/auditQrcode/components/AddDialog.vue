<template>
  <div>
    <el-dialog
      :title="itemData ? '编辑信息' : '创建信息'"
      :visible.sync="dialogVisible"
      width="700px"
      :before-close="handleClose"
    >
      <div>请填写表单内容，然后点击保存，即会自动生成页面内容链接的二维码</div>
      <div class="item">
        <span>被审计单位名称</span>
        <el-input
          placeholder="请输入"
          v-model="params.auditedUnitName"
          style="width: 350px"
        ></el-input>
      </div>
      <div class="item">
        <span>事务所名称</span>
        <el-input
          placeholder="请输入"
          v-model="params.firmName"
          style="width: 350px"
        ></el-input>
      </div>
      <div class="item">
        <span>委托项目</span>
        <el-input
          placeholder="请输入"
          v-model="params.entrustedProject"
          style="width: 350px"
        ></el-input>
      </div>
      <div class="item">
        <span>报告文号</span>
        <el-input
          placeholder="请输入"
          v-model="params.documentNumber"
          style="width: 350px"
        ></el-input>
      </div>
      <div class="item">
        <span>报告日期</span>
        <el-date-picker
          v-model="params.documentDate"
          style="width: 350px"
          value-format="yyyy-MM-dd"
          format="yyyy 年 MM 月 dd 日"
          type="date"
          placeholder="选择日期"
        >
        </el-date-picker>
      </div>
      <div class="item">
        <span>中国注册会计师1</span>
        <el-input
          placeholder="请输入"
          v-model="params.signAccountant1"
          style="width: 350px"
        ></el-input>
      </div>
      <div class="item">
        <span>中国注册会计师2</span>
        <el-input
          placeholder="请输入"
          v-model="params.signAccountant2"
          style="width: 350px"
        ></el-input>
      </div>
      <div class="item">
        <span>报备状态</span>
        <el-select
          placeholder="请选择"
          v-model="params.reportStatus"
          style="width: 350px"
        >
          <el-option
            v-for="item in qrStatusList"
            :key="item.dictValue"
            placeholder="请选择"
            :value="item.dictValue"
            :label="item.dictLabel"
          ></el-option>
        </el-select>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submit">保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getDicts } from "@/api/system/dict/data";

export default {
  props: {
    itemData: Object,
  },
  data() {
    return {
      dialogVisible: true,
      qrStatusList: [],
      params: {
        auditedUnitName: "",
        firmName: "",
        entrustedProject: "",
        documentNumber: "",
        signAccountant1: "",
        signAccountant2: "",
        reportStatus: "",
        documentDate: "",
      },
      oldJsonDate: null,
    };
  },
  mounted() {
    if (this.itemData) {
      this.oldJsonDate = JSON.parse(JSON.stringify(this.itemData));
      this.params = JSON.parse(JSON.stringify(this.itemData));
    }
    getDicts("qr_report_status").then((res) => {
      if (res.code == 200) {
        this.qrStatusList = res.data;
      }
    });
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
    submit() {
      console.log(this.params);
      if (this.params.id) {
        this.params.oldJsonDate = JSON.stringify(this.oldJsonDate);
        this.params.newJsonDate = JSON.stringify(this.params);
      }
      this.$emit("submit", this.params);
    },
  },
};
</script>

<style lang="less" scoped>
.item {
  margin-top: 16px;
  span {
    margin-right: 8px;
    width: 120px;
    display: inline-block;
    text-align: right;
  }
}
</style>