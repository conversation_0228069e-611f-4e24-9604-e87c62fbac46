<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="98px"
    >
      <el-form-item label="出差开始时间">
        <el-date-picker
          v-model="goErrandStart"
          value-format="yyyy-MM-dd"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleQuery"
          :picker-options="pickerOptions"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="出差结束时间">
        <el-date-picker
          v-model="goErrandEnd"
          value-format="yyyy-MM-dd"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleQuery"
          :picker-options="pickerOptions"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="申请人" prop="applicantName">
        <el-input
          v-model.trim="queryParams.applicantName"
          placeholder="请输入申请人"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
        />
      </el-form-item>

      <el-form-item label="关联项目" prop="projectName">
        <el-input
          v-model.trim="queryParams.projectName"
          placeholder="请输入关联项目"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
        />
      </el-form-item>

      <el-form-item label="出差同行人" prop="companions">
        <el-input
          v-model.trim="queryParams.companions"
          placeholder="请输入出差同行人"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
        />
      </el-form-item>

      <el-form-item label="审核状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择审核状态"
          clearable
          @clear="handleQuery"
          @change="handleQuery"
        >
          <el-option
            v-for="dict in dict.type.check_work_approve_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="生效状态" prop="effective">
        <el-select
          v-model="queryParams.effective"
          placeholder="请选择生效状态"
          clearable
          @clear="handleQuery"
          @change="handleQuery"
        >
          <el-option
            v-for="dict in effectiveList"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="出差起始地点" prop="setOut">
        <el-input
          v-model.trim="queryParams.setOut"
          placeholder="请输入出差起始地点"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
        />
      </el-form-item>
      <el-form-item label="出差到达地点" prop="reach">
        <el-input
          v-model.trim="queryParams.reach"
          placeholder="请输入出差到达地点"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <el-divider></el-divider>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['goErrand:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="add"
          >新建</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['goErrand:export']"
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出列表</el-button
        >
      </el-col>

      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <el-table
      :data="configList"
      @selection-change="handleSelectionChange"
      row-key="id"
      ref="multipleTable"
    >
      <el-table-column
        type="selection"
        width="55"
        align="center"
        fixed="left"
        reserve-selection
      />
      <el-table-column
        type="index"
        label="序号"
        width="50"
        fixed="left"
        :index="columnIndex"
      />
      <el-table-column label="出差单号" align="center" width="130">
        <template #default="{ row }">
          <el-button size="mini" type="text" @click="viewProcessId(row)">{{
            row.businessTripCode
          }}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="申请人" align="center" width="130" fixed="left">
        <template #default="{ row }">
          <el-button size="mini" type="text" @click="getUserData(row,'applicantUserId')">{{
            row.applicantName
          }}</el-button>
        </template>
      </el-table-column>

      <el-table-column label="出差起止地点" align="center" width="220">
        <template #default="{ row }">
          <div
            v-for="(item, index) in row.businessTripSlaveList"
            :key="index"
            style="height: 46px"
            class="flex flex-col justify-start"
          >
            <div>
              {{ item.setOut }}
              <i class="el-icon-right" style="color: rgba(193, 156, 83, 1)"></i
              >{{ item.reach }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="出差起止时间" align="center" width="280">
        <template #default="{ row }">
          <div
            v-for="(item, index) in row.businessTripSlaveList"
            :key="index"
            style="height: 46px"
            class="flex flex-col justify-start"
          >
            <div>
              {{ `${item.startTime} ${item.startTimePeriod}` }} 至
              {{ `${item.endTime} ${item.endTimePeriod}` }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="出发时长(天)"
        align="center"
        prop="times"
        width="140"
      >
        <template #default="{ row }">
          <div
            v-for="(item, index) in row.businessTripSlaveList"
            :key="index"
            style="height: 46px"
            class="flex flex-col justify-start"
          >
            <div>{{ item.times }}</div>
            <div
              v-show="row.businessTripSlaveList.length > 1"
              class="relative bottom-2"
            >
              (合计{{ row.businessTripTimes }}天)
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="交通工具" align="center" prop="vehicle">
        <template #default="{ row }">
          <div
            v-for="(item, index) in row.businessTripSlaveList"
            :key="index"
            style="height: 46px"
          >
            <div>{{ item.vehicle }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="关联项目"
        prop="projectName"
        align="center"
        width="230"
      >
      </el-table-column>
      <el-table-column label="出差同行人" align="center" width="300">
        <template #default="{ row }">
          <div class="cursor-pointer">
            <el-tag
              v-for="item in row.companionsList"
              :key="item.label"
              effect="plain"
              class="mx-2 my-1"
              @click="getUserData(item)"
            >
              {{ item.nickName }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="审核状态" align="center" width="120">
        <template #default="{ row }">
          <div>
            {{ dict.label.check_work_approve_status[row.status] }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="生效状态" align="center" width="120">
        <template #default="{ row }">
          <div>
            {{ effectiveObj[row.effective] }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="审核完成时间"
        align="center"
        prop="auditCompletionTime"
        width="160"
      />
      <el-table-column label="操作" align="center" fixed="right" width="200px">
        <template #default="{ row }">
          <el-button
            size="mini"
            type="text"
            @click="submit(row)"
            v-if="['1'].includes(row.status)"
            >提交</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="handleUpdateOpear(row)"
            v-hasPermi="['goErrand:update']"
            v-if="['1', '4'].includes(row.status)"
            >修改</el-button
          >
          <el-button
            v-hasPermi="['goErrand:delete']"
            size="mini"
            type="text"
            style="color: red"
            @click="handleDelete(row)"
            v-if="['4'].includes(row.status)"
            >删除</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="viewProcessId(row)"
            v-if="['2', '3'].includes(row.status)"
            >查看流程</el-button
          >
          <el-button
            size="mini"
            type="text"
            v-hasPermi="['goErrand:cancel']"
            v-if="
              ['2', '3'].includes(row.status) &&
              row.createBy == $store.getters.name &&
              row.effective == 0
            "
            @click="cancel(row)"
            >作废出差申请</el-button
          >
          <el-dropdown
            @command="handleCommand($event, row)"
            class="ml-2"
            v-if="['1', '4'].includes(row.status)"
          >
            <span class="el-dropdown-link mr-2" style="font-size: 14px">
              >>更多
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                command="submit"
                v-if="['4'].includes(row.status)"
                >提交</el-dropdown-item
              >
              <el-dropdown-item command="view">查看详情</el-dropdown-item>
              <el-dropdown-item
                v-hasPermi="['goErrand:delete']"
                command="delete"
                style="color: red"
                v-if="['1'].includes(row.status)"
                >删除</el-dropdown-item
              >
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <SelectCompany
      v-if="selectCompanyType"
      @close="closeCompany"
      @submit="submitCompany"
    />
    <UserDetail
      v-model="userDetailType"
      @close="userDetailType = false"
      :id="userId"
    />
    <DetailDialogVoid
      v-model="detailDialogVoid"
      @close="detailDialogVoid = false"
      :form="detailDialogVoidForm"
    />
  </div>
</template>

<script>
import {
  businessTripList,
  deleteBusinessTrip,
  getBusinessTrip,
} from "@/api/checkWork/goErrand";
import DetailDialogVoid from "./components/DetailDialogVoid.vue";
import config from "./components/config";
import { clone } from "xe-utils";
export default {
  name: "GoErrand",
  components: { DetailDialogVoid },
  dicts: ["check_work_approve_status"],
  data() {
    return {
      ...config,
      // 显示搜索条件
      showSearch: true,
      // 参数表格数据
      configList: [],
      // 查询参数
      total: 0,
      goErrandStart: [],
      goErrandEnd: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        applicantName: "",
        projectName: "",
        companions: "",
        status: "",
        effective: "",
        setOut: "",
        reach: "",
      },
      multipleSelection: [],
      processData: {},
      selectCompanyType: false,
      userDetailType: false,
      userId: "",
      detailDialogVoidForm: {},
      detailDialogVoid: false,
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.getList();
    },

    /** 查询参数列表 */
    async getList() {
      const { rows, total } = await businessTripList(this.getParams());
      this.configList = rows;
      this.total = total;
    },
    getParams() {
      const params = clone(this.queryParams);
      params.startTimeStart = this.goErrandStart && this.goErrandStart[0];
      params.startTimeEnd = this.goErrandStart && this.goErrandStart[1];
      params.endTimeStart = this.goErrandEnd && this.goErrandEnd[0];
      params.endTimeEnd = this.goErrandEnd && this.goErrandEnd[1];
      return params;
    },
    columnIndex(index) {
      return (
        index + 1 + (this.queryParams.pageNum - 1) * this.queryParams.pageSize
      );
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.goErrandStart = [];
      this.goErrandEnd = [];
      this.handleQuery();
    },
    add() {
      this.$router.push({
        path: "/checkWorkOther/goErrandAdd/add",
        query: {
          type: "add",
          title: "新增出差申请",
        },
      });
    },
    handleUpdateOpear(row) {
      this.$router.push({
        path: "/checkWorkOther/goErrandAdd/" + row.id,
        query: {
          id: row.id,
          type: "update",
          title: row.applicantName + "修改出差申请",
        },
      });
    },
    handleCommand(command, record) {
      const obj = {
        submit: this.submit,
        view: this.view,
        cancel: this.cancel,
        delete: this.handleDelete,
      };
      obj[command](record);
    },

    view(row) {
      this.$router.push({
        path: "/checkWorkOther/goErrandView/" + row.id,
        query: {
          id: row.id,
          title: row.applicantName + "出差申请详情",
        },
      });
    },
    cancel(row) {
      this.detailDialogVoidForm = row;
      this.detailDialogVoid = true;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = [row.id];
      const idNames = row.applicantName;
      this.$modal
        .confirm('是否确认删除申请人为"' + idNames + '"的数据项？')
        .then(function () {
          return deleteBusinessTrip(ids);
        })
        .then(async () => {
          await this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    getUserData(data, userId = "userId") {
      this.userId = data[userId];
      this.userDetailType = true;
    },

    async submit(data) {
      this.submitUpdata(data);
    },

    async submitUpdata(value) {
      this.processData = value;
      const processId = value?.processId;
      if (processId && value.status != 4) {
        this.$router.push({
          path: "/oaWork/processFormView",
          query: {
            oid: processId,
            myActiviteType: true,
          },
        });
        return;
      }
      try {
        this.selectCompanyType = true;
      } catch (error) {}
    },
    submitCompany(e) {
      getBusinessTrip({ companyId: e }).then((res) => {
        if (res.code == 200) {
          this.selectCompanyType = false;
          sessionStorage.setItem(
            "oa-checkWorkGoErrand",
            JSON.stringify(this.processData)
          );
          this.$router.push({
            path: "/oaWork/updateProcessForm",
            query: {
              templateId: res.templateId,
              classificationId: res.classificationId,
              companyId: res.companyId,
              checkWorkGoErrand: true,
            },
          });
        }
      });
    },
    closeCompany() {
      this.selectCompanyType = false;
      this.getList();
    },
    viewProcessId(value) {
      if (!value.processId) {
        return;
      }
      this.$router.push({
        path: "/oaWork/processFormView",
        query: {
          oid: value.processId,
          businessId: value.processId,
        },
      });
    },
    handleSelectionChange(selection) {
      this.multipleSelection = selection;
    },
    handleExport() {
      this.download(
        "/business/trip/export",
        {
          ...this.getParams(),
          ids: this.multipleSelection?.map((item) => item.id)?.join(","),
        },
        `出差申请.xlsx`
      );
    },
  },
};
</script>
<style lang="less" scoped>
</style>
