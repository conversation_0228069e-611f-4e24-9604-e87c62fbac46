<template>
  <div style="font-size: 18px">
    <div>{{ contentData.title }}</div>
    <div
      v-for="(item1, index1) in showList"
      :key="index1"
      style="display: flex"
    >
      <div>{{ item1.label }}:</div>
      <div class="text-black">【{{ contentData[item1.value] }}】</div>
    </div>
    <div
      v-show="
        contentData.businessTripSlaveList &&
        contentData.businessTripSlaveList.length > 1
      "
    >
      <div
        v-for="(item1, index1) in contentData.businessTripSlaveList"
        :key="index1"
        class="my-2"
      >
        <div v-for="(item2, index2) in itemList" :key="index2" class="flex">
          <div>{{ item2.label }}{{ index1 + 1 }}:</div>
          <div class="text-black">【{{ item1[item2.value] }}】</div>
        </div>
      </div>
    </div>
    <div class="mt-2">
      <div class="flex">
        <div v-if="contentData.handleState == '2'">
          {{ isSelf ? "该申请已被拒绝，拒绝原因为" : "取消原因" }}
        </div>
        <div v-else>该申请已被同意</div>
        <div class="mr-1" v-show="contentData.refuseReason">
          【 <span style="color: red">{{ contentData.refuseReason }}</span> 】
        </div>
      </div>
    </div>
  </div>
</template>
    
    <script>
import { businessTripHandleId } from "@/api/checkWork/goErrand";
export default {
  name: "GoErrandRemind",
  data() {
    return {
      showList: [],
      showListSelfOne: Object.freeze([
        { label: "出差申请人", value: "applicantName" },
        { label: "出差单号", value: "businessTripCode" },
        { label: "出差起止地点", value: "startEndaddress" },
        { label: "出差起止时间", value: "startEndTime" },
        { label: "出差时长", value: "businessTripTimes" },
        { label: "交通工具", value: "vehicle" },
        { label: "关联项目", value: "projectName" },
        { label: "出差同行人", value: "companionsList" },
        { label: "审核状态", value: "status" },
      ]),
      showListSelfMore: Object.freeze([
        { label: "出差申请人", value: "applicantName" },
        { label: "出差单号", value: "businessTripCode" },
        { label: "关联项目", value: "projectName" },
        { label: "出差同行人", value: "companionsList" },
        { label: "审核状态", value: "status" },
      ]),
      showListNotSelfOne: Object.freeze([
        { label: "出差申请人", value: "applicantName" },
        { label: "出差单号", value: "businessTripCode" },
        { label: "出差起止地点", value: "startEndaddress" },
        { label: "出差起止时间", value: "startEndTime" },
        { label: "出差时长", value: "businessTripTimes" },
        { label: "交通工具", value: "vehicle" },
        { label: "关联项目", value: "projectName" },
        { label: "出差同行人", value: "companionsList" },
      ]),
      showListNotSelfMore: Object.freeze([
        { label: "出差申请人", value: "applicantName" },
        { label: "出差单号", value: "businessTripCode" },
        { label: "关联项目", value: "projectName" },
        { label: "出差同行人", value: "companionsList" },
      ]),
      itemList: Object.freeze([
        { label: "出差起止地点", value: "startEndaddress" },
        { label: "出差起止时间", value: "startEndTime" },
        { label: "出差时长", value: "times" },
        { label: "交通工具", value: "vehicle" },
      ]),
      data: JSON.parse(this.$route.query.row),
      contentData: {},
      isSelf: false,
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    async init() {
      const { data } = await businessTripHandleId(this.data.correlationId);
      if (data.applicantUserId == this.data.disposeUser) {
        this.isSelf = true;
        data.title = `您在${this.data.createTime}申请取消了一条出差申请`;
      } else {
        data.title = `【${data.applicantName}】 于${
          this.data.createTime
        }取消了一条审核状态为 【${
          this.$store.state.data.KV_MAP.check_work_approve_status[data.status]
        }】 的出差申请:`;
      }
      if (data.businessTripSlaveList?.length == 1 && this.isSelf) {
        this.showList = this.showListSelfOne;
      } else if (data.businessTripSlaveList?.length == 1 && !this.isSelf) {
        this.showList = this.showListNotSelfOne;
      } else if (data.businessTripSlaveList?.length > 1 && this.isSelf) {
        this.showList = this.showListSelfMore;
      } else if (data.businessTripSlaveList?.length > 1 && !this.isSelf) {
        this.showList = this.showListNotSelfMore;
      }
      const item = data.businessTripSlaveList[0];
      data.startEndaddress = `${item.setOut} → ${item.reach}`;
      data.startEndTime = `${item.startTime} ${item.startTimePeriod} 至 ${item.endTime} ${item.endTimePeriod}`;
      data.vehicle = item.vehicle;
      data.businessTripTimes = `${data.businessTripTimes}天`;
      data.businessTripSlaveList.forEach((item, index) => {
        item.startEndaddress = `${item.setOut} → ${item.reach}`;
        item.startEndTime = `${item.startTime} ${item.startTimePeriod} 至 ${item.endTime} ${item.endTimePeriod}`;
        item.times = `${item.times}天(合计${data.businessTripTimes})`;
      });
      data.status =
        this.$store.state.data.KV_MAP.check_work_approve_status[data.status];
      data.companionsList = data.companionsList
        .map((item) => item.nickName)
        ?.join(",");
      this.contentData = data;
    },
  },
};
</script>
    