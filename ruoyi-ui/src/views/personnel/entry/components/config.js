import { getToken } from "@/utils/auth";
const regularCard =
  /^\d{6}((((((19|20)\d{2})(0[13-9]|1[012])(0[1-9]|[12]\d|30))|(((19|20)\d{2})(0[13578]|1[02])31)|((19|20)\d{2})02(0[1-9]|1\d|2[0-8])|((((19|20)([13579][26]|[2468][048]|0[48]))|(2000))0229))\d{3})|((((\d{2})(0[13-9]|1[012])(0[1-9]|[12]\d|30))|((\d{2})(0[13578]|1[02])31)|((\d{2})02(0[1-9]|1\d|2[0-8]))|(([13579][26]|[2468][048]|0[048])0229))\d{2}))(\d|X|x)$/;
const regularName = new RegExp(
  "^([\u4E00-\uFA29]|[\uE7C7-\uE7F3]|[a-zA-Z·]){1,20}$"
);
const regularSalaryAccount = /^[1-9]\d{9,29}$/;
const regularPhone = /^(?:(?:\+|00)86)?1\d{10}$/;
const regularProbationMonths = /^\d+(?=\.{0,1}\d+$|$)/;
export default{
  regular: Object.freeze({
    regularCard,
    regularName,
    regularSalaryAccount,
    regularPhone,
  }),
  rules: Object.freeze({
    name: [
      { required: true, message: "请输入员工姓名", trigger: "blur" },
      {
        pattern: regularName,
        message: "请输入合法的员工姓名",
        trigger: "change",
      },
    ],
    onboardingCompany: [
      { required: true, message: "请选择入职公司", trigger: "change" },
    ],
    onboardingDept: [
      { required: true, message: "请选择入职部门", trigger: "change" },
    ],
    onboardingPost: [
      { required: true, message: "请选择入职岗位", trigger: "change" },
    ],
    idCard: [
      {
        pattern: regularCard,
        message: "请输入合法的身份证号码",
        trigger: "change",
      },
      { required: true, message: "请输入身份证号", trigger: "change" },
    ],
    phoneNum: [
      { required: true, message: "请输入手机号", trigger: "change" },
      {
        pattern: regularPhone,
        message: "请输入合法的手机号",
        trigger: "change",
      },
    ],
    sex: [{ required: true, message: "请选择性别", trigger: "change" }],
    currentAddress: [{ required: true, message: "请输入现住址", trigger: "change" }],
    onboardingTime: [
      { required: true, message: "请选择入职日期", trigger: "change" },
    ],
    officeSpace: [
      { required: true, message: "请输入办公场地", trigger: "blur" },
    ],
    probationMonths: [
      { required: true, message: "请输入试用期期限", trigger: "blur" },
      {
        pattern: regularProbationMonths,
        message: "请输入合法的试用期期限",
        trigger: "change",
      },
    ],
    leaderName: [
      { required: true, message: "请选择直接上级", trigger: "change" },
    ],
    politicalLandscape: [
      { required: true, message: "请选择政治面貌", trigger: "change" },
    ],
    openingBank: [
      { required: true, message: "请输入开户行", trigger: "change" },
    ],
    salaryAccount: [
      { required: true, message: "请输入工资账号", trigger: "blur" },
      {
        pattern: regularSalaryAccount,
        message: "请输入合法的工资账号",
        trigger: "change",
      },
    ],
    sysName: [{ required: true, message: "请输入系统登录名", trigger: "blur" }],
    initialPassword: [
      { required: true, message: "请输入初始密码", trigger: "blur" },
    ],
    fileList: [{ required: true, message: "请上传文件" }],
  }),
  pickerOptions: Object.freeze({
    shortcuts: [
      {
        text: "今天",
        onClick(picker) {
          picker.$emit("pick", new Date());
        },
      },
      {
        text: "昨天",
        onClick(picker) {
          const date = new Date();
          date.setTime(date.getTime() - 3600 * 1000 * 24);
          picker.$emit("pick", date);
        },
      },
      {
        text: "一周前",
        onClick(picker) {
          const date = new Date();
          date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
          picker.$emit("pick", date);
        },
      },
    ],
  }),
  upload: Object.freeze({
    // 是否禁用上传
    // isUploading: false,
    // 设置上传的请求头部
    headers: { Authorization: "Bearer " + getToken() },
    // 上传的地址
    url: process.env.VUE_APP_BASE_API + "/personnel/onboarding/uploadFile",
  }),
  auditState: Object.freeze({
    1: "审核通过",
    2: "未审核",
    3: "审核不通过",
    4: "审核中",
  }),
  /** 转换数据结构 */
  normalizer(node){
    if (node.children && !node.children.length) {
      delete node.children;
    }
    return {
      id: node.id,
      label: node.label,
      children: node.children,
    };
  },
  accessCardList: Object.freeze([
    { label: "是", value: "0" },
    { label: "否", value: "1" },
  ]),

  attendanceEntryList: Object.freeze([
    { label: "是", value: "0" },
    { label: "否", value: "1" },
  ]),
};
