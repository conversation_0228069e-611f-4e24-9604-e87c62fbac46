<template>
  <CommonDetailRoom  type="add" @submitForm="submitForm" />
</template>

<script>
import { meetingRoomAdd} from "@/api/meeting/management";

import CommonDetailRoom from "@/views/meeting/components/commonDetailRoom.vue";
export default {
  name: "ManagementAdd",
  components: { CommonDetailRoom },
  data() {
    return {};
  },
  watch: {},
  computed: {},
  mounted() {
  },
  methods: {
    async submitForm(value){
      await meetingRoomAdd(value);
      this.$router.go(-1);
    },
    
  },
};
</script>
<style lang="less" scoped>
</style>

