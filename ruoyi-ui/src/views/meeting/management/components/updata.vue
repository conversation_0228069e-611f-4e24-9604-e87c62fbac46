<template>
  <CommonDetailRoom
    type="updata"
    @submitForm="submitForm"
    :formProp="formProp"
  />
</template>

<script>
import { meetingRoomUpdata, meetingRoomDetail } from "@/api/meeting/management";

import CommonDetailRoom from "@/views/meeting/components/commonDetailRoom.vue";
export default {
  name: "ManagementUpdata",
  components: { CommonDetailRoom },
  data() {
    return {
      formProp: {},
    };
  },
  watch: {},
  computed: {},
  mounted() {
    this.init();
  },
  methods: {
    async init() {
      const { data } = await meetingRoomDetail(this.$route.params.id);
      this.formProp = data;
    },
    async submitForm(value) {
      await meetingRoomUpdata(value);
      this.$message.success("操作成功!");
      this.$router.go(-1);
    },
  },
};
</script>
<style lang="less" scoped>
</style>

