<template>
  <CommonDetailRoom type="view"  :formProp="formProp" />
</template>

<script>
import { meetingRoomDetail } from "@/api/meeting/management";
import CommonDetailRoom from "@/views/meeting/components/commonDetailRoom.vue";
export default {
  name: "ManagementView",
  components: { CommonDetailRoom },
  data() {
    return {
      formProp: {},
    };
  },
  watch: {},
  computed: {},
  mounted() {
    this.init();
  },
  methods: {
    async init() {
      const { data } = await meetingRoomDetail(this.$route.params.id);
      this.formProp = data;
    },
  },
};
</script>
<style lang="less" scoped>
</style>

