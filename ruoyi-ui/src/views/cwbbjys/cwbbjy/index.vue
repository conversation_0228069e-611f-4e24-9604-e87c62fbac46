<template>
  <div class="app-container">
    <el-form
      ref="form"
      :model="fromData"
      :rules="rules"
      style="text-align: center"
    >
      <div v-show="showProgress" style="text-align: center">
        <div style="margin-bottom: 20px !important">
          <el-row type="flex" justify="center" style="height: 30px">
            <el-col>
              <el-form-item
                style="display: flex; justify-content: center"
                label="公司名称："
                prop="guaranteeCompanyId"
              >
                <el-select
                  v-model="fromData.guaranteeCompanyId"
                  clearable
                  placeholder="请选择"
                  @change="getreportTypeList()"
                >
                  <el-option
                    v-for="item in custList"
                    :key="item.guaranteeCompanyId"
                    :label="item.guaranteeCompanyName"
                    :value="item.guaranteeCompanyId"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div style="margin-bottom: 20px !important">
          <el-row type="flex" justify="center" style="height: 30px">
            <el-col>
              <el-form-item
                style="display: flex; justify-content: center"
                label="报表类型："
                prop="reportType"
              >
                <el-select
                  v-model="fromData.reportType"
                  clearable
                  placeholder="请选择"
                  @change="getreportNameList()"
                >
                  <el-option
                    v-for="item in dict.type.report_date_type.filter(
                      (itemT) => reportTypeList.indexOf(itemT.value) > -1
                    )"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div style="margin-bottom: 20px !important">
          <el-row type="flex" justify="center" style="height: 30px">
            <el-col>
              <el-form-item
                style="display: flex; justify-content: center"
                label="报表名称："
                prop="id"
              >
                <el-select v-model="fromData.id" clearable placeholder="请选择">
                  <el-option
                    v-for="item in reportNameList"
                    :key="item.id"
                    :label="item.reportFormsName"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>
      <div v-show="showProgress" style="height: 20px"></div>
      <!-- 上传组件的div-->
      <div v-show="showProgress" style="text-align: center">
        <div>
          <span style="">上传待校验文件</span>
          <el-upload
            ref="uploadExcel"
            :limit="2"
            accept=".xlsx, .xls"
            :headers="upload.headers"
            :action="upload.url + '?id=' + fromData.id"
            :before-remove="beforeRemove"
            :on-change="handleChange"
            :before-upload="beforeUpload"
            :on-progress="handleFileUploadProgress"
            :on-success="handleFileSuccess"
            :on-remove="handleRemove"
            :auto-upload="false"
            :file-list="upFileList"
            :data="uploadData"
            drag
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <div class="el-upload__tip" slot="tip">
              <span style="font-size: 13px; color: #cccccc"
                >请选择《.xlsx / .xls》文件导入</span
              ><br />
            </div>
          </el-upload>
        </div>
        <br />
        <div
          v-if="buttonFlag == false"
          style="width: 100%; height: 60px; line-height: 45px"
        >
          <el-button size="mini" type="info" disabled>开始检验</el-button>
        </div>
        <div
          v-if="buttonFlag == true"
          style="width: 100%; height: 60px; line-height: 45px"
        >
          <el-button size="mini" type="primary" @click="handleImport"
            >开始检验</el-button
          >
        </div>
      </div>
      <div v-show="showProgressSubsequent">
        <el-table
          v-loading="showProgressSubsequent"
          element-loading-text="上传中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgb(255, 255, 255)"
          style="width: 100%"
        />
      </div>
      <div
        v-if="showProgress === false && showProgressSubsequent === false"
        style="text-align: center"
      >
        <div
          style="
            font-size: 23px;
            font-weight: bold;
            height: 50px;
            color: #131313;
            text-align: left;
          "
        >
          {{ fileName }}
        </div>
        <div
          style="
            font-size: 15px;
            margin-left: 1px;
            color: #bdbbbb;
            height: 20px;
            float: left;
            display: inline;
          "
        >
          公司：

          <template
            v-for="item in custList.filter(
              (item) => item.guaranteeCompanyId === fromData.guaranteeCompanyId
            )"
          >
            {{ item.guaranteeCompanyName }}
          </template>
        </div>
        <div
          style="
            font-size: 15px;
            margin-left: 1px;
            color: #bbb7b7;
            width: 300px;
            height: auto;
            float: left;
            display: inline;
          "
        >
          报表类型：
          <template
            v-for="item in dict.type.report_date_type.filter(
              (item) => item.value === fromData.reportType
            )"
          >
            {{ item.label }}
          </template>
        </div>
        <div
          style="
            font-size: 15px;
            margin-left: 1px;
            color: #bbb7b7;
            width: 300px;
            height: auto;
            float: left;
            display: inline;
          "
        >
          报表名称：
          <template
            v-for="item in reportNameList.filter(
              (item) => item.id === fromData.id
            )"
          >
            {{ item.reportFormsName }}
          </template>
        </div>
        <div style="height: 18px"></div>
        <el-divider></el-divider>
        <div style="height: 18px"></div>
        <div>
          <el-table v-loading="loading" :data="cwbbjyfhzList" border>
            <el-table-column label="效验规则" align="center" prop="key" />
            <el-table-column label="结果" align="center" prop="value">
              <template slot-scope="scope">
                <span v-if="scope.row.value == '校验通过'"
                  ><i class="el-icon-success" style="color: #1ab394"></i>
                  通过</span
                >
                <span
                  v-if="scope.row.value !== '校验通过'"
                  style="color: #f34303"
                  ><i class="el-icon-circle-close" style="color: #f34303"></i
                  >{{ scope.row.value }}</span
                >
              </template>
            </el-table-column>
          </el-table>
          <div style="height: 50px"></div>
          <div slot="footer" class="dialog-footer">
            <el-button @click="cancel">关闭</el-button>
          </div>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script>
import { custList, reportTypeList, reportNameList } from "@/api/cwbbjy/cwbbjy";
import { getToken } from "@/utils/auth";
export default {
  dicts: ["sys_normal_disable", "report_date_type"],
  data() {
    return {
      fileName: null,
      // 遮罩层
      loading: true,
      showProgress: true,
      //文件集合
      upFileList: [],
      //上传携带的参数
      fromData: {
        guaranteeCompanyId: "",
        reportFormsName: null,
        reportType: null,
        id: null,
      },
      uploadData: {
        guaranteeCompanyId: "",
        reportFormsName: null,
        reportType: null,
        id: null,
      },
      buttonFlag: false,
      showProgressSubsequent: false,
      // 用户导入参数
      upload: {
        // 是否禁用上传
        // isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/core/cwbbjy/importData",
        // 对应的id
      },
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 财务报校验表格数据
      custList: [],
      reportTypeList: [],
      reportNameList: [],
      cwbbjyfhzList: [],
      // 表单参数
      form: {},

      // 表单校验
      rules: {
        guaranteeCompanyId: [
          { required: true, message: "公司不能为空", trigger: "blur" },
        ],
        id: [{ required: true, message: "报表名称不能为空", trigger: "blur" }],
        reportType: [
          { required: true, message: "报表类型不能为空", trigger: "blur" },
        ],
      },
    };
  },
  watch: {},
  created() {
    this.getcustList();
  },
  methods: {
    getcustList() {
      this.loading = true;
      this.fromData.id = null;
      this.fromData.reportType = null;
      this.reportTypeList = [];
      custList().then((response) => {
        this.custList = response.data;
      });
    },
    getreportTypeList() {
      this.loading = true;
      this.fromData.id = null;
      this.fromData.reportType = null;
      this.reportTypeList = [];
      reportTypeList(this.fromData).then((response) => {
        var tempList = response.data;
        for (var itemE of tempList) {
          this.reportTypeList.push(itemE.reportType);
        }
      });
    },
    getreportNameList() {
      this.loading = true;
      reportNameList(this.fromData).then((response) => {
        this.reportNameList = response.data;
      });
    },

    // 取消按钮
    cancel() {
      this.showProgress = true;
      // this.getList2();
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {};
      this.resetForm("form");
    },

    //文件上传前处理
    // 文件上传 - 上传前
    beforeUpload(file, fileList) {
      let name = file.name;
      this.fileName = name;
      //定义文件最大的限制，单位：MB
      var maxSize = 2048;
      //文件的大小
      var fileSize = file.size / 1024 / 1024;
      //进行文件的判断
      if (fileSize <= 0) {
        this.$message.error("上传文件大小不能为 0 MB");
        return false;
      } else if (fileSize < maxSize) {
        this.uploadData = {}; //上传携带的参数名
        let promise = new Promise((resolve) => {
          this.$nextTick(function () {
            resolve(true);
          });
        });
        return promise;
      } else {
        this.$message.error(`上传文件大小不能超过2G!`);
        return false;
      }
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {},
    // 文件上传成功处理
    handleFileSuccess(response) {
      this.xiangyinganniu = true;
      this.buttonFlag = false;
      console.log("返回数据：", response);
      this.handleRemove();
      this.$refs.uploadExcel.clearFiles();
      // this.submitFileForm(response);
      this.opendaoruwenjian = false;
      this.showProgressSubsequent = false;
      this.showProgress = false;
      this.openchdnggongdr = true;
      this.cwbbjyfhzList = response.data;
      this.loading = false;
    },

    // 提交上传文件
    handleImport() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.xiangyinganniu = true;
          let file = this.$refs.uploadExcel.uploadFiles[0];
          if (file != undefined) {
            this.showProgress = false;
            this.showProgressSubsequent = true;
            this.$refs.uploadExcel.submit();
          } else {
            this.showProgress = true;
            this.showProgressSubsequent = false;
          }
        }
      });
    },
    beforeRemove(file, upFileList) {
      this.buttonFlag = false;
    },
    handleRemove(file, fileList) {},

    handleChange(file, fileList) {
      if (file !== null) {
        if (this.xiangyinganniu == true) {
          this.xiangyinganniu = false;
        } else {
          this.buttonFlag = true;
        }
      }
      if (fileList.length > 1) {
        fileList.splice(0, 1);
      }
    },
  },
};
</script>
