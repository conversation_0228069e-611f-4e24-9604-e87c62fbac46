<template>
  <div>
    <el-dialog
      title="修改立项信息"
      :visible.sync="dialogVisible"
      width="1000px"
      :before-close="handleClose"
    >
      <div class="item" style="display: flex">
        <span>项目名称：</span>
        <div>{{ detail.projectForm.projectName }}</div>
      </div>
      <div class="item" style="display: flex">
        <span>项目负责人：</span>
        <div>
          <div
            style="display: inline-block"
            v-for="item in detail.projectForm.projectPrincipalList"
            :key="item.nickName"
          >
            {{ item.nickName }}，
          </div>
        </div>
      </div>
      <div class="item">
        <span><i>*</i>立项时间：</span>
        <el-date-picker
          v-model="params.projectDate"
          type="date"
          placeholder="选择日期"
        >
        </el-date-picker>
      </div>
      <div class="item" style="display: flex">
        <span><i>*</i> 渠道方：</span>
        <div style="display: flex">
          <div>
            <el-radio
              @change="changeRadio"
              v-model="params.channelType"
              label="1"
              >内部</el-radio
            >
            <div style="color: #999" class="relative top-1">
              内部渠道方为指定公司员工
            </div>
            <div style="margin-top: 12px" v-if="params.channelType == 1">
              <el-select v-model="params.channelForm" multiple="" collapse-tags>
                <el-option
                  v-for="item in qudaofangList"
                  :key="item.userId"
                  :label="item.nickName"
                  :value="item.userId"
                ></el-option>
              </el-select>
              <el-button
                type="primary"
                style="margin-left: 12px"
                size="mini"
                @click="deptUserListType = true"
                >+ 选择用户</el-button
              >
            </div>
          </div>
          <div style="margin-left: 120px">
            <el-radio
              @change="changeRadio"
              v-model="params.channelType"
              label="2"
              >外部</el-radio
            >
            <div style="color: #999" class="relative top-1">
              输入自定义的外部渠道方的名称
            </div>
            <div style="margin-top: 12px" v-if="params.channelType == 2">
              <el-input
                placeholder="请输入"
                v-model="params.channelSide"
              ></el-input>
            </div>
          </div>
        </div>
      </div>
      <div class="item" style="display: flex">
        <span>项目描述：</span>
        <el-input
          type="textarea"
          :rows="4"
          style="width: 700px"
          placeholder="请输入项目描述，限500字"
          v-model="params.projectDescribe"
        >
        </el-input>
      </div>
      <el-divider></el-divider>
      <div style="font-weight: bold; margin-bottom: 12px">联系方式</div>
      <div style="color: #999">
        联系方式信息仅项目负责人、内部渠道方、录入人本人、或管理员可见
      </div>
      <div style="color: #999">
        资金方与资产方联系方式至少有一方需要填写姓名与电话
      </div>
      <div class="project_phone">
        <div class="item">
          <div class="title2">资金方</div>
          <div class="phone">
            <span>联系人姓名：</span
            ><el-input
              v-model="userForm.fundName"
              style="width: 200px"
            ></el-input>
          </div>
          <div class="phone">
            <span>联系人电话：</span
            ><el-input
              v-model="userForm.fundTel"
              style="width: 200px"
            ></el-input>
          </div>
          <div class="phone">
            <span>微信：</span
            ><el-input
              v-model="userForm.fundWx"
              style="width: 200px"
            ></el-input>
          </div>
          <div class="phone">
            <span>所属部门：</span
            ><el-input
              v-model="userForm.fundDept"
              style="width: 200px"
            ></el-input>
          </div>
        </div>
        <div class="item">
          <div class="title2">资产方</div>
          <div class="phone">
            <span>联系人姓名：</span
            ><el-input
              v-model="userForm.productName"
              style="width: 200px"
            ></el-input>
          </div>
          <div class="phone">
            <span>联系人电话：</span
            ><el-input
              v-model="userForm.productTel"
              style="width: 200px"
            ></el-input>
          </div>
          <div class="phone">
            <span>微信：</span
            ><el-input
              v-model="userForm.productWx"
              style="width: 200px"
            ></el-input>
          </div>
          <div class="phone">
            <span>所属部门：</span
            ><el-input
              v-model="userForm.productDept"
              style="width: 200px"
            ></el-input>
          </div>
        </div>
      </div>
      <el-divider></el-divider>
      <div style="margin-left: 31%">
        <div>修改立项信息，需要由管理员进行审核，点击下一步，将发起OA流程</div>
        <div>申请如果被通过，立即生效</div>

        <div>
          审核结果，请关注 [OA办公-我的流程]，或在 [我的项目日志] 中查看
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submit">下一步</el-button>
      </span>
    </el-dialog>
    <DeptUserList
      deptName="渠道"
      @close="deptUserListType = false"
      v-if="deptUserListType"
      @submit="selectQDF"
      :userList="userList"
    />
    <SelectCompany
      v-if="SelectCompanyType"
      @close="SelectCompanyType = false"
      @submit="submitCompany"
    />
  </div>
</template>

<script>
import { getXmglProcessFlow, queryProcessCompany } from "@/api/oa/deploy";
import { getChannelTree, listSelectData } from "@/api/xmgl/project";

import DeptUserList from "../../../businessInformation/projectDeploy/components/DeptUserList/index.vue";
export default {
  components: {
    DeptUserList,
  },
  props: {
    detail: Object,
  },
  data() {
    return {
      SelectCompanyType: false,
      qudaofangList: [],
      deptUserListType: false,
      dialogVisible: true,
      userList: [],
      params: {
        projectDate: "",
        projectDescribe: "",
        channelForm: [],
        channelType: "1",
        channelFormName: [],
        channelSide: "",
      },
      userForm: {
        fundName: "",
        fundTel: "",
        fundWx: "",
        fundDept: "",
        productName: "",
        productTel: "",
        productWx: "",
        productDept: "",
      },
      oldData: null,
      form: null,
      oaModuleType: undefined,
    };
  },
  mounted() {
    console.log(this.detail);
    this.form = JSON.parse(JSON.stringify(this.detail));
    this.oldData = JSON.parse(JSON.stringify(this.detail));
    if (
      this.form.userContactWayList &&
      this.form.userContactWayList.length > 0
    ) {
      let obj = JSON.parse(JSON.stringify(this.form.userContactWayList[0]));
      this.oldData.userform = obj;
    }

    this.params = this.form.projectForm;
    if (
      this.params.projectChannelList &&
      this.params.projectChannelList.length > 0
    ) {
      this.params.projectChannelList.forEach((item) => {
        this.qudaofangList.push({
          userId: item.channelId,
          nickName: item.nickName,
        });
      });
      this.params.channelForm = this.params.projectChannelList.map(
        (item) => item.channelId
      );
      this.params.channelFormName = this.params.projectChannelList.map(
        (item) => item.nickName || item.userName
      );
    }

    if (
      this.form.userContactWayList &&
      this.form.userContactWayList.length > 0
    ) {
      this.userForm = this.form.userContactWayList[0];
    }
    this.oldData.projectForm = JSON.parse(JSON.stringify(this.params));
    this.getChannelTree();
    this.getOaModuleType();
  },
  methods: {
    async getOaModuleType() {
      const { isChannel, isOperation } = await listSelectData();
      this.oaModuleType = isChannel && !isOperation ? 35 : 29;
    },
    async getChannelTree() {
      const { rows } = await getChannelTree();
      this.userList = rows;
    },
    changeRadio() {
      if (this.params.channelType == 1) {
        this.params.channelSide = "";
      } else {
        this.params.channelForm = [];
      }
    },
    submitCompany(v) {
      this.SelectCompanyType = false;
      getXmglProcessFlow({
        oaModuleType: this.oaModuleType,
        companyId: v,
      }).then((res) => {
        if (this.params.channelType == 1) {
          this.params.channelSide = "";
        } else {
          this.params.channelForm = [];
        }
        let data = {
          newData: {
            projectForm: this.params,
            userform: this.userForm,
            deployId: this.params.deployId,
            userId: sessionStorage.getItem("userId"),
          },
          oldData: this.oldData,
        };
        console.log(data);

        sessionStorage.setItem("editLxProData", JSON.stringify(data));
        this.$router.push({
          path: "/oaWork/updateProcessForm",
          query: {
            templateId: res.data.id,
            classificationId: res.data.classificationId,
            companyId: v,
            editLxProject: true,
          },
        });
      });
    },
    submit() {
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (this.userForm.fundTel && !phoneRegex.test(this.userForm.fundTel)) {
        this.$message.warning("手机号格式不正确");
        return;
      }
      if (
        this.userForm.productTel &&
        !phoneRegex.test(this.userForm.productTel)
      ) {
        this.$message.warning("手机号格式不正确");
        return;
      }
      this.SelectCompanyType = true;
    },
    handleClose() {
      this.$emit("close");
    },
    selectQDF(e) {
      this.qudaofangList = e;
      this.params.channelForm = this.qudaofangList.map((item) => item.userId);
      this.params.channelFormName = this.qudaofangList.map(
        (item) => item.userNickName || item.nickName || item.userName
      );
      this.deptUserListType = false;
    },
  },
};
</script>

<style lang="less" scoped>
.item {
  margin-top: 16px;
  span {
    display: inline-block;
    font-weight: bold;
    width: 100px;
    text-align: right;
    margin-right: 9px;
    i {
      color: red;
      margin-right: 5px;
    }
  }
}
.project_phone {
  display: flex;
  justify-content: space-between;
  .item {
    width: 46%;
    .title2 {
      padding-left: 16px;
      font-weight: bold;
      font-size: 16px;
      width: 100%;
      height: 40px;
      line-height: 40px;
      background: #f8f8f9;
    }
    .phone {
      margin-top: 20px;
      span {
        font-weight: bold;
        display: inline-block;
        margin-left: 9px;
        width: 90px;
        text-align: right;
      }
    }
  }
}
</style>
