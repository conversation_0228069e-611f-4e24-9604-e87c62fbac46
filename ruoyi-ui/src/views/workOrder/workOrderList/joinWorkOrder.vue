<template>
    <div class="app-container">
        <!-- 搜索栏 -->
        <el-form :model="queryParams" inline size="small" label-width="82px" v-show="showSearch">
            <el-form-item label="工单主题">
                <el-input v-model="queryParams.workOrderTitle" placeholder="请输入工单主题" clearable />
            </el-form-item>
            <el-form-item label="提出人">
                <el-select filterable v-model="queryParams.requesterId" placeholder="请选择提出人" clearable>
                    <el-option v-for="item in userList" :key="item.userId" :label="item.nickName"
                        :value="item.userId"></el-option>
                </el-select>

            </el-form-item>
            <el-form-item label="当前执行人">
                <el-select filterable v-model="queryParams.currentExecutor" placeholder="请选择执行人" clearable>
                    <el-option v-for="item in userList" :key="item.userId" :label="item.nickName"
                        :value="item.userId"></el-option>
                </el-select>

            </el-form-item>
            <el-form-item label="工单状态">
                <el-select v-model="queryParams.workOrderStatus" placeholder="请选择状态" clearable>
                    <el-option v-for="item in dict.type.work_order_status_type" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="研发进度">
                <el-select v-model="queryParams.rndProgress" placeholder="请选择进度" clearable>
                    <el-option v-for="item in dict.type.work_order_yanfa_jindu_type" :key="item.value"
                        :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="优先级">
                <el-select v-model="queryParams.requirementPriority" placeholder="请选择优先级" clearable>
                    <el-option v-for="item in dict.type.work_order_xuqiu_type" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="提交时间">
                <el-date-picker style="width: 194px;" v-model="queryParams.requirementSubmissionTime" type="date"
                    value-format="yyyy-MM-dd" placeholder="选择日期" />
            </el-form-item>
            <el-form-item label="完成时间">
                <el-date-picker style="width: 194px;" v-model="queryParams.completeTime" type="date"
                    value-format="yyyy-MM-dd" placeholder="选择日期" />
            </el-form-item>
            <el-button size="mini" type="primary" @click="handleSearch" icon="el-icon-search"
                v-hasPermi="['joinWorkOrder:list']">搜索</el-button>
            <el-button size="mini" @click="handleReset" icon="el-icon-refresh">重置</el-button>
        </el-form>

        <el-divider />

        <!-- 操作栏 -->
        <div class="operation-bar">
            <div style="display: flex; align-items: center">
                <el-button type="primary" @click="handleCreate" icon="el-icon-circle-plus"
                    v-hasPermi="['joinWorkOrder:add']">创建工单</el-button>
                <el-button type="warning" @click="handleExport" icon="el-icon-upload"
                    v-hasPermi="['joinWorkOrder:export']">导出</el-button>

            </div>
        </div>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
        <!-- 数据表格 -->
        <el-table :data="tableData" style="width: 100%; margin-top: 20px">
            <el-table-column v-if="this.columns[0].visible" :key="Math.random()" prop="id" label="工单ID"
                           width="100" fixed="left" />
            <el-table-column v-if="this.columns[0].visible" :key="Math.random()" prop="workOrderTitle" label="工单主题"
                min-width="200" fixed="left">
                <template slot-scope="scope">
                    <a @click="handleViewDetail(scope.row)" style="color: #409eff; cursor: pointer;">
                        {{ scope.row.workOrderTitle }}
                    </a>
                </template>
            </el-table-column>
            <el-table-column v-if="this.columns[1].visible" :key="Math.random()" prop="requirementPriority" label="优先级"
                width="120">
                <template slot-scope="scope">
                    {{dict.type.work_order_xuqiu_type.find(item => item.value === scope.row.requirementPriority).label
                    }}
                </template>
            </el-table-column>
            <el-table-column v-if="this.columns[2].visible" :key="Math.random()" prop="requesterName" label="提出人"
                width="120" />
            <el-table-column width="140" v-if="this.columns[3].visible" :key="Math.random()" prop="currentExecutorName"
                label="当前执行人">
                <template slot-scope="scope">
                    <div v-for="(item, index) in scope.row.personnelList.filter(item => item.sourceSecondCategory === 'ORDER_RENYUAN_ZHIXINGREN')"
                        :key="index">
                        {{ item.personnelName }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column v-if="this.columns[4].visible" :key="Math.random()" prop="requirementSubmissionTime"
                label="提交时间" width="160">
                <template slot-scope="scope">
                    {{ $format(scope.row.requirementSubmissionTime, 'yyyy-MM-dd HH:mm:ss') }}
                </template>
            </el-table-column>
            <el-table-column v-if="this.columns[5].visible" :key="Math.random()" prop="acceptanceTime" label="受理时间"
                width="160">
                <template slot-scope="scope">
                    {{ $format(scope.row.acceptanceTime, 'yyyy-MM-dd') }}
                </template>
            </el-table-column>
            <el-table-column v-if="this.columns[6].visible" :key="Math.random()" prop="workOrderStatus" label="工单状态"
                width="120">
                <template slot-scope="scope">
                    {{dict.type.work_order_status_type.find(item => item.value === scope.row.workOrderStatus).label
                    }}
                </template>
            </el-table-column>
            <el-table-column v-if="this.columns[7].visible" :key="Math.random()" prop="rndProgress" label="研发进度"
                width="120">
                <template slot-scope="scope">
                    {{scope.row.rndProgress ? dict.type.work_order_yanfa_jindu_type.find(item => item.value ===
                        scope.row.rndProgress).label : '-'
                    }}
                </template>
            </el-table-column>
            <el-table-column v-if="this.columns[8].visible" :key="Math.random()" prop="workOrderType" label="工单类型"
                width="120">
                <template slot-scope="scope">
                    {{dict.type.work_order_type.find(item => item.value === scope.row.workOrderType).label
                    }}
                </template>
            </el-table-column>
            <el-table-column v-if="this.columns[9].visible" :key="Math.random()" prop="requesterDepartmentName"
                label="提出人部门" width="120" />
            <el-table-column v-if="this.columns[10].visible" :key="Math.random()" prop="requirementBackground"
                label="需求背景" width="160" />
            <el-table-column v-if="this.columns[11].visible" :key="Math.random()" prop="requirementPurpose" label="需求目的"
                width="160" />
            <el-table-column v-if="this.columns[12].visible" :key="Math.random()" prop="requirementDescription"
                label="需求描述" width="160" />
            <el-table-column v-if="this.columns[13].visible" :key="Math.random()" prop="expectedCompletionDate"
                label="期望完成时间" width="160" />
            <el-table-column v-if="this.columns[14].visible" :key="Math.random()" prop="requirementRemark" label="备注"
                width="160" />
            <el-table-column v-if="this.columns[15].visible" :key="Math.random()" prop="requirementImplementationSystem"
                label="实现系统" width="120">
                <template slot-scope="scope">
                    {{scope.row.requirementImplementationSystem ? dict.type.work_order_shixian_system.find(item =>
                        item.value ===
                        scope.row.requirementImplementationSystem).label : '-'
                    }}
                </template>
            </el-table-column>
            <el-table-column v-if="this.columns[16].visible" :key="Math.random()" prop="systemFunctionModule"
                label="系统功能模块" width="120" />
            <el-table-column v-if="this.columns[17].visible" :key="Math.random()" prop="projectRisk" label="项目风险"
                width="120">
                <template slot-scope="scope">
                    {{scope.row.projectRisk ? dict.type.work_order_project_fengxian.find(item => item.value ===
                        scope.row.projectRisk).label : '-'
                    }}
                </template>
            </el-table-column>
            <el-table-column v-if="this.columns[18].visible" :key="Math.random()" prop="" label="相关人" width="120" />
            <el-table-column v-if="this.columns[19].visible" :key="Math.random()" prop="externalStakeholderInfo"
                label="外部相关人" width="120" />
            <el-table-column v-if="this.columns[20].visible" :key="Math.random()" prop="" label="涉及部门" width="120">
                <template slot-scope="scope">
                    <div v-for="(item, index) in scope.row.personnelList.filter(item => item.sourceSecondCategory === 'ORDER_RENYUAN_BUMENXUQIUFUZEREN')"
                        :key="index">
                        {{ item.departmentName }}
                    </div>

                </template>
            </el-table-column>
            <el-table-column v-if="this.columns[21].visible" :key="Math.random()" prop="" label="部门需求负责人" width="150">
                <template slot-scope="scope">
                    <div v-for="(item, index) in scope.row.personnelList.filter(item => item.sourceSecondCategory === 'ORDER_RENYUAN_BUMENXUQIUFUZEREN')"
                        :key="index">
                        {{ item.departmentName + '-' + item.personnelName }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column v-if="this.columns[22].visible" :key="Math.random()" prop="" label="产品经理" width="120">
                <template slot-scope="scope">
                    <div v-for="(item, index) in scope.row.personnelList.filter(item => item.sourceSecondCategory === 'ORDER_RENYUAN_CHANPINJINGLI')"
                        :key="index">
                        {{ item.personnelName }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column v-if="this.columns[23].visible" :key="Math.random()" prop="" label="项目经理" width="120">
                <template slot-scope="scope">
                    <div v-for="(item, index) in scope.row.personnelList.filter(item => item.sourceSecondCategory === 'ORDER_RENYUAN_XIANGMUJINGLI')"
                        :key="index">
                        {{ item.personnelName }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column v-if="this.columns[24].visible" :key="Math.random()" prop="" label="开发人员" width="120">
                <template slot-scope="scope">
                    <div v-for="(item, index) in scope.row.personnelList.filter(item => item.sourceSecondCategory === 'ORDER_RENYUAN_KAIFARENYUAN')"
                        :key="index">
                        {{ item.personnelName }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column v-if="this.columns[25].visible" :key="Math.random()" prop="" label="测试人员" width="120">
                <template slot-scope="scope">
                    <div v-for="(item, index) in scope.row.personnelList.filter(item => item.sourceSecondCategory === 'ORDER_RENYUAN_BUMENXUQIUFUZEREN')"
                        :key="index">
                        {{ item.personnelName }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column v-if="this.columns[26].visible" :key="Math.random()" prop="" label="设计排期" width="180">
                <template slot-scope="scope">
                    {{ scope.row.designScheduleStartDate ? scope.row.designScheduleStartDate + '-' +
                        scope.row.designScheduleEndDate : '-' }}
                </template>
            </el-table-column>
            <el-table-column v-if="this.columns[27].visible" :key="Math.random()"
                prop="developmentScheduleStartDate，developmentScheduleEndDate" label="开发排期" width="180">
                <template slot-scope="scope">
                    {{ scope.row.developmentScheduleStartDate ? scope.row.developmentScheduleStartDate + '-' +
                        scope.row.developmentScheduleEndDate : '-' }}
                </template>
            </el-table-column>
            <el-table-column v-if="this.columns[28].visible" :key="Math.random()"
                prop="testingScheduleStartDate，testingScheduleEndDate" label="测试排期" width="180">
                <template slot-scope="scope">
                    {{ scope.row.testingScheduleStartDate ? scope.row.testingScheduleStartDate + '-' +
                        scope.row.testingScheduleEndDate : '-' }}
                </template>
            </el-table-column>
            <el-table-column v-if="this.columns[29].visible" :key="Math.random()"
                prop="acceptanceTestingScheduleStartDate，acceptanceTestingScheduleEndDate" label="验收测试排期" width="180">
                <template slot-scope="scope">
                    {{ scope.row.acceptanceTestingScheduleStartDate ? scope.row.acceptanceTestingScheduleStartDate + '-'
                        +
                        scope.row.acceptanceTestingScheduleEndDate : '-' }}
                </template>
            </el-table-column>
            <el-table-column v-if="this.columns[30].visible" :key="Math.random()"
                prop="requirementScheduleStartDate，requirementScheduleEndDate" label="需求排期" width="180">
                <template slot-scope="scope">
                    {{ scope.row.requirementScheduleStartDate ? scope.row.requirementScheduleStartDate + '-'
                        +
                        scope.row.requirementScheduleEndDate : '-' }}
                </template>
            </el-table-column>
            <el-table-column v-if="this.columns[31].visible" :key="Math.random()" prop="expectedGoLiveDate"
                label="预计上线时间" width="160" />
            <el-table-column v-if="this.columns[32].visible" :key="Math.random()" prop="fileList" label="需求附件"
                width="250">
                <template slot-scope="scope">
                    <div v-for="(item, index) in scope.row.fileList" :key="index">
                        <el-button type="text" @click="handlePreview(item)">{{ item.fileName }}</el-button>
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" width="280">
                <template slot-scope="{row}">

                    <el-button v-show="row.workOrderStatus === 'ORDER_STATUS_CAOGAO' && row.requesterId == userId"
                        type="primary" v-hasPermi="['joinWorkOrder:add']" @click="operate(row, 'submit')">提交</el-button>
                    <el-button v-hasPermi="['joinWorkOrder:editStatusSl']"
                        v-show="row.workOrderStatus === 'ORDER_STATUS_DAISHOULI'" type="primary"
                        @click="operate(row, 'accept')">受理</el-button>
                    <el-button v-hasPermi="['joinWorkOrder:editStatusTjys']"
                        v-show="row.workOrderStatus === 'ORDER_STATUS_YISHOULI'" type="primary"
                        @click="operate(row, 'submitAccept')">提交验收</el-button>
                    <el-button v-hasPermi="['joinWorkOrder:editStatusYstg']"
                        v-show="row.workOrderStatus === 'ORDER_STATUS_DAIYANSHOU' && row.personnelList.filter(item => item.sourceSecondCategory === 'ORDER_RENYUAN_ZHIXINGREN').map(v => v.personnelId).includes(userId + '')"
                        type="primary" @click="operate(row, 'acceptPass')">验收通过</el-button>
                    <el-button v-hasPermi="['joinWorkOrder:editStatusXqwc']"
                        v-show="row.workOrderStatus === 'ORDER_STATUS_YANSHOUTONGGUO'" type="primary"
                        @click="operate(row, 'complete')">需求完成</el-button>
                    <el-button v-hasPermi="['joinWorkOrder:editStatusBjcg']"
                        v-show="row.workOrderStatus === 'ORDER_STATUS_CAOGAO' && row.requesterId == userId" type="text"
                        @click="operate(row, 'editDraft')">编辑草稿</el-button>
                    <el-button v-hasPermi="['joinWorkOrder:edit']"
                        v-show="['ORDER_STATUS_YISHOULI', 'ORDER_STATUS_DAIYANSHOU', 'ORDER_STATUS_YANSHOUTONGGUO'].includes(row.workOrderStatus)"
                        type="text" @click="operate(row, 'edit')">修改工单</el-button>
                    <el-button v-hasPermi="['joinWorkOrder:editStatusQxxq']"
                        v-show="['ORDER_STATUS_DAISHOULI', 'ORDER_STATUS_YISHOULI', 'ORDER_STATUS_DAIYANSHOU', 'ORDER_STATUS_YANSHOUTONGGUO'].includes(row.workOrderStatus)"
                        type="text" @click="operate(row, 'cencle')">取消需求</el-button>
                    <el-button v-hasPermi="['joinWorkOrder:editStatusZtxq']"
                        v-show="['ORDER_STATUS_DAISHOULI', 'ORDER_STATUS_YISHOULI', 'ORDER_STATUS_DAIYANSHOU', 'ORDER_STATUS_YANSHOUTONGGUO'].includes(row.workOrderStatus)"
                        type="text" @click="operate(row, 'stop')">暂停需求</el-button>
                    <el-button v-hasPermi="['joinWorkOrder:editStatusHfzt']"
                        v-show="row.workOrderStatus === 'ORDER_STATUS_YIZHANTING'" type="text"
                        @click="operate(row, 'restore')">恢复暂停</el-button>
                    <el-button v-hasPermi="['joinWorkOrder:delete']"
                        v-show="row.workOrderStatus === 'ORDER_STATUS_CAOGAO' && row.requesterId == userId" type="text"
                        @click="operate(row, 'delete')">删除</el-button>

                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" @pagination="getList" />
        <AddWorkOrder v-if="addType" @close="close" :itemData="itemData" />
        <EditWorkOrder v-if="editType" @close="close" @success="close" :itemData="itemData" />
        <Detail v-if="detailType" @close="close" :itemData="itemData" type="joinWorkOrder" />
        <el-image ref="previewImg" v-show="false" :src="photoUrl" :preview-src-list="imagePreviewUrls"></el-image>
    </div>
</template>

<script>
import { checkPermi } from "@/utils/permission"; // 权限判断函数
import { formRequest } from '@/utils/request'
import { downloadByUrl } from "@/api/oa/processTemplate";
import {

    listUser,
} from "@/api/system/user";
import { getFilesPathMapping } from "@/api/cdlb/files";
import { getDicts } from "@/api/system/dict/data";
import { getCurrentUserInfo } from "@/api/system/user";
import { workOrderList } from "@/api/workOrder/workOrder";
import AddWorkOrder from './components/AddWorkOrder.vue'
import EditWorkOrder from './components/EditWorkOrder.vue'
import Detail from './components/Detail.vue'
export default {
    dicts: ['work_order_type', "work_order_xuqiu_type", "work_order_status_type", 'work_order_yanfa_jindu_type', 'work_order_shixian_system', 'work_order_project_fengxian', 'work_order_dongtai_type', 'work_order_renyuan_type',],
    components: {
        AddWorkOrder,
        EditWorkOrder,
        Detail
    },
    data() {
        return {
            photoUrl: '',
            imagePreviewUrls: [],
            detailType: false,
            editType: false,
            itemData: null,
            addType: false,
            columns: [
                { key: 0, label: `工单主题`, visible: true },
                { key: 1, label: `优先级`, visible: true },
                { key: 2, label: `提出人`, visible: true },
                { key: 3, label: `当前执行人`, visible: true },
                { key: 4, label: `提出时间`, visible: true },
                { key: 5, label: `受理时间`, visible: true },
                { key: 6, label: `工单状态`, visible: true },
                { key: 7, label: `研发进度`, visible: true },
                { key: 8, label: `工单类型`, visible: false },
                { key: 9, label: `提出人部门`, visible: false },
                { key: 10, label: `需求背景`, visible: false },
                { key: 11, label: `需求目的`, visible: false },
                { key: 12, label: `需求描述`, visible: false },
                { key: 13, label: `期望完成时间`, visible: false },
                { key: 14, label: `备注`, visible: false },
                { key: 15, label: `实现系统`, visible: false },
                { key: 16, label: `系统功能模块`, visible: false },
                { key: 17, label: `项目风险`, visible: false },
                { key: 18, label: `相关人`, visible: false },
                { key: 19, label: `外部相关人`, visible: false },
                { key: 20, label: `涉及部门`, visible: false },
                { key: 21, label: `部门需求负责人`, visible: false },
                { key: 22, label: `产品经理`, visible: false },
                { key: 23, label: `项目经理`, visible: false },
                { key: 24, label: `开发人员`, visible: false },
                { key: 25, label: `测试人员`, visible: false },
                { key: 26, label: `设计排期`, visible: false },
                { key: 27, label: `开发排期`, visible: false },
                { key: 28, label: `测试排期`, visible: false },
                { key: 29, label: `验收测试排期`, visible: false },
                { key: 30, label: `需求排期`, visible: false },
                { key: 31, label: `预计上线时间`, visible: false },
                { key: 32, label: `需求附件`, visible: false },

            ],
            showSearch: true,
            showSearch: true,
            isMeSubmmitFlag: false,
            isMeJoinFlag: false,
            isDoingFlag: false,
            isDaiShouLiFlag: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                isMeSubmmitFlag: 1,
                isMeJoinFlag: 0,
                isDoingFlag: 1,
                isDaiShouLiFlag: 1,
                workOrderTitle: '',
                requesterId: '',
                currentExecutor: '',
                workOrderStatus: '',
                rndProgress: '',
                requirementPriority: '',
                requirementSubmissionTime: '',
                completeTime: ''
            },
            // 过滤条件
            filter: {
                myCreated: false,
                myInvolved: false,
                inProgress: false,
                pending: false
            },
            // 表格数据
            tableData: [],
            total: 0,
            // 选项列表
            statusOptions: [],
            progressOptions: [],
            priorityOptions: [],
            userList: [],
            userId: null,

        }
    },
    mounted() {

        this.userId = sessionStorage.getItem('userId')
        listUser().then((response) => {
            this.userList = response.rows
        });
        console.log(this.dict.type.work_order_xuqiu_type);
        if (this.checkPermi(["joinWorkOrder:list"])) {
            this.getList()
        } else {
            this.$message.warning('您没有权限查看工单列表');
        }

    },
    methods: {
        checkPermi,
        handleViewDetail(v) {
            if (this.checkPermi(["joinWorkOrder:queryDetails"])) {
                this.itemData = v
                this.detailType = true
            }

        },
        // 获取工单列表
        getList() {
            // 调用API接口
            console.log('获取工单列表', this.queryParams)

            workOrderList({ ...this.queryParams }).then(res => {

                this.tableData = res.rows
                this.total = res.total
            })
        },
        // 搜索
        handleSearch() {
            this.queryParams.pageNum = 1
            this.getList()
        },
        // 重置
        handleReset() {
            this.queryParams = {
                pageNum: 1,
                pageSize: 10,
                workOrderTitle: '',
                requesterId: '',
                currentExecutor: '',
                workOrderStatus: '',
                rndProgress: '',
                requirementPriority: '',
                requirementSubmissionTime: '',
                completeTime: '',
                isMeSubmmitFlag: 1,
                isMeJoinFlag: 0,
                isDoingFlag: 1,
                isDaiShouLiFlag: 1,
            }
            this.getList()
        },
        // 创建工单
        handleCreate() {
            console.log('创建工单')
            this.itemData = null
            this.addType = true

        },
        close() {
            this.addType = false
            this.editType = false
            this.detailType = false
            this.getList()
        },
        // 导出
        handleExport() {
            console.log('导出工单')
            this.download(
                "/system/workOrder/export",
                {
                    ...this.queryParams,
                },
                '工单列表' + `_${new Date().getTime()}.xlsx`,
                'get'
            );
        },
        submitDetail(e) {
            this.detailType = false
            this.operate(this.itemData, e)
        },
        orderStatus(e) {
            if (e == 'ORDER_STATUS_CAOGAO') {
                return '草稿'
            } else if (e == 'ORDER_STATUS_DAISHOULI') {
                return '待受理'
            }
            else if (e == 'ORDER_STATUS_YISHOULI') {
                return '已受理'
            } else if (e == 'ORDER_STATUS_DAIYANSHOU') {
                return '待验收'
            }
            else if (e == 'ORDER_STATUS_YANSHOUTONGGUO') {
                return '验收通过'
            } else if (e == 'ORDER_STATUS_YIWANCHENG') {
                return '需求完成'
            }
            else if (e == 'ORDER_STATUS_YIZHANTING') {
                return '已暂停'
            }
            else if (e == 'ORDER_STATUS_YIQUXIAO') {
                return '已取消'
            }
        },
        // 操作
        operate(row, type) {
            switch (type) {
                case 'submit':
                    //如果row.personnelList中的对象departmentId和personnelId都为空，那么删除这个对象
                    row.personnelList = row.personnelList.filter(item => item.departmentId || item.personnelId)
                    let data = {
                        ...row,
                        files: row.fileList,
                        departmentPersonnelListJson: JSON.stringify(row.personnelList),
                        workOrderStatus: 'ORDER_STATUS_DAISHOULI',
                        dynamicType: 'ORDER_DONGTAI_CHUANGJIANGONGDAN'
                    }
                    formRequest(`/system/workOrder/update`, {
                        ...data
                    }).then(res => {
                        console.log(res);
                        this.$message.success('提交成功');
                        this.getList()
                    })
                    break;
                case 'accept':
                    this.$confirm('是否确认受理此工单？受理后工单将变为已受理状态，并转至工单编辑界面?', '受理工单', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.handleAccept(row)
                    })
                    break;
                case 'submitAccept':
                    this.$confirm('请确定此工单需求已完成，提交验收后，需要由当前执行人点击[验收通过]，项目经理才能完成此工单', '提交验收', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        formRequest(`/system/workOrder/updateStatus`, {
                            workOrderId: row.id,
                            dynamicType: 'ORDER_STATUS_DAIYANSHOU'
                        }).then(res => {
                            console.log(res);
                            this.$message.success('操作成功');
                            formRequest('/system/workOrder/dynamic/publish', {
                                operatorId: sessionStorage.getItem('userId'),
                                dynamicContent: '更新工单状态从[受理]到[提交验收]',
                                workOrderMainId: row.id,
                            })
                            this.getList()
                        })
                    })
                    break;
                case 'acceptPass':
                    this.$confirm('请确定此工单需求已通过验收，验收通过后，需要由项目经理点击[需求完成]结束此工单', '验收通过', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        formRequest(`/system/workOrder/updateStatus`, {
                            workOrderId: row.id,
                            dynamicType: 'ORDER_STATUS_YANSHOUTONGGUO'
                        }).then(res => {
                            console.log(res);
                            this.$message.success('操作成功');
                            formRequest('/system/workOrder/dynamic/publish', {
                                operatorId: sessionStorage.getItem('userId'),
                                dynamicContent: '更新工单状态从[待验收]到[验收通过]',
                                workOrderMainId: row.id,
                            })
                            this.getList()
                        })
                    })

                    break;
                case 'complete':
                    this.$confirm('工单完成后将不能再修改，请确保工单信息正确无误，再点击完成', '需求完成', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        formRequest(`/system/workOrder/updateStatus`, {
                            workOrderId: row.id,
                            dynamicType: 'ORDER_STATUS_YIWANCHENG'
                        }).then(res => {
                            console.log(res);
                            this.$message.success('操作成功');
                            formRequest('/system/workOrder/dynamic/publish', {
                                operatorId: sessionStorage.getItem('userId'),
                                dynamicContent: '更新工单状态从[验收通过]到[需求完成]',
                                workOrderMainId: row.id,
                            })
                            this.getList()
                        })
                    })

                    break;
                case 'editDraft':
                    this.editDraft(row)
                    break;
                case 'edit':
                    this.itemData = row
                    this.editType = true
                    break;
                case 'cencle':
                    console.log(row);

                    this.$confirm('是否确认取消此工单？', '取消需求', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        formRequest(`/system/workOrder/updateStatus`, {
                            workOrderId: row.id,
                            dynamicType: 'ORDER_STATUS_YIQUXIAO'
                        }).then(res => {
                            console.log(res);
                            this.$message.success('操作成功');
                            formRequest('/system/workOrder/dynamic/publish', {
                                operatorId: sessionStorage.getItem('userId'),
                                dynamicContent: `更新工单状态从[${this.orderStatus(row.workOrderStatus)}]到[取消工单]`,
                                workOrderMainId: row.id,
                            })
                            this.getList()
                        })
                    })

                    break;
                case 'stop':
                    this.$confirm('是否确认暂停此工单？暂停后工单将变为已暂停状态，在恢复暂停前不能编辑', '暂停需求', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        formRequest(`/system/workOrder/updateStatus`, {
                            workOrderId: row.id,
                            dynamicType: 'ORDER_STATUS_YIZHANTING'
                        }).then(res => {
                            console.log(res);
                            this.$message.success('操作成功');
                            formRequest('/system/workOrder/dynamic/publish', {
                                operatorId: sessionStorage.getItem('userId'),
                                dynamicContent: `更新工单状态从[${this.orderStatus(row.workOrderStatus)}]到[暂停需求]`,
                                workOrderMainId: row.id,
                            })
                            this.getList()
                        })
                    })

                    break;
                case 'restore':
                    this.$confirm('是否确认恢复此暂停的工单?恢复后工单将变为待受理状态', '恢复暂停', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        formRequest(`/system/workOrder/updateStatus`, {
                            workOrderId: row.id,
                            dynamicType: 'ORDER_STATUS_DAISHOULI'
                        }).then(res => {
                            console.log(res);
                            this.$message.success('操作成功');
                            formRequest('/system/workOrder/dynamic/publish', {
                                operatorId: sessionStorage.getItem('userId'),
                                dynamicContent: `更新工单状态从[暂停需求]到[待受理]`,
                                workOrderMainId: row.id,
                            })
                            this.getList()
                        })
                    })

                    break;
                case 'delete':
                    this.$confirm('是否确认删除此工单?', '删除工单', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        //如果row.personnelList中的对象departmentId和personnelId都为空，那么删除这个对象
                        row.personnelList = row.personnelList.filter(item => item.departmentId || item.personnelId)
                        let data = {
                            ...row,
                            files: row.fileList,
                            departmentPersonnelListJson: JSON.stringify(row.personnelList),
                        }

                        formRequest(`/system/workOrder/update`, {
                            ...data,

                            status: '1'
                        }).then(res => {
                            console.log(res);
                            this.$message.success('删除成功');
                            this.getList()
                        })
                    })

                    break;

            }
        },
        editDraft(row) {
            console.log('编辑草稿', row)
            this.itemData = row
            this.addType = true


        },
        handleAccept(row) {
            console.log('受理', row)
            this.itemData = row
            //如果row.personnelList中的对象departmentId和personnelId都为空，那么删除这个对象
            row.personnelList = row.personnelList.filter(item => item.departmentId || item.personnelId)
            let data = {
                files: row.fileList,
                departmentPersonnelListJson: JSON.stringify(row.personnelList),
            }


            formRequest('/system/workOrder/update', {
                workOrderTitle: row.workOrderTitle,
                workOrderType: row.workOrderType,
                requesterId: row.requesterId,
                requesterDepartmentId: row.requesterDepartmentId,
                requirementBackground: row.requirementBackground,
                requirementPurpose: row.requirementPurpose,
                requirementDescription: row.requirementDescription,
                expectedCompletionDate: row.expectedCompletionDate,
                requirementPriority: row.requirementPriority,
                requirementRemark: row.requirementRemark,
                requirementSubmissionTimeStr: this.$format(row.requirementSubmissionTime, 'yyyy-MM-dd HH:mm:ss'),
                fileIds: row.fileList && row.fileList.length > 0 ? row.fileList.map(item => { return item.id }) : [],
                workOrderStatus: 'ORDER_STATUS_YISHOULI',
                id: row.id,
                dynamicType: 'ORDER_DONGTAI_SHOULIGONGDAN',
                requesterId: row.requesterId,
                ...data
            }).then(res => {
                console.log(res);
                this.$message.success('受理成功')
                this.itemData.id = res.data
                this.detailType = true
                this.getList()

            })
        },

        getDicts() {
            Promise.all([getDicts("work_order_type"), getDicts("work_order_yanfa_jindu_type"), getDicts("work_order_xuqiu_type")]).then((res) => {
                console.log(res);
                this.statusOptions = res[0].data
                this.progressOptions = res[1].data
                this.priorityOptions = res[2].data
            })
        },
        handlePreview(file) {
            console.log(file, "===");

            if (file.fileName.endsWith(".pdf")) {
                //文件是pdf格式
                getFilesPathMapping().then((resp) => {
                    this.pdfUrl = resp.msg + (file.fileAddress || file.fileUrl);
                    window.open(this.pdfUrl);
                    return;
                });
                return;
            } else if (
                file.fileName.endsWith(".jpg") ||
                file.fileName.endsWith(".jpeg") ||
                file.fileName.endsWith(".png") ||
                file.fileName.endsWith(".gif")
            ) {
                //文件是图片格式
                getFilesPathMapping().then((resp) => {
                    this.photoUrl = resp.msg + (file.fileAddress || file.fileUrl);
                    console.log(this.photoUrl);
                    let array = new Set([]);
                    array.add(resp.msg + (file.fileAddress || file.fileUrl));
                    let from = Array.from(array);
                    this.imagePreviewUrls = from;
                    this.$refs.previewImg.showViewer = true;
                });
                // this.showImgViewer = true;
            } else {
                //文件下载
                this.handleDownload(file);
            }
        },

        handleDownload(file) {
            const url = file.fileAddress || file.fileUrl; //图片的https链接
            console.log(url);
            downloadByUrl({
                url: url,
            }).then((res) => {
                let href = window.URL.createObjectURL(new Blob([res])); // 根据后端返回的url对应的文件流创建URL对象
                const link = document.createElement("a"); //创建一个隐藏的a标签
                link.target = "_blank";
                link.href = href; //设置下载的url
                link.download = file.fileName; //设置下载的文件名
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(href); // 释放掉blob对象
            });
        },
    }
}
</script>

<style scoped lang="less">
/deep/ .el-form-item {
    margin-right: 16px !important;
}

.operation-bar {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 20px;
}

.el-switch {
    margin-left: 30px;
}
</style>
