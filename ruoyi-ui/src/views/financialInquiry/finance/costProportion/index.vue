<template>
  <div class="p-5">
    <ShowHeader :header="header" />
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      label-width="70px"
      style="margin-bottom: 10px"
    >
      <el-form-item label="账套" prop="accountSetsId">
        <el-select
          v-model="queryParams.accountSetsId"
          placeholder="请选择账套"
          multiple
          filterable
          @change="handleQuery"
        >
          <el-option
            v-for="(item, index) in accountSetsList"
            :key="index"
            :label="item.companyName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="时间维度" prop="timeDimension">
        <el-select
          v-model="queryParams.timeDimension"
          placeholder="请选择时间维度"
          @change="handleTime"
        >
          <el-option
            v-for="(item, index) in timeDimensionList"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="统计时间" prop="time">
        <el-date-picker
          v-if="queryParams.timeDimension == 'month'"
          v-model="queryParams.time"
          type="monthrange"
          range-separator="至"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          value-format="yyyy-MM"
        >
        </el-date-picker>
        <div v-if="queryParams.timeDimension == 'year'">
          <el-date-picker
            v-model="queryParams.time[0]"
            type="year"
            placeholder="开始年份"
            :pickerOptions="pickerOptionsStart"
            value-format="yyyy"
          >
          </el-date-picker>
          <span class="mx-1">至</span>
          <el-date-picker
            v-model="queryParams.time[1]"
            type="year"
            placeholder="结束年份"
            :pickerOptions="pickerOptionsEnd"
            value-format="yyyy"
          >
          </el-date-picker>
        </div>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <ShowMoney :moneyList="totalProList" :money="totalPro" />

    <el-button
      type="warning"
      plain
      class="mb-2"
      icon="el-icon-download"
      size="mini"
      @click="handleExport"
      >导出</el-button
    >
    <MyTable
      :columns="columns"
      :source="configList"
      :queryParams="queryParams"
      default-expand-all
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    >
    </MyTable>
  </div>
</template>

<script>
import { getAllAccountSetsList } from "@/api/oa/voucharRules";
import { projectParameter } from "@/api/businessInformation/productInformation";
import ShowMoney from "../../components/showMoney";
import ShowHeader from "../../components/showHeader";
import config from "./components/config";
import XEUtils from "xe-utils";
import form from "@/views/financialInquiry/mixin/form";

export default {
  name: "CostProportion",
  components: { ShowMoney, ShowHeader },
  mixins: [form],
  data() {
    return {
      ...config,
      totalProList: [{ label: "管理费用合计", value: "totalPro" }],
      totalPro: {},
      accountSetsList: [],
      queryParams: {
        accountSetsId: undefined,
        time: [],
        timeDimension: "month",
      },
      configList: [],
    };
  },

  created() {
    this.init();
  },
  methods: {
    init() {
      this.getAllAccountSetsList();
      this.getMoney();
      this.getDefaultMonth();
      this.getList();
    },
    async getAllAccountSetsList() {
      const data = await getAllAccountSetsList();
      this.accountSetsList = [...data];
    },
    getMoney() {
      this.totalPro = {
        totalPro: "23,456,789.00",
      };
    },
    getColumns(rows) {
      const value = [
        { three: "11111", five: "55555", name: "账套A" },
        { three: "11111", five: "55555", name: "账套B" },
        { three: "11111", five: "55555", name: "账套C" },
      ];
      let columns = XEUtils.clone(this.columnsInit, true);
    },
    async getList() {
      const { rows } = await projectParameter(this.queryParams);
      this.getColumns(rows);
      this.configList = rows;
    },
    handleTime(value) {
      const obj = {
        month: () => {
          this.getDefaultMonth();
        },
        year: () => {
          this.getDefaultYear();
        },
      };
      obj[value]();
      this.handleQuery();
    },
    handleQuery() {
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleExport() {
      this.download(
        "personnel/onboarding/export",
        {
          ...this.queryParams,
        },
        `费用占比.xlsx`
      );
    },
  },
};
</script>
