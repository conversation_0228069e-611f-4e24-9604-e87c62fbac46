<template>
  <div class="p-5">
    <ShowHeader :header="header" />
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      label-width="70px"
      style="margin-bottom: 10px"
    >
      <el-form-item label="时间维度" prop="timeType">
        <el-select
          v-model="queryParams.timeType"
          placeholder="请选择时间维度"
          @change="handleTime"
        >
          <el-option
            v-for="(item, index) in timeDimensionList"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="统计时间" prop="time">
        <el-date-picker
          v-if="queryParams.timeType == 'month'"
          v-model="queryParams.time"
          type="monthrange"
          range-separator="至"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          value-format="yyyy-MM"
        >
        </el-date-picker>
        <div v-if="queryParams.timeType == 'year'">
          <el-date-picker
            v-model="queryParams.time[0]"
            type="year"
            placeholder="开始年份"
            :pickerOptions="pickerOptionsStart"
            value-format="yyyy"
          >
          </el-date-picker>
          <span class="mx-1">至</span>
          <el-date-picker
            v-model="queryParams.time[1]"
            type="year"
            placeholder="结束年份"
            :pickerOptions="pickerOptionsEnd"
            value-format="yyyy"
          >
          </el-date-picker>
        </div>
        <MyQuarterPicker
          v-if="queryParams.timeType == 'quarter'"
          :value.sync="queryParams.time"
          @change="changeQuarterPicker"
        ></MyQuarterPicker>
      </el-form-item>
      <el-form-item label="账套" prop="accountsSetsIds">
        <el-select
          v-model="queryParams.accountsSetsIds"
          placeholder="请选择账套"
          multiple
          filterable
          @change="handleQuery"
        >
          <el-option
            v-for="(item, index) in accountSetsList"
            :key="index"
            :label="item.companyName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="公司类型" prop="companyTypes">
        <el-select
          v-model="queryParams.companyTypes"
          placeholder="请选择公司类型"
          multiple
          filterable
          @change="handleQuery"
        >
          <el-option
            v-for="(item, index) in companyTypeList"
            :key="index"
            :label="item.dictLabel"
            :value="item.dictCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <ShowMoney :moneyList="totalNetProfitList" :money="totalNetProfit" />
    <el-tabs v-model="activeName" type="card">
      <el-tab-pane label="数据表格" name="table">
        <el-button
          v-hasPermi="['fc:companyNetProfit:export']"
          type="warning"
          plain
          class="mb-2"
          icon="el-icon-download"
          size="mini"
          @click="handleExports"
          >导出</el-button
        >
        <MyTable
          :columns="columns"
          :source="configList"
          :queryParams="queryParams"
        >
        </MyTable>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-tab-pane>
      <el-tab-pane label="图表" name="echarts">
        <MyEchart
          :opationDate="opationDate"
          style="width: 80vw; height: 400px"
        ></MyEchart>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import {
  accountSets,
  companyNetProfitList,
  companyNetProfitExport,
  getCompanyType
} from "@/api/financialInquiry/president";
import ShowMoney from "../../components/showMoney";
import ShowHeader from "../../components/showHeader";
import config from "./components/config";
import XEUtils from "xe-utils";
import form from "@/views/financialInquiry/mixin/form";
import { convertDateToQuarter } from "@/utils";
import exportTable from "@/views/financialInquiry/mixin/exportTable";

export default {
  name: "NetProfit",
  components: { ShowMoney, ShowHeader },
  mixins: [form, exportTable],
  data() {
    return {
      ...config,
      totalNetProfitList: [
        { label: "公司净利润合计", value: "totalNetProfit" },
      ],
      totalNetProfit: {},
      accountSetsList: [],
      companyTypeList: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        accountsSetsIds: undefined,
        companyTypes: undefined,
        time: [],
        timeType: "month",
      },
      activeName: "table",
      configList: [],
      total: 0,
      opationDate: {},
    };
  },

  created() {
    this.init();
  },
  methods: {
    init() {
      this.getAllAccountSetsList();
      this.getDefaultMonth();
      this.getTableEcharts();
      this.getCompanyType();
    },
    async getCompanyType(){
      const data= await getCompanyType();
      this.companyTypeList=data;
    },
    async getTableEcharts() {
      await this.getList();
      this.getOpationDate();
    },
    async getAllAccountSetsList() {
      const { data } = await accountSets();
      this.accountSetsList = [...data];
    },
    changeQuarterPicker(value) {},
    getMoney(map) {
      this.totalNetProfit = {
        totalNetProfit: map.sumTotalAmt.toLocaleString("zh-CN"),
      };
    },
    getParams(exports = false) {
      const params = XEUtils.clone(this.queryParams);
      if(params.time){
         params.startTime = params.time[0].replace(/-/g, ".");
         params.endTime = params.time[1].replace(/-/g, ".");
      }
      if (params.timeType == "quarter") {
        params.startTime = convertDateToQuarter(params.time[0]); 
        params.endTime = convertDateToQuarter(params.time[1]);
      }
      if (!exports) {
        const toSting = ["accountsSetsIds","companyTypes"];
        toSting.forEach((item) => {
          params[item] = params[item]?.join() || undefined;
        });
      }
      delete params.time;
      return params;
    },
    getColumns(rows) {
      const startColumns = [
        { label: "账套", prop: "accountSetsName", width: 300,fixed:'left' },
        { label: "合计", prop: "totalAmt", width: 150,fixed:'left' },
      ];
      this.monthColums = [];
      Object.keys(rows[0]?.amtMap || {}).forEach((item) => {
        this.monthColums.push({
          label: item,
          prop: item,
          width: 150,
        });
      });
      this.columns = startColumns.concat(this.monthColums);
    },
    async getList() {
      const { rows, total, map } = await companyNetProfitList(this.getParams());
      this.getColumns(rows);
      this.getMoney(map);
      this.configList = this.handlerConfigList(rows);
      this.total = total;
    },
    handleTime(value) {
      const obj = {
        month: () => {
          this.getDefaultMonth();
        },
        quarter: () => {
          this.getDefaultMonth(18);
        },
        year: () => {
          this.getDefaultYear();
        },
      };
      obj[value]();
      this.handleQuery();
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getTableEcharts();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    async handleExports() {
      const params = XEUtils.clone(this.getParams(true), true);
      delete params.pageNum;
      delete params.pageSize;
      const rows = await companyNetProfitExport(params);
      const exportData = this.handlerConfigList(rows);
      this.handleExport(exportData, "公司净利润");
    },
    getOpationDate() {
      const xData = this.monthColums.map((item) => item.prop);
      const legendData = this.configList.map((item) => item.accountSetsName);
      const series = this.configList.map((item) => {
        return {
          name: item.accountSetsName,
          type: "bar",
          stack: "accountSetsName",
          data: Object.values(item?.amtMap || {}),
        };
      });
      this.opationDate = {
        tooltip: {
          trigger: "axis",
        },
        toolbox: {
          feature: {
            dataView: { show: true, readOnly: false },
            magicType: { show: true, type: ["line", "bar"] },
            restore: { show: true },
            saveAsImage: { show: true },
          },
        },
        legend: {
          data: legendData,
          right: "10%",
        },
        xAxis: [
          {
            type: "category",
            data: xData,
            axisPointer: {
              type: "shadow",
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "金额",
          },
        ],
        series,
      };
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .pagination-container {
  padding: 0px !important;
}
</style>
