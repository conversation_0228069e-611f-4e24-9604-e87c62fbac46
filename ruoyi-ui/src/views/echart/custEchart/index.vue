<template>
  <div style="width: 1370px;height:2000px; " >

          <div style="width: 1350px;height:20px;"></div>
          <div style="width: 1350px; ">
                <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="70px">
                      <el-form-item label="系统" prop="platformNo">
                                <el-select v-model="platformNoParam" placeholder="请选择系统名称" filterable multiple size="small"
                                          >
                                  <el-option
                                    v-for="dict in platformNoSelect"
                                    :key="dict.value"
                                    :label="dict.label"
                                    :value="dict.value"
                                  />
                                </el-select>
                      </el-form-item>
                      <el-form-item label="担保公司" prop="custNo">
                                <el-select v-model="custNoParam" placeholder="请选择担保公司" filterable multiple size="small"
                                          >
                                  <el-option
                                    v-for="dict in custNoSelect"
                                    :key="dict.value"
                                    :label="dict.label"
                                    :value="dict.value"
                                  />
                                </el-select>
                      </el-form-item>
                      <el-form-item label="合作方" prop="partnerNo">
                              <el-select v-model="partnerNoParam" placeholder="请选择合作方" filterable multiple size="small"
                                        >
                                <el-option
                                  v-for="dict in partnerNoSelect"
                                  :key="dict.value"
                                  :label="dict.label"
                                  :value="dict.value"
                                />
                              </el-select>
                      </el-form-item>
                    <el-form-item label="资金方" prop="fundNo">
                            <el-select v-model="fundNoParam" class="el-select-text" placeholder="请选择资金方" filterable multiple size="small"
                                      >
                              <el-option
                                v-for="dict in fundNoSelect"
                                :key="dict.value"
                                :label="dict.label"
                                :value="dict.value"
                              />
                            </el-select>
                    </el-form-item>
                    <MoreSearch modelCode="ECHARTS" :params="queryParams" v-show="showMoreSearch" byId="companyCode"></MoreSearch>
                    <el-form-item>
                      <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery" class="ml-14">搜索</el-button>
                      <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                      <el-button @click="showMoreSearch=!showMoreSearch;queryParams.moreSearch=undefined" type="text"
                        >更多搜索条件<i  :class="showMoreSearch?'el-icon-arrow-down':'el-icon-arrow-up'"/></el-button
                      >
                    </el-form-item>
                </el-form>
            </div>
        <!-- </el-col>
    </el-row> -->
    <div style="width: 1350px;height:1px;">
          <el-divider></el-divider>
    </div>

    <!-- <el-row type="flex" :gutter="20">
      <el-col :span="1"> </el-col>
      <el-col :span="23"> </el-col>
    </el-row> -->
    <div style="width: 1400px;height:10px;"></div>
        <div v-loading="loading" id="charts1" style="width: 1350px;height:700px; float:right;"></div>
        <div style="width: 1350px;height:1200px;">
          <div style="width: 20px;height:1000px; float:left;"></div>
            <div style="width: 800px;height:1000px; float:left;">
              <span class="spanfont">各担保公司占比</span>
                  <div style="width: 780px;height:1px;">
                    <el-divider></el-divider>
                  </div>
                  <div style="width: 800px;height:20px;"></div>
                  <div v-loading="loading" id="charts3"  style="width: 800px;height:1000px; float:right;">
                  </div>
            </div>
            <div  style="width: 490px;height:610px; float:right">
                <span class="spanfont">各担保公司在贷余额明细</span>
                <div style="width: 490px;height:1px;">
                  <el-divider></el-divider>
                </div>
                <div style="width: 490px;height:20px; float:right"></div>
                 <div v-loading="loading" style="width: 490px;height:1000px; float:right">
                    <el-table
                      :data="tableData"
                      border
                      height="800"
                      :header-cell-style="{fontSize: '14px'}"
                      style="width: 100%;font-size: 14px">
                      <el-table-column
                        prop="dictName"
                        label="担保公司"
                        align="center"
                        width="200">
                      </el-table-column>
                      <el-table-column
                        prop="count"
                        label="在贷余额（万元）"
                        align="center"
                        width="160">
                      </el-table-column>
                      <el-table-column
                        prop="ratio"
                        align="center"
                        label="占比"
                        >
                      </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>


  </div>
</template>
<script>
import * as echarts from 'echarts';
import { getDicts} from "@/api/system/dict/data";
import {userRole}  from "@/api/system/role";
import { homeChartData} from '@/api/system/view'

import {custEchart,fundEchartData} from '@/api/system/echarts'
//  start 引用联级字典查询
import {getSysDictRefList,getSelectSysDictRefList} from '@/api/ref/ref'
import { clone } from "xe-utils";

//  end 引用联级字典查询
export default {
   name: 'CustEchart',
  dicts: ['sys_platform_code', 'product_no', 'fund_no', 'partner_no', 'platform_no', 'cust_no'],
  data() {
    return {
      //X轴数据
      custxaxis:[],
      //柱状图数据
      custpillarData:[],
      //饼图数据
      custpieData:[],
      //表格数据
      tableData:[],
      loginUserRole:"",
      //  start 新增参数
      platformNoParam: '',
      custNoParam: '',
      partnerNoParam: '',
      fundNoParam: '',
      productNoParam: '',


      sysDictRefParam: {
        dictType: '',
        dictValue: '',
        pDictType: '',
        pDictValue: '',
        selectDictDatas:''
      },
      platformNoSelect: [],
      custNoSelect: [],
      fundNoSelect: [],
      partnerNoSelect: [],
      productNoSelect: [],
      // end 新增参数

      //外部系统
      externalsystems:[],
      //担保公司编码
      dbcompany:[],
      //合作方
      partnerdata:[],
      //资金方
      capitaldata:[],
      querydatatype:"cust_no",
      externalsystem:"platform_no",
      partnerscode:"partner_no",
      capitalcode:"fund_no",
      productcode:"product_no",

        // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
       // 表单参数
      form: {},
       // 查询参数
      queryParams: {
        platformNo: null,
        custNo: null,
        partnerNo: null,
        fundNo: null,
        params: {
         moduleTypeOfNewAuth: 'ECHARTS',
        }
      },
      showMoreSearch:false
    };
  }, mounted(){
    this.getList();
    //  start 页面刷新时对数据的处理

    // this.initSelect()
     this.initSelectData()
    //  end 页面刷新时对数据的处理

    this.getexternalsystem();
    this.getdbcompany();
    this.getpartner();
    this.getcapital();
//  this.getLoginUserRole();
  },
  methods:{

     //wzy渲染下拉框
     initSelectData() {
      getSelectSysDictRefList({ unitType: 4, moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.platformNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 0, moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.custNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 3, moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.productNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 2, moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.fundNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 1, moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.partnerNoSelect = response;
      });
    },
     getCustNoList(val) {
      const flag = this.lateByte(this.queryParams.platformNo) > this.lateByte(val.toString())
      this.queryParams.platformNo = this.platformNoParam.toString()
      if (val == null || val === '' || flag) {
        this.custNoSelect = null
        this.partnerNoSelect = null
        this.fundNoSelect = null
        this.productNoSelect = null

        // this.queryParams.custNo = null
        // this.queryParams.partnerNo = null
        // this.queryParams.fundNo = null
        // this.queryParams.productNo = null

        // this.custNoParam = null
        // this.partnerNoParam = null
        // this.fundNoParam = null
        // this.productNoParam = null
        this.sysDictRefParam.dictType = 'cust_no'
        this.sysDictRefParam.pDictType = 'platform_no'
        this.sysDictRefParam.pDictValue = this.queryParams.platformNo
        this.sysDictRefParam.selectDictDatas =""
          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {

            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {

        this.sysDictRefParam.dictType = 'cust_no'
        this.sysDictRefParam.pDictType = 'platform_no'
        this.sysDictRefParam.pDictValue = this.queryParams.platformNo
        this.sysDictRefParam.selectDictDatas =""
       if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            // this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
    getPartnerNoList(val) {
      const flag = this.lateByte(this.queryParams.custNo) > this.lateByte(val.toString())
      this.queryParams.custNo = this.custNoParam.toString()
      if (val == null || val === '' || flag) {
        this.partnerNoSelect = null
        this.fundNoSelect = null
        this.productNoSelect = null

        // this.queryParams.partnerNo = null
        // this.queryParams.fundNo = null
        // this.queryParams.productNo = null

        // this.partnerNoParam = null
        // this.fundNoParam = null
        // this.productNoParam = null
        this.sysDictRefParam.dictType = 'partner_no'
        // this.sysDictRefParam.dictValue=val
        this.sysDictRefParam.pDictType = 'cust_no'
        this.sysDictRefParam.pDictValue = this.queryParams.custNo
        this.sysDictRefParam.selectDictDatas =""

          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {
        this.sysDictRefParam.dictType = 'partner_no'
        // this.sysDictRefParam.dictValue=val
        this.sysDictRefParam.pDictType = 'cust_no'
        this.sysDictRefParam.pDictValue = this.queryParams.custNo
        this.sysDictRefParam.selectDictDatas =""

          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            // this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
    getFundNoList(val) {
      const flag = this.lateByte(this.queryParams.partnerNo) > this.lateByte(val.toString())
      this.queryParams.partnerNo = this.partnerNoParam.toString()
      if (val == null || val === ''|| flag) {
        this.fundNoSelect = null
        this.productNoSelect = null
        // this.queryParams.fundNo = null
        // this.queryParams.productNo = null
        // this.fundNoParam = null
        // this.productNoParam = null
        this.sysDictRefParam.dictType = 'fund_no'
        this.sysDictRefParam.pDictType = 'partner_no'
        this.sysDictRefParam.pDictValue = this.queryParams.partnerNo
         this.sysDictRefParam.selectDictDatas =""

      if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
      else {
        this.sysDictRefParam.dictType = 'fund_no'
        this.sysDictRefParam.pDictType = 'partner_no'
        this.sysDictRefParam.pDictValue = this.queryParams.partnerNo
        this.sysDictRefParam.selectDictDatas =""
         if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }

        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            // this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
    getProductNoList(val) {
      const flag = this.lateByte(this.queryParams.partnerNo) > this.lateByte(val.toString())

      this.queryParams.fundNo = this.fundNoParam.toString()
      if (val == null || val === '' ||flag) {
        this.productNoSelect = null
        this.sysDictRefParam.dictType = 'product_no'
        this.sysDictRefParam.pDictType = 'fund_no'
        this.sysDictRefParam.pDictValue = this.queryParams.fundNo
this.sysDictRefParam.selectDictDatas =""
       if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {
        this.sysDictRefParam.dictType = 'product_no'
        this.sysDictRefParam.pDictType = 'fund_no'
        this.sysDictRefParam.pDictValue = this.queryParams.fundNo

this.sysDictRefParam.selectDictDatas =""
        if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }

        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            // this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
      getProductNoValue(val) {
      const flag = this.lateByte(this.queryParams.productNo) > this.lateByte(val.toString())

      this.queryParams.productNo = this.productNoParam.toString()

       if (val == null || val === '' ||flag) {
        this.productNoSelect = null
        // this.queryParams.productNo = null
        this.productNoParam = null
        this.sysDictRefParam.dictType = ''
        this.sysDictRefParam.pDictType = 'product_no'
        this.sysDictRefParam.pDictValue =''
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
          this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {

        this.sysDictRefParam.dictType = ''
        this.sysDictRefParam.pDictType = 'product_no'
        this.sysDictRefParam.pDictValue = this.queryParams.productNo

        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
          this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }

    },
     lateByte(sTargetStr) {
      var sTmpStr, sTmpChar;
      var nOriginLen = 0;
      var nStrLength = 0;

      sTmpStr = new String(sTargetStr);
      nOriginLen = sTmpStr.length;

      for (var i = 0; i < nOriginLen; i++) {
        sTmpChar = sTmpStr.charAt(i);

        if (escape(sTmpChar).length > 4) {
          nStrLength += 2;
        } else if (sTmpChar != '/r') {
          nStrLength++;
        }
      }
      return nStrLength;
    },
    //end




 /** 查询数据 */
    getList() {
      this.loading = true;
      this.queryParams.platformNo = this.platformNoParam.toString();
      this.queryParams.custNo = this.custNoParam.toString();
      this.queryParams.partnerNo = this.partnerNoParam.toString();
      this.queryParams.fundNo = this.fundNoParam.toString();
      this.queryParams.productNo = this.productNoParam.toString();
      const params=clone(this.queryParams,true);
      params.moreSearch=params.moreSearch&&JSON.stringify(params.moreSearch)
        custEchart(params).then(response => {

          //echart数据
           this.custxaxis = response.xaxis;
           this.custpillarData  = response.pillarEchart;
           this.custpieData = response.pieData;

           this.tableData = response.custTableData;
            //柱状图
            this.partnerPillarEChart();
            //饼图
            this.partnerpie();

          this.loading = false;
        }
      );
    },
 /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
         //  start 重置逻辑更新
      this.resetForm('queryForm')

      this.platformNoParam = ''
      this.custNoParam = ''
      this.partnerNoParam = ''
      this.fundNoParam = ''
      this.queryParams.moreSearch=undefined;
      this.handleQuery()
      this.initSelectData()
      //  end 重置逻辑更新
    },
     // 多选框选中数据
    // handleSelectionChange(selection) {
    //   this.ids = selection.map(item => item.id)
    //   this.single = selection.length !== 1
    //   this.multiple = !selection.length
    // },
      //获取外部系统平台编码
    getexternalsystem(){
        getDicts(this.externalsystem).then(response =>{
            this.externalsystems = response.data;
        } );
    },
    //获取担保公司编码
    getdbcompany(){
        getDicts(this.querydatatype).then(response =>{
            this.dbcompany = response.data;
        } );
    },
    //获取合作方编码
    getpartner(){
        getDicts(this.partnerscode).then(response =>{
            this.partnerdata = response.data;
        } );
    },
    //获取资金方编码
    getcapital(){
        getDicts(this.capitalcode).then(response =>{
            this.capitaldata = response.data;
        } );
    },
     partnerPillarEChart(){
            var myChart = this.$echarts.init(document.getElementById('charts1'));
            var option={
                  tooltip: {
                            trigger: 'axis',
                        },
                         legend: {
                          type:'scroll',
                          left: 'center',
                          top: 50
                       },
                       grid: {
                              x: 125,
                              y: 100,
                              x2: 20,
                              y2: 120,
                              borderWidth: 1,
                            },
                        title: {
                             left: 'left',
                            text: '各担保公司在贷余额分布',
                            padding: [40, 0,0, 10],
                              textStyle: {
                              fontSize: 18,
                              color: '#333333',
                              fontWeight: "normal"
                              }
                        },

                        color: ['#409EFF'],
                         xAxis: {
                            type: 'category',
                            boundaryGap: true,
                            data: this.custxaxis,
                            axisLabel: { show: true, textStyle: {color: '#666666',fontSize : 12} ,rotate : 30}


                        },
                         toolbox: {
                            show: true,
                            feature: {
                                dataZoom: {
                                        yAxisIndex: 'none'
                                          },
                                dataView: { readOnly: false },
                                // 柱状图或者折线图展示
                                magicType: { type: ['line', 'bar'] },

                                restore: {},
                                saveAsImage: {},

                            },
                            top:"60px",
                            right:"14px"
                        },
                        yAxis: {
                          name: "单位（万元）",
                           show: true,axisLine: {show:false}, axisTick: {show:false}, splitLine:{show:true},
                            type: 'value',
                            boundaryGap: ['2%', '100%'],
                              max:function (value) {
                                return value.max + value.max/10;
                            },
                            min:function (value) {
                                return value.min + value.min/10;
                            }
                        },

                        series:  this.custpillarData
             };
              myChart.clear()
            myChart.setOption(option);

  },
     partnerpie(){
             var myChart = this.$echarts.init(document.getElementById('charts3'));
             var option={

                  toolbox: {
                  show: true,
                  feature: {
                        dataView: { readOnly: false },
                        restore: {},
                        saveAsImage: {},
                  }
                },
              color: ['#3AA1FF', '#66CC85', '#F5DD67', '#F15A75'],
                tooltip: {
                  trigger: 'item',
                  formatter: "{b} : {c}({d}%)"
                },
                  legend: {
                          bottom: '260',

                    },
                 series:[
                    {

                        type: 'pie',
                        avoidLabelOverlap: true,
                        radius: '50%',
                        label: {
                          normal:{
                            show: true,
                            color: '#666666'
                            // position: 'center'
                          }
                         },
                         emphasis: {//选中的样式
                            borderColor: 'rgba(0,0,0,0)',
                            borderWidth: 1,
                            label: {
                                show: true,//选中时不显示数据标签,

                            },
                            labelLine: {
                                show: true,//选中时不显示数据标签引导线
                                // length: 50,
                                lineStyle: {
                                    width: 1,
                                     color: '#ff8000',
                                    type: 'solid',

                                }
                            }
                        },
                    center: ['50%', '40%'],
                      labelLine: {//设置延长线的长度
                          normal: {
                              length: 30,//设置延长线的长度
                              length2: 50,//设置第二段延长线的长度
                          }
                      },
                        data:this.custpieData
                    }
                 ]

             };

              myChart.clear()
            myChart.setOption(option);
        },

  },

}
</script>
<style>
.el-row {
    margin-bottom: 20px;
  }
   .grid-content {
    /* border-radius: 10px;
    height: 50px;
    line-height: 14px; */
    color:#9D9D9D;
    /* font-weight:bold; */
    font-size:14px;
    text-align: center;
  }
  .grid-contentfont{
      color:#333333;
    font-weight:bold;
    font-size:14px;
  }
  .grid-contentcol{
    height: 20px;
    line-height: 30px;
    left: 30px;
  }
   .grid-col1 {
    border-radius: 4px;
    height: 36px;

  }
  .bg-purple {
    background: #9D9D9D;
  }
  #col-line {
      float: left;
      width: 1px;
      height: 60px;
      background: 	#E6E6E6;
    }

.span{
    color:#ff8000;
    font-weight:bold;
    font-size:28px;
  }
.spancol{
    color:#333333;
    font-weight:bold;
    font-size:20px;
    display: inline-block;
    padding-top:10px;

}
.spancol2{
    font-size:14px;
    color:#9D9D9D;
    display:block;
}
.echartspan{
  color: 	#007fff;
  font-size:12px;
  font-weight:bold;
  margin-left: 100px;
}
.balancediv{
  width: 100px;
  height:35px;
}
.item {
      margin: 4px;
    }

.spanfont {
  color: 	#333333;
  font-size:18px;
  font-weight:normal;
  font-family:"Microsoft YaHei";
}
</style>



