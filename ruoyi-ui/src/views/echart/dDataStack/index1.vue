<template>
  <div  style="width: 1500px;height:1950px;"   >
   <!--     start 进行添加-->
     <div style="font-size: 15px"> <div :title="tipsMsg"  style="color: red"  ><pre>{{tipsMsg}}</pre></div></div>
<!-- end 结束表头-->
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="130px">
            <el-form-item label="外部系统名称" prop="platformNo">
                      <el-select v-model="platformNoParam" placeholder="请选择系统名称" filterable multiple size="small"
                                >
                        <el-option
                          v-for="dict in platformNoSelect"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        />
                      </el-select>
            </el-form-item>
            <el-form-item label="担保公司" prop="custNo">
                      <el-select v-model="custNoParam" placeholder="请选择担保公司" filterable multiple size="small"
                                >
                        <el-option
                          v-for="dict in custNoSelect"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        />
                      </el-select>
            </el-form-item>
            <el-form-item label="合作方" prop="partnerNo">
                    <el-select v-model="partnerNoParam" placeholder="请选择合作方" filterable multiple size="small"
                              >
                      <el-option
                        v-for="dict in partnerNoSelect"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
            </el-form-item>
          <el-form-item label="资金方" prop="fundNo">
                  <el-select v-model="fundNoParam" placeholder="请选择资金方" filterable multiple size="small"
                            >
                    <el-option
                      v-for="dict in fundNoSelect"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
          </el-form-item>
          <el-form-item label="产品编码" prop="productNo">
              <el-select v-model="productNoParam" placeholder="请选择产品" filterable multiple size="small"
                        >
                <el-option
                  v-for="dict in productNoSelect"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
          </el-form-item>

      <el-form-item label="开始时间">
        <el-date-picker
          v-model="queryParams.portrayalNo"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="结束时间">
        <el-date-picker
          v-model="queryParams.remark"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="统计维度" prop="portrayalType">
          <el-select v-model="queryParams.portrayalType" placeholder="请选择统计维度 "  clearable size="small">
           <el-option
              v-for="dict in statisticalDimension"
             :key="dict.value"
             :label="dict.lable"
             :value="dict.value"
            />
          </el-select>
       </el-form-item>
       <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">统计</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
      </el-form>

        <div v-loading="loading" id="charts" style="width: 100%;height:550px; float:left"></div>
        <div v-loading="loading" id="charts1" style="width: 50%;height:550px; float:right"></div>
        <div v-loading="loading" id="charts2" style="width: 50%;height:550px; float:left"></div>
        <div v-loading="loading" id="charts3" style="width: 50%;height:550px; float:right"></div>
        <div v-loading="loading" id="charts4" style="width: 50%;height:550px; float:left"></div>

   </div>

</template>

<script>
import * as echarts from 'echarts';
import { rundatastack} from '@/api/system/echarts'
//  start 引用联级字典查询
import {getSysDictRefList,getSelectSysDictRefList} from '@/api/ref/ref'
//  end 引用联级字典查询
import {tipsMsg} from '../../data/common'
export default {
  name: 'DDataStack',
  dicts: ['sys_platform_code', 'product_no', 'fund_no', 'partner_no', 'platform_no', 'cust_no'],
  data() {
    return {


tipsMsg:null,
  // 日期范围
      //  start 新增参数
      platformNoParam: '',
      custNoParam: '',
      partnerNoParam: '',
      fundNoParam: '',
      productNoParam: '',


      sysDictRefParam: {
        dictType: '',
        dictValue: '',
        pDictType: '',
        pDictValue: '',
        selectDictDatas:''
      },
      platformNoSelect: [],
      custNoSelect: [],
      fundNoSelect: [],
      partnerNoSelect: [],
      productNoSelect: [],
      // end 新增参数

        // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
       // 日期范围
      dateRange: [],
      xAxisData:[],
      //累计贷款余额
      balanceAndTotalData:[],
      //累计贷款本金
      amountAndTotalData:[],
      //新增贷款本金
      addAmountAndTotalData:[],
      //累计还款本金
      totalPrintAmountAndTotalData:[],
      //新增还款本金
      addRepayAmountAndTotalData:[],
      //统计维度
      statisticalDimension:[
        {
          lable:"外部系统",
          value:"platform_no"
        },
        {
          lable:"担保公司",
          value:"cust_no"
        },{
          lable:"合作方",
          value:"partner_no"
        },{
          lable:"资金方",
          value:"fund_no"
        },{
          lable:"产品",
          value:"product_no"
        }
      ],
       // 表单参数
      form: {},
       // 查询参数
      queryParams: {
        platformNo: null,
        custNo: null,
        partnerNo: null,
        fundNo: null,
        productNo: null,
        vintageDay:null,
        portrayalNo: null,
        remark: null,
        portrayalType:null,
        params: {
         moduleTypeOfNewAuth: 'ECHARTS',
        }
      },
    };
  },
  mounted(){
  this.nowtime();
  this.getList();
   this.tipsMsg= tipsMsg
    //  start 页面刷新时对数据的处理
    // this.initSelect()
     this.initSelectData()
    //  end 页面刷新时对数据的处理

  },
    methods:{

  //start

      //wzy渲染下拉框
      initSelectData() {
      getSelectSysDictRefList({ unitType: 4,moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.platformNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 0,moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.custNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 3,moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.productNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 2,moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.fundNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 1,moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.partnerNoSelect = response;
      });
    },
     getCustNoList(val) {
      const flag = this.lateByte(this.queryParams.platformNo) > this.lateByte(val.toString())
      this.queryParams.platformNo = this.platformNoParam.toString()
      if (val == null || val === '' || flag) {
        this.custNoSelect = null
        this.partnerNoSelect = null
        this.fundNoSelect = null
        this.productNoSelect = null

        // this.queryParams.custNo = null
        // this.queryParams.partnerNo = null
        // this.queryParams.fundNo = null
        // this.queryParams.productNo = null

        // this.custNoParam = null
        // this.partnerNoParam = null
        // this.fundNoParam = null
        // this.productNoParam = null
        this.sysDictRefParam.dictType = 'cust_no'
        this.sysDictRefParam.pDictType = 'platform_no'
        this.sysDictRefParam.pDictValue = this.queryParams.platformNo
        this.sysDictRefParam.selectDictDatas =""
          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
          
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {

        this.sysDictRefParam.dictType = 'cust_no'
        this.sysDictRefParam.pDictType = 'platform_no'
        this.sysDictRefParam.pDictValue = this.queryParams.platformNo
        this.sysDictRefParam.selectDictDatas =""
       if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            // this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
    getPartnerNoList(val) {
      const flag = this.lateByte(this.queryParams.custNo) > this.lateByte(val.toString())
      this.queryParams.custNo = this.custNoParam.toString()
      if (val == null || val === '' || flag) {
        this.partnerNoSelect = null
        this.fundNoSelect = null
        this.productNoSelect = null

        // this.queryParams.partnerNo = null
        // this.queryParams.fundNo = null
        // this.queryParams.productNo = null

        // this.partnerNoParam = null
        // this.fundNoParam = null
        // this.productNoParam = null
        this.sysDictRefParam.dictType = 'partner_no'
        // this.sysDictRefParam.dictValue=val
        this.sysDictRefParam.pDictType = 'cust_no'
        this.sysDictRefParam.pDictValue = this.queryParams.custNo
        this.sysDictRefParam.selectDictDatas =""

          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {
        this.sysDictRefParam.dictType = 'partner_no'
        // this.sysDictRefParam.dictValue=val
        this.sysDictRefParam.pDictType = 'cust_no'
        this.sysDictRefParam.pDictValue = this.queryParams.custNo
        this.sysDictRefParam.selectDictDatas =""

          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            // this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
    getFundNoList(val) {
      const flag = this.lateByte(this.queryParams.partnerNo) > this.lateByte(val.toString())
      this.queryParams.partnerNo = this.partnerNoParam.toString()
      if (val == null || val === ''|| flag) {
        this.fundNoSelect = null
        this.productNoSelect = null
        // this.queryParams.fundNo = null
        // this.queryParams.productNo = null
        // this.fundNoParam = null
        // this.productNoParam = null
        this.sysDictRefParam.dictType = 'fund_no'
        this.sysDictRefParam.pDictType = 'partner_no'
        this.sysDictRefParam.pDictValue = this.queryParams.partnerNo
         this.sysDictRefParam.selectDictDatas =""

      if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } 
      else {
        this.sysDictRefParam.dictType = 'fund_no'
        this.sysDictRefParam.pDictType = 'partner_no'
        this.sysDictRefParam.pDictValue = this.queryParams.partnerNo
        this.sysDictRefParam.selectDictDatas =""
         if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }

        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            // this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
    getProductNoList(val) {
      const flag = this.lateByte(this.queryParams.partnerNo) > this.lateByte(val.toString())

      this.queryParams.fundNo = this.fundNoParam.toString()
      if (val == null || val === '' ||flag) {
        this.productNoSelect = null
        // this.queryParams.productNo = null
        // this.productNoParam = null
        this.sysDictRefParam.dictType = 'product_no'
        this.sysDictRefParam.pDictType = 'fund_no'
        this.sysDictRefParam.pDictValue = this.queryParams.fundNo
this.sysDictRefParam.selectDictDatas =""
       if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {
        this.sysDictRefParam.dictType = 'product_no'
        this.sysDictRefParam.pDictType = 'fund_no'
        this.sysDictRefParam.pDictValue = this.queryParams.fundNo

this.sysDictRefParam.selectDictDatas =""
        if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }

        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
      getProductNoValue(val) {
      const flag = this.lateByte(this.queryParams.productNo) > this.lateByte(val.toString())

      this.queryParams.productNo = this.productNoParam.toString()
      
       if (val == null || val === '' ||flag) {
        this.productNoSelect = null
        // this.queryParams.productNo = null
        this.productNoParam = null
        this.sysDictRefParam.dictType = ''
        this.sysDictRefParam.pDictType = 'product_no'
        this.sysDictRefParam.pDictValue =''
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
          this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {

        this.sysDictRefParam.dictType = ''
        this.sysDictRefParam.pDictType = 'product_no'
        this.sysDictRefParam.pDictValue = this.queryParams.productNo

        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
          this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            // this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }

    },

      //end


 /** 查询数据 */
    getList() {
      this.loading = true;
      this.queryParams.platformNo = this.platformNoParam.toString();
      this.queryParams.custNo = this.custNoParam.toString();
      this.queryParams.partnerNo = this.partnerNoParam.toString();
      this.queryParams.fundNo = this.fundNoParam.toString();
      this.queryParams.productNo = this.productNoParam.toString();
      rundatastack(this.queryParams).then(response => {

          this.xAxisData = response.xiaxis;
             //累计贷款余额
          this.balanceAndTotalData = response.balanceAndTotalData,
          //累计贷款本金
          this.amountAndTotalData = response.amountAndTotalData,
          //新增贷款本金
          this.addAmountAndTotalData = response.addAmountAndTotalData,
          //累计还款本金
          this.totalPrintAmountAndTotalData = response.totalPrintAmountAndTotalData,
          //新增还款本金
          this.addRepayAmountAndTotalData = response.addRepayAmountAndTotalData,

        this.secondEchaert();
        this.secondEchaert2();
        this.secondEchaert3();
        this.secondEchaert4();
        this.secondEchaert5();
        this.loading = false;
      }
      );
    },
 /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
         //  start 重置逻辑更新
      this.resetForm('queryForm')

      this.platformNoParam = ''
      this.custNoParam = ''
      this.partnerNoParam = ''
      this.fundNoParam = ''
      this.productNoParam = ''
      this.handleQuery()
      this.initSelectData()
      //  end 重置逻辑更新
    },
     // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },

      //  start 新增方法
    lateByte(sTargetStr) {
      var sTmpStr, sTmpChar;
      var nOriginLen = 0;
      var nStrLength = 0;

      sTmpStr = new String(sTargetStr);
      nOriginLen = sTmpStr.length;

      for (var i = 0; i < nOriginLen; i++) {
        sTmpChar = sTmpStr.charAt(i);

        if (escape(sTmpChar).length > 4) {
          nStrLength += 2;
        } else if (sTmpChar != '/r') {
          nStrLength++;
        }
      }
      return nStrLength;
    },

    //  end 新增方法

//默认时间计算
    nowtime() {

      let nowDate = new Date();

      let date = {

        // 获取当前年份

        year: nowDate.getFullYear(),

        //获取当前月份
        lastyear : nowDate.getFullYear()-1,

        month: (nowDate.getMonth() + 1 < 10 ? "0" + (nowDate.getMonth() + 1) : nowDate.getMonth()+1) ,

        //获取当前日期

        date1: nowDate.getDate(),
      };

        //拼接

      this.queryParams.remark = date.year + "-" + date.month + "-" + date.date1;
      this.queryParams.portrayalNo = date.lastyear + "-" + date.month + "-" + date.date1;

    },

    secondEchaert(){
            var myChart = this.$echarts.init(document.getElementById('charts'));
            var option={
                  tooltip: {
                            trigger: 'axis',
                        },
                         legend: {
                          //  orient: 'vertical',
                          type:'scroll',
                          left: 'center',
                          top: 50

                       },
                       grid: {

                              y: 100,
                              x2: 100,
                              // y2: 10,
                              
                              borderWidth: 1,
                            },
                        title: {
                            left: 'center',
                            text: '贷款余额与笔数',
                            x: 'center',
                        },
                         xAxis: {
                            type: 'category',
                            boundaryGap: true,
                            data: this.xAxisData
                        },
                         toolbox: {
                            show: true,
                            feature: {
                                dataZoom: {
                                        yAxisIndex: 'none'
                                          },
                                dataView: { readOnly: false },
                                // 柱状图或者折线图展示
                                magicType: { type: ['line', 'bar'] },

                                restore: {},
                                saveAsImage: {},

                            },
                        },
                        yAxis: {
                            type: 'value',
                            boundaryGap: ['0%', '100%']
                        }, 
                        dataZoom: [
                             {
                            type: 'slider',
                            show: true,
                            xAxisIndex: [0],
                            start: 0,
                            end: 100
                            },
                            {
                            type: 'slider',
                            show: true,
                            yAxisIndex: [0],
                            left: '95%',
                            start: 0,
                            end: 100
                            },
                            {
                            type: 'inside',
                            xAxisIndex: [0],
                            start: 1,
                            end: 10
                            },
                            // {
                            // type: 'inside',
                            // yAxisIndex: [0],
                            // start: 1,
                            // end: 100
                            // }

                        ],
                        series: this.balanceAndTotalData
             };
              myChart.clear()
            myChart.setOption(option);

    },
    secondEchaert2(){
            var myChart = this.$echarts.init(document.getElementById('charts1'));
            var option={
                  tooltip: {
                            trigger: 'axis',
                        },
                         legend: {
                          type:'scroll',
                          left: 'center',
                          top: 50

                       },
                       grid: {
                              x: 110,
                              y: 100,
                              x2: 70,
                              // y2: 25,
                              borderWidth: 1,
                            },
                        title: {
                             left: 'center',
                            text: '贷款本金金额与笔数',

                        },
                         xAxis: {
                            type: 'category',
                            boundaryGap: true,
                            data: this.xAxisData
                        },
                         toolbox: {
                            show: true,
                            feature: {
                                dataZoom: {
                                        yAxisIndex: 'none'
                                          },
                                dataView: { readOnly: false },
                                // 柱状图或者折线图展示
                                magicType: { type: ['line', 'bar'] },

                                restore: {},
                                saveAsImage: {},
                            }
                        },
                        yAxis: {
                            type: 'value',
                            boundaryGap: ['0%', '100%']
                        }, dataZoom: [
                             {
                            type: 'slider',
                            show: true,
                            xAxisIndex: [0],
                            start: 0,
                            end: 100
                            },
                            {
                            type: 'slider',
                            show: true,
                            yAxisIndex: [0],
                            left: '93%',
                            start: 0,
                            end: 100
                            },
                            {
                            type: 'inside',
                            xAxisIndex: [0],
                            start: 1,
                            end: 10
                            },
                            // {
                            // type: 'inside',
                            // yAxisIndex: [0],
                            // start: 1,
                            // end: 100
                            // }

                        ],
                        series:  this.amountAndTotalData
             };
              myChart.clear()
            myChart.setOption(option);

  },

  secondEchaert3(){
            var myChart = this.$echarts.init(document.getElementById('charts2'));
            var option={
                  tooltip: {
                            trigger: 'axis',
                        },
                        legend: {
                          type:'scroll',
                          left: 'center',
                          top: 50

                       },
                       grid: {
                              x: 110,
                              y: 100,
                              x2: 70,
                              // y2: 25,
                              borderWidth: 1,
                            },
                        title: {
                            left: 'center',
                            text: '新增贷款本金金额与笔数'
                        },
                         xAxis: {
                            type: 'category',
                            boundaryGap: true,
                            data: this.xAxisData
                        },
                         toolbox: {
                            show: true,
                            feature: {
                                dataZoom: {
                                        yAxisIndex: 'none'
                                          },
                                dataView: { readOnly: false },
                                // 柱状图或者折线图展示
                                magicType: { type: ['line', 'bar'] },

                                restore: {},
                                saveAsImage: {},
                            }
                        },
                        yAxis: {
                            type: 'value',
                            boundaryGap: ['0%', '100%']
                        }, dataZoom: [
                             {
                            type: 'slider',
                            show: true,
                            xAxisIndex: [0],
                            start: 0,
                            end: 100
                            },
                            {
                            type: 'slider',
                            show: true,
                            yAxisIndex: [0],
                            left: '93%',
                            start: 0,
                            end: 100
                            },
                            {
                            type: 'inside',
                            xAxisIndex: [0],
                            start: 1,
                            end: 10
                            },
                      

                        ],
                        series: this.addAmountAndTotalData
             };
              myChart.clear()
            myChart.setOption(option);

    },

    secondEchaert4(){
            var myChart = this.$echarts.init(document.getElementById('charts3'));
            var option={
                  tooltip: {
                            trigger: 'axis',
                        },
                        legend: {
                          type:'scroll',
                          left: 'center',
                          top: 50

                       },
                       grid: {
                              x: 110,
                              y: 100,
                              x2: 70,
                              // y2: 25,
                              borderWidth: 1,
                            },
                        title: {
                            left: 'center',
                            text: '累计还款本金与笔数'
                        },
                         xAxis: {
                            type: 'category',
                            boundaryGap: true,
                            data: this.xAxisData
                        },
                         toolbox: {
                            show: true,
                            feature: {
                                dataZoom: {
                                        yAxisIndex: 'none'
                                          },
                                dataView: { readOnly: false },
                                // 柱状图或者折线图展示
                                magicType: { type: ['line', 'bar'] },

                                restore: {},
                                saveAsImage: {},
                            }
                        },
                        yAxis: {
                            type: 'value',
                            boundaryGap: ['0%', '100%']
                        }, dataZoom: [
                             {
                            type: 'slider',
                            show: true,
                            xAxisIndex: [0],
                            start: 0,
                            end: 100
                            },
                            {
                            type: 'slider',
                            show: true,
                            yAxisIndex: [0],
                            left: '93%',
                            start: 0,
                            end: 100
                            },
                            {
                            type: 'inside',
                            xAxisIndex: [0],
                            start: 1,
                            end: 10
                            },
                            // {
                            // type: 'inside',
                            // yAxisIndex: [0],
                            // start: 1,
                            // end: 100
                            // }

                        ],
                        series:  this.totalPrintAmountAndTotalData
             };
              myChart.clear()
            myChart.setOption(option);

        },
        secondEchaert5(){
            var myChart = this.$echarts.init(document.getElementById('charts4'));
            var option={
                  tooltip: {
                            trigger: 'axis',
                        },
                        legend: {
                          type:'scroll',
                          left: 'center',
                          top: 50

                       },
                       grid: {
                              x: 110,
                              y: 100,
                              x2: 70,
                              // y2: 25,
                              borderWidth: 1,
                            },
                        title: {
                            left: 'center',
                            text: '新增还款本金与笔数'
                        },
                         xAxis: {
                            type: 'category',
                            boundaryGap: true,
                            data: this.xAxisData
                        },
                         toolbox: {
                            show: true,
                            feature: {
                                dataZoom: {
                                        yAxisIndex: 'none'
                                          },
                                dataView: { readOnly: false },
                                // 柱状图或者折线图展示
                                magicType: { type: ['line', 'bar'] },

                                restore: {},
                                saveAsImage: {},
                            }
                        },
                        yAxis: {
                            type: 'value',
                            boundaryGap: ['0%', '100%']
                        }, dataZoom: [
                             {
                            type: 'slider',
                            show: true,
                            xAxisIndex: [0],
                            start: 0,
                            end: 100
                            },
                            {
                            type: 'slider',
                            show: true,
                            yAxisIndex: [0],
                            left: '93%',
                            start: 0,
                            end: 100
                            },
                            {
                            type: 'inside',
                            xAxisIndex: [0],
                            start: 1,
                            end: 10
                            },
                            // {
                            // type: 'inside',
                            // yAxisIndex: [0],
                            // start: 1,
                            // end: 100
                            // }

                        ],
                        series: this.addRepayAmountAndTotalData
             };
              myChart.clear()
            myChart.setOption(option);

        },





  }

};
</script>
