<template>
  <div  style="width: 1500px;height:1950px;"   >
   <!--    start 进行添加-->
   <div style="font-size: 15px"> <div :title="tipsMsg"  style="color: red"  ><pre>{{tipsMsg}}</pre></div></div>
<!-- end 结束表头-->
      <el-form :model="queryParams" :rules="headrules" ref="queryForm" :inline="true" v-show="showSearch" label-width="130px">
            <el-form-item label="外部系统名称" prop="platformNo">
                      <el-select v-model="platformNoParam" placeholder="请选择系统名称" filterable multiple size="small"
                                >
                        <el-option
                          v-for="dict in platformNoSelect"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        />
                      </el-select>
            </el-form-item>
            <el-form-item label="担保公司" prop="custNo">
                      <el-select v-model="custNoParam" placeholder="请选择担保公司" filterable multiple size="small"
                                >
                        <el-option
                          v-for="dict in custNoSelect"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        />
                      </el-select>
            </el-form-item>
            <el-form-item label="合作方" prop="partnerNo">
                    <el-select v-model="partnerNoParam" placeholder="请选择合作方" filterable multiple size="small"
                              >
                      <el-option
                        v-for="dict in partnerNoSelect"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
            </el-form-item>
          <el-form-item label="资金方" prop="fundNo">
                  <el-select v-model="fundNoParam" placeholder="请选择资金方" filterable multiple size="small"
                            >
                    <el-option
                      v-for="dict in fundNoSelect"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
          </el-form-item>
          <el-form-item label="产品编码" prop="productNo">
              <el-select v-model="productNoParam" placeholder="请选择产品" filterable multiple size="small"
                        >
                <el-option
                  v-for="dict in productNoSelect"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
          </el-form-item>

      <el-form-item label="开始时间" prop="portrayalNo">
        <el-date-picker
          v-model="queryParams.portrayalNo"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="结束时间" prop="remark">
        <el-date-picker
          v-model="queryParams.remark"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="统计维度" prop="portrayalType">
          <el-select v-model="queryParams.portrayalType" placeholder="请选择统计维度 "  clearable size="small">
           <el-option
              v-for="dict in statisticalDimension"
             :key="dict.value"
             :label="dict.lable"
             :value="dict.value"
            />
          </el-select>
       </el-form-item>
       <MoreSearch modelCode="ECHARTS" :params="queryParams" v-show="showMoreSearch" byId="companyCode"></MoreSearch>
       <el-form-item>
        <el-button style="margin-left:50px" type="primary" icon="el-icon-search" size="mini" @click="handleQuery">统计</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button @click="showMoreSearch=!showMoreSearch;queryParams.moreSearch=undefined" type="text"
          >更多搜索条件<i  :class="showMoreSearch?'el-icon-arrow-down':'el-icon-arrow-up'"/></el-button
        >
      </el-form-item>
      </el-form>

        <div style="width: 100%;height:550px; float:left">
            <div style="width: 100%;height:50px; float:left">
              <el-button-group style="margin-right:30px;float:right">
                  <el-button @click="echartsDay()">日</el-button>
                  <el-button @click="echartsWeek()">周</el-button>
                  <el-button @click="echartsMonth()">月</el-button>
                  <el-button @click="echartsYear()">年</el-button>
                </el-button-group>
              </div>
          <div v-loading="loading"  id="charts" style="width: 100%;height:500px; float:left"></div>
        </div>
        <div  style="width: 50%;height:550px; float:right">
          <div style="width: 100%;height:50px; float:left">
              <el-button-group style="margin-right:30px;float:right">
                 <el-button @click="echarts1Day()">日</el-button>
                  <el-button @click="echarts1Week()">周</el-button>
                  <el-button @click="echarts1Month()">月</el-button>
                  <el-button @click="echarts1Year()">年</el-button>
                </el-button-group>
              </div>
            <div v-loading="loading" id="charts1" style="width: 100%;height:500px; float:right">
            </div>
        </div>
        <div  style="width: 50%;height:550px; float:left">
                <div style="width: 100%;height:50px; float:left">
                  <el-button-group style="margin-right:30px;float:right">
                     <el-button @click="echarts2Day()">日</el-button>
                    <el-button @click="echarts2Week()">周</el-button>
                    <el-button @click="echarts2Month()">月</el-button>
                    <el-button @click="echarts2Year()">年</el-button>
                    </el-button-group>
                </div>

              <div v-loading="loading" id="charts2" style="width: 100%;height:500px; float:left">
              </div>
        </div>
        <div  style="width: 50%;height:550px; float:right">
              <div style="width: 100%;height:50px; float:left">
                  <el-button-group style="margin-right:30px;float:right">
                     <el-button @click="echarts3Day()">日</el-button>
                    <el-button @click="echarts3Week()">周</el-button>
                    <el-button @click="echarts3Month()">月</el-button>
                    <el-button @click="echarts3Year()">年</el-button>
                    </el-button-group>
                </div>
                <div v-loading="loading" id="charts3" style="width: 100%;height:500px; float:right">
                </div>
        </div>
        <div  style="width: 50%;height:550px; float:left">
                <div style="width: 100%;height:50px; float:left">
                  <el-button-group style="margin-right:30px;float:right">
                     <el-button @click="echarts4Day()">日</el-button>
                    <el-button @click="echarts4Week()">周</el-button>
                    <el-button @click="echarts4Month()">月</el-button>
                    <el-button @click="echarts4Year()">年</el-button>
                    </el-button-group>
                </div>
              <div v-loading="loading" id="charts4" style="width: 100%;height:500px; float:left">
              </div>
        </div>

   </div>

</template>

<script>
import * as echarts from 'echarts';
import { runcond} from '@/api/system/echarts'
//  start 引用联级字典查询
import {getSysDictRefList,getSelectSysDictRefList} from '@/api/ref/ref'
//  end 引用联级字典查询
import {tipsMsg} from '../../data/common'
import { clone } from "xe-utils";
export default {
  name: 'DData',
  dicts: ['sys_platform_code', 'product_no', 'fund_no', 'partner_no', 'platform_no', 'cust_no'],
  data() {
    return {
headrules:{
  portrayalNo:[
     { type: 'string', required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  remark:[
      { type: 'string', required: true, message: '请选择结束时间', trigger: 'change' }
  ]
},

tipsMsg:null,
  // 日期范围
      //  start 新增参数
      platformNoParam: '',
      custNoParam: '',
      partnerNoParam: '',
      fundNoParam: '',
      productNoParam: '',


      sysDictRefParam: {
        dictType: '',
        dictValue: '',
        pDictType: '',
        pDictValue: '',
        selectDictDatas:''
      },
      platformNoSelect: [],
      custNoSelect: [],
      fundNoSelect: [],
      partnerNoSelect: [],
      productNoSelect: [],
      //  end 新增参数

        // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
       // 日期范围
      dateRange: [],
      //echartx轴
      xAxisData:[],
       //echart1x轴
      xAxis1Data:[],
       //echart2x轴
      xAxis2Data:[],
       //echart3x轴
      xAxis3Data:[],
       //echart4x轴
      xAxis3Data:[],
      //累计贷款余额
      balanceAndTotalData:[],
      //累计贷款本金
      amountAndTotalData:[],
      //新增贷款本金
      addAmountAndTotalData:[],
      //累计还款本金
      totalPrintAmountAndTotalData:[],
      //新增还款本金
      addRepayAmountAndTotalData:[],
      //
      c1:"",
      c2:"",
      c3:"",
      C4:"",
      C5:"",
      //echart  x轴 日周月年x轴数据
      echartxaxisday:[],
      echartxaxisweek:[],
       echartxaxismonth:[],
      echartxaxisyear:[],
      //countX轴
      countWeekXaxis:[],
      countMonthXaxis:[],
      countYearXaxis:[],
      //图表数据
      echartdataday:[],
      echartdataweek:[],
      echartdatamonth:[],
      echartdatayear:[],



       //echart1
      //图表数据
      echart1dataday:[],
      echart1dataweek:[],
      echart1datamonth:[],
      echart1datayear:[],

       //echart2
      //图表数据
      echart2dataday:[],
      echart2dataweek:[],
      echart2datamonth:[],
      echart2datayear:[],
       //echart3
      //图表数据
      echart3dataday:[],
      echart3dataweek:[],
      echart3datamonth:[],
      echart3datayear:[],
       //echart4
      //图表数据
      echart4dataday:[],
      echart4dataweek:[],
      echart4datamonth:[],
      echart4datayear:[],

      //统计维度
      statisticalDimension:[
        {
          lable:"外部系统",
          value:"platform_no"
        },
        {
          lable:"担保公司",
          value:"cust_no"
        },{
          lable:"合作方",
          value:"partner_no"
        },{
          lable:"资金方",
          value:"fund_no"
        },{
          lable:"产品",
          value:"product_no"
        }
      ],
       // 表单参数
      form: {},
       // 查询参数
      queryParams: {
        platformNo: null,
        custNo: null,
        partnerNo: null,
        fundNo: null,
        productNo: null,
        vintageDay:null,
        portrayalNo: null,
        remark: null,
        portrayalType:null,
        params: {
         moduleTypeOfNewAuth: 'ECHARTS',
        }
      },
      showMoreSearch:false
    };
  },
  mounted(){
    this.tipsMsg= tipsMsg
  this.nowtime();
  this.getList();
    //  start 页面刷新时对数据的处理

    // this.initSelect()
     this.initSelectData()
    //  end 页面刷新时对数据的处理

  },
    methods:{
      //start

      //wzy渲染下拉框
      initSelectData() {
      getSelectSysDictRefList({ unitType: 4,moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.platformNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 0,moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.custNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 3,moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.productNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 2,moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.fundNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 1,moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.partnerNoSelect = response;
      });
    },
     getCustNoList(val) {
      const flag = this.lateByte(this.queryParams.platformNo) > this.lateByte(val.toString())
      this.queryParams.platformNo = this.platformNoParam.toString()
      if (val == null || val === '' || flag) {
        this.custNoSelect = null
        this.partnerNoSelect = null
        this.fundNoSelect = null
        this.productNoSelect = null

        // this.queryParams.custNo = null
        // this.queryParams.partnerNo = null
        // this.queryParams.fundNo = null
        // this.queryParams.productNo = null

        // this.custNoParam = null
        // this.partnerNoParam = null
        // this.fundNoParam = null
        // this.productNoParam = null
        this.sysDictRefParam.dictType = 'cust_no'
        this.sysDictRefParam.pDictType = 'platform_no'
        this.sysDictRefParam.pDictValue = this.queryParams.platformNo
        this.sysDictRefParam.selectDictDatas =""
          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {

            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {

        this.sysDictRefParam.dictType = 'cust_no'
        this.sysDictRefParam.pDictType = 'platform_no'
        this.sysDictRefParam.pDictValue = this.queryParams.platformNo
        this.sysDictRefParam.selectDictDatas =""
       if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            // this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
    getPartnerNoList(val) {
      const flag = this.lateByte(this.queryParams.custNo) > this.lateByte(val.toString())
      this.queryParams.custNo = this.custNoParam.toString()
      if (val == null || val === '' || flag) {
        this.partnerNoSelect = null
        this.fundNoSelect = null
        this.productNoSelect = null

        // this.queryParams.partnerNo = null
        // this.queryParams.fundNo = null
        // this.queryParams.productNo = null

        // this.partnerNoParam = null
        // this.fundNoParam = null
        // this.productNoParam = null
        this.sysDictRefParam.dictType = 'partner_no'
        // this.sysDictRefParam.dictValue=val
        this.sysDictRefParam.pDictType = 'cust_no'
        this.sysDictRefParam.pDictValue = this.queryParams.custNo
        this.sysDictRefParam.selectDictDatas =""

          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {
        this.sysDictRefParam.dictType = 'partner_no'
        // this.sysDictRefParam.dictValue=val
        this.sysDictRefParam.pDictType = 'cust_no'
        this.sysDictRefParam.pDictValue = this.queryParams.custNo
        this.sysDictRefParam.selectDictDatas =""

          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            // this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
    getFundNoList(val) {
      const flag = this.lateByte(this.queryParams.partnerNo) > this.lateByte(val.toString())
      this.queryParams.partnerNo = this.partnerNoParam.toString()
      if (val == null || val === ''|| flag) {
        this.fundNoSelect = null
        this.productNoSelect = null
        // this.queryParams.fundNo = null
        // this.queryParams.productNo = null
        // this.fundNoParam = null
        // this.productNoParam = null
        this.sysDictRefParam.dictType = 'fund_no'
        this.sysDictRefParam.pDictType = 'partner_no'
        this.sysDictRefParam.pDictValue = this.queryParams.partnerNo
         this.sysDictRefParam.selectDictDatas =""

      if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
      else {
        this.sysDictRefParam.dictType = 'fund_no'
        this.sysDictRefParam.pDictType = 'partner_no'
        this.sysDictRefParam.pDictValue = this.queryParams.partnerNo
        this.sysDictRefParam.selectDictDatas =""
         if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }

        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            // this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
    getProductNoList(val) {
      const flag = this.lateByte(this.queryParams.partnerNo) > this.lateByte(val.toString())

      this.queryParams.fundNo = this.fundNoParam.toString()
      if (val == null || val === '' ||flag) {
        this.productNoSelect = null
        // this.queryParams.productNo = null
        // this.productNoParam = null
        this.sysDictRefParam.dictType = 'product_no'
        this.sysDictRefParam.pDictType = 'fund_no'
        this.sysDictRefParam.pDictValue = this.queryParams.fundNo
this.sysDictRefParam.selectDictDatas =""
       if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {
        this.sysDictRefParam.dictType = 'product_no'
        this.sysDictRefParam.pDictType = 'fund_no'
        this.sysDictRefParam.pDictValue = this.queryParams.fundNo

this.sysDictRefParam.selectDictDatas =""
        if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }

        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            // this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
      getProductNoValue(val) {
      const flag = this.lateByte(this.queryParams.productNo) > this.lateByte(val.toString())

      this.queryParams.productNo = this.productNoParam.toString()

       if (val == null || val === '' ||flag) {
        this.productNoSelect = null
        // this.queryParams.productNo = null
        this.productNoParam = null
        this.sysDictRefParam.dictType = ''
        this.sysDictRefParam.pDictType = 'product_no'
        this.sysDictRefParam.pDictValue =''
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
          this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {

        this.sysDictRefParam.dictType = ''
        this.sysDictRefParam.pDictType = 'product_no'
        this.sysDictRefParam.pDictValue = this.queryParams.productNo

        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
          this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }

    },

      //end
      //累计贷款余额与笔数
      echartsDay(){
        this.xAxisData = this.echartxaxisday
        this.balanceAndTotalData = this.echartdataday
        this.c1 = "日"
        this.secondEchaert();
      },
       echartsWeek(){
        this.xAxisData = this.countWeekXaxis
        this.balanceAndTotalData = this.echartdataweek
        this.c1 = "周"
        this.secondEchaert();
      },
       echartsMonth(){
        this.xAxisData = this.countMonthXaxis
        this.balanceAndTotalData = this.echartdatamonth
        this.c1 = "月"
        this.secondEchaert();
      },
       echartsYear(){
        this.xAxisData = this.countYearXaxis
        this.balanceAndTotalData = this.echartdatayear
        this.c1 = "年"
        this.secondEchaert();
      },
      //累计贷款本金
      echarts1Day(){
        this.xAxis1Data = this.echartxaxisday
        this.amountAndTotalData = this.echart1dataday
        this.c2 = "日"
        this.secondEchaert2();
      },
       echarts1Week(){
        this.xAxis1Data = this.countWeekXaxis
        this.amountAndTotalData = this.echart1dataweek
        this.c2 = "周"
        this.secondEchaert2();
      },
       echarts1Month(){
        this.xAxis1Data = this.countMonthXaxis
        this.amountAndTotalData = this.echart1datamonth
        this.c2 = "月"
        this.secondEchaert2();
      },
       echarts1Year(){
        this.xAxis1Data = this.countYearXaxis
        this.amountAndTotalData = this.echart1datayear
        this.c2 = "年"
        this.secondEchaert2();
      },
      //新增贷款本金
       echarts2Day(){
        this.xAxis2Data = this.echartxaxisday
        this.addAmountAndTotalData = this.echart2dataday
        this.c3 = "日"
        this.secondEchaert3();
      },
       echarts2Week(){
        this.xAxis2Data = this.echartxaxisweek
        this.addAmountAndTotalData = this.echart2dataweek
        this.c3 = "周"
        this.secondEchaert3();
      },
       echarts2Month(){
        this.xAxis2Data = this.echartxaxismonth
        this.addAmountAndTotalData = this.echart2datamonth
        this.c3 = "月"
        this.secondEchaert3();
      },
       echarts2Year(){
        this.xAxis2Data = this.echartxaxisyear
        this.addAmountAndTotalData = this.echart2datayear
        this.c3 = "年"
        this.secondEchaert3();
      },
      //累计还款本金
       echarts3Day(){
        this.xAxis3Data = this.echartxaxisday
        this.totalPrintAmountAndTotalData = this.echart3dataday
        this.c4 = "日"
        this.secondEchaert4();
      },
       echarts3Week(){
        this.xAxis3Data = this.countWeekXaxis
        this.totalPrintAmountAndTotalData = this.echart3dataweek
        this.c4 = "周"
        this.secondEchaert4();
      },
       echarts3Month(){
        this.xAxis3Data = this.countMonthXaxis
        this.totalPrintAmountAndTotalData = this.echart3datamonth
        this.c4 = "月"
        this.secondEchaert4();
      },
       echarts3Year(){
        this.xAxis3Data = this.countYearXaxis
        this.totalPrintAmountAndTotalData = this.echart3datayear
        this.c4 = "年"
        this.secondEchaert4();
      },
      //新增还款本金
       echarts4Day(){
        this.xAxis4Data = this.echartxaxisday
        this.addRepayAmountAndTotalData = this.echart4dataday
        this.c5 = "日"
        this.secondEchaert5();
      },
       echarts4Week(){
        this.xAxis4Data = this.echartxaxisweek
        this.addRepayAmountAndTotalData = this.echart4dataweek
        this.c5 = "周"
        this.secondEchaert5();
      },
       echarts4Month(){
        this.xAxis4Data = this.echartxaxismonth
        this.addRepayAmountAndTotalData = this.echart4datamonth
        this.c5 = "月"
        this.secondEchaert5();
      },
       echarts4Year(){
        this.xAxis4Data = this.echartxaxisyear
        this.addRepayAmountAndTotalData = this.echart4datayear
        this.c5 = "年"
        this.secondEchaert5();
      },

 /** 查询数据 */
    getList() {
      this.loading = true;
      this.queryParams.platformNo = this.platformNoParam.toString();
      this.queryParams.custNo = this.custNoParam.toString();
      this.queryParams.partnerNo = this.partnerNoParam.toString();
      this.queryParams.fundNo = this.fundNoParam.toString();
      this.queryParams.productNo = this.productNoParam.toString();
      const params=clone(this.queryParams,true);
      params.moreSearch=params.moreSearch&&JSON.stringify(params.moreSearch)
      runcond(params).then(response => {

                //echart  x轴 日周月年x轴数据
      this.echartxaxisday = response.xiaxis,
      this.echartxaxisweek= response.weekxixas,
       this.echartxaxismonth= response.monthxaxis,
      this.echartxaxisyear= response.yearxaxis,
      //countx轴
        this.countWeekXaxis= response.weekcountxaxis,
       this.countMonthXaxis= response.monthcountxaxis,
       this.countYearXaxis= response.yearcountxaxis,
      //图表数据
      this.echartdataday= response.balanceAndTotalData,
      this.echartdataweek= response.weekbalanceAndTotalData,
      this.echartdatamonth= response.monthbalanceAndTotalData,
      this.echartdatayear= response.yearbalanceAndTotalData,
       //echart1
      //图表数据
      this.echart1dataday= response.amountAndTotalData,
     this. echart1dataweek= response.weekamountAndTotalData,
      this.echart1datamonth= response.monthamountAndTotalData,
      this.echart1datayear= response.yearamountAndTotalData,

       //echart2
      //图表数据
      this.echart2dataday= response.addAmountAndTotalData,
      this.echart2dataweek= response.weekaddAmountAndTotalData,
      this.echart2datamonth= response.monthaddAmountAndTotalData,
      this.echart2datayear= response.yearaddAmountAndTotalData,
       //echart3  x轴
      //图表数据
      this.echart3dataday= response.totalPrintAmountAndTotalData,
      this.echart3dataweek= response.weektotalPrintAmountAndTotalData,
     this. echart3datamonth= response.monthtotalPrintAmountAndTotalData,
     this. echart3datayear= response.yeartotalPrintAmountAndTotalData,
       //echart4  x轴
      //图表数据
      this.echart4dataday= response.addRepayAmountAndTotalData,
      this.echart4dataweek= response.weekaddRepayAmountAndTotalData,
      this.echart4datamonth= response.monthaddRepayAmountAndTotalData,
      this.echart4datayear= response.yearaddRepayAmountAndTotalData,

      this.echartsDay();
      this.echarts1Day();
      this.echarts2Day();
      this.echarts3Day();
      this.echarts4Day();

        this.loading = false;
      }
      );
    },
 /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
       this.$refs["queryForm"].validate(valid => {
            if(valid){
              this.getList();
            }else{
              this.$message.error('请选择统计日期后重新统计！');
            }

           });
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
         //  start 重置逻辑更新
      this.resetForm('queryForm')

      this.platformNoParam = ''
      this.custNoParam = ''
      this.partnerNoParam = ''
      this.fundNoParam = ''
      this.productNoParam = ''
      this.queryParams.moreSearch=undefined;
      this.handleQuery()
      this.initSelectData()
      //  end 重置逻辑更新
    },
     // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },

    lateByte(sTargetStr) {
      var sTmpStr, sTmpChar;
      var nOriginLen = 0;
      var nStrLength = 0;

      sTmpStr = new String(sTargetStr);
      nOriginLen = sTmpStr.length;

      for (var i = 0; i < nOriginLen; i++) {
        sTmpChar = sTmpStr.charAt(i);

        if (escape(sTmpChar).length > 4) {
          nStrLength += 2;
        } else if (sTmpChar != '/r') {
          nStrLength++;
        }
      }
      return nStrLength;
    },

    //  end 新增方法

//默认时间计算
    nowtime() {

      let nowDate = new Date();

      let date = {

        // 获取当前年份

        year: nowDate.getFullYear(),

        //获取当前月份
        lastyear : nowDate.getFullYear()-1,

        month: (nowDate.getMonth() + 1 < 10 ? "0" + (nowDate.getMonth() + 1) : nowDate.getMonth()+1) ,

        //获取当前日期

        date1: nowDate.getDate(),
      };

        //拼接

      this.queryParams.remark = date.year + "-" + date.month + "-" + date.date1;
      this.queryParams.portrayalNo = date.lastyear + "-" + date.month + "-" + date.date1;
    },

    secondEchaert(){
            var myChart = this.$echarts.init(document.getElementById('charts'));
            var option={
                  tooltip: {
                            trigger: 'axis',
                        },
                         legend: {
                          //  orient: 'vertical',
                          type:'scroll',
                          left: 'center',
                          top: 50

                       },
                       grid: {

                              y: 100,
                              x2: 100,
                              // y2: 25,
                              borderWidth: 1,
                            },
                        title: {
                            left: 'center',
                            text: '贷款余额与笔数（'+this.c1+'）',
                            x: 'center',
                        },
                         xAxis: {
                            type: 'category',
                            boundaryGap: true,
                            data: this.xAxisData
                        },
                         toolbox: {
                            show: true,
                            feature: {
                                dataZoom: {
                                        yAxisIndex: 'none'
                                          },
                                dataView: { readOnly: false },
                                // 柱状图或者折线图展示
                                magicType: { type: ['line', 'bar'] },

                                restore: {},
                                saveAsImage: {},

                            },
                        },
                        yAxis: {
                          name: "单位（元，笔）",
                            type: 'value',
                            boundaryGap: ['0%', '100%']
                        }, dataZoom: [
                             {
                            type: 'slider',
                            show: true,
                            xAxisIndex: [0],
                            start: 0,
                            end: 100
                            },
                            {
                            type: 'slider',
                            show: true,
                            yAxisIndex: [0],
                            left: '95%',
                            start: 0,
                            end: 100
                            },
                            {
                            type: 'inside',
                            xAxisIndex: [0],
                            start: 1,
                            end: 10
                            },
                            // {
                            // type: 'inside',
                            // yAxisIndex: [0],
                            // start: 1,
                            // end: 100
                            // }

                        ],
                        series: this.balanceAndTotalData
             };
              myChart.clear()
            myChart.setOption(option);

    },
    secondEchaert2(){
            var myChart = this.$echarts.init(document.getElementById('charts1'));
            var option={
                  tooltip: {
                            trigger: 'axis',
                        },
                         legend: {
                          type:'scroll',
                          left: 'center',
                          top: 50

                       },
                       grid: {
                              x: 110,
                              y: 100,
                              x2: 70,
                              // y2: 25,
                              borderWidth: 1,
                            },
                        title: {
                             left: 'center',
                            text: '贷款本金金额与笔数（'+this.c2+'）',

                        },
                         xAxis: {
                            type: 'category',
                            boundaryGap: true,
                            data: this.xAxis1Data
                        },
                         toolbox: {
                            show: true,
                            feature: {
                                dataZoom: {
                                        yAxisIndex: 'none'
                                          },
                                dataView: { readOnly: false },
                                // 柱状图或者折线图展示
                                magicType: { type: ['line', 'bar'] },

                                restore: {},
                                saveAsImage: {},
                            }
                        },
                        yAxis: {
                          name: "单位（元，笔）",
                            type: 'value',
                            boundaryGap: ['0%', '100%']
                        }, dataZoom: [
                             {
                            type: 'slider',
                            show: true,
                            xAxisIndex: [0],
                            start: 0,
                            end: 100
                            },
                            {
                            type: 'slider',
                            show: true,
                            yAxisIndex: [0],
                            left: '93%',
                            start: 0,
                            end: 100
                            },
                            {
                            type: 'inside',
                            xAxisIndex: [0],
                            start: 1,
                            end: 10
                            },
                            // {
                            // type: 'inside',
                            // yAxisIndex: [0],
                            // start: 1,
                            // end: 100
                            // }

                        ],
                        series:  this.amountAndTotalData
             };
              myChart.clear()
            myChart.setOption(option);

  },

  secondEchaert3(){
            var myChart = this.$echarts.init(document.getElementById('charts2'));
            var option={
                  tooltip: {
                            trigger: 'axis',
                        },
                        legend: {
                          type:'scroll',
                          left: 'center',
                          top: 50

                       },
                       grid: {
                              x: 110,
                              y: 100,
                              x2: 70,
                              // y2: 25,
                              borderWidth: 1,
                            },
                        title: {
                            left: 'center',
                            text: '新增贷款本金金额与笔数（'+this.c3+'）'
                        },
                         xAxis: {
                            type: 'category',
                            boundaryGap: true,
                            data: this.xAxis2Data
                        },
                         toolbox: {
                            show: true,
                            feature: {
                                dataZoom: {
                                        yAxisIndex: 'none'
                                          },
                                dataView: { readOnly: false },
                                // 柱状图或者折线图展示
                                magicType: { type: ['line', 'bar'] },

                                restore: {},
                                saveAsImage: {},
                            }
                        },
                        yAxis: {
                          name: "单位（元，笔）",
                            type: 'value',
                            boundaryGap: ['0%', '100%']
                        }, dataZoom: [
                             {
                            type: 'slider',
                            show: true,
                            xAxisIndex: [0],
                            start: 0,
                            end: 100
                            },
                            {
                            type: 'slider',
                            show: true,
                            yAxisIndex: [0],
                            left: '93%',
                            start: 0,
                            end: 100
                            },
                            {
                            type: 'inside',
                            xAxisIndex: [0],
                            start: 1,
                            end: 10
                            },
                            // {
                            // type: 'inside',
                            // yAxisIndex: [0],
                            // start: 1,
                            // end: 100
                            // }

                        ],
                        series: this.addAmountAndTotalData
             };
              myChart.clear()
            myChart.setOption(option);

    },

    secondEchaert4(){
            var myChart = this.$echarts.init(document.getElementById('charts3'));
            var option={
                  tooltip: {
                            trigger: 'axis',
                        },
                        legend: {
                          type:'scroll',
                          left: 'center',
                          top: 50

                       },
                       grid: {
                              x: 110,
                              y: 100,
                              x2: 70,
                              // y2: 25,
                              borderWidth: 1,
                            },
                        title: {
                            left: 'center',
                            text: '累计还款本金与笔数（'+this.c4+'）'
                        },
                         xAxis: {
                            type: 'category',
                            boundaryGap: true,
                            data: this.xAxis3Data
                        },
                         toolbox: {
                            show: true,
                            feature: {
                                dataZoom: {
                                        yAxisIndex: 'none'
                                          },
                                dataView: { readOnly: false },
                                // 柱状图或者折线图展示
                                magicType: { type: ['line', 'bar'] },

                                restore: {},
                                saveAsImage: {},
                            }
                        },
                        yAxis: {
                          name: "单位（元，笔）",
                            type: 'value',
                            boundaryGap: ['0%', '100%']
                        }, dataZoom: [
                             {
                            type: 'slider',
                            show: true,
                            xAxisIndex: [0],
                            start: 0,
                            end: 100
                            },
                            {
                            type: 'slider',
                            show: true,
                            yAxisIndex: [0],
                            left: '93%',
                            start: 0,
                            end: 100
                            },
                            {
                            type: 'inside',
                            xAxisIndex: [0],
                            start: 1,
                            end: 10
                            },
                            // {
                            // type: 'inside',
                            // yAxisIndex: [0],
                            // start: 1,
                            // end: 100
                            // }

                        ],
                        series:  this.totalPrintAmountAndTotalData
             };
              myChart.clear()
            myChart.setOption(option);

        },
        secondEchaert5(){
            var myChart = this.$echarts.init(document.getElementById('charts4'));
            var option={
                  tooltip: {
                            trigger: 'axis',
                        },
                        legend: {
                          type:'scroll',
                          left: 'center',
                          top: 50

                       },
                       grid: {
                              x: 110,
                              y: 100,
                              x2: 70,
                              // y2: 25,
                              borderWidth: 1,
                            },
                        title: {
                            left: 'center',
                            text: '新增还款本金与笔数（'+this.c5+'）'
                        },
                         xAxis: {
                            type: 'category',
                            boundaryGap: true,
                            data: this.xAxis4Data
                        },
                         toolbox: {
                            show: true,
                            feature: {
                                dataZoom: {
                                        yAxisIndex: 'none'
                                          },
                                dataView: { readOnly: false },
                                // 柱状图或者折线图展示
                                magicType: { type: ['line', 'bar'] },

                                restore: {},
                                saveAsImage: {},
                            }
                        },
                        yAxis: {
                          name: "单位（元，笔）",
                            type: 'value',
                            boundaryGap: ['0%', '100%']
                        }, dataZoom: [
                             {
                            type: 'slider',
                            show: true,
                            xAxisIndex: [0],
                            start: 0,
                            end: 100
                            },
                            {
                            type: 'slider',
                            show: true,
                            yAxisIndex: [0],
                            left: '93%',
                            start: 0,
                            end: 100
                            },
                            {
                            type: 'inside',
                            xAxisIndex: [0],
                            start: 1,
                            end: 10
                            },
                            // {
                            // type: 'inside',
                            // yAxisIndex: [0],
                            // start: 1,
                            // end: 100
                            // }

                        ],
                        series: this.addRepayAmountAndTotalData
             };
              myChart.clear()
            myChart.setOption(option);

        },





  }

};
</script>
