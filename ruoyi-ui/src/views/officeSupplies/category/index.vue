<template>
  <div style="height: 100%">
    <div class="search">
      <div class="item">
        <span>类别名称</span>
        <el-input v-model="params.categoryName" clearable="" placeholder="请输入类别名称" style="width: 200px"></el-input>
      </div>

      <div class="item">
        <span>所属公司</span>
        <el-select placeholder="请选择所属公司" clearable="" style="width: 200px" v-model="params.companyId" filterable>
          <el-option v-for="item in projects" :key="item.id" :label="item.companyShortName" :value="item.id">
          </el-option>
        </el-select>
      </div>

      <el-button size="mini" type="primary" icon="el-icon-search" @click="search">搜 索</el-button>
      <el-button size="mini" icon="el-icon-refresh" @click="reset">重 置</el-button>
    </div>
    <div class="solid"></div>
    <div class="content">
      <div class="left">
        <el-input v-model="filterText" placeholder="请输入类别名称" style="width: 210px"></el-input>
        <el-tree class="filter-tree" :data="leftTreeList" :props="defaultProps" :default-expand-all="false"
          :filter-node-method="filterNode" @node-click="handleNodeClick" ref="tree" node-key="id"
          :highlight-current="true">
        </el-tree>
      </div>
      <div class="right">
        <div class="header_btn" style="display: flex; align-items: center">
          <el-button v-hasPermi="['categroy:addEdit']"
            style="border-color: #aed8ff; background: #e8f4ff; color: #3fa1ff" icon="el-icon-plus" type="primary"
            size="mini" @click="newData">新建</el-button>
          <el-button type="success" plain icon="el-icon-edit" size="mini" @click="exportExcel"
            v-hasPermi="['categroy:export']">导出列表</el-button>
          <el-button style="
              border-color: #aed8ff;
              background: #e8f4ff;
              color: #3fa1ff;
              position: absolute;
              right: 0;
            " type="primary" size="mini" @click="reset">返回至主目录</el-button>
        </div>
        <el-table :data="tableData" style="width: 100%; margin-top: 16px; margin-left: 4px">
          <el-table-column align="center" show-overflow-tooltip="" prop="categoryName" label="类别名称" width="180">
            <template #default="{ row }">
              <div style="color: rgb(63, 161, 255); cursor: pointer" @click="detail(row)">
                {{ row.categoryName }}
              </div>
            </template>
          </el-table-column>

          <el-table-column align="center" show-overflow-tooltip="" prop="parentName" label="上级类别" />

          <el-table-column align="center" show-overflow-tooltip="" prop="companyShortName" label="所属公司" />
          <!-- 新增启用状态列 -->
          <el-table-column align="center" label="启用状态" width="120">
            <template #default="{ row }">
              <el-switch v-model="row.status" active-value="0" inactive-value="1" @change="handleStatusChange(row)" />
            </template>
          </el-table-column>
          <el-table-column align="center" show-overflow-tooltip="" prop="shortNum" label="排序" />
          <el-table-column align="center" show-overflow-tooltip="" prop="createByName" label="创建人" />
          <el-table-column align="center" show-overflow-tooltip="" prop="updateTime" label="最新修改时间" />
          <el-table-column align="center" show-overflow-tooltip="" prop="createTime" label="创建时间" />
          <el-table-column align="center" fixed="right" width="170" label="操作">
            <template #default="{ row }">
              <el-button @click="detail(row)" type="text" size="small">查看详情</el-button>
              <el-button @click="edit(row)" type="text" size="small" v-hasPermi="['categroy:addEdit']">修改</el-button>
              <el-button v-hasPermi="['categroy:del']" @click="del(row)" type="text" size="small"
                style="color: red">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="params.pageNum" :page-sizes="[10, 20, 50, 100]"
          :limit.sync="params.pageSize" @pagination="getList" />
      </div>
    </div>
    <AddItem :detailType="detailType" :leftTreeList="leftTreeList" :editData="editData" v-if="addItemType"
      :projects="projects" :deTreeList="treeSelect" @close="close" :parentData="parentData" />
  </div>
</template>

<script>
import Cookies from "js-cookie";
import AddItem from "./components/AddItem.vue";
import {
  categoryTree,
  getAuthCompany,
  categoryMainDetail,
  categoryMainDel,
  list,
  changeStatusCate,
  queryJudge
} from "@/api/officeSupplies/officeSupplies";

export default {
  name: "Directory",
  components: {
    AddItem,
  },
  data() {
    return {
      parentData: null,
      defaultProps2: {
        children: "children",
        label: "label",
      },
      deTreeList: [],

      disabledView: false,
      editData: null,
      treeSelect: [],
      addItemType: false,
      defaultProps: {
        children: "children",
        label: "categoryName",
      },
      projects: [],
      leftTreeList: [],

      selectItemType: false,
      total: 0,
      params: {
        categoryName: "",
        companyId: "",
        pageNum: 1,
        pageSize: 10,
      },
      tableData: [],
      selectList: [],
      time: [],
      filterText: "",
    };
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  created() { },

  activated() { },
  mounted() {
    this.getTreeList();
    this.getList();
  },
  methods: {
    handleStatusChange(row) {
      console.log(row.status);

      changeStatusCate({
        id: row.id,

        status: row.status
      }).then(res => {
        this.$message.success('状态更新成功');
        this.getList(); // 刷新列表保持数据同步
      }).catch(() => {
        // 操作失败时恢复原状态
        row.status = row.status == 1 ? 0 : 1;
      });
    },
    exportExcel() {
      this.download(
        "/offCategory/categoryMain/export",
        {
          ...this.params,
        },
        `办公用品类别_${new Date().getTime()}.xlsx`
      );
    },
    edit(value) {
      categoryMainDetail(value.id).then((res) => {
        this.editData = res.data;
        this.detailType = false
        this.addItemType = true;
      });
    },
    detail(value) {
      categoryMainDetail(value.id).then((res) => {
        this.editData = res.data;

        this.addItemType = true;
        this.detailType = true
      });
    },
    newData() {
      this.editData = null;
      this.disabledView = false;
      this.detailType = false
      this.addItemType = true;
    },

    del(value) {
      const ids = value.id;
      queryJudge(ids).then(res => {
        if (!res.data.hasChild && !res.data.hasSupply && value.parentId == 0) {
          this.$confirm(`确认删除主类别【${value.categoryName}】吗`, "警告", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              this.categoryMainDel(ids)
            })
        } else if (value.parentId == 0 && res.data.hasSupply) {
          this.$confirm(`类别【${value.categoryName}】为主类别，请转移本类别下的物品，再进行删除`, "警告", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            this.categoryMainDel(ids)
          })
        } else if (value.parentId == 0 && !res.data.hasSupply && res.data.hasChild) {
          this.$confirm(`主类别删除，其下的子类别也会删除，确认删除主类别【${value.categoryName}】吗`, "警告", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            this.categoryMainDel(ids)
          })
        } else {
          this.$confirm(`确认删除类别【${value.categoryName}】吗，删除后，该类别下的物品将存放在上级类别下`, "警告", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            this.categoryMainDel(ids)
          })
        }
      })
    },
    categoryMainDel(id) {
      categoryMainDel(id).then((res) => {
        if (res.code == 200) {
          this.$message.success("操作成功");
          this.getTreeList();
          this.getList();
        }
      });
    },
    search() {
      this.params.pageNum = 1;
      this.getList();
    },
    reset() {
      this.params = {
        categoryName: "",
        companyId: "",
        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
    },

    handleNodeClick(data) {
      console.log(data);
      this.parentData = data;
      this.params.id = data.id;
      this.getList();
    },
    getTreeList() {
      categoryTree().then((res) => {
        this.leftTreeList = res.data;
      });
      getAuthCompany().then((res) => {
        // this.treeSelect = res.data.dept;
        this.projects = res.rows
      });
    },
    getList() {
      let params = {
        ...this.params,
      };
      list({ ...params }).then((res) => {
        if (res.code == 200) {
          this.tableData = res.rows;

          this.total = res.total;
        }
      });
    },
    getArrEqual(arr1, arr2) {
      for (let i = 0; i < arr2.length; i++) {
        for (let j = 0; j < arr1.length; j++) {
          if (arr1[j].id === arr2[i].id) {
            arr1[j].acttype = true;
          }
        }
      }
    },

    toDetail(v) {
      getDetail(v.id).then((res) => { });
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.categoryName.indexOf(value) !== -1;
    },
    close() {
      this.addItemType = false;
      this.getTreeList();
      this.getList();
    },

  },
};
</script>

<style lang="less" scoped>
.content {
  display: flex;
  width: 100%;
  padding: 16px;

  .left {
    width: 280px;
    height: 650px;
    overflow-y: auto;
    border: 1px solid #ccc;
    flex-shrink: 0;
    padding: 16px;
    box-sizing: border-box;
  }

  .right {
    width: calc(100% - 280px);
    padding-left: 12px;

    .el-button {
      height: 32px;
    }
  }
}

.search {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  .item {
    display: flex;
    align-items: center;
    margin-right: 16px;

    span {
      margin-right: 9px;
    }
  }
}

.el-button {
  height: 36px;
  margin-left: 4px;
  margin-right: 12px;
}

.solid {
  width: 100%;
  height: 1px;
  background: #f2f2f2;
  margin-top: 12px;
}

.selsct {
  width: 14px;
  cursor: pointer;
}
</style>