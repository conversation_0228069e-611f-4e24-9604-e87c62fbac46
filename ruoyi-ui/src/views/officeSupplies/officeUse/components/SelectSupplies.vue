<template>
  <el-dialog title="选择物品" :visible.sync="dialogVisible" width="1200px" append-to-body :before-close="close">
    <div class="dialog-content">
      <!-- 左侧分类树 -->
      <div class="left-tree">
        <el-card shadow="never" style="height:600px;overflow: auto;">
          <el-tree class="filter-tree" :data="leftTreeList" :props="defaultProps" :default-expand-all="false"
            @node-click="handleNodeClick" ref="tree" node-key="id" :highlight-current="true">
          </el-tree>
        </el-card>
      </div>

      <!-- 右侧内容 -->
      <div class="right-content">
        <!-- 搜索栏 -->
        <div class="search-container">
          <el-input v-model="params.itemName" placeholder="输入物品名称" clearable style="width: 300px" />
          <el-button type="primary" icon="el-icon-search" @click="handleSearch" style="margin-left: 10px">搜索</el-button>
        </div>

        <!-- 上方物品表格 -->
        <el-table :data="tableData" height="300" style="width: 100%; margin-top: 16px; margin-left: 4px">
          <el-table-column align="center" show-overflow-tooltip="" prop="itemName" label="物品名称" width="180">
            <template #default="{ row }">
              <div style="color: rgb(63, 161, 255); cursor: pointer" @click="toDetail(row)">
                {{ row.itemName }}
              </div>
            </template>
          </el-table-column>

          <el-table-column align="center" show-overflow-tooltip="" prop="categoryName" label="所属类别" />

          <el-table-column align="center" show-overflow-tooltip="" prop="amount" label="数量" />
          <el-table-column align="center" show-overflow-tooltip="" prop="measureUnit" label="计量单位" />
          <el-table-column align="center" show-overflow-tooltip="" prop="updateTime" label="最新修改时间" />
          <el-table-column align="center" show-overflow-tooltip="" prop="createTime" label="创建时间" />
          <el-table-column align="center" fixed="right" width="170" label="操作">
            <template slot-scope="{ row }">
              <el-button  style="font-size: 20px;" type="text" :icon="isSelected(row) ? 'el-icon-remove' : 'el-icon-circle-plus'
                " @click="toggleItem(row)" />
            </template>
          </el-table-column>
        </el-table>

        <!-- 下方已选物品表格 -->
        <div class="selected-title">已选物品（{{ selectedItems.length }}）</div>
        <el-table :data="selectedItems" border height="200" style="margin-top: 10px">
          <el-table-column align="center" show-overflow-tooltip="" prop="itemName" label="物品名称" width="180">
            <template #default="{ row }">
              <div style="color: rgb(63, 161, 255); cursor: pointer" @click="toDetail(row)">
                {{ row.itemName || row.supplyName }}
              </div>
            </template>
          </el-table-column>

          <el-table-column align="center" show-overflow-tooltip="" prop="categoryName" label="所属类别" />

          <el-table-column align="center" show-overflow-tooltip="" prop="amount" label="数量">
            <template slot-scope="scope">
              {{ scope.row.amount || scope.row.beforeNum }}
            </template>
          </el-table-column>
          <el-table-column label="本次申请数量" width="200">
            <template slot-scope="{ row }">
              <el-input-number v-model="row.applyNum" :min="1"
                :max="row.negativeInventory === '0' ? Infinity : row.amount" :precision="0" controls-position="right"
                size="small" @change="(val) => handleQuantityChange(val, row)" />
            </template>
          </el-table-column>
          <el-table-column align="center" show-overflow-tooltip="" prop="measureUnit" label="计量单位" />
          <el-table-column prop="residueNum" label="库存剩余量" width="120">
            <template slot-scope="{ row }">
              {{ row.residueNum }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80" fixed="right">
            <template slot-scope="{ row }">
              <el-button style="font-size: 20px;" type="text" icon="el-icon-remove" @click="removeFromSelected(row)" />
            </template>
          </el-table-column>
        </el-table>

        <!-- 操作按钮 -->
        <div style="text-align: right; margin-top: 15px">
          <el-button @click="close">取 消</el-button>
          <el-button type="primary" @click="handleConfirm">确 定</el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import {
  supplyMainList,
  categoryTree,
  getAuthCompany,
  supplySettle,
  supplyMainDel,
} from "@/api/officeSupplies/officeSupplies";
export default {
  name: "SelectSupplies",
  props: {
    type: String,
    list: Array,
  },
  data() {
    return {
      params: {
        itemName: "",
        pageNum: 1,
        pageSize: 9999,
      },
      leftTreeList: [],
      dialogVisible: true,
      // 分类树数据
      categoryTree: [],
      defaultProps: {
        children: "children",
        label: "categoryName",
      },
      // 搜索参数
      searchParams: {
        categoryId: null,
        name: "",
      },
      // 物品列表
      itemList: [],
      // 已选物品
      selectedItems: [],
      // 示例数据
      tableData: [],
    };
  },
  mounted() {
    this.getTreeList();
    this.getList();
    if (this.list.length > 0) {
      this.selectedItems = this.list;
    }
  },
  methods: {
    // 获取分类树
    getTreeList() {
      categoryTree().then((res) => {
        this.leftTreeList = res.data;
      });
      getAuthCompany().then((res) => {
        // this.treeSelect = res.data.dept;
        // this.projects = res.data.company;
      });
    },

    // 树节点点击
    handleNodeClick(data) {
      console.log(data);
      this.params.categoryId = data.id;

      this.getList();
    },
    // 搜索
    handleSearch() {
      this.getList();
    },
    // 添加选中
    // 判断是否已选
    isSelected(item) {
      return this.selectedItems.some((i) => (i.id || i.supplyId) === item.id);
    },

    // 切换选中状态
    toggleItem(item) {
      if (this.isSelected(item)) {
        this.removeFromSelected(item);
      } else {
        this.addToSelected(item);
      }
    },

    // 移除选中
    removeFromSelected(item) {
      const index = this.selectedItems.findIndex((i) => i.id === item.id);
      if (index >= 0) {
        this.selectedItems.splice(index, 1);
      }
    },
    toDetail(v) {
      console.log(v);
      this.$router.push({
        path: '/officeSuppliesOther/maintenanceDetail',
        query: { id: v.id }
      })

    },
    // 确认选择
    handleConfirm() {
      this.$emit("submit", this.selectedItems);
      this.$emit("close");
    },
    close() {
      this.$emit("close");
    },

    // 修改获取列表方法
    getList() {
      let params = {
        ...this.params,
        itemType: this.type, queryFlag: '1'
      };
      supplyMainList({ ...params }).then((res) => {
        if (res.code == 200) {
          this.tableData = res.rows.map((item) => ({
            ...item,
            // 修改为直接计算数值
            residueNum: (item.amount || 0) - (item.usedAmount || 0),
          }));
        }
      });
    },

    // 修改添加选中方法
    addToSelected(item) {
      if (!this.isSelected(item)) {
        this.selectedItems.push({
          ...item,
          applyNum: 1,
          // 修改为直接计算数值
          residueNum: (item.amount || 0) - (item.usedAmount || 1),
        });
      }
    },

    // 修改数量变化处理
    handleQuantityChange(val, row) {
      // 修复逻辑：根据是否允许负库存控制输入范围
      const max = row.negativeInventory === '1' ?
        (row.amount || row.beforeNum) :
        Infinity;

      // 当超过最大允许值且不允许负库存时
      if (row.negativeInventory === '1' && val > max) {
        this.$message.warning("申请数量不能超过可用库存");
        row.applyNum = max;  // 强制设置为最大可用值
        val = max;  // 同步当前值
      }

      // 统一计算剩余数量（允许负库存时为实际计算结果）
      row.residueNum = (row.amount || row.beforeNum) - val;
    },
  },
};
</script>

<style scoped>
.dialog-content {
  display: flex;
}

.left-tree {
  width: 200px;
  margin-right: 20px;
  overflow: auto;
}

.right-content {
  flex: 1;
  overflow: hidden;
}

.search-container {
  display: flex;
  align-items: center;
}

.selected-title {
  font-weight: bold;
  color: #606266;
  margin-top: 20px;
}

.el-tree {
  min-height: 300px;
}
</style>