<template>
  <div class="p-5">
    <MyForm
      labelWidth="95px"
      v-model="queryParams"
      :columns="formColumns"
      @onSearchList="handleQuery"
    >
    </MyForm>
    <el-button
      type="warning"
      plain
      class="mb-5"
      icon="el-icon-download"
      size="mini"
      @click="handleExport"
      v-hasPermi="['data:productFD10:export']"
      >导出</el-button
    >
    <MyTable
      :columns="columns"
      :source="configList"
      :queryParams="queryParams"
      @orderChange="orderChange"
      :default-sort = "{prop: 'loanMonth', order: 'descending'}"
    >
    </MyTable>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import XEUtils from "xe-utils";
import { formatNumberWithCommas } from "@/utils";
import { companyList } from "@/api/businessInformation/companyInformation";
import { getSelectSysDictRefList } from "@/api/ref/ref";
import { listFd10 } from "@/api/data/productFD10";
import config from "./components/config";
export default {
  name: "ProductFD10",
  data() {
    return {
      ...config,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        fpd1:undefined,
        fpd3:undefined,
        fpd7:undefined,
        sortMap:{loanMonth:'desc'}
      },
      configList: [],
      total: 0,
    };
  },
  computed: {},
  created() {
    this.init();
  },
  methods: {
    init() {
      this.initSelectData();
      this.getList();
    },
    initSelectData() {
      this.getProjectParameter();
    },

    async getProjectParameter() {
       getSelectSysDictRefList({ unitType: 4, moduleTypeOfNewAuth: 'DATAREPORT' }).then((response) => {
        this.formColumns[0].options = response;
      });
      getSelectSysDictRefList({ unitType: 0, moduleTypeOfNewAuth: 'DATAREPORT' }).then((response) => {
       this.formColumns[1].options  = response;
      });
      getSelectSysDictRefList({ unitType: 3, moduleTypeOfNewAuth: 'DATAREPORT' }).then((response) => {
        this.formColumns[4].options  = response;
      });
      getSelectSysDictRefList({ unitType: 2, moduleTypeOfNewAuth: 'DATAREPORT' }).then((response) => {
        this.formColumns[3].options  = response;
      });
      getSelectSysDictRefList({ unitType: 1, moduleTypeOfNewAuth: 'DATAREPORT' }).then((response) => {
        this.formColumns[2].options = response;
      });
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    orderChange(value) {
      const prioritizedProps = ["productName", "loanMonth"];
      const sortedArr = value.sort((a, b) => {
        const aIndex = prioritizedProps.indexOf(a.prop);
        const bIndex = prioritizedProps.indexOf(b.prop);
        return (
          (aIndex === -1 ? value.length : aIndex) -
          (bIndex === -1 ? value.length : bIndex)
        );
      });
      const result = sortedArr.reduce((acc, { order, prop }) => {
        if (prop.indexOf("cntFpd") != -1) {
          acc[prop.replace(/cntFpd/g, "cntDpd")] = order;
        } else {
          acc[prop.replace(/fpd/g, "balDpd")] = order;
        }
        return acc;
      }, {});
      this.queryParams.sortMap = result;
      this.getList();
    },
    async getList() {
      const params = XEUtils.clone(this.queryParams, true);
      params.fpd1Start = params.fpd1?.split("-")[0];
      params.fpd1End = params.fpd1?.split("-")[1];
      params.fpd3Start = params.fpd3?.split("-")[0];
      params.fpd4End = params.fpd3?.split("-")[1];
      params.fpd7Start = params.fpd7?.split("-")[0];
      params.fpd7End = params.fpd7?.split("-")[1];
      delete params.fpd1;
      delete params.fpd3;
      delete params.fpd7;
      const { rows, total } = await listFd10(params);
      this.configList = rows;
      this.total = total;
      this.handerConfigList();
    },
    handerConfigList() {
      const toformatNumber = ["loanAmt"];
      this.configList.forEach((item) => {
        toformatNumber.forEach((item1) => {
          item[item1] = formatNumberWithCommas(item[item1]);
        });
        Object.keys(item).forEach((item1) => {
          if (item1?.indexOf("fpd") != -1 || item1?.indexOf("cntFpd") != -1) {
            item[item1] =item[item1]!=undefined&&item[item1]!=null?item[item1] + "%":"";
          }
        });
      });
    },
    handleExport() {
      const params = XEUtils.clone(this.queryParams, true);
      params.sortMapString = JSON.stringify(params.sortMap);
      delete params.sortMap;
      delete params.pageNum;
      delete params.pageSize;
      this.download("fpd/analysis/export", params, `产品首逾率FPD.xlsx`);
    },
  },
};
</script>
