<template>
  <div class="app-container">
    <div v-if="!form.projectName" style="width: 600px; margin: 0 auto">
      <span>请选择项目名称</span><br />
      <span
        >项目名称由项目经理在 [业务信息配置-项目名称]
        中创建，并且请确认您已获得该项目权限</span
      ><br />
      <div style="margin-top: 16px">
        <span style="color: #000; margin-right: 9px">项目名称</span>
        <el-select
          filterable
          v-model="form.oaProjectDeployId"
          style="width: 400px"
          @change="changeProject"
          placeholder="请选择"
        >
          <el-option
            v-for="item in projectList"
            :key="item.id"
            :label="item.projectName"
            :value="item.id"
          ></el-option>
        </el-select>
      </div>
    </div>

    <el-form ref="form" v-else :model="form" :rules="rules" label-width="80px">
      <el-row>
        <el-col :span="9">
          <el-form-item label="项目名称" prop="projectName">
            <el-select
              filterable
              v-model="form.oaProjectDeployId"
              @change="changeProject"
              style="width: 97%"
              placeholder="请选择"
            >
              <el-option
                v-for="item in projectList"
                :key="item.id"
                :label="item.projectName"
                :value="item.id"
              ></el-option>
            </el-select>
            <p>项目名称需要由项目经理在 [业务信息配置-项目名称] 中创建</p>
          </el-form-item></el-col
        >
        <el-col :span="4"></el-col>
        <el-col :span="9">
          <el-form-item label="项目类型" prop="projectType">
            <el-select @change="changeProjectType" v-model="form.projectType">
              <el-option
                v-for="item in projectTypeList"
                :key="item.id"
                :value="item.projectType"
                :label="item.projectTypeName"
              ></el-option>
            </el-select>
            <p style="color: red" v-if="projectTypeList.length == 0">
              当前项目类型未配置正确的关联关系，请联系技术部
            </p>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="9">
          <el-form-item label="担保公司" prop="custName">
            <el-select
              v-model="form.custName"
              placeholder="请选择"
              style="width: 97%"
              filterable
              clearable
              size="small"
            >
              <el-option
                v-for="dict in custNameSelect"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4"></el-col>
        <el-col :span="9">
          <el-form-item label="汇款公司" prop="incomeCustName">
            <el-autocomplete
              class="inline-input"
              v-model="form.incomeCustName"
              :fetch-suggestions="querySearchincome"
              placeholder="请输入"
              style="width: 97%"
              :maxlength="30"
              show-word-limit
              clearable
              @select="huoqu2"
            ></el-autocomplete>
            <!-- <el-input style="width: 40%;" v-model="form.incomeCustName" maxlength="30" show-word-limit clearable placeholder="请输入汇款公司" /> -->
          </el-form-item>
        </el-col>
      </el-row>

      <el-divider></el-divider>
      <div v-show="addNormal">
        <span class="form2span1">信息费公司与费率</span><br />
        <span class="form2span21"
          >设置信息费公司信息，用于自动计算应付信息费。信息费公司必须从[OA-收款人配置]中选择，如果没有需要新建</span
        ><br />
        <span class="form2span21"
          >您可以设置多个费率方案，实现同一担保公司对应多种费率</span
        ><br />
        <span class="form2span21">信息费 = 收入 × 费率% × (1-税率%)</span><br />
        <span class="form2span21">提成信息费= 收入 × 费率%</span><br />
        <br />

        <div style="width: 70%; background: #fafafa; border: 1px solid #cccccc">
          <div style="width: 90%; height: 30px; margin-left: 50px">
            <div style="height: 10px; margin-left: 153px; margin-top: 12px">
              <span
                style="
                  font-size: 14px;
                  font-weight: bold;
                  font-family: 'Microsoft YaHei';
                "
                >信息费公司名称</span
              >
              <span
                style="
                  margin-left: 433px;
                  font-size: 14px;
                  font-weight: bold;
                  font-family: 'Microsoft YaHei';
                "
                >费率</span
              >
              <span
                style="
                  margin-left: 102px;
                  font-size: 14px;
                  font-weight: bold;
                  font-family: 'Microsoft YaHei';
                "
                >税率</span
              >
            </div>
            <el-divider />
          </div>
          <br />

          <div v-for="(schemeDomain, schemeIndex) in schemeList">
            <div style="margin-left: 10px; float: left">
              <span style="font-size: 14px">方案{{ schemeIndex + 1 }}</span>
              <el-button
                type="primary"
                v-if="schemeIndex > 0"
                style="height: 32px; line-height: 10px; margin-left: 20px"
                @click="removeSchemeListDomain(schemeDomain, schemeIndex)"
                >- 删除方案</el-button
              >
            </div>
            <div style="margin-left: 120px">
              <el-form-item
                label-witdh="200px"
                v-for="(domain, index) in schemeDomain.cwProjectCusts"
                :label="''"
                :key="domain.key"
                :prop="'domain.' + index + '.value'"
                :required="true"
              >
                <div
                  style="
                    display: flex;
                    align-items: center;
                    margin-bottom: 14px;
                  "
                >
                  <el-select
                    @change="changeAcc(schemeIndex, index, $event)"
                    v-model="domain.oaTraderId"
                    placeholder="请选择"
                    style="width: 500px"
                    filterable=""
                  >
                    <el-option
                      v-for="item in accountCompanyList"
                      :key="item.id"
                      :label="item.project"
                      :value="item.id"
                    ></el-option>
                  </el-select>

                  <span style="margin-left: 20px"></span
                  ><el-input
                    type="number"
                    v-model="domain.rate"
                    :prop="'domain.' + index + '.value'"
                    style="width: 12%"
                    ><template slot="append">%</template></el-input
                  >
                  <span style="margin-left: 20px"></span
                  ><el-input
                    type="number"
                    v-model="domain.taxRate"
                    :prop="'domain.' + index + '.value'"
                    style="width: 12%"
                  >
                    <template slot="append">%</template></el-input
                  >
                  <el-button
                    type="primary"
                    v-if="index > 0"
                    style="margin-left: 20px; height: 32px; line-height: 10px"
                    @click.prevent="
                      removeDomainSchemeDomain(schemeIndex, domain)
                    "
                    >-删除</el-button
                  >
                </div>
              </el-form-item>
              <span
                style="color: red; size: 13px"
                v-show="schemeDomain.checkRepeatFlag"
                >请检查公司名称，不能有重复的信息费公司名称！</span
              >

              <el-form-item>
                <el-button
                  type="primary"
                  style="height: 32px; line-height: 10px"
                  @click="addDomainSchemeDomain(schemeIndex)"
                  >+ 添加信息费公司</el-button
                >
              </el-form-item>
            </div>
            <div style="width: 90%; margin-left: 50px">
              <el-divider />
            </div>
          </div>

          <div style="width: 90%; margin-left: 10px; height: 55px">
            <el-button
              style="height: 32px; line-height: 10px"
              type="primary"
              @click="addSchemeListDomain"
              >+ 添加方案</el-button
            >
          </div>
        </div>

        <el-divider></el-divider>
      </div>

      <div v-show="addLaw">
        <span class="form2span1">信息费公司与费率</span><br />
        <span class="form2span21"
          >设置信息费公司信息，用于自动计算信息费。信息费公司必须从[OA-收款人配置]中选择，如果没有需要新建</span
        ><br />
        <span class="form2span21"
          >信息费 = ( 真实回款金额 - 服务费 - ( 本金 × 3%)) × 费率</span
        ><br />
        <!--          <span class="form2span21">提成信息费= 信息费 × （1 - 税率%）</span><br>-->
        <br />

        <div style="width: 65%; background: #fafafa; border: 1px solid #cccccc">
          <div style="width: 90%; height: 30px; margin-left: 50px">
            <div style="height: 10px; margin-left: 33px; margin-top: 12px">
              <span
                style="
                  font-size: 14px;
                  font-weight: bold;
                  font-family: 'Microsoft YaHei';
                "
                >信息费公司名称</span
              >
              <span
                style="
                  margin-left: 433px;
                  font-size: 14px;
                  font-weight: bold;
                  font-family: 'Microsoft YaHei';
                "
                >费率</span
              >
            </div>
            <el-divider />
          </div>
          <br />
          <el-form-item
            v-for="(domain, index) in cwProjectLawCusts"
            :label="'' + (index + 1) + ''"
            :key="domain.key"
            :required="true"
          >
            <div
              style="display: flex; align-items: center; margin-bottom: 16px"
            >
              <el-select
                @change="changeAccLaw(index, $event)"
                v-model="domain.oaTraderId"
                placeholder="请选择"
                style="width: 500px"
                filterable=""
              >
                <el-option
                  v-for="item in accountCompanyList"
                  :key="item.id"
                  :label="item.project"
                  :value="item.id"
                ></el-option>
              </el-select>
              <!--            @blur="checkRepeat(index)"-->
              <!--            @change="checkRepeat(index)"-->
              <!-- <el-input maxlength="30"
    show-word-limit clearable v-model="domain.custName" style="width: 30%;"></el-input> -->
              <span style="margin-left: 20px"></span
              ><el-input
                type="number"
                v-model="domain.rate"
                style="width: 120px"
                ><template slot="append">%</template></el-input
              >
              <!--              <span style="margin-left:20px">税率</span><el-input type="number" v-model="domain.taxRate"  :prop="'domain.' + index + '.value'" style="width: 120px;"> <template slot="append">%</template></el-input>-->
              <el-button
                type="primary"
                v-if="index > 0"
                style="margin-left: 60px; height: 32px; line-height: 10px"
                @click.prevent="removeDomain(domain)"
                >-删除</el-button
              >
            </div>
          </el-form-item>
          <span style="color: red; size: 13px" v-show="checkRepeatFlagLaw"
            >请检查公司名称，不能有重复的信息费公司名称！</span
          >

          <el-form-item>
            <el-button
              style="height: 32px; line-height: 10px; margin-bottom: 16px"
              type="primary"
              @click="addDomain"
              >+ 添加信息费公司</el-button
            >
          </el-form-item>
        </div>
      </div>
      <div>
        <el-form-item label-width="140px" label="是否生成记账凭证：">
          <el-radio v-model="form.generateCertificateFlag" :label="0"
            >否</el-radio
          >
          <el-radio v-model="form.generateCertificateFlag" :label="1"
            >是</el-radio
          >
        </el-form-item>
        <span style="color: #999">{{
          form.generateCertificateFlag == 1
            ? "项目将在每个期次完结后，自动生成2种记账凭证"
            : "如果选择是，项目将在每个期次金额确认后，自动生成2种记账凭证"
        }}</span>
        <div v-if="form.generateCertificateFlag == 1">
          <el-table
            border
            :data="tableData"
            style="width: 100%; margin-top: 16px"
          >
            <el-table-column prop="zjsx" label="资金事项" />
            <el-table-column prop="zy" label="摘要" />
            <el-table-column prop="date" label="借方科目">
              <template slot-scope="scope">
                <div v-if="scope.row.jfkm == 1">
                  <p style="margin-bottom: 0">直接收款类型:</p>
                  <span style="color: #999">银行存款-担保费收款方简称</span>
                  <p style="margin-bottom: 0; margin-top: 12px">
                    混合平台收益类型:
                  </p>
                  <span style="color: #999">预收账款-项目名称</span>
                </div>
                <div v-else>主营业务成本-项目名称</div>
              </template>
            </el-table-column>
            <el-table-column prop="jfje" label="借方金额">
              <template slot-scope="scope">
                <div v-if="scope.row.jfje == 1">该期次的收入</div>
                <div v-else>
                  {{ addNormal ? "该期次的应付信息费" : "该期次的信息费" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="date" label="贷方科目">
              <template slot-scope="scope">
                <div v-if="scope.row.dfkm == 1">
                  <p style="margin-bottom: 0">担保费收入-项目名称</p>
                </div>
                <div v-else>
                  <p style="margin-bottom: 0">每个期次生成2类贷方科目:</p>
                  <span style="color: #999"
                    >应付账款-项目名称-信息费公司名称</span
                  ><br />
                  <span style="color: #999">营业外收入</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="date" label="贷方金额">
              <template slot-scope="scope">
                <div v-if="scope.row.dfje == 1">
                  <p style="margin-bottom: 0">收入:</p>
                </div>
                <div v-else>
                  <p style="margin-bottom: 0">2类贷方科目对应金额:</p>
                  <span style="color: #999">{{
                    addNormal ? "每个公司的实付信息费" : "信息费取整"
                  }}</span
                  ><br />
                  <span style="color: #999">{{
                    addNormal
                      ? "所有公司的应付减实付"
                      : "应付信息费减信息费取整"
                  }}</span>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <el-form-item
            style="margin-top: 16px"
            label-width="140px"
            label="凭证所属账套："
          >
            <el-select
              filterable
              v-model="form.accountSetsId"
              style="width: 300px"
              placeholder="请选择"
            >
              <el-option
                v-for="item in accountList"
                :key="item.id"
                :label="item.companyName"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            style="margin-top: 16px"
            label-width="140px"
            label="担保费收入的类型："
          >
            <el-radio v-model="form.guaranteeIncomeType" :label="0"
              >直接收款</el-radio
            >
            <el-radio v-model="form.guaranteeIncomeType" :label="1"
              >混合平台收益</el-radio
            >
          </el-form-item>
          <el-form-item
            style="margin-top: 16px"
            label-width="140px"
            label="担保费收款方："
          >
            <el-select
              v-model="form.guarantyPayee"
              placeholder="请选择"
              style="width: 300px"
              @change="changeGuaranty"
              filterable=""
            >
              <el-option
                v-for="item in accountCompanyList"
                :key="item.id"
                :label="item.project"
                :value="item.id"
              ></el-option>
            </el-select>
            <span style="margin-left: 16px"
              >简称：{{ jiancheng ? jiancheng : "--" }}</span
            >
          </el-form-item>
          <span
            style="
              display: inline-block;
              margin-left: 139px;
              margin-top: 12px;
              color: #999;
            "
            >该收款方的简称用于生成担保费收入记账凭证的借方二级科目</span
          >
        </div>
      </div>
      <el-divider></el-divider>

      <div v-show="projectRoleList.length > 0">
        <span class="form2span1">项目角色</span><br />
        <span> <span>根据 [通用授权] 功能自动进行分配，如需修改，在列表页点击【项目角色授权】，由拥有权限的用户对其他用户进行授权</span></span>
        <el-table :data="projectRoleList" border="" style="width: 100%">
          <el-table-column prop="date" label="本项目角色" width="120">
            <template slot-scope="scope">
              {{
                scope.row.featureUserFlag == 1
                  ? "会计"
                  : scope.row.featureUserFlag == 2
                  ? "出纳"
                  : scope.row.featureUserFlag == 3
                  ? "业务"
                  : "查看权限"
              }}
            </template>
          </el-table-column>
          <el-table-column prop="name" label="说明" show-overflow-tooltip="">
            <template slot-scope="scope">
              {{
                scope.row.featureUserFlag == 1
                  ? "负责录入每期次收入并计算出信息费金额"
                  : scope.row.featureUserFlag == 2
                  ? "负责确认每期次的收入及信息费金额是否正确"
                  : scope.row.featureUserFlag == 3
                  ? "负责确认每期次的收入及信息费金额是否正确"
                  : "在会计与业务之外，其他有查看权限的用户"
              }}
            </template>
          </el-table-column>
          <el-table-column prop="address" label="人员" show-overflow-tooltip="">
            <template
              slot-scope="scope"
              v-if="
                scope.row.featureUserList &&
                scope.row.featureUserList.length > 0
              "
            >
              <span
                v-for="item in scope.row.featureUserList"
                :key="item.featureUserId"
                >{{ item.authorizedUserNickName }}，</span
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-form>
    <div style="width: 100%; height: 60px" v-if="form.projectName">
      <div
        style="width: 100%; height: 60px; line-height: 60px; text-align: center"
      >
        <el-button type="primary" @click="submitForm('form')"
          >创建项目</el-button
        >
        <el-button @click="quxiaoForm()">取 消</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import {
  projectShowxiala,
  addProjectshow,
  getuser,
  listDeploy,
  listTrader,
  getAccountSetsList,
  getFilterOaProjectDeployList,
  getSelectProjectTypeByOaProjectDeployId,
  getProjectRoleByOaProjectDeployId,
} from "@/api/caiwu/project";
import { getAllAccountSetsList } from "@/api/oa/voucharRules";
import {splicingListByCode} from "@/api/businessInformation/productInformation";
export default {
  name: "Project",
  data() {
    return {
      projectRoleList: [],
      projectTypeList: [],
      accountList: [],
      jiancheng: "",
      tableData: [
        {
          zjsx: "担保费收入",
          zy: "项目名称+期次+担保费收入",
          jfkm: "1",
          jfje: "1",
          dfkm: "1",
          dfje: 1,
        },
        {
          zjsx: "信息费",
          zy: "项目名称+日期+应支付信息费",
          jfkm: "2",
          jfje: "2",
          dfkm: "2",
          dfje: 2,
        },
      ],
      //担保公司选择框
      danbaoCompanySelect: [],
      //普通项目用 - 信息费公司与费率
      schemeList: [
        {
          cwProjectCusts: [
            {
              custName: "",
              rate: "0",
              taxRate: "0",
              schemeFlag: null,
              oaTraderId: "",
            },
          ],
          //检查重复的名称标志 - 通道和分润
          checkRepeatFlag: false,
        },
      ],
      //法催项目用 - 信息费公司与费率
      cwProjectLawCusts: [
        {
          custName: "",
          rate: "0",
          taxRate: "0",
          schemeFlag: null,
        },
      ],
      // //检查重复的名称标志 - 通道和分润
      // checkRepeatFlag: false,
      //检查重复的名称标志 - 法催
      checkRepeatFlagLaw: false,
      // projectType: 0,

      dynamicValidateForm: {
        domains: [
          {
            value: "",
          },
        ],
      },
      projectData: null,
      // 遮罩层
      loading: true,
      //进入页面默认普通项目被展示出来
      addNormal: false,
      //进入页面默认法催项目隐藏
      addLaw: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 财务项目管理主表格数据
      projectList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      //会计用户集合
      kuaijiList: [],
      //出纳用户集合
      chunaList: [],
      //业务用户集合
      yewuList: [],
      //所有用户角色集合
      alluserList: [],
      // 查询参数
      queryParams: {
        dateRange: [],
        pageNum: 1,
        pageSize: 10,
        projectName: null,
        custName: null,
        incomeCustName: null,
        projectFlag: null,
        status: null,
        createTime: null,
        updateTime: null,
      },

      // 表单参数
      form: {
        id: "",
        projectTypeCode: "",
        generateCertificateFlag: 0,
        accountSetsId: "",
        oaProjectDeployId: "",
        guaranteeIncomeType: null,
        guarantyPayee: "",
        projectType: null,
        projectPortfolioCode: null,
        projectName: null,
        custName: null,
        incomeCustName: null,
        //预存收入
        prestoreIncomeFlag: 0,
        cwProjectCusts: [
          {
            custName: "",
            rate: "0",
            taxRate: "0",
            schemeFlag: null,
            oaTraderId: "",
          },
        ],
        //会计选中集合
        accountants: [],
        //出纳选中集合
        cashiers: [],
        //业务选中集合
        salesmans: [],
        //查看权限选中集合
        checkUserList: [],
        //导出权限选中集合
        exportUserList: [],
      },
      accountCompanyList: [],
      // 表单校验
      rules: {
        projectName: [
          { required: true, message: "项目名称不能为空", trigger: "blur" },
          {
            min: 1,
            max: 30,
            message: "长度在 1 到 30 个字符",
            trigger: "blur",
          },
        ],
        projectType: [
          { required: true, message: "项目类型不能为空", trigger: "blur" },
        ],
        custName: [
          {
            required: true,
            message: "担保公司不能为空",
            trigger: ["blur", "change"],
          },
          {
            min: 1,
            max: 30,
            message: "长度在 1 到 30 个字符",
            trigger: ["blur", "change"],
          },
        ],
        incomeCustName: [
          {
            required: true,
            message: "汇款公司不能为空",
            trigger: ["blur", "change"],
          },
          {
            min: 1,
            max: 30,
            message: "长度在 1 到 30 个字符",
            trigger: ["blur", "change"],
          },
        ],
        // projectFlag: [
        //   { required: true, message: "项目状态：0正常 1终止不能为空", trigger: "blur" }
        // ],
        // createTime: [
        //   { required: true, message: "创建时间不能为空", trigger: "blur" }
        // ],
        // updateTime: [
        //   { required: true, message: "更新时间不能为空", trigger: "blur" }
        // ],
        // accountants: [
        //   {
        //     type: "array",
        //     required: true,
        //     message: "会计不能为空",
        //     trigger: "change",
        //   },
        // ],
        // cashiers: [
        //   {
        //     type: "array",
        //     required: true,
        //     message: "出纳不能为空",
        //     trigger: "change",
        //   },
        // ],
        // salesmans: [
        //   {
        //     type: "array",
        //     required: true,
        //     message: "业务不能为空",
        //     trigger: "change",
        //   },
        // ],
      },
      //担保公司下拉框
      custNameSelect: [],
      //收入公司下拉框
      incomeCustNameselect: [],
      //信息费公司下拉框
      feenameList: [],
      projectTypeSelectAll:[]
    };
  },
  created() {
    this.getxiala();
    this.getProjectTypesList();
  },
  computed: {
    leaveDataNew() {
      return JSON.parse(JSON.stringify(this.cwProjectLawCusts)); //watch监听要求
    },
    leaveDataNew1() {
      return JSON.parse(JSON.stringify(this.schemeList)); //watch监听要求
    },
  },
  watch: {
    leaveDataNew: {
      handler(newValue, oldValue) {
        for (let i = 0; i < newValue.length; i++) {
          if (oldValue[i].custName != newValue[i].custName) {
            this.checkRepeat(i);
            break;
          }
        }
      },
      immediate: false,
      deep: true,
    },
    leaveDataNew1: {
      handler(newValue, oldValue) {
        for (let i = 0; i < newValue.length; i++) {
          // if (oldValue[i].custName != newValue[i].custName) {
          this.checkAllRepeatSchemeDomain(i);
          // break;
          // }
        }
      },
      immediate: false,
      deep: true,
    },
    // 'form.cwProjectCusts.custName':{
    //   //deep:true 设置深度监听 当值发生变化时触发handler方法
    //   deep:true,
    //   handler:function(newVal,oldVal){
    //     console.log('ewqewqewqewqqwe',oldVal);
    //     console.log('ddsadasdasdasdsadasd',newVal);
    //     //取newVal实时校验
    //
    //   }
    // }
  },
  methods: {
    async getProjectTypesList(){
      const {rows}=await splicingListByCode({firstDataCode:'project_type'});
      this.projectTypeSelectAll=rows;
    },
    changeGuaranty(e) {
      let data = this.accountCompanyList.find((item) => {
        return item.id == e;
      });
      this.jiancheng = data.abbreviation;
    },
    changeAcc(schemeIndex, index, e) {
      console.log(schemeIndex, index, e);
      let data = this.accountCompanyList.find((item) => {
        return item.id == e;
      });
      this.schemeList[schemeIndex].cwProjectCusts[index].custName =
        data.project;
      console.log(this.schemeList);
    },
    changeAccLaw(index, e) {
      let data = this.accountCompanyList.find((item) => {
        return item.id == e;
      });
      this.cwProjectLawCusts[index].custName = data.project;
      console.log(this.cwProjectLawCusts, "--");
    },
    changeProjectType(e) {
      console.log(e);
      let data = this.projectTypeList.find((item) => {
        return item.projectType == e;
      });
      this.form.projectTypeCode = data.projectTypeCode;
      this.form.projectPortfolioCode = data.projectPortfolioCode;
      getProjectRoleByOaProjectDeployId({
        oaProjectDeployId: this.form.oaProjectDeployId,
      }).then((res) => {
        if (res.code == 200 && res.data.length > 0) {
          let arr = [];
          res.data.forEach((item) => {
            if (this.form.projectPortfolioCode == 'lawUrging') {
              if (
                item.featureUserFlag == 1 ||
                item.featureUserFlag == 2 ||
                item.featureUserFlag == 88
              ) {
                arr.push(item);
              }
            } else {
              if (
                item.featureUserFlag == 1 ||
                item.featureUserFlag == 3 ||
                item.featureUserFlag == 88
              ) {
                arr.push(item);
              }
            }
          });
          this.projectRoleList = arr;
        }
      });
      if (this.form.projectPortfolioCode == 'lawUrging') {
        this.addLaw = true;
        this.addNormal = false;
      } else {
        this.addLaw = false;
        this.addNormal = true;
      }
    },
    changeProject(e) {
      console.log(e);
      this.form.projectType = null;
      this.form.projectPortfolioCode = null;
      this.addNormal = false;
      this.addLaw = false;
      this.projectRoleList = [];

      getSelectProjectTypeByOaProjectDeployId({ oaProjectDeployId: e }).then(
        (res) => {
          if (res.code == 200) {
            res.data.forEach((item) => {
              item.projectTypeName = "";
              this.projectTypeSelectAll.forEach(item1=>{
                if(item.projectTypeCode==item1.id){
                  item.projectTypeName=item1.dataName
                }
              })
              // item.projectTypeName =
              //   item.projectType == 0
              //     ? "通道业务"
              //     : item.projectType == 1
              //     ? "法催业务"
              //     : item.projectType == 2
              //     ? "分润业务"
              //     : "保函业务";
            });
            this.projectTypeList = res.data;
            if (this.projectTypeList.length == 1) {
              this.form.projectTypeCode =
                this.projectTypeList[0].projectTypeCode;
              this.form.projectType = this.projectTypeList[0].projectType;
              this.form.projectPortfolioCode = this.projectTypeList[0].projectPortfolioCode;
              getProjectRoleByOaProjectDeployId({ oaProjectDeployId: e }).then(
                (res) => {
                  if (res.code == 200 && res.data.length > 0) {
                    let arr = [];
                    res.data.forEach((item) => {
                      if (this.form.projectPortfolioCode == 'lawUrging') {
                        if (
                          item.featureUserFlag == 1 ||
                          item.featureUserFlag == 2 ||
                          item.featureUserFlag == 88
                        ) {
                          arr.push(item);
                        }
                      } else {
                        if (
                          item.featureUserFlag == 1 ||
                          item.featureUserFlag == 3 ||
                          item.featureUserFlag == 88
                        ) {
                          arr.push(item);
                        }
                      }
                    });
                    this.projectRoleList = arr;
                  }
                }
              );
              if (this.form.projectPortfolioCode == 'lawUrging') {
                this.addLaw = true;
                this.addNormal = false;
              } else {
                this.addLaw = false;
                this.addNormal = true;
              }
            }
          }
        }
      );
      let data = this.projectList.find((item) => {
        return item.id == e;
      });

      this.form.projectName = data.projectName;
    },
    huoqu1(item) {
      this.form.custName = item.value;
    },
    huoqu2(item) {
      this.form.incomeCustName = item.value;
    },
    // todo 删除后检查所有的方案是否是正常的
    checkAllRepeatSchemeDomain(schemeIndex) {
      var a = 0;
      if (this.schemeList[schemeIndex].cwProjectCusts.length > 1) {
        for (
          let i = 0;
          i < this.schemeList[schemeIndex].cwProjectCusts.length;
          i++
        ) {
          for (
            let j = 0;
            j < this.schemeList[schemeIndex].cwProjectCusts.length;
            j++
          ) {
            if (i !== j) {
              if (
                this.schemeList[schemeIndex].cwProjectCusts[i].custName !==
                  "" &&
                this.schemeList[schemeIndex].cwProjectCusts[j].custName !== ""
              ) {
                if (
                  this.schemeList[schemeIndex].cwProjectCusts[i].custName ===
                  this.schemeList[schemeIndex].cwProjectCusts[j].custName
                ) {
                  a++;
                }
              }
            }
          }

          // if (this.schemeList[schemeIndex].cwProjectCusts[i-1].custName !== '' && this.schemeList[schemeIndex].cwProjectCusts[i].custName !== '') {
          //   if (this.schemeList[schemeIndex].cwProjectCusts[i-1].custName === this.schemeList[schemeIndex].cwProjectCusts[i].custName) {
          //     a++;
          //   }
          // }
        }
      }
      if (a > 0) {
        this.schemeList[schemeIndex].checkRepeatFlag = true;
      } else {
        this.schemeList[schemeIndex].checkRepeatFlag = false;
      }
    },
    //检查信息费公司名称是否有重复
    checkRepeatSchemeDomain(schemeIndex, index) {
      var cwProjectCust = this.schemeList[schemeIndex].cwProjectCusts[index];
      if (this.form.projectPortfolioCode != 'lawUrging') {
        var a = 0;
        if (this.schemeList[schemeIndex].cwProjectCusts.length > 1) {
          for (
            let i = 0;
            i < this.schemeList[schemeIndex].cwProjectCusts.length;
            i++
          ) {
            if (i !== index) {
              if (
                cwProjectCust.custName !== "" &&
                this.schemeList[schemeIndex].cwProjectCusts[i].custName !== ""
              ) {
                if (
                  cwProjectCust.custName ===
                  this.schemeList[schemeIndex].cwProjectCusts[i].custName
                ) {
                  a++;
                }
              }
            }

            // if (this.schemeList[schemeIndex].cwProjectCusts[i - 1].custName !== '' && this.schemeList[schemeIndex].cwProjectCusts[i].custName !== '') {
            //   if (this.schemeList[schemeIndex].cwProjectCusts[i - 1].custName === this.schemeList[schemeIndex].cwProjectCusts[i].custName) {
            //     a++;
            //   }
            // }
          }
        }
        if (a > 0) {
          this.schemeList[schemeIndex].checkRepeatFlag = true;
        } else {
          this.schemeList[schemeIndex].checkRepeatFlag = false;
        }
      }
    },
    //检查信息费公司名称是否有重复
    checkRepeat(index) {
      var cwProjectLawCust = this.cwProjectLawCusts[index];
      if (this.form.projectPortfolioCode == 'lawUrging') {
        //第一条信息不检验 index === 0
        // if (index > 0) {
        //   if (this.cwProjectLawCusts[index - 1].custName === this.cwProjectLawCusts[index].custName) {
        //     this.checkRepeatFlag = true;
        //   } else {
        //     this.checkRepeatFlag = false;
        //   }
        // }
        var a = 0;
        if (this.cwProjectLawCusts.length > 1) {
          for (let i = 0; i < this.cwProjectLawCusts.length; i++) {
            if (i !== index) {
              if (
                cwProjectLawCust.custName !== "" &&
                this.cwProjectLawCusts[i].custName !== ""
              ) {
                if (
                  cwProjectLawCust.custName ===
                  this.cwProjectLawCusts[i].custName
                ) {
                  a++;
                }
              }
            }
          }
          //   if (this.cwProjectLawCusts[i - 1].custName !== '' && this.cwProjectLawCusts[i].custName !== '') {
          //     if (this.cwProjectLawCusts[i - 1].custName === this.cwProjectLawCusts[i].custName) {
          //       a++;
          //     }
          //   }
          // }
        }
        if (a > 0) {
          this.checkRepeatFlagLaw = true;
        } else {
          this.checkRepeatFlagLaw = false;
        }
      }
    },
    //获取下拉数据
    getxiala() {
      projectShowxiala().then((response) => {
        // this.custNameSelect = response.custList
        this.custNameSelect = response.custListDanBao;
        this.incomeCustNameselect = response.incomeList;
        this.feenameList = response.FeeCust;
      });
      getFilterOaProjectDeployList().then((res) => {
        if (res.code == 200) {
          this.projectList = res.rows;
        }
      });
      listTrader({ isEnable: "Y" }).then((res) => {
        if (res.code == 200) {
          res.rows.map((item) => {
            item.project = "";
            item.project +=
              item.userName + "（" + item.accountNumber.slice(-4) + "）";
          });
          this.accountCompanyList = res.rows;
        }
      });
      getAllAccountSetsList().then((res) => {
        this.accountList = res;
      });
    },
    //担保公司输入框

    querySearchcust(queryString, cb) {
      var restaurants = this.custNameSelect;
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    //信息费公司
    querySearchfee(queryString, cb) {
      var restaurants = this.feenameList;
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    //汇款公司
    querySearchincome(queryString, cb) {
      var restaurants = this.incomeCustNameselect;
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (
          restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) !==
          -1
        );
      };
    },
    //取消
    quxiaoForm() {
      this.$router.push({ path: "/caiwu/returnAmount" });
    },

    //获取业务
    getuserList() {
      getuser().then((response) => {
        this.yewuList = response.yewu;
        this.kuaijiList = response.kuaiji;
        this.chunaList = response.chuna;
        this.alluserList = response.alluser;
      });
    },

    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    removeSchemeListDomain(schemeDomain, schemeIndex) {
      var index = this.schemeList.indexOf(schemeDomain);
      console.log("索引", index);
      if (index !== -1) {
        this.schemeList.splice(index, 1);
      }
      // this.checkAllRepeatSchemeDomain(schemeIndex);
    },
    removeDomainSchemeDomain(schemeIndex, item) {
      var index = this.schemeList[schemeIndex].cwProjectCusts.indexOf(item);
      if (index !== -1) {
        this.schemeList[schemeIndex].cwProjectCusts.splice(index, 1);
      }
      this.checkRepeatSchemeDomain(schemeIndex, index);
    },
    removeDomain(item) {
      // if (this.form.projectType === 0 || this.form.projectType === 2) {
      //   var index = this.form.cwProjectCusts.indexOf(item)
      //   if (index !== -1) {
      //     this.form.cwProjectCusts.splice(index, 1)
      //   }
      // } else if (this.form.projectType ==1) {
      var index = this.cwProjectLawCusts.indexOf(item);
      if (index !== -1) {
        this.cwProjectLawCusts.splice(index, 1);
      }
      this.checkRepeat(index);
      // }
    },
    addSchemeListDomain() {
      this.schemeList.push({
        cwProjectCusts: [
          {
            custName: "",
            rate: "0",
            taxRate: "0",
            schemeFlag: null,
          },
        ],
        //检查重复的名称标志 - 通道和分润
        checkRepeatFlag: false,
      });
    },
    addDomainSchemeDomain(schemeIndex) {
      this.schemeList[schemeIndex].cwProjectCusts.push({
        custName: "",
        rate: "0",
        taxRate: "0",
        schemeFlag: null,
        key: Date.now(),
      });
    },
    addDomain() {
      this.cwProjectLawCusts.push({
        custName: "",
        rate: "0",
        taxRate: "0",
        key: Date.now(),
      });
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectName: null,
        custName: null,
        incomeCustName: null,
        //动态表单数组
        cwProjectCusts: [
          {
            custName: "",
            rate: "0",
            taxRate: "0",
          },
        ],

        projectFlag: null,
        status: "0",
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加财务项目管理主";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getProject(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改财务项目管理主";
      });
    },

    /** 提交按钮 */
    submitForm() {
      console.log(this.form.projectType);
      if (this.form.generateCertificateFlag == 1 && !this.form.accountSetsId) {
        this.$message.warning("请选择凭证所属账套");
        return;
      }
      if (
        this.form.generateCertificateFlag == 1 &&
        this.form.guaranteeIncomeType == null
      ) {
        this.$message.warning("请选择担保费收入的类型");
        return;
      }
      if (this.form.generateCertificateFlag == 1 && !this.form.guarantyPayee) {
        this.$message.warning("请选择担保费收款方");
        return;
      }
      //进行信息费公司与费率的转换
      if (this.form.projectPortfolioCode != 'lawUrging') {
        this.form.cwProjectCusts = [];
        for (let i = 0; i < this.schemeList.length; i++) {
          //给信息费的方案标识
          for (let j = 0; j < this.schemeList[i].cwProjectCusts.length; j++) {
            this.schemeList[i].cwProjectCusts[j].schemeFlag = i + 1;
            var cwProjectCust = this.schemeList[i].cwProjectCusts[j];
            this.form.cwProjectCusts.push(cwProjectCust);
          }
        }
      } else {
        this.form.cwProjectCusts = this.cwProjectLawCusts;
      }
      var a = 0;
      var b = 0;
      console.log(this.form.cwProjectCusts, "---");
      if (this.form.projectPortfolioCode != 'lawUrging') {
        for (let i = 0; i < this.form.cwProjectCusts.length; i++) {
          if (
            this.form.cwProjectCusts[i].custName === null ||
            this.form.cwProjectCusts[i].custName === "" ||
            this.form.cwProjectCusts[i].rate === null ||
            this.form.cwProjectCusts[i].rate === "" ||
            this.form.cwProjectCusts[i].taxRate === null ||
            this.form.cwProjectCusts[i].taxRate === "" ||
            this.form.cwProjectCusts[i].schemeFlag === null ||
            this.form.cwProjectCusts[i].schemeFlag === ""
          ) {
            a++;
          }
        }
      } else if (this.form.projectPortfolioCode == 'lawUrging') {
        for (let i = 0; i < this.form.cwProjectCusts.length; i++) {
          if (
            this.form.cwProjectCusts[i].custName === null ||
            this.form.cwProjectCusts[i].custName === "" ||
            this.form.cwProjectCusts[i].rate === null ||
            this.form.cwProjectCusts[i].rate === ""
          ) {
            a++;
          }
        }
      }
      if (this.form.projectPortfolioCode != 'lawUrging') {
        if (
          this.form.projectName === null ||
          this.form.projectName === "" ||
          this.form.custName === null ||
          this.form.custName === "" ||
          this.form.incomeCustName === null ||
          this.form.incomeCustName === ""
        ) {
          this.$message.error("有必填项未输入，请检查！");
        } else {
          if (a > 0) {
            this.$message.error("有必填项未输入，请检查！");
          } else {
            //todo 修复费率或者税率大于1000
            if (this.form.projectPortfolioCode != 'lawUrging') {
              for (let i = 0; i < this.form.cwProjectCusts.length; i++) {
                if (
                  Number(this.form.cwProjectCusts[i].rate) > 999 ||
                  Number(this.form.cwProjectCusts[i].taxRate) > 999
                ) {
                  b++;
                }
              }
            } else if (this.form.projectPortfolioCode == 'lawUrging') {
              for (let i = 0; i < this.form.cwProjectCusts.length; i++) {
                if (Number(this.form.cwProjectCusts[i].rate) > 999) {
                  b++;
                }
              }
            }
            if (b > 0) {
              this.$message.error("税率或费率设置错误！");
            } else {
              var filter = this.schemeList.filter(
                (t) => t.checkRepeatFlag === true
              );
              if (filter.length !== 0) {
                this.$message.error(
                  "请检查信息费公司名称，信息费公司不允许出现多个相同的名称"
                );
              } else {
                addProjectshow(this.form).then((response) => {
                  this.$modal.msgSuccess("新增成功");

                  //普通项目完成后跳转到普通项目页面
                  if (this.form.projectPortfolioCode != 'lawUrging') {
                    this.$router.push({
                      path: "/caiwu/projectDetails",
                      query: { productId: response.id },
                    });
                  }
                  //法催项目完成后跳转到法催项目页面
                  if (this.form.projectPortfolioCode == 'lawUrging') {
                    this.$router.push({
                      path: "/caiwu/projectDetailsLaw",
                      query: { productId: response.id },
                    });
                  }
                });
              }
            }
          }
        }
      } else if (this.form.projectPortfolioCode == 'lawUrging') {
        console.log("99999999999999");
        if (
          this.form.projectName === null ||
          this.form.projectName === "" ||
          this.form.custName === null ||
          this.form.custName === "" ||
          this.form.incomeCustName === null ||
          this.form.incomeCustName === ""
        ) {
          this.$message.error("有必填项未输入，请检查！");
        } else {
          if (a > 0) {
            this.$message.error("有必填项未输入，请检查！");
          } else {
            console.log("88888888888888888");
            //todo 修复费率或者税率大于1000
            if (this.form.projectPortfolioCode != 'lawUrging') {
              for (let i = 0; i < this.form.cwProjectCusts.length; i++) {
                if (
                  Number(this.form.cwProjectCusts[i].rate) > 999 ||
                  Number(this.form.cwProjectCusts[i].taxRate) > 999
                ) {
                  b++;
                }
              }
            } else if (this.form.projectPortfolioCode == 'lawUrging') {
              for (let i = 0; i < this.form.cwProjectCusts.length; i++) {
                if (Number(this.form.cwProjectCusts[i].rate) > 999) {
                  b++;
                }
              }
            }
            if (b > 0) {
              this.$message.error("税率设置错误！");
            } else {
              if (this.checkRepeatFlagLaw === true) {
                this.$message.error(
                  "请检查信息费公司名称，信息费公司不允许出现多个相同的名称"
                );
              } else {
                addProjectshow(this.form).then((response) => {
                  this.$modal.msgSuccess("新增成功");

                  //普通项目完成后跳转到普通项目页面
                  if (this.form.projectPortfolioCode != 'lawUrging') {
                    this.$router.push({
                      path: "/caiwu/projectDetails",
                      query: { productId: response.id },
                    });
                  }
                  //法催项目完成后跳转到法催项目页面
                  if (this.form.projectPortfolioCode == 'lawUrging') {
                    this.$router.push({
                      path: "/caiwu/projectDetailsLaw",
                      query: { productId: response.id },
                    });
                  }
                });
              }
            }
          }
        }
      }
    },

    prestoreIncomeFlagChange(val) {
      if (val === 0) {
        this.form.prestoreIncomeFlag = 0;
      }
      if (val == 1) {
        this.form.prestoreIncomeFlag = 1;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.row_item {
  display: flex;
  align-items: center;
  .item_input {
    display: flex;
    align-items: center;
    margin-right: 150px;
    span {
      margin-right: 8px;
      display: inline-block;
      width: 70px;
      text-align: right;
    }
    i {
      color: red;
      margin-right: 5px;
    }
  }
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

input[type="number"] {
  -moz-appearance: textfield;
}
.el-row {
  margin-bottom: 20px;
}
.grid-content {
  /* border-radius: 10px;
    height: 50px;
    line-height: 14px; */
  color: #9d9d9d;
  /* font-weight:bold; */
  font-size: 14px;
  text-align: center;
  margin-left: 24px;
}
.grid-contentfont {
  color: #333333;
  /* font-weight:bold; */
  font-size: 14px;
}
.grid-contentcol {
  height: 20px;
  line-height: 40px;
  left: 30px;
}
.grid-col1 {
  border-radius: 4px;
  height: 36px;
}
.bg-purple {
  background: #9d9d9d;
}
#col-line {
  float: left;
  width: 1px;
  height: 60px;
  background: #e6e6e6;
}

.form2span1 {
  /* color:#ff8000; */
  font-weight: bold;
  /* margin-bottom: 8px; */
  font-family: "Microsoft YaHei";
  font-size: 14px;
}
.form2span21 {
  color: #999;
  /* font-weight:bold; */
  /*margin: 4px;*/
  font-family: "Microsoft YaHei";
  font-size: 12px;
}
.form2span31 {
  color: #999;
  /* font-weight:bold; */
  /* margin-top: 8px;*/
  /*margin-left: 80px;*/
  font-family: "Microsoft YaHei";
  font-size: 14px;
}
.spancol {
  color: #333333;
  font-weight: bold;
  font-size: 24px;
  display: inline-block;
  margin-left: 24px;
  /* padding-top:10px; */
}
.amounting {
  font-size: 14px;
  color: #9d9d9d;
  margin-left: 24px;
  margin-top: 10px;
  display: block;
}
.amounting2 {
  font-size: 14px;
  color: #9d9d9d;
  margin-left: 24px;
  /* margin-top:px; */
  display: block;
}
.spancol2 {
  font-size: 14px;
  color: #9d9d9d;
  margin-left: 24px;
  display: block;
}
.echartspan {
  color: #007fff;
  font-size: 12px;
  /* font-weight:bold; */
  /* margin-left: 60px; */
}
.balancediv {
  width: 100px;
  height: 35px;
}
.item {
  margin: 4px;
}
.divsecend {
  /* border-radius: px; */
  min-height: 100px;
  background-color: #ffffff;
}
.spanfont {
  color: #333333;
  font-size: 16px;
  font-weight: normal;
  font-family: "Microsoft YaHei";
  margin-left: 24px;
}

.inner {
  width: 49%;
  height: 12%;
  background: #ffffff;
  margin: 4px;
}
.innerone1 {
  width: 18.3%;
  height: 100px;
  background: #ffffff;
  margin: 4px;
}
.innerone2 {
  width: 50.3%;
  height: 100px;
  background: #ffffff;
  margin: 4px;
}
.innerone3 {
  width: 29.3%;
  height: 100px;
  background: #ffffff;
  margin: 4px;
}
.inneraaa {
  width: 24.3%;
  height: 100px;
  background: #ffffff;
  margin: 4px;
}
.dialogspan {
  font-size: 10px;
  color: #afadad;
  /* font-weight:bold; */
}
</style>
