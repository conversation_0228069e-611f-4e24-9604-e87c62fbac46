<template>
  <div style="padding: 20px 20px 0 20px">
    <div style="display: flex; height: 30px; align-items: center">
      <div style="margin-right: 10px">
        <el-switch v-model="horizontal" @change="horizontalChange"></el-switch>
        横向
      </div>
      <div style="margin-right: 10px">
        <el-switch v-model="expendCompany" @change="changeExpend"></el-switch>
        显示公司
      </div>
      <div style="margin-right: 10px">
        <el-switch v-model="expendDep" @change="changeExpend"></el-switch>
        显示部门
      </div>
      <div style="margin-right: 10px">
        <el-switch v-model="expendPost" @change="changeExpend"></el-switch>
        显示岗位
      </div>
      <div style="margin-right: 10px">
        <el-switch
          v-model="expendNumbers"
          @change="changeExpend"
        ></el-switch>
        显示管理人数
      </div>
      <div style="margin-right: 10px">
        <el-switch v-model="expandAll" @change="expandChange"></el-switch>
        全部展开
      </div>
      <div style="margin-right: 10px">
        <el-checkbox v-model="partTimeJob"></el-checkbox>
        兼岗
      </div>
    </div>
    <div style="padding: 10px 0px">
      <el-input
        type="text"
        style="width: 200px; margin-right: 10px"
        v-model.trim="keyword"
        placeholder="请输入搜索内容"
        clearable
        @keyup.enter.native="handleEnter"
        @input="handleEnterInput"
      />
      <span v-show="isActiveList.length"
        >{{ isActiveCurrent }}/{{ isActiveList.length }}</span
      >
      <el-button
        type="primary"
        size="small"
        @click="search"
        style="margin-left: 20px"
        >搜索</el-button
      >
      <el-button size="small" @click="resetSearch">重置</el-button>
    </div>

    <div
      style="height: 78vh; border: 1px solid #eee; position: relative"
      class="organizationalUser"
      :class="[isHorizontalOne ? 'isHorizontalOne' : '']"
    >
      <i
        @click="exportPic"
        class="el-icon-download"
        style="
          font-size: 24px;
          position: absolute;
          right: 20px;
          top: 2px;
          z-index: 100;
          cursor: pointer;
        "
      ></i>
      <zm-tree-org
        :key="treekKey"
        ref="organizationalUser"
        class="organizationalUser"
        :data="data"
        :horizontal="horizontal"
        :node-draggable="false"
        :only-one-node="false"
        :clone-node-drag="false"
        :define-menus="defineMenus"
        :default-expand-level="defaultLevel"
        @on-expand="onExpand"
        :toolBar="toolBar"
        disabled
        collapsable
      >
        <template v-slot="{ node }">
          <div
            :class="[
              'common',
              keyword && node.label && node.label.indexOf(keyword) != -1
                ? 'isActive'
                : '',
              'tree-org-node__text node-label',
            ]"
          >
            <div>
              <span
                :style="{
                  color:
                    keyword && node.label && node.label.indexOf(keyword) != -1
                      ? 'rgb(64, 158, 255)'
                      : 'black',
                }"
                >{{ node.label }}</span
              >
              <!-- <span v-show="node.status==1">( 已停用 )</span> -->
            </div>
            <div>
              <div v-show="expendCompany">公司: {{ node.companyName }}</div>
              <div v-show="expendDep">部门: {{ node.deptName }}</div>
              <div v-show="expendPost">岗位: {{ node.postName }}</div>
              <div v-show="partTimeJob && expendPost">
                <div v-for="(item, index) in node.postList" :key="index">
                  {{ item.postName }}(兼岗)
                </div>
              </div>
              <div v-show="expendNumbers">管理人数: {{ node.numbers }}</div>
            </div>
          </div>
        </template>
        <!-- <template v-slot:expand="{node}">
          <div>{{node.children.length}}</div>
        </template>  -->
      </zm-tree-org>
    </div>
  </div>
</template>
<script>
import XEUtils from "xe-utils";
import { exportPic } from "@/utils/exportPic";
export default {
  name: "OrganizationalUserCommon",
  props: {
    api: {
      type: Function,
      required: true,
    },
  },
  data() {
    return {
      treekKey: 1,
      toolBar: Object.freeze({
        scale: false,
        expand: false,
      }),
      expandAll: false,
      defaultLevel: 1,
      data: {},
      horizontal: true,
      expendCompany: false,
      expendDep: false,
      expendPost: false,
      partTimeJob: false,
      expendNumbers: false,
      keyword: "",
      isActiveList: [], //高亮数组
      isActiveCurrent: 0, //当前搜索的是第几个
      currentDom: null,
      isHorizontalOne: false, //是否是横向排列且仅有一个根节点
      isOnlyOne: true, //后台返回数据是否有根节点
    };
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      await this.getData();
    },
    async getData() {
      const { rows } = await this.api();
      this.isOnlyOne = rows.length == 1 ? true : false;
      const treeData = {
        id: 0,
        children: rows,
      };
      this.data = XEUtils.clone(treeData, true);
      // this.defaultLevel = this.getNodeDepth(treeData);
    },
    getNodeDepth(node) {
      let depth = 0;
      function calculateDepth(currentNode, currentDepth) {
        if (currentDepth > depth) {
          depth = currentDepth;
        }
        if (currentNode.children) {
          currentNode.children.forEach((child) => {
            calculateDepth(child, currentDepth + 1);
          });
        }
      }
      calculateDepth(node, 0);
      return depth;
    },
    defineMenus() {},
    search() {
      this.handleEnter();
    },
    resetSearch() {
      this.keyword = "";
      this.handleEnterInput();
    },
    handleEnter() {
      this.toggleExpand(this.data, true);
      this.$nextTick(() => {
        this.$refs.organizationalUser.scale = 1;
        const initX = 1040; //初始x偏移量
        const initY = 540; //初始y偏移量
        const isActiveList = document.querySelectorAll(
          ".organizationalUser .isActive"
        );
        if (isActiveList.length && !this.isActiveList.length) {
          isActiveList.forEach((item) => {
            const top =
              item.getBoundingClientRect().top < 0
                ? 0
                : item.getBoundingClientRect().top;
            const left =
              item.getBoundingClientRect().left < 0
                ? 0
                : item.getBoundingClientRect().left;
            this.isActiveList.push([left, top]);
          });
        }
        if (this.isActiveList.length) {
          if (!(this.isActiveList.length > this.isActiveCurrent))
            this.isActiveCurrent = 0;
          const x = -this.isActiveList[this.isActiveCurrent][0] + initX;
          const y = -this.isActiveList[this.isActiveCurrent][1] + initY;
          this.$refs.organizationalUser.onDrag(x, y);
          this.removeAll(isActiveList);
          this.setCurrent(isActiveList);
          this.isActiveCurrent++;
        }
      });
    },
    setCurrent(dom,num=0) {
      this.currentDom = dom[this.isActiveCurrent-num];
      this.currentDom.classList.add("isCurrenActive");
    },
    removeAll(dom) {
      dom.forEach((item) => {
        item.classList.remove("isCurrenActive");
      });
    },
    removeCurrent() {
      if (this.currentDom) {
        this.currentDom.classList.remove("isCurrenActive");
        this.currentDom = null;
      }
    },
    changeExpend() {
      this.treekKey++;
      this.$nextTick(() => {
        this.$refs.organizationalUser.scale = 1;
        const initX = 1040; //初始x偏移量
        const initY = 540; //初始y偏移量
        const isActiveList = document.querySelectorAll(
          ".organizationalUser .isActive"
        );
        this.isActiveList = [];
        if (isActiveList.length) {
          isActiveList.forEach((item) => {
            const top =
              item.getBoundingClientRect().top < 0
                ? 0
                : item.getBoundingClientRect().top;
            const left =
              item.getBoundingClientRect().left < 0
                ? 0
                : item.getBoundingClientRect().left;
            this.isActiveList.push([left, top]);
          });
        }
        if (this.isActiveList.length) {
          if (!(this.isActiveList.length > this.isActiveCurrent))
            this.isActiveCurrent = 0;
          const x = -this.isActiveList[this.isActiveCurrent][0] + initX;
          const y = -this.isActiveList[this.isActiveCurrent][1] + initY;
          this.$refs.organizationalUser.onDrag(x, y);
          this.removeAll(isActiveList);
          this.setCurrent(isActiveList,1);
        }
      });
    },

    horizontalChange() {
      if (!this.horizontal && this.isOnlyOne) {
        this.isHorizontalOne = true;
      } else {
        this.isHorizontalOne = false;
      }
      this.keyword = "";
      this.handleEnterInput();
    },
    handleEnterInput() {
      this.removeCurrent();
      this.isActiveList = [];
      this.isActiveCurrent = 0;
      this.$refs.organizationalUser.scale = 1;
      this.$refs.organizationalUser.onDrag(0, 0);
    },
    exportPic() {
      const dom = document.querySelector(".organizationalUser .tree-org-node");
      exportPic(dom, "人员组织架构");
    },
    onExpand(e) {
      if (e.target._prevClass?.indexOf("expanded") != -1) {
        this.$refs.organizationalUser.scale = 1;
        this.$refs.organizationalUser.onDrag(0, 0);
      }
    },
    expandChange() {
      this.toggleExpand(this.data, this.expandAll);
      this.$refs.organizationalUser.scale = 1;
      this.$refs.organizationalUser.onDrag(0, 0);
    },
    toggleExpand(data, val) {
      if (Array.isArray(data)) {
        data.forEach((item) => {
          this.$set(item, "expand", val);
          if (item.children) {
            this.toggleExpand(item.children, val);
          }
        });
      } else {
        this.$set(data, "expand", val);
        if (data.id == 0) this.$set(data, "expand", true);
        if (data.children) {
          this.toggleExpand(data.children, val);
        }
      }
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .is-empty {
  display: none;
}
::v-deep .is-empty + .tree-org-node__children::before {
  border: none;
}
::v-deep
  .is-empty
  + .tree-org-node__children
  > .tree-org-node:first-child:before {
  border: none;
}
::v-deep .is-empty + .tree-org-node__children::after {
  border-left: none;
}

::v-deep .isHorizontalOne {
  .is-empty {
    + .tree-org-node__children > .tree-org-node:first-child::after {
      border-left: none;
    }
  }
}
::v-deep .tree-org > .tree-org-node {
  border: 1px dashed rgba(0, 0, 0, 0.2);
  padding: 20px;
}
.common {
  color: rgba(156, 153, 153, 0.986);
  background: rgb(248, 247, 226);
  min-width: 280px;
  text-align: left;
}
.isActive {
  color: rgb(64, 158, 255);
  border: 1px solid #ccc;
  box-shadow: 0px 0px 5px #bbb;
}
.isCurrenActive {
  background: rgb(221, 194, 144) !important;
}
</style>