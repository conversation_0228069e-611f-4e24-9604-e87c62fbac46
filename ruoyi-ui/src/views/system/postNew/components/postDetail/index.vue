<template>
  <div class="py-5">
    <Views v-if="type == 'view'" :form="detailForm" :id="id"  @goEdit="goEdit"/>
    <AddUpdate v-else :form="detailForm" :type="type" :id="id"/>
  </div>
</template>

<script>
import { getPost } from "@/api/system/post";
import AddUpdate from "./components/addUpdate.vue";
import Views from "./components/view.vue";

export default {
  name: "PostDetail",
  components: { AddUpdate, Views },
  props: {
    id: {
      type: [String,Number],
      required: true,
    },
    type: {
      type: [String,Number],
      required: true,
    },
  },

  data() {
    return {
      detailForm: {},
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.getPost();
    },
    async getPost() {
      if (this.type != "add") {
        const { data } = await getPost(this.id);
        this.detailForm = data;
      }else{
        this.detailForm={};
      }
    },
    addUpdateCallBack(){
      this.$emit('addUpdateCallBack')
    },
    goEdit(){
      this.$emit('goEdit')
    }
  },
};
</script>
