<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="担保公司" prop="custName">
        <el-input
          v-model="queryParams.custName"
          placeholder="请输入担保公司"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="汇款公司" prop="incomeCustName">
        <el-input
          v-model="queryParams.incomeCustName"
          placeholder="请输入汇款公司"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目状态：0正常 1终止" prop="projectFlag">
        <el-input
          v-model="queryParams.projectFlag"
          placeholder="请输入项目状态：0正常 1终止"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

<!--&lt;!&ndash;数据统计他table&ndash;&gt;-->
<!--    <el-row  type="flex" >-->
<!--      <el-col :span="1"></el-col>-->

<!--      <el-col :span="5" >-->
<!--        <div class="grid-col1 ">-->
<!--          <span  class="spancol2">收入总计 <el-tooltip class="item" effect="dark" content="若合作方未在D+1日上送数据，会导致统计结果出现误差" placement="top">-->
<!--          <i class="el-icon-warning-outline"></i>-->
<!--        </el-tooltip></span>-->
<!--          <span class="spancol">111111</span>-->
<!--        </div>-->
<!--      </el-col>-->
<!--      <el-col :span="1"><div id="col-line"></div></el-col>-->
<!--      <el-col :span="5" >-->
<!--        <div class="grid-col1">-->
<!--          <span class="spancol2">信息费总计 <el-tooltip class="item" effect="dark" content="若合作方未在D+1日上送数据，会导致统计结果出现误差" placement="top">-->
<!--      <i class="el-icon-warning-outline"></i>-->
<!--        </el-tooltip></span>-->
<!--          <span class="spancol">{{this.totalCount}}</span>-->
<!--        </div>-->
<!--      </el-col>-->
<!--      <el-col :span="1"><div id="col-line"></div></el-col>-->
<!--      <el-col :span="5">-->
<!--        <div class="grid-col1  ">-->
<!--          <span class="spancol2">昨日新增贷款本金 <el-tooltip class="item" effect="dark" content="若合作方未在D+1日上送数据，会导致统计结果出现误差" placement="top">-->
<!--      <i class="el-icon-warning-outline"></i>-->
<!--        </el-tooltip></span>-->
<!--          <span class="spancol">{{this.addTotalAmount}}</span>-->
<!--        </div>-->
<!--      </el-col>-->
<!--      <el-col :span="1"><div id="col-line"></div></el-col>-->
<!--      <el-col :span="5" >-->
<!--        <div class="grid-col1  ">-->
<!--          <span class="spancol2">昨日新增贷款笔数 <el-tooltip class="item" effect="dark" content="若合作方未在D+1日上送数据，会导致统计结果出现误差" placement="top">-->
<!--      <i class="el-icon-warning-outline"></i>-->
<!--        </el-tooltip> </span>-->
<!--          <span class="spancol">{{this.addTotalCount}}</span>-->
<!--        </div>-->
<!--      </el-col>-->
<!--    </el-row >-->





    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:project:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:project:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:project:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:project:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="projectList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
<!--      <el-table-column label="主键" align="center" prop="id" />-->
      <el-table-column label="项目名称" align="center" prop="projectName"/>
      <el-table-column label="担保公司" align="center" prop="custName" />
      <el-table-column label="汇款公司" align="center" prop="incomeCustName" />
      <el-table-column label="收入" align="center" prop="incomeAmt" />
      <el-table-column label="信息费" align="center" prop="feeAmt" />
      <el-table-column label="提成信息费" align="center" prop="feeAmt2" />
      <el-table-column label="毛利" align="center" prop="grossProfitAmt" />
      <el-table-column label="提成毛利" align="center" prop="grossProfitAmt2" />
      <el-table-column label="信息费未结清" align="center" prop="unfeeAmt" />
      <el-table-column label="期次未完结" align="center" prop="phaseStatus" />
      <el-table-column label="用户标识" align="center" prop="userFlag" />
<!--      <el-table-column label="项目状态：0正常 1终止" align="center" prop="projectFlag" />-->
<!--      <el-table-column label="状态，0正常 1禁用" align="center" prop="status" />-->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:project:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:project:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改财务项目管理主对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="form.projectName" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="担保公司" prop="custName">
          <el-input v-model="form.custName" placeholder="请输入担保公司" />
        </el-form-item>
        <el-form-item label="汇款公司" prop="incomeCustName">
          <el-input v-model="form.incomeCustName" placeholder="请输入汇款公司" />
        </el-form-item>
<!--        <span>-->
<!--          <h3>信息费公司与费率</h3>-->
<!--          必须设置信息费公司名称，才能在录入信息费金额时选择该公司。若不设置费率或税率，则需要在录入金额时手工输入-->

<!--          信息费 = 收入 × 费率% × (1-税率%)-->

<!--          提成信息费= 收入 × 费率%-->
<!--        </span>-->
        <el-form-item label="公司名称" >
          <el-input  placeholder="请输入" />
        </el-form-item>



        <el-form-item label="项目状态：0正常 1终止" prop="projectFlag">
          <el-input v-model="form.projectFlag" placeholder="请输入项目状态：0正常 1终止" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {getProject, delProject, addProject, updateProject,listProject2 } from "@/api/system/project";

export default {
  // name: "Project",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 财务项目管理主表格数据
      projectList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectName: null,
        custName: null,
        incomeCustName: null,
        projectFlag: null,
        status: null,
        incomeAmt:null,
        phaseStatus:null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        projectName: [
          { required: true, message: "项目名称不能为空", trigger: "blur" }
        ],
        custName: [
          { required: true, message: "担保公司不能为空", trigger: "blur" }
        ],
        incomeCustName: [
          { required: true, message: "汇款公司不能为空", trigger: "blur" }
        ],
        projectFlag: [
          { required: true, message: "项目状态：0正常 1终止不能为空", trigger: "blur" }
        ],
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
        updateTime: [
          { required: true, message: "更新时间不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询财务项目管理主列表 */
    getList() {
      this.loading = true;
      listProject2(this.queryParams).then(response => {
        this.projectList = response.rows;
         this.total = response.total;
         this.loading = false;
      });
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectName: null,
        custName: null,
        incomeCustName: null,
        projectFlag: null,
        status: "0",
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        incomeAmt:null,
        phaseStatus:null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
       this.$router.push('/addProject');
      this.title = "添加财务项目管理主";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getProject(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改财务项目管理主";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateProject(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addProject(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除财务项目管理主编号为"' + ids + '"的数据项？').then(function() {
        return delProject(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/project/export', {
        ...this.queryParams
      }, `project_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
<style>

  .bg-purple-dark {
  background: #99a9bf;
}

  .bg-purple-light {
  background: #e5e9f2;
}
.grid-content {
  border-radius: 4px;
  min-height: 36px;
}
.row-bg {
  padding: 10px 0;
  background-color: #f9fafc;
}
</style>
<style>
  #col-line {
    float: left;
    width: 1px;
    height: 60px;
    background: 	#E6E6E6;
  }
</style>
