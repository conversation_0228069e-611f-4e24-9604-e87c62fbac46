<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      title="批量设置假期"
      :visible.sync="innerValue"
      width="550px"
      @open="handleOpen"
    >
      <div>
        <el-form ref="form" :model="form" label-width="130px" :rules="rules">
          <el-form-item label="年份" prop="year">
            <el-date-picker
              v-model="form.year"
              type="year"
              placeholder="选择年份"
              value-format="yyyy"
              @change="changeYear"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="休息日期" prop="date">
            <el-date-picker
              value-format="yyyy-MM-dd"
              type="dates"
              :default-value="defaultValue"
              v-model="form.date"
              placeholder="选择休息日期"
              @change="changeDay"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <div class="footer">
          <el-button type="primary" @click="onSubmit">确定</el-button>
          <el-button @click="innerValue = false">取消</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";
import { calendarList, addWarning } from "@/api/system/workdayManage";
import { clone } from "xe-utils";

export default {
  mixins: [vModelMixin],
  name: "DetailDialog",
  data() {
    return {
      form: {
        year: "",
        date: [],
      },
      rules: Object.freeze({
        year: [{ required: true, message: "年份不能为空", trigger: "change" }],
        date: [
          { required: true, message: "休息日期不能为空", trigger: "change" },
        ],
      }),
      defaultValue: [],
      params: [],
      initDay: [],
      initRow: [],
    };
  },
  mounted() {},
  methods: {
    handleOpen() {
      this.init();
    },
    async init() {
      this.form.year = "";
      this.form.date = [];
    },
    async changeYear() {
      const { rows } = await calendarList({ year: this.form.year });
      if (rows.length > 0) {
        this.initRow = clone(rows, true);
        this.form.date = rows
          .filter((item) => item.holiday == 1)
          .map((item) => item.date);
        this.defaultValue = [this.form.date[0]];
        this.initDay = clone(this.form.date, true);
      } else {
        this.form.year = new Date().getFullYear() + "";
        this.$message.error("该年度法定节假日暂未生成数据");
        this.changeYear();
      }
    },
    changeDay(value) {
      const arrFiltered = this.initDay.filter((item) => !value.includes(item));
      const arr1Filtered = value.filter((item) => !this.initDay.includes(item));
      // 合并过滤后的元素，生成新的对象数组
      const result = [
        ...arrFiltered.map((item) => item),
        ...arr1Filtered.map((item) => item),
      ];
      console.log(result);
      this.params = [];
      this.params = this.initRow
        .filter((item) => result.includes(item.date))
        .map((item) => {
          if (item.holiday == 1) {
            item.holiday = 2;
          } else if (item.holiday == 2) {
            item.holiday = 1;
          }

          return item;
        });
    },
    onSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          await addWarning(this.params);
          this.innerValue = false;
          this.$message({
            type: "success",
            message: "设置成功!",
          });
          this.$emit("on-submit");
        }
      });
    },
  },
};
</script>

