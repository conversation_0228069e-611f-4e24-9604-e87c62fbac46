<template>
  <div>
    <el-dialog
      :title="
        itemDetail && seeType
          ? '查看角色管理-范围'
          : itemDetail && !seeType
          ? '修改角色管理-范围'
          : '新增角色管理-范围'
      "
      :visible.sync="dialogVisible"
      width="850px"
      :before-close="handleClose"
    >
      <div class="flex">
        <span><i>*</i>角色名称</span>
        <el-input
          :disabled="seeType"
          placeholder="请输入角色名称"
          style="width: 220px"
          v-model="params.roleName"
        ></el-input>
      </div>
      <div class="flex">
        <span><i>*</i>权限字符</span>
        <el-input
          :disabled="seeType"
          placeholder="请输入角色字符"
          style="width: 220px"
          v-model="params.roleKey"
        ></el-input>
      </div>
      <div class="flex">
        <span><i>*</i>状态</span>
        <el-radio :disabled="seeType" v-model="params.status" label="0"
          >正常</el-radio
        >
        <el-radio :disabled="seeType" v-model="params.status" label="1"
          >停用</el-radio
        >
      </div>
      <div class="flex">
        <span><i>*</i>角色顺序</span>
        <el-input-number
          :disabled="seeType"
          controls-position="right"
          style="width: 220px"
          v-model="params.roleSort"
        ></el-input-number>
      </div>
      <p>已授权用户</p>
      <el-table :data="params.userList" :row-class-name="rowClassName">
        <el-table-column label="用户名称" prop="userName" />
        <el-table-column label="用户昵称" prop="nickName" />>
        <el-table-column label="状态" align="center" prop="status">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.sys_normal_disable"
              :value="scope.row.status"
            />
          </template>
        </el-table-column>
        <el-table-column label="公司">
          <template slot-scope="scope">
            <template
              v-for="item in userPostListAll.filter(
                (item) => item.userId === scope.row.userId
              )"
            >
             {{ item.unitShortName }} <br/>
            </template>
          </template>
        </el-table-column>

        <el-table-column label="部门">
          <template slot-scope="scope">
            <template
              v-for="item in userPostListAll.filter(
                (item) => item.userId === scope.row.userId
              )"
            >
              {{ item.deptName }} <br/>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="岗位">
          <template slot-scope="scope">
            <template
              v-for="item in userPostListAll.filter(
                (item) => item.userId === scope.row.userId
              )"
            >
              {{ item.postName }} <br/>
            </template>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          v-if="!seeType"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-circle-close"
              @click="deleteRow(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div style="margin: 12px 0">
        <el-button v-if="!seeType" type="primary" size="mini" @click="addPer"
          >+添加用户</el-button
        >
      </div>
      <div class="flex">
        <span><i>*</i>权限范围</span>
        <el-select
          :disabled="seeType"
          style="width: 220px"
          placeholder="请选择权限范围"
          v-model="params.dataScope"
        >
          <el-option
            v-for="item in roleList"
            :key="item.dictValue"
            :label="item.dictLabel"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </div>
      <div class="flex" style="display: flex" v-if="params.dataScope == 8">
        <span>范围勾选</span>
        <div
          style="
            display: flex;
            margin: 0px 0 20px 0px;
            width: 500px;
            padding: 16px;
            box-sizing: border-box;
            border: 1px solid #ccc;
          "
        >
          <el-tree
            :data="unitList"
            show-checkbox
            :disabled="seeType"
            node-key="unitId"
            :props="defaultProps2"
            ref="tree2"
            @check="handleCheckChange2"
          ></el-tree>
        </div>
      </div>

      <div class="flex" v-if="params.dataScope == 5">
        <span>范围勾选</span>
        <el-checkbox
          :disabled="seeType"
          @change="changeCheck($event, 0)"
          v-model="checked"
          >展开/折叠</el-checkbox
        ><el-checkbox
          :disabled="seeType"
          @change="changeCheck($event, 1)"
          v-model="checked2"
          >全选/全不选</el-checkbox
        ><el-checkbox
          :disabled="seeType"
          @change="changeCheck($event, 2)"
          v-model="params.menuCheckStrictly"
          >父子联动</el-checkbox
        >
      </div>
      <div
        v-if="params.dataScope == 5"
        style="
          display: flex;
          margin: 20px 0 20px 75px;
          width: 500px;
          padding: 16px;
          box-sizing: border-box;
          border: 1px solid #ccc;
        "
      >
        <el-tree
          :data="treeData"
          :disabled="seeType"
          show-checkbox
          node-key="id"
          :check-strictly="!params.menuCheckStrictly"
          :props="defaultProps"
          ref="tree"
          @check="handleCheckChange"
        ></el-tree>
      </div>
      <div class="flex" style="display: flex">
        <span>备注</span>
        <el-input
          :disabled="seeType"
          type="textarea"
          :rows="3"
          style="width: 400px"
          placeholder="请输入内容"
          v-model="params.remark"
        ></el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" v-if="!seeType" @click="submit"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      title="添加用户"
      :visible.sync="dialogVisible2"
      width="480px"
      :before-close="handleClose2"
    >
      <el-select
        style="width: 400px"
        clearable
        v-model="innerFormUserIds"
        placeholder="请选择添加的用户"
        filterable
        collapse-tags=""
        multiple
      >
        <el-option
          v-for="item in userListEnable"
          :key="item.userId"
          :label="item.nickName + '（账号：' + item.userName + '）'"
          :value="item.userId"
        >
        </el-option>
        <!-- +'；状态：'+(item.status==='0'?'正常':'禁用') -->
      </el-select>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose2">取 消</el-button>
        <el-button type="primary" @click="addPostUser">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  optionselect,
  userPostSetList,
  getUnit,
  treeselect,
} from "@/api/system/post";
import { getDicts } from "@/api/system/dict/data";
import { getUserListAll, getUserListEnable } from "@/api/system/user";
import { roleScope, editroleScope } from "@/api/oa/processConfig";

export default {
  props: {
    itemDetail: Object,
    seeType: Boolean,
  },
  dicts: ["sys_normal_disable"],
  components: {},
  data() {
    return {
      checked: false,
      checked2: false,
      defaultProps: {
        children: "children",
        label: "label",
      },
      defaultProps2: {
        children: "children",
        label: "unitName",
      },
      innerFormUserIds: [],
      dialogVisible2: false,
      PersonnelAuthorityType: false,
      dialogVisible: true,
      params: {
        roleName: "",
        roleKey: "",
        roleSort: 0,
        status: "0",
        unitIds: [],
        deptIds: [],
        menuCheckStrictly: true,
        dataScope: "",
        userList: [],
        remark: "",
      },
      userPostListAll: [],
      roleList: [],
      userListEnable: [],
      unitList: [],
      deptList: [],
      treeData: [],
    };
  },
  mounted() {
    if (this.itemDetail) {
      this.params = Object.assign(this.params, this.itemDetail);
      if (this.params.roleUnit) {
        this.params.unitIds = this.params.roleUnit.map((item) => item.unitId);
        this.$nextTick(()=>{
          this.$refs.tree2.setCheckedKeys(this.params.unitIds,true)
        })
      }
      if (this.params.roleDept) {
        this.params.deptIds = this.params.roleDept.map((item) => item.deptId);
        this.$nextTick(()=>{
          this.$refs.tree.setCheckedKeys(this.params.deptIds,true)
        })
      }
    }
    getUnit().then((res) => {
      if (res.code == 200) {
        this.unitList = res.rows;
      }
    });
    treeselect().then((res) => {
      if (res.code == 200) {
        this.treeData = res.data;
        this.deptList = this.dg(res.data, []);
        console.log(this.deptList);
      }
    });
    getDicts("role_scope").then((res) => {
      this.roleList = res.data;
    });
    this.getUserListEnableF();
    this.getUserPostSetListF();
  },
  methods: {
    changeCheck(value, type) {
      if (type == 0) {
        for (let i = 0; i < this.treeData.length; i++) {
          this.$refs.tree.store.nodesMap[this.treeData[i].id].expanded = value;
        }
      } else if (type == 1) {
        this.$refs.tree.setCheckedNodes(value ? this.treeData : []);
        this.params.deptIds = this.$refs.tree
          .getCheckedNodes()
          .map((item) => item.id);
      } else {
        this.params.menuCheckStrictly = value ? true : false;
      }
    },
    handleCheckChange2() {
      this.params.unitIds = this.$refs.tree2
        .getCheckedNodes()
        .map((item) => item.unitId);
    },
    handleCheckChange() {
      this.params.deptIds = this.$refs.tree
        .getCheckedNodes()
        .map((item) => item.id);
    },
    submit() {
      if (!this.params.roleName) {
        this.$message.warning("请输入角色名称");
        return;
      }
      if (!this.params.roleKey) {
        this.$message.warning("请输入角色字符");
        return;
      }
      if (!this.params.dataScope) {
        this.$message.warning("请选择权限范围");
        return;
      }
      // if (this.params.dataScope == 5) {
      //   this.params.deptIds = this.deptList.map((item) => item.id);
      // }
      // if (this.params.dataScope == 8) {
      //   this.params.unitList = this.unitList.map((item) => item.unitId);
      // }
      if (this.itemDetail) {
        let params = {
          roleName: this.params.roleName,
          roleId: this.params.roleId,
          roleKey: this.params.roleKey,
          roleSort: this.params.roleSort,
          status: this.params.status,
          unitIds: this.params.unitIds,
          deptIds: this.params.deptIds,
          userList: this.params.userList,
          dataScope: this.params.dataScope,
          remark: this.params.remark,
          menuCheckStrictly: true,
        };
        editroleScope(params).then((res) => {
          if (res.code == 200) {
            this.$message.success("操作成功");
            this.$emit("submit");
          }
        });
      } else {
        roleScope(this.params).then((res) => {
          if (res.code == 200) {
            this.$message.success("操作成功");
            this.$emit("submit");
          }
        });
      }
    },
    addPer() {
      this.dialogVisible2 = true;

      for (var itemUserE of this.params.userList) {
        this.userListEnable = this.userListEnable.filter(
          (item) => item.userId !== itemUserE.userId
        );
      }
    },
    addPostUser() {
      var item = {
        userId: "",
        userName: "",
        nickName: "",
        status: "",
      };
      for (var itemId of this.innerFormUserIds) {
        for (var itemUser of this.userListEnable) {
          if (itemId === itemUser.userId) {
            item = {
              userId: itemUser.userId,
              userName: itemUser.userName,
              nickName: itemUser.nickName,
              status: itemUser.status,
            };
            this.params.userList.push(item);
          }
        }
      }
      this.dialogVisible2 = false;
      this.innerFormUserIds = [];
      this.$modal.msgSuccess("点击确定后保存修改");
    },
    getUserListEnableF() {
      getUserListEnable().then((response) => {
        this.userListEnable = response.data;
      });
    },
    rowClassName({ row, rowIndex }) {
      row.xh = rowIndex + 1;
    },
    deleteRow(row) {
      this.params.userList.splice(row.xh - 1, 1);
      this.$modal.msgSuccess("点击确定后保存修改");
    },
    handleClose() {
      this.$emit("close");
    },
    getUserPostSetListF() {
      userPostSetList().then((response) => {
        this.userPostListAll = response.data;
      });
    },
    handleClose2() {
      this.dialogVisible2 = false;
    },
    dg(list, arr) {
      list.forEach((item) => {
        if (item.children && item.children.length > 0) {
          this.dg(item.children, arr);
        }
        arr.push(item);
      });
      return arr;
    },
  },
};
</script>

<style lang="less" scoped>
.flex {
  margin-bottom: 12px;
  span {
    margin-right: 9px;
    display: inline-block;
    width: 67px;
    text-align: right;
    i {
      color: red;
      margin-right: 5px;
    }
  }
}
</style>