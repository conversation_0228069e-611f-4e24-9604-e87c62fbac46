<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="innerValue"
      width="550px"
      title="导入"
      @close="handleClose"
      @open="handleOpen"
    >
      <div class="content">
        <el-upload
          ref="upload"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          :file-list="upload.fileList"
          :on-change="changeFileList"
          drag
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">
            将文件拖到此处，或
            <em>点击上传</em>
          </div>
          <div class="el-upload__tip" style="color: red" slot="tip">
            提示：仅允许导入“xlsx”或“xls”格式文件！
          </div>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <div class="footer">
          <el-button
            type="success"
            @click="importTemplate"
            v-show="url.downloadUrl"
            >下载模板</el-button
          >
          <el-button type="primary" @click="onSave">确定</el-button>
          <el-button @click="innerValue = false">取消</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";
import { getToken } from "@/utils/auth";

export default {
  mixins: [vModelMixin],
  name: "UploadImportant",
  props: {
    url: {
      type: Object,
      required: true,
      default: {
        importUrl: "",
        downloadUrl: "",
      },
    },
  },
  watch: {
    url: {
      handler(val) {
        if (val) {
          this.upload.url = process.env.VUE_APP_BASE_API + val.importUrl;
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + this.url.importUrl,
        fileList: [],
      },
    };
  },
  mounted() {},
  methods: {
    init() {
      this.handleOpen();
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.isUploading = false;
      if (response.code == "200") {
        this.$modal.msgSuccess(response.msg);
      } else {
        this.$modal.msgError(response.msg);
      }

      this.$refs.upload.clearFiles();
      this.innerValue = false;
      this.$emit("on-save-success",response);
    },
    onSave() {
      this.$refs.upload.submit();
    },
    changeFileList(file, fileList) {
      if (fileList.length > 0) {
        this.upload.fileList = [fileList[fileList.length - 1]];
      }
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download(
        this.url.downloadUrl,
        {},
        `模板_${new Date().getTime()}.xlsx`
      );
    },
    async handleOpen() {},
    handleClose() {},
  },
};
</script>
<style lang="less" scoped>
.content {
  max-height: 70vh;
  padding-right: 20px;
}
.footer {
  display: flex;
  justify-content: flex-end;
}
</style>