<template>
  <div>
    <div class="flex flex-wrap">
      <div
        v-for="(item, index) in myForm.companyBusinessTypeMappingList"
        :key="index"
        class="mr-3 mb-3 border border-solid rounded px-2 flex h-6 items-center relative top-2"
        style="
          border-color: #cccccc;
          background-color: #f2f2f2;
          font-size: 13px;
        "
      >
        <div class="h-6 leading-6">{{ item.dictLabel }}</div>
        <div  @click="deletType(item)" class="cursor-pointer">
          <i class="el-icon-close"></i>
        </div>
      </div>
      <el-button
        style="height: 30px"
        class="relative top-1"
        type="primary"
        size="mini"
        icon="el-icon-plus"
        @click="add"
        >添加</el-button
      >
    </div>
    <el-dialog
      title="添加产品分类"
      :visible.sync="dialogVisible"
      width="550px"
      @open="open"
      append-to-body	
    >
      <el-cascader
        v-model="value"
        :options="options"
        filterable
        :props="{
          expandTrigger: 'hover',
          children: 'children',
          label: 'dataName',
          value: 'id',
        }"
      ></el-cascader>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import XEUtils from "xe-utils";
import { getDataManageList } from "@/api/notice/management";
import { setEmptyArrayToUndefined, getNameById } from "@/utils";
import { projectNameRuleGetRule } from "@/api/businessInformation/specialProducts";
export default {
  name: "ProductCompanyClassification",
  props: {
    myForm: {
      type: Object,
      required: false,
      default: () => {}, 
    },
  },
  data() {
    return {
      options: [],
      value: [],
      dialogVisible: false,
    };
  },
 
  computed: {},
  mounted() {
    this.init();
  },
  methods: {
    async init() {
      this.getDataManageList();
    },
    add() {
      this.dialogVisible = true;
    },
    open() {
      this.handleOptions();
      this.value = [];
    },
    async getDataManageList() {
      const { rows } = await getDataManageList({
        firstDataCode: "business_type",
      });
      this.options = rows[0].fPiattaformas;
      setEmptyArrayToUndefined(this.options, "fPiattaformas");
      this.handleOptions();
    },
    addDisabled(options) {
      options.forEach((item) => {
        if (
          this.myForm.companyBusinessTypeMappingList
            ?.map((item1) => item1.dictCode)
            .includes(item.id)
        ) {
          this.$set(item, "disabled", true);
        } else {
          this.$set(item, "disabled", false);
        }
        
      });
    },
    handleOptions() {
      this.addDisabled(this.options);
      
    },

    deletType(value) {
      this.myForm.companyBusinessTypeMappingList = this.myForm.companyBusinessTypeMappingList.filter(
        (item) => item.dictLabel !== value.dictLabel
      );
    },
    async submit() {
      if (!this.value || !this.value.length) {
        this.$message.warning("请选择产品分类");
        return;
      }
      const dictLabel = getNameById(this.options, this.value[0]);
      const companyBusinessTypeMappingList = this.myForm.companyBusinessTypeMappingList
        ? this.myForm.companyBusinessTypeMappingList.concat([
            { dictLabel, dictCode: this.value[0] },
          ])
        : [{ dictLabel, dictCode: this.value[0] }];

      
      this.$set(this.myForm, "companyBusinessTypeMappingList", companyBusinessTypeMappingList);
      this.dialogVisible = false;
    },
  },
};
</script>


