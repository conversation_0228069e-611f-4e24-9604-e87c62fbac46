<template>
  <a-config-provider :locale="locale">
    <a-form>
      <a-form-item>
        <kCheckbox v-model="options.isEdit" label="是否可编辑" />
      </a-form-item>
      <a-form-item label="组件类型">
        <Radio :options="componentType" v-model="options.componentType" />
      </a-form-item>
      <a-form-item label="关联元素">
        <Select
          v-model="options.associatedElement"
          :options="options.associatedElements"
          :allowClear="options.clearable"
        />
      </a-form-item>
      <a-form-item label="类型">
        <Radio :options="types" v-model="options.selectType" />
      </a-form-item>
    </a-form>
  </a-config-provider>
</template>

<script>
import zhCN from "ant-design-vue/lib/locale-provider/zh_CN";
import {pluginManager} from "../../../packages";
import kCheckbox from "../../../packages/components/KCheckbox/index.vue";

const Select = pluginManager.getComponent("select").component;
const Radio = pluginManager.getComponent("radio").component;

export default {
  name: "payee",
  props: ["options"],
  components: { Select, Radio, kCheckbox},
  created() {

  },
  data() {
    return {
      locale: zhCN,
      showRange: false,
      showInitValue: false,
      //范围字典
      rangeDict: [],
      initDict: [],
      // 部门树选项
      deptTrees: undefined,
      companyMap: {},
      deptMap: {},
      postMap: {},
      userMap: {},
      selectedRange: undefined,
      selectedInitValue: undefined,
      componentType: [
        {value: "0", label: "收款人"},
        {value: "1", label: "付款人"},
      ],
      types: [
        {value: "0", label: "名称"},
        {value: "1", label: "账号"},
        {value: "2", label: "开户行"}
      ]
    };
  },
  methods: {

  }
};
</script>
