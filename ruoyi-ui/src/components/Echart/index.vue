<template>
  <div :id="echartsId" style="width: 100%; height: 100%"></div>
</template>

<script>
import * as echarts from "echarts";
import { echartsToo, echartsLegend, echartsGrid } from "@/utils/echarts";

export default {
  name: "MyEchart",
  components: {},
  props: {
    opationDate: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      chart: null,
      echartsId: `echartsId${Math.random().toFixed(8)}`,
    };
  },
  watch: {
    opationDate(value) {
      /** 只允许渲染一次 */
      if (value) this.initChart(this.opationDate);
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart(this.opationDate);
    });
  },
  methods: {
    initChart(option) {
      const { tooltip, legend, grid } = { ...option };
      this.$set(
        this.opationDate,
        "tooltip",
        tooltip ? Object.assign(tooltip, echartsToo) : echartsToo
      );
      this.$set(
        this.opationDate,
        "legend",
        legend ? Object.assign(legend, echartsLegend) : echartsLegend
      );
      this.$set(
        this.opationDate,
        "grid",
        grid ? Object.assign(grid, echartsGrid) : echartsGrid
      );
      this.chart = echarts.getInstanceByDom(
        document.getElementById(this.echartsId)
      );
      if (this.chart == null) {
        this.chart = echarts.init(document.getElementById(this.echartsId));
      }
      this.chart.setOption(option, true);
      this.chart.resize();
      window.addEventListener("resize", () => {
        setTimeout(() => {
          this.chart.resize();
        }, 100);
      });
    },
  },
};
</script>
