import Layout from '@/layout'

export default [
  {
    path: '/formcenter/list',
    component: Layout,
    hidden: true,
    permissions: ['nocode:form:list'],
    children: [
      {
        path: 'modifyForm',
        component: () => import('@/views/formcenter/list/modifyForm'),
        name: 'modifyForm',
        meta: { title: '修改表单', activeMenu: '/formcenter/list' }
      }
    ]
  },
  {
    path: '/table',
    component: Layout,
    hidden: true,
    permissions: ['nocode:form:list'],
    children: [
      {
        path: 'data/:formId',
        component: (resolve) => require(['@/views/datacenter/table/data'], resolve),
        name: 'TableData',
        meta: { title: '数据查看' }
      },
      {
        path: 'config/:formId',
        component: (resolve) => require(['@/views/datacenter/table/config'], resolve),
        name: 'TableConfig',
        meta: { title: '属性配置' }
      },
    ]
  },
  {
    path: '/formcenter/design',
    component: Layout,
    hidden: true,
    permissions: ['nocode:form:list'],
    children: [
      {
        path: 'addForm',
        component: () => import('@/views/formcenter/design/index'),
        name: 'addForm',
        meta: { title: '新增表单', activeMenu: '/formcenter/design' }
      }
    ]
  },
]

