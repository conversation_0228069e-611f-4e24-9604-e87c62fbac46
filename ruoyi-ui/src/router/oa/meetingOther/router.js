import Layout from "@/layout";

export default [
  {
    path: "/meetingOther",
    name: "MeetingOther",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "sponsor",
        component: () => import("@/views/meeting/staging/components/sponsor"),
        name: "Sponsor",
        meta: { title: '发起会议', activeMenu: "/meeting/staging",noCache: true},
      },
      {
        path: "meetingDetails/:id",
        component: () => import("@/views/meeting/notice/components/meetingDetails"),
        name: "MeetingDetails",
        meta: { title: '会议详情', activeMenu: "/meeting/staging" },
      },
      {
        path: "managementAdd",
        component: () => import("@/views/meeting/management/components/add"),
        name: "managementAdd",
        meta: { title: '新增会议室', activeMenu: "/meeting/management",noCache: true },
      },
      {
        path: "managementUpdata/:id",
        component: () => import("@/views/meeting/management/components/updata"),
        name: "ManagementUpdata",
        meta: { title: '修改会议室', activeMenu: "/meeting/management",noCache: true },
      },
      {
        path: "managementView/:id",
        component: () => import("@/views/meeting/management/components/view"),
        name: "ManagementView",
        meta: { title: '会议室详情', activeMenu: "/meeting/management" },
      },
      {
        path: "NoticeReceipt/:id",
        component: () => import("@/views/meeting/notice/components/receipt"),
        name: "NoticeReceipt",
        meta: { title: '填写会议回执', activeMenu: "/meeting/notice" },
      },
    ],
  },
];
