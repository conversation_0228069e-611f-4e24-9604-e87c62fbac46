import request from '@/utils/request'

// 查询通知待办信息列表
export function listNotify(query) {
  return request({
    url: '/system/notify/list',
    method: 'get',
    params: query
  })
}

// 查询通知待办信息列表
export function listaccomplishNotify(query) {
  return request({
    url: '/system/notify/accomplishList',
    method: 'get',
    params: query
  })
}
export function updateNotifyStatus(params) {
  return request({
    url: '/system/notify/updateNotifyStatus',
    method: 'get',
    params
  })
}
// 查询通知待办信息详细
export function getNotify(id) {
  return request({
    url: '/system/notify/' + id,
    method: 'get'
  })
}

// 新增通知待办信息
export function addNotify(data) {
  return request({
    url: '/system/notify',
    method: 'post',
    data: data
  })
}
export function getNotifyByProcessId(data) {
  return request({
    url: '/system/notify/getNotifyByProcessId',
    method: 'post',
    data: data
  })
}

// 修改通知待办信息
export function updateNotify(data) {
  return request({
    url: '/system/notify',
    method: 'put',
    data: data
  })
}

// 删除通知待办信息
export function delNotify(id) {
  return request({
    url: '/system/notify/' + id,
    method: 'delete'
  })
}
