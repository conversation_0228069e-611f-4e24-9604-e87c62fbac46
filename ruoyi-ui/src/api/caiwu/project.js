import request from '@/utils/request'
//获取账套
export function getAccountSetsList(id) {
  return request({
    url: '/account-sets/getAccountSetsList/' + id,
    method: 'get'
  })
}
export function getProjectCertificateFlagByProjectId(query) {
  return request({
    url: '/cwxmgl/cust/getProjectCertificateFlagByProjectId',
    method: 'get',
    params: query
  })
}
export function getSelectProjectTypeByOaProjectDeployId(query) {
  return request({
    url: '/cwxmgl/cust/getSelectProjectTypeByOaProjectDeployId',
    method: 'get',
    params: query
  })
}
export function getProjectRoleByOaProjectDeployId(query) {
  return request({
    url: '/cwxmgl/cust/getProjectRoleByOaProjectDeployId',
    method: 'get',
    params: query
  })
}
export function listTrader(query) {
  return request({
    url: '/oasystem/trader/list',
    method: 'get',
    params: query
  })
}
export function getProjectSumByProjectId(query) {
  return request({
    url: '/cwxmgl/cust/getProjectSumByProjectId/',
    method: 'get',
    params: query
  })
}
export function getLawProjectSumByProjectId(query) {
  return request({
    url: '/cwxmgl/cust/getLawProjectSumByProjectId',
    method: 'get',
    params: query
  })
}
export function getFilterOaProjectDeployList(query) {
  return request({
    url: '/cwxmgl/cust/getFilterOaProjectDeployList',
    method: 'get',
    params: query
  })
}
export function listDeploy(query) {
  return request({
    url: '/oasystem/deploy/list',
    method: 'get',
    params: query
  })
}
//查询项目详情三个字段


export function getDeatil(query) {
  return request({
    url: '/cwxmgl/cust/list/project/detail/three',
    method: 'get',
    params: query
  })
}

// 查询财务项目管理主列表
export function listProject(query) {
  return request({
    url: '/system/project/list',
    method: 'get',
    params: query
  })
}
// 查询财务项目管理主列表
export function projectShow(query) {
  return request({
    url: '/cwxmgl/cust/list/overs',
    method: 'get',
    params: query
  })
}

// 下拉框
export function projectShowxiala() {
  return request({
    url: '/system/projectShow/getCustNameXiaLa',
    method: 'get'

  })
}

// 下拉框 - 法催项目
export function projectShowLawxiala() {
  return request({
    url: '/system/projectShow/getXiaLaForLaw',
    method: 'get'

  })
}
//获取项目信息

export function getUpdatepeoject(query) {
  return request({
    url: '/cwxmgl/cust/list/project/detail',
    method: 'get',
    params: query
  })
}
//修改项目信息

// 新增财务项目管理主
export function updateProjectshow(data) {
  return request({
    url: '/system/projectShow/updatePro',
    method: 'post',
    data: data
  })
}
export function updateFeeCompany(data) {
  return request({
    url: '/cwxmgl/cust/updateFeeCompany',
    method: 'post',
    data: data
  })
}
//查询项目所属业务人员idlist


export function getyeuwList(query) {
  return request({
    url: '/system/projectShow/getyewuidList',
    method: 'get',
    params: query
  })
}
export function getUserRoleByProjectId(query) {
  return request({
    url: '/cwxmgl/cust/getUserRoleByProjectId',
    method: 'get',
    params: query
  })
}
//用户列表下拉框
export function getuser() {
  return request({
    url: '/system/projectShow/userList',
    method: 'get'

  })
}
//新增业务期次
export function addyewuqici(data){
  return request({
    url: '/system/projectShow/addyewuqici',
    method: 'post',
    data: data
  })
}

//新增业务期次 - 法催项目
export function addyewuqiciForLaw(data){
  return request({
    url: '/system/projectShow/addyewuqiciForLaw',
    method: 'post',
    data: data
  })
}

//新增收入 - 法催项目
export function addIncomeForLaw(data){
  return request({
    url: '/system/projectShow/addIncomeForLaw',
    method: 'post',
    data: data
  })
}
//修改收入（实际上是删除） - 法催项目
export function changePhase(data){
  return request({
    url: '/system/projectShow/changePhase',
    method: 'post',
    data: data
  })
}
//修改
export function updataYewuqici(data){
  return request({
    url: '/system/projectShow/updateyewuqici',
    method: 'post',
    data: data
  })
}
//提交录入
export function lrsrsubmit(data){
  return request({
    url: '/system/projectShow/addshouru',
    method: 'post',
    data: data
  })
}

//修改驳回后的收入金额
export function updateIncomeSubmit(data){
  return request({
    url: '/system/projectShow/updateIncomeSubmit',
    method: 'post',
    data: data
  })
}
//打款新增
export function submitdakuanData(data){
  return request({
    url: '/system/projectShow/addPay',
    method: 'post',
    data: data
  })
}
//打款新增 - 法催项目
export function submitLawdakuanData(data){
  return request({
    url: '/system/projectShow/addPayForLaw',
    method: 'post',
    data: data
  })
}
// 期次详情
export function getProjectqicideteils(query) {
  return request({
    url: '/cwxmgl/cust/list/conduct',
    method: 'get',
    params: query
  })
}
export function createVoucher(query) {
  return request({
    url: '/system/projectShow/createVoucher',
    method: 'get',
    params: query
  })
}
// 法催项目 - 查看是否有正在进行的期次
export function getLawProjectqicideteils(query) {
  return request({
    url: '/cwxmgl/cust/list/conductLaw',
    method: 'get',
    params: query
  })
}
//期次详情列表
export function getyewuxiangqing(query) {
  return request({
    url: '/cwxmgl/cust/list/conduct/detail',
    method: 'get',
    params: query
  })
}
//期次详情列表 - 法催项目
export function getyewuxiangqingLaw(query) {
  return request({
    url: '/cwxmgl/cust/list/conduct/lawDetail',
    method: 'get',
    params: query
  })
}

export function getupdateyewuqiciByid(query) {
  return request({
    url: '/cwxmgl/cust/list/conduct/change',
    method: 'get',
    params: query
  })
}
//获取打款信息

export function getdakuanTable(query) {
  return request({
    url: '/cwxmgl/cust/list/pay/detail',
    method: 'post',
    data: query
  })
}
//获取打款信息 - 法催项目
export function getdakuanTableForLaw(query) {
  return request({
    url: '/cwxmgl/cust/list/pay/lawDetail',
    method: 'post',
    data: query
  })
}
//获取代办人集合
export function getUserList(query) {
  return request({
    url: '/system/projectShow/getUserList',
    method: 'get',
    params: query
  })
}
// 详情信息
export function getProjectdeteils(query) {
  return request({
    url: '/cwxmgl/cust/list/over/detail',
    method: 'get',
    params: query
  })
}

// 详情信息 - 法催项目
export function getLawProjectdeteils(query) {
  return request({
    url: '/cwxmgl/cust/list/over/lawDetail',
    method: 'get',
    params: query
  })
}
//确认信息费
export function querenFee(query) {
  return request({
    url: '/system/projectShow/querenFee',
    method: 'get',
    params: query
  })
}

//驳回信息费
export function rejectionfanfei(data) {
  return request({
    url: '/system/projectShow/rejectionFee',
    method: 'post',
    data: data
  })
}

//确认收入与信息费 - 法催项目
export function querenIncomeAndFeeForLaw(query) {
  return request({
    url: '/system/projectShow/querenIncomeAndFeeForLaw',
    method: 'get',
    params: query
  })
}
//录入信息费
export function lurufanfeiadd(query) {
  return request({
    url: 'system/projectShow/insertfee',
    method: 'post',
    data: query
  })
}
//修改信息费

export function updatefanfei(query) {
  return request({
    url: 'system/projectShow/updatefee',
    method: 'post',
    data: query
  })
}

//驳回信息费后 - 修改信息费
export function updateFeeSubmit(data) {
  return request({
    url: 'system/projectShow/updateFeeSubmit',
    method: 'post',
    data: data
  })
}
//得到信息费详情
export function getfanfeidateil(query) {
  return request({
    url: '/cwxmgl/cust/list/fee/detail',
    method: 'get',
    params: query
  })
}
//修改业务负责人
export function updateyewufuze(data){
  return request({
    url: '/system/projectShow/updateYeWu',
    method: 'post',
    data: data
  })
}

// 新增财务项目管理主
export function addProjectshow(data) {
  return request({
    url: '/system/projectShow',
    method: 'post',
    data: data
  })
}

//确认打款

export function querendakuan(query) {
  return request({
    url: '/system/projectShow/querendakuan',
    method: 'get',
    params: query
  })
}
//确认打款 - 法催项目

export function querendakuanForLaw(query) {
  return request({
    url: '/system/projectShow/querendakuanForLaw',
    method: 'get',
    params: query
  })
}
// 确认录入收入
export function submitshouru(query) {
  return request({
    url: '/system/projectShow/querenluru',
    method: 'get',
    params: query
  })
}

// 驳回收入
export function rejectionshouru(data) {
  return request({
    url: '/system/projectShow/rejectionIncome',
    method: 'post',
    data: data
  })
}

export function closeProject (projectId){
  return request({
    url: '/system/projectShow/closeProject/' + projectId,
    method: 'get'
  })
}

// 新增财务项目管理主
export function addProject(data) {
  return request({
    url: '/system/project',
    method: 'post',
    data: data
  })
}

// 修改财务项目管理主
export function updateProject(data) {
  return request({
    url: '/system/project',
    method: 'put',
    data: data
  })
}
// 撤回替换信息费公司
export function revocationReplaceFeeCompanyInfo(data) {
  return request({
    url: '/cwxmgl/cust/revocationReplaceFeeCompanyInfo',
    method: 'put',
    data: data
  })
}
// 删除财务项目管理主
export function delProject(id) {
  return request({
    url: '/system/project/' + id,
    method: 'delete'
  })
}

// export function beachExportFile(){
//   return request({
//     url: '/system/projectShow/over/detail/allExport',
//     method:'post',
//   })
// }
//新修改的批量导出逻辑
export function beachExportFile(){
  return request({
    url: '/system/projectShow/over/detail/allExportNew',
    method:'post',
  })
}

//判断当前登录人是否有导出权限
export function getloginexport(query) {
  return request({
    url: '/system/projectShow/checkExportRole',
    method: 'get',
    params: query
  })
}


//判断当前登录人可以导出的项目数量是否时0
export function getloginexportNum() {
  return request({
    url: '/system/projectShow/checkExportProNum',
    method: 'get',
  })
}
//删除期次信息
export function deletetremData(data) {
  return request({
    url: '/system/projectShow/deleteincome',
    method: 'post',
    data: data
  })
}

//删除期次信息 - 法催项目
export function deleteincomeForLaw(query) {
  return request({
    url: '/system/projectShow/deleteincomeForLaw',
    method: 'get',
    params: query
  })
}
export function getProjectFeeCompanyInfoByProjectId(query) {
  return request({
    url: '/cwxmgl/cust/getProjectFeeCompanyInfoByProjectId',
    method: 'get',
    params: query
  })
}
//查找最近一个月的出信息费公司和信息费公司findRecentlyCustFeeCompanyAndFeeCompany
export function findLawRecentlyCustFeeCompanyAndFeeCompany(query) {
  return request({
    url: '/cwxmgl/cust/findLawRecentlyCustFeeCompanyAndFeeCompany',
    method: 'get',
    params: query
  })
}
export function getFlowAlreadyPaylnfoFromOA(query) {
  return request({
    url: '/cwxmgl/cust/getFlowAlreadyPayInfoFromOA',
    method: 'get',
    params: query
  })
}
//查询挂起的信息费合 - 法催项目
export function findLawSuspendFlagIsOneSum(data) {
  return request({
    url: '/cwxmgl/cust/findLawSuspendFlagIsOneSum',
    method: 'post',
    data: data
  })
}
export function addNewReplaceFeeCompanyInfo(data) {
  return request({
    url: '/cwxmgl/cust/addNewReplaceFeeCompanyInfo',
    method: 'post',
    data: data
  })
}
//普通项目 - 使用收入表id查对应的驳回信息
export function getRejectionDetails(query) {
  return request({
    url: '/cwxmgl/cust/getRejectionDetails',
    method: 'get',
    params: query
  })
}

//普通项目 - 查询预存收入表 - 所有，预存和抵扣
export function getPrestoreIncomeDetails(query) {
  return request({
    url: '/cwxmgl/cust/prestoreIncomeList',
    method: 'get',
    params: query
  })
}

//普通项目 - 查询预存收入表 - 只查预存
export function getPrestoreIncomeDetailsForIn(query) {
  return request({
    url: '/cwxmgl/cust/prestoreIncomeListForIn',
    method: 'get',
    params: query
  })
}

//普通项目 - 新增/修改预存收入表
export function addPrestoreIncomeList(data) {
  return request({
    url: '/cwxmgl/cust/insertPrestoreIncomeList',
    method: 'post',
    data: data
  })
}

// 下拉框 - 提成基数待确认
export function projectShowxialaForAck() {
  return request({
    url: '/cwxmgl/ack/selectList',
    method: 'get'

  })
}
