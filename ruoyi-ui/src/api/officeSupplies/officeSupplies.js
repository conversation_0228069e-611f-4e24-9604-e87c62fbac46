
import request from "@/utils/request";
export function categoryTree(params) {
    return request({
        url: '/offCategory/categoryMain/categoryTree',
        method: 'get',
        params
    })
}
export function getAuthCompany(params) {
    return request({
        url: '/offSupplyMain/supplyMain/getAuthCompany',
        method: 'get',
        params
    })
}
export function categoryMainDetail(id) {
    return request({
        url: '/offCategory/categoryMain/' + id,
        method: 'get',

    })
}
export function supplyMainDetail(id) {
    return request({
        url: '/offSupplyMain/supplyMain/' + id,
        method: 'get',

    })
}
export function supplyPurchaseDetail(id) {
    return request({
        url: '/offSupplyPurchase/supplyPurchase/' + id,
        method: 'get',

    })
}
export function supplyPurchaseEdit(data) {
    return request({
        url: '/offSupplyPurchase/supplyPurchase',
        method: 'put',
        data
    })
}
export function changeStatusCate(data) {
    return request({
        url: '/offCategory/categoryMain/changeStatus',
        method: 'put',
        data
    })
}
export function changeStatusSupply(data) {
    return request({
        url: '/offSupplyMain/supplyMain/changeStatus ',
        method: 'put',
        data
    })
}
export function supplyMainDel(id) {
    return request({
        url: '/offSupplyMain/supplyMain/' + id,
        method: 'delete',


    })
}
export function supplyPurchaseDel(id) {
    return request({
        url: '/offSupplyPurchase/supplyPurchase/' + id,
        method: 'delete',


    })
}

export function receiveMainDel(id) {
    return request({
        url: '/offReceiveMain/receiveMain/' + id,
        method: 'delete',


    })
}
export function list(data) {
    return request({
        url: '/offCategory/categoryMain/list',
        method: 'post',
        data
    })
}

export function receiveMainAdd(data) {
    return request({
        url: '/offReceiveMain/receiveMain',
        method: 'post',
        data
    })
}
export function receiveMainLpAdd(data) {
    return request({
        url: '/offReceiveMain/receiveMain/lpAdd',
        method: 'post',
        data
    })
}
export function supplyReceiveReport(data) {
    return request({
        url: '/offReceiveMain/receiveMain/supplyReceiveReport',
        method: 'post',
        data
    })
}

export function supplyPurchaseList(data) {
    return request({
        url: '/offSupplyPurchase/supplyPurchase/list',
        method: 'post',
        data
    })
}
export function supplyPurchase(data) {
    return request({
        url: '/offSupplyPurchase/supplyPurchase',
        method: 'post',
        data
    })
}
export function receiveMainList(data) {
    return request({
        url: '/offReceiveMain/receiveMain/list',
        method: 'post',
        data
    })
}
export function supplyMainList(data) {
    return request({
        url: '/offSupplyMain/supplyMain/list',
        method: 'post',
        data
    })
}
export function supplySettle(data) {
    return request({
        url: '/offSupplyMain/supplyMain/supplySettle',
        method: 'post',
        data
    })
}
export function updateProcessStatus(data) {
    return request({
        url: '/offReceiveMain/receiveMain/updateProcessStatus',
        method: 'post',
        data
    })
}
export function queryJudge(id) {
    return request({
        url: '/offCategory/categoryMain/queryJudge/'+id,
        method: 'get',
        
    })
}

export function updateInfoPur(data) {
    return request({
        url: '/offSupplyPurchase/supplyPurchase/updateInfo',
        method: 'post',
        data
    })
}
export function getSupplyFlow(params) {
    return request({
        url: '/informationFlowController/getSupplyFlow',
        method: 'get',
        params
    })
}
export function getGiftFlow(params) {
    return request({
        url: '/informationFlowController/getGiftFlow',
        method: 'get',
        params
    })
}
export function getPurchaseFlow(params) {
    return request({
        url: '/informationFlowController/getPurchaseFlow',
        method: 'get',
        params
    })
}
export function viewRecord(id) {
    return request({
        url: '/offSupplyMain/supplyMain/viewRecord/' + id,
        method: 'get',

    })
}
 
export function receiveMainDetail(id) {
    return request({
        url: '/offReceiveMain/receiveMain/' + id,
        method: 'get',

    })
}
export function getReceiveByProcessId(id) {
    return request({
        url: '/offReceiveMain/receiveMain/getReceiveByProcessId/' + id,
        method: 'get',

    })
}
export function receiveMainEdit(data) {
    return request({
        url: '/offReceiveMain/receiveMain',
        method: 'put',
        data
    })
}
export function supplyMainAdd(data) {
    return request({
        url: '/offSupplyMain/supplyMain',
        method: 'post',
        data
    })
}
export function supplyMainEdit(data) {
    return request({
        url: '/offSupplyMain/supplyMain/update',
        method: 'post',
        data
    })
}
export function categoryMainAdd(data) {
    return request({
        url: '/offCategory/categoryMain',
        method: 'post',
        data
    })
}
export function categoryMainEdit(data) {
    return request({
        url: '/offCategory/categoryMain',
        method: 'put',
        data
    })
}
export function categoryMainDel(id) {
    return request({
        url: '/offCategory/categoryMain/' + id,
        method: 'delete',

    })
}