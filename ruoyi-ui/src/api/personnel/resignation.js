import request from "@/utils/request";

export function getResignation(params) {
  return request({
    url: "/personnel/resignation/list",
    method: "get",
    params,
  });
}

export function delResignation(configId) {
  return request({
    url: '/personnel/resignation/' + configId,
    method: 'delete'
  })
}
export function addResignation(data) {
  return request({
    url: "/personnel/resignation",
    method: "post",
    data,
  });
}
export function updateResignation(data) {
  return request({
    url: "/personnel/resignation",
    method: "put",
    data,
  });
}
export function getPersonnelResignation(params) {
  return request({
    url: "/personnelflow/getPersonnelResignationFlow",
    method: "get",
    params,
  });
}
export function commitResignation(data) {
  return request({
    url: "/personnel/resignation/commitResignation",
    method: "post",
    data,
  });
}
export function personnelResignation(configId) {
  return request({
    url: "/personnel/resignation/"+configId,
    method: "get",
  });
}
export function passResignation(data) {
  return request({
    url: "/personnel/resignation/passResignation",
    method: "post",
    data,
  });
}
export function unpassResignation(data) {
  return request({
    url: "/personnel/resignation/unpassResignation",
    method: "post",
    data,
  });
}
export function listForResignation(params) {
  return request({
    url: "/personnel/archives/listForResignation",
    method: "get",
    params,
  });
}
export function getHandover(query) {
  return request({
      url: '/system/user/listForPersonnel',
      method: 'get',
      params: query
  })
}
