import request from '@/utils/request'

// 查询收入预测报配置列表
export function listConfiguration() {
  return request({
    url: '/yybbsc/configuration/list',
    method: 'get'
  })
}

// 用参数码 查询具体的某一个参数列表
export function configurationListByParameterCode(query) {
  return request({
    url: '/yybbsc/configuration/configurationListByParameterCode',
    method: 'get',
    params: query
  })
}

// 校验要修改的配置日期是否符合逻辑
export function checkConfiguration(data) {
  return request({
    url: '/yybbsc/configuration/checkConfiguration',
    method: 'post',
    data: data
  })
}

// 提交修改的配置
export function changeConfiguration(data) {
  return request({
    url: '/yybbsc/configuration/changeConfiguration',
    method: 'post',
    data: data
  })
}

// // 查询收入预测报配置详细
// export function getConfiguration(id) {
//   return request({
//     url: '/yybbsc/configuration/' + id,
//     method: 'get'
//   })
// }
//
// // 新增收入预测报配置
// export function addConfiguration(data) {
//   return request({
//     url: '/yybbsc/configuration',
//     method: 'post',
//     data: data
//   })
// }
//
// // 修改收入预测报配置
// export function updateConfiguration(data) {
//   return request({
//     url: '/yybbsc/configuration',
//     method: 'put',
//     data: data
//   })
// }
//
// // 删除收入预测报配置
// export function delConfiguration(id) {
//   return request({
//     url: '/yybbsc/configuration/' + id,
//     method: 'delete'
//   })
// }
