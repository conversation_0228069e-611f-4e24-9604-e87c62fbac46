import request from "@/utils/request";

export function overtimeList(params) {
  return request({
    url: "/work/overtime/list",
    method: "get",
    params,
  });
}
export function workOvertimeId(id) {
  return request({
    url: '/work/overtime/' + id,
    method: 'get'
  })
}

export function overtimeProcessId(configId) {
  return request({
    url: "/work/overtime/processId/"+configId,
    method: "get",
  });
}
export function getWorkOvertimeFlow(params) {
  return request({
    url: "/personnelflow/getWorkOvertimeFlow",
    method: "get",
    params,
  });
}
export function delOvertime(configId) {
  return request({
    url: '/work/overtime/' + configId,
    method: 'delete'
  })
}
export function addOvertime(data) {
  return request({
    url: '/work/overtime',
    method: 'post',
    data: data
  })
}
export function passWorkOvertime(data) {
  return request({
    url: '/work/overtime/passWorkOvertime',
    method: 'post',
    data: data
  })
}
export function unpassWorkOvertime(data) {
  return request({
    url: '/work/overtime/unpassWorkOvertime',
    method: 'post',
    data: data
  })
}
export function commitWorkOvertime(data) {
  return request({
    url: '/work/overtime/commitWorkOvertime',
    method: 'put',
    data: data
  })
}
export function updateOvertime(data) {
  return request({
    url: '/work/overtime',
    method: 'put',
    data: data
  })
}
export function voidWorkOvertime(data) {
  return request({
    url: '/work/overtime/voidWorkOvertime',
    method: 'put',
    data
  })
}
export function workHandleOvertime(id) {
  return request({
    url: "/work/overtime/handle/"+id,
    method: "get",
  });
}