import request from '@/utils/request'

export function getNoticeMainNoticeList(data) {
  return request({
    url: '/noticeMain/notice/list',
    method: 'post',
    data
  })
}
export function updateNoticeMainNotice(data) {
  return request({
    url: '/noticeMain/notice',
    method: 'put',
    data
  })
}
export function addNoticeMainNotice(data) {
  return request({
    url: '/noticeMain/notice',
    method: 'post',
    data
  })
}
export function updatePublishRevocation(data) {
  return request({
    url: '/noticeMain/notice/publishRevocation',
    method: 'put',
    data
  })
}
export function deleteNotice(id) {
  return request({
    url: '/noticeMain/notice/deleteNotice/' + id,
    method: 'delete'
  })
}
export function getHomePostCompany(params) {
  return request({
    url: '/noticeMain/notice/getHomePostCompany',
    method: 'get',
    params
  })
}
export function getNoticeMainNotice(id) {
  return request({
    url: '/noticeMain/notice/'+id,
    method: 'get',
  })
}
export function getNoticeAuthCompany(params) {
  return request({
    url: '/noticeMain/notice/getAuthCompany',
    method: 'get',
    params
  })
}
export function getDataManageList(params) {
  return request({
    url: '/systemData/manage/dataManageList',
    method: 'get',
    params
  })
}
