import request from "@/utils/request";

export function selectTypeAdd(data) {
    return request({
        url: "/system/selectType",
        method: "post",
        data

    });
}
export function selectTypeEdit(data) {
    return request({
        url: "/system/selectType",
        method: "put",
        data

    });
}
export function selectTypeDetail(id) {
    return request({
        url: "/system/selectType/" + id,
        method: "get",
    });
}
export function selectTypeList(params) {
    return request({
        url: "/system/selectType/list",
        method: "get",
        params
    });
}
export function delSystemSelectType(id) {
    return request({
      url: '/system/selectType/' + id,
      method: 'delete'
    })
  }
  