import Vue from 'vue'
import Vuex from 'vuex'
import app from './modules/app'
import user from './modules/user'
import tagsView from './modules/tagsView'
import permission from './modules/permission'
import settings from './modules/settings'
import getters from './getters'
import data from "./modules/data";


Vue.use(Vuex)

const store = new Vuex.Store({
  state:{
    editTemplate:false,
    changeAuthType:false,
    principalId:null,
  },
  modules: {
    app,
    user,
    tagsView,
    permission,
    settings,
    data
  },
  getters
})

export default store
