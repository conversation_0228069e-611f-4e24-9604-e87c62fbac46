export default {
  data() {
    return {
      dataListMultipleSuperSelection: [],
    };
  },
  methods: {
    handleSelectionChangeDataList(val) {
      this.dataListMultipleSuperSelection = val;
    },
    getNames() {
      let name = "";
      let nameExport = "";
      if (this.followData.oaModuleType == "1") {
        name = "资料审核.zip";
        nameExport = "资料审核列表.xlsx";
      } else if (this.followData.oaModuleType == "2") {
        name = "资料下载审核.zip";
        nameExport = "资料下载审核列表.xlsx";
      } else if (this.followData.oaModuleType == "3") {
        name = "资料用印审核.zip";
        nameExport = "资料用印审核列表.xlsx";
      } else if (this.followData.oaModuleType == "ZL4") {
        name = "资料用印审核.zip";
        nameExport = "资料用印审核列表.xlsx";
      } else if (this.followData.oaModuleType == "JG2") {
        name = "监管资料下载审核.zip";
        nameExport = "监管资料下载审核列表.xlsx";
      } else if (this.followData.oaModuleType == "JG3") {
        name = "监管资料用印审核.zip";
        nameExport = "监管资料用印审核列表.xlsx";
      } else {
        name = "监管资料录入审核.zip";
        nameExport = "监管资料录入审核列表.xlsx";
      }
      return { name, nameExport };
    },
    async batchDownloadSupervise() {
      const { name } = this.getNames();
      if (!this.dataListMultipleSuperSelection.length) {
        this.$modal.msgError("请勾选列表数据进行操作");
        return;
      }
      const ids = this.dataListMultipleSuperSelection.map((item) => item.id);
      if (["1", "2", "3","ZL4"].includes(this.followData.oaModuleType)) {
        this.download(
          "system/information/downloadFileZip",
          { idArray: ids },
          name
        );
      } else {
        this.download(
          "supervise/information/downloadFileZip",
          { idArray: ids },
          name
        );
      }
    },
    handleExportSupervise() {
      const { nameExport } = this.getNames();
      const selection = this.dataListMultipleSuperSelection.length
        ? this.dataListMultipleSuperSelection
        : this.tableData;
      if (["1", "2", "3","ZL4"].includes(this.followData.oaModuleType)) {
        this.download(
          "/system/information/exportAll",
          {
            ids: JSON.stringify(selection.map((item) => item.id)),
          },
          nameExport
        );
      } else {
        this.download(
          "/supervise/information/exportAll",
          {
            ids: JSON.stringify(selection.map((item) => item.id)),
          },
          nameExport
        );
      }
    },
  },
};
