import OverTime from "@/views/oaWork/updateProcessForm/components/overTime/Form.vue";
import { overtimeProcessId,commitWorkOvertime,updateOvertime } from "@/api/checkWork/overTime";
export default {
  components: {
    OverTime
  },
  data() {
    return {
      tableCheckWorkOverTime:{},
    };
  },
  methods: {
    async initCheckWorkOverTime() {
      if (this.$route.query.checkWorkOverTime) {
        this.tableCheckWorkOverTime = JSON.parse(
          sessionStorage.getItem("oa-checkWorkOverTimeForm")
        );
      }
      if (this.$route.query.oid && this.oaModuleType==12) {
        const id = this.$route.query.businessId || this.$route.query.oid;
          const { data } = await overtimeProcessId(id);
          if(data)this.tableCheckWorkOverTime=data;
      }
    },
    async checkWorkOverTimeProcessTo(response,isSave) {
      if (
      (  sessionStorage.getItem("oa-checkWorkOverTimeForm") &&
       Object.keys(this.tableCheckWorkOverTime).length > 0 &&
        this.$route.query.checkWorkOverTime)||(this.$route.query.oid && Object.keys(this.tableCheckWorkOverTime).length > 0)
      ) {
        const id = this.tableCheckWorkOverTime.id;
        const params = {
          processId: response.data,
          id,
        };
        if(isSave){
          updateOvertime(params).then(async (res) => {
            if (res.code == 200) {
              sessionStorage.removeItem("oa-checkWorkOverTimeForm");
            }
          });
        }else{
          commitWorkOvertime(params).then(async (res) => {
            if (res.code == 200) {
              sessionStorage.removeItem("oa-checkWorkOverTimeForm");
            }
          });
        }
        
      }
    },
    checkWorkOverTimeDraft(processId){
      if(this.oaModuleType=='12'){
        const id = this.tableCheckWorkOverTime.id;
        const params = {
          processId,
          id,
        };
        commitWorkOvertime(params).then(async (res) => {
          if (res.code == 200) {
            sessionStorage.removeItem("oa-checkWorkOverTimeForm");
          }
        });
      }
    },
  }
};
