import { getFilesPathMapping } from "@/api/cdlb/files";
import { downloadByUrl } from "@/api/oa/processTemplate";
export default {
  data() {
    return {
      photoUrl: null,
      pdfUrl:null,
      imagePreviewUrls: [], // 预览图片的下标
    };
  },
  methods: {
  handlePreview(file) {
    console.log(file)
    if (file.hasOwnProperty("fileName")) {
      file.name = file.fileName;
    }
    if (file.hasOwnProperty("response")) {
      file.url = file.response.data.fileUrl||file.response.data.url||file.response.data.filePath;
    }else{
      file.url=file.downLoadUrl || file.url || file.fileUrl||file.filePath; //图片的https链接
    }
    console.log(file.url,222222222222)
    if (file.name.endsWith(".pdf")) {
      //文件是pdf格式
      getFilesPathMapping().then((resp) => {
        this.pdfUrl = resp.msg + file.url;
        window.open(this.pdfUrl);
        return;
      });
    } else if (
      file.name.endsWith(".jpg") ||
      file.name.endsWith(".jpeg") ||
      file.name.endsWith(".png") ||
      file.name.endsWith(".gif")
    ) {
      //文件是图片格式
      getFilesPathMapping().then((resp) => {
        console.log(file.url)
        this.photoUrl = resp.msg + file.url;
        let array = new Set([]);
        array.add(resp.msg + file.url);
        let from = Array.from(array);
        this.imagePreviewUrls = from;
        this.$refs.previewImg.showViewer = true;
      });
      // this.showImgViewer = true;
    } else {
      //文件下载
      this.handleDownload(file);
    }
  },

  handleDownload(file) {
    if (file.hasOwnProperty("fileName")) {
      file.name = file.fileName;
    }
    if (file.hasOwnProperty("response")) {
      file.url = file.response?.data.fileUrl||file.response?.data.url||file.response?.data.filePath;
    }
    const url = file.downLoadUrl || file.url || file.fileUrl||file.filePath; //图片的https链接
    downloadByUrl({
      url: url,
    }).then((res) => {
      let href = window.URL.createObjectURL(new Blob([res])); // 根据后端返回的url对应的文件流创建URL对象
      const link = document.createElement("a"); //创建一个隐藏的a标签
      link.target = "_blank";
      link.href = href; //设置下载的url
      link.download = file.name; //设置下载的文件名
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(href); // 释放掉blob对象
    });
  },
}
};
