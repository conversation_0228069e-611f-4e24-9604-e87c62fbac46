export default {
  data() {
    return {
      informListMultipleSelection: [],
    };
  },
  methods: {
    handleSelectionChangeInformList(val) {
      this.informListMultipleSelection = val;
    },
    async batchDownload() {
      const name=this.flowData.flowInfo.oaModuleType==2?"资料下载审核.zip":"资料用印审核.zip";
      if (!this.informListMultipleSelection.length) {
        this.$modal.msgError("请勾选列表数据进行操作");
        return;
      }
      const ids=this.informListMultipleSelection.map(item=>item.id);
      this.download(
        "system/information/downloadFileZip",
        {idArray:ids},
        name
      );
    },
  },
};
