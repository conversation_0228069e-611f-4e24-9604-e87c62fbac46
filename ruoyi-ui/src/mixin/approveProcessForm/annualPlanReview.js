import AnnualPlanReview from "@/views/oaWork/updateProcessForm/components/annualPlan/Table.vue";
import { selectAnnualPlanProcessList, passAnnualPlanProcess } from "@/api/perAppraisal/annualPlan";
export default {
  components: {
    AnnualPlanReview,
  },
  data() {
    return {
      tableAnnualPlanReview:null
    };
  },
  methods: {
    async initAnnualPlanReview() {
      const id = this.$route.query.businessId || this.$route.query.oid;
      const { rows } = await selectAnnualPlanProcessList({processId:id});
      if(rows)this.tableAnnualPlanReview=rows;
     
    },
    passAnnualPlanReview() {
      const ids = this.tableAnnualPlanReview.map((item) => item.id);
      passAnnualPlanProcess({ids});
    },
  },
};
