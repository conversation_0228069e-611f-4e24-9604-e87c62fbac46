package com.ruoyi;

import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;

/**
 * 启动程序
 *
 * <AUTHOR>
 */
@ComponentScan({"com.ruoyi.*", "org.ruoyi.*"})
@MapperScan(basePackages = {"org.ruoyi.core.**.mapper"})
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
public class RuoYiApplication {
    /**
     * 日志记录器
     */
    private static final Logger logger = LoggerFactory.getLogger(RuoYiApplication.class);

    public static void main(String[] args) {
        SpringApplication.run(RuoYiApplication.class, args);
        logger.info("数据平台 RuoYiApplication 服务启动成功");
    }

    @Bean
    public PaginationInterceptor paginationInterceptor() {
        PaginationInterceptor paginationInterceptor = new PaginationInterceptor();
        return paginationInterceptor;
    }
}
