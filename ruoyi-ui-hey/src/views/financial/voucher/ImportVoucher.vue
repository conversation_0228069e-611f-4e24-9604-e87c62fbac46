<template>
  <app-content class="h-panel">
    <!--		<div class="h-panel-bar"><span class="h-panel-title">凭证列表</span></div>-->
    <div class="float-right h-input h-input-prefix-icon">
      <input type="text" placeholder="输入账套名称" v-model="accountSetsName" @keyup.enter="searchAccountSets"/>
      <i class="h-icon-search"></i>
    </div>
    <Tabs :datas="accountSetsList" v-model="accountsSetsSelected" @click="accountsSetsChange"></Tabs>
    <div>
      资金事项：
      <Select v-width="300" :datas="datas1" :filterable="true"></Select>
    </div>
    <div class="center margin-top">
        <template>
          <div>
            <el-upload
              class="upload-demo"
              drag
              action="https://jsonplaceholder.typicode.com/posts/"
              multiple>
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              <div class="el-upload__tip" slot="tip">只能上传jpg/png文件，且不超过500kb</div>
            </el-upload>
          </div>
        </template>
    </div>
  </app-content>
</template>

<script>
export default {
  name: "ImportVoucher",
  data() {
    return {
      accountSetsList: [],
      //账套选中标识
      accountsSetsSelected: null,
      accountSetsName: "",
      options: {
        max_file_size: '5mb'
      },
      file: [],
      datas1: []
    };
  },
  created() {
    this.$store.dispatch("init");
  },
  watch: {},
  computed: {
    User() {
      return this.$store.state.financial.User;
    },
    currentAccountSets() {
      return this.$store.state.financial.currentAccountSets;
    },
    myAccountSets() {
      return this.$store.state.financial.myAccountSets;
    },
  },
  mounted() {
    //账套列表
    this.pushDatas()
  },
  methods: {
    fileChange(e) {
      if (this.$refs.file.files.length) {
        let formData = new FormData();
        formData.append('file', this.$refs.file.files[0]);
        this.loading = true;
        this.$api.voucher.import(formData).then(({data}) => {
          if (data) {
            this.accountDate = data;
          }
          this.$store.dispatch('init', this.currentAccountSets.id);
          this.$Message("亲,导入成功~");
        }).finally(() => {
          this.loading = false;
        });

        this.$refs.file.value = "";
      }
    },
    accountsSetsChange(data) {
      //查询
      this.$store.dispatch('init', data.key).then(() => {
        this.loadList();
      })
    },
    auditStateChange(){
      this.loadList();
    },
    searchAccountSets(event){
      if(this.accountSetsName != ''){
        let accountSetsMap = {};
        this.myAccountSets.forEach(accountSets => {accountSetsMap[accountSets.companyName] =  accountSets.id})
        //查询
        this.$store.dispatch('init', accountSetsMap[this.accountSetsName]).then(() => {
          this.accountSetsList = [];
          let accountSet = {
            title: this.currentAccountSets.companyName,
            key: this.currentAccountSets.id
          }
          this.accountSetsList.push(accountSet)
        })

      }else{
        this.pushDatas()
      }
    },
    pushDatas(){
      this.accountSetsList = [];
      //账套列表
      this.myAccountSets.forEach(e=>{
        let accountSet = {
          title: e.companyName,
          key: e.id
        }
        this.accountSetsList.push(accountSet)
      })
      this.accountsSetsSelected = this.currentAccountSets.id;
    }
  }
};
</script>
<style lang='less' scoped>
.h-panel-body {
  table {
    width: 100%;
    border-collapse: collapse;

    td {
      padding: 7px;
    }

    &.header {
      background-color: @primary-color;
      color: white;
    }
  }

  .details {
    font-size: 12px;
    margin: 15px 0;
    border: 1px solid @gray2-color;

    .actions {
      text-align: right;
      padding-right: 20px;

      span, a {
        display: none;
      }
    }

    input {
      display: none;

      &.display {
        display: inline-block;
      }
    }

    &-header {
      background-color: @gray3-color;
      color: @dark3-color;
    }

    td, th {
      border-bottom: 1px solid @gray2-color;
    }

    tr:hover:not(.details-header) {
      background-color: #dff7df;
      cursor: pointer;
    }

    &:hover {
      box-shadow: 0 0 10px 0 #dadada;
      border-color: #dadada;

      .actions {
        span, a {
          display: inline-block;
        }
      }

      input {
        display: inline-block;
      }
    }
  }
}
</style>
