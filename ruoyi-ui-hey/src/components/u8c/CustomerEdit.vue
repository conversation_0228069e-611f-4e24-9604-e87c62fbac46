<template>
  <div>
    <Dialog
      width="600"
      :visible.sync="showDialog"
      @close="handleCancel"
      title="配置客商档案关联"
    >
      <div class="item">
        <span>用友U8C系统客商：</span>
        <input
          disabled
          type="text"
          style="width: 300px"
          v-model="params.u8cMerchantName"
        />
      </div>
      <div class="item">
        <span>用友U8C客商类型：</span>
        <input
          disabled
          type="text"
          style="width: 300px"
          v-model="params.u8cInteriorTypeName"
        />
      </div>
      <div class="item" style="align-items: start">
        <span><i>*</i>智慧财务系统客商：</span>
        <div style="height: 75px">
          <Select
            v-if="!checkbox"
            style="width: 300px"
            v-model="params.merchantIdIndex"
            @change="changeMerchant"
            placeholder="请输入/选择智慧财务系统客商"
            :datas="merchantNames"
            keyName="index"
            titleName="supplierName"
            filterable
          />
          <input
            type="text"
            v-model="params.merchantName"
            style="width: 300px; margin-bottom: 20px"
            v-else
            placeholder="请输入人员姓名"
          />
          <br />
          <Checkbox v-model="checkbox">选择系统外部用户关联</Checkbox>
        </div>
      </div>
      <div class="item">
        <span>关联方式：</span>
        <Select
          style="width: 300px"
          v-model="params.relevanceType"
          placeholder="选择用友U8C客商后自动回显"
          disabled
          :datas="types.relevanceTypes"
          keyName="dictValue"
          titleName="dictLabel"
          filterable
        />
      </div>
      <div class="item" style="align-items: start">
        <span><i>*</i>修改原因：</span>
        <textarea
          style="width: 300px"
          v-model="params.updateReason"
          type="text"
        />
      </div>
      <template #footer>
        <Button style="margin-right: 16px" @click="handleCancel">取消</Button>
        <Button @click="handleOk" color="blue">确定</Button>
      </template>
    </Dialog>
  </div>
</template>
  
  <script>
import Dialog from "./Dialog.vue";
export default {
  props: {
    itemData: Object,

    types: Object,
  },
  components: {
    Dialog,
  },
  data() {
    return {
      merchantNames: [],
      params: {
        merchantIdIndex: "",
        u8cMerchantName: "",
        u8cInteriorTypeName: "",
        updateReason: "",
      },
      checkbox: false,
      showDialog: true,
    };
  },
  mounted() {
    this.getWisdomBusi();
    this.params = Object.assign(this.params, this.itemData);
    this.params.u8cInteriorTypeName =
      this.types.patronTypes[this.params.u8cInteriorType].dictLabel;
    if (this.params.relevanceType === undefined) {
      this.params.relevanceType = 1;
    } else {
      this.params.relevanceType = this.params.relevanceType * 1;
    }
    if (this.params.merchantType == 4) {
      this.checkbox = true;
    }
  },
  watch: {
    checkbox(newval, oldval) {
      if (newval) {
        this.params.merchantId = null;
        this.params.merchantIdIndex = null;

        this.params.merchantType = 4;
      } else {
        this.params.merchantId = null;

        this.params.merchantIdIndex = null;
        this.params.merchantType = null;
      }
    },
  },
  methods: {
    changeMerchant(e) {
      console.log(e);
      this.params.merchantId = e.supplierId;
      this.params.merchantName = e.supplierName;
      this.params.merchantType = e.supplierType;
    },
    handleCancel() {
      this.$emit("close");
    },
    handleOk() {
      if (!this.params.merchantName) {
        this.$Message({ type: "warn", text: "请输入智慧财务系统客商" });
        return;
      }
      if (!this.params.updateReason) {
        this.$Message({ type: "warn", text: "请填写修改原因" });
        return;
      }

      this.params.isInterior = this.params.merchantType == 4 ? 0 : 1;
      this.$api.u8c.suppliersEidt({ ...this.params }).then((res) => {
        this.$Message({ type: "success", text: "修改成功" });
        this.$emit("close");
      });
      console.log(this.params);
    },
    getWisdomBusi() {
      this.$api.u8c.getWisdomBusi().then((res) => {
        this.merchantNames = res.rows;
        this.merchantNames.forEach((item, index) => {
          item.index = index + 1;
          if (
            this.params.merchantType &&
            this.params.merchantId &&
            item.supplierId == this.params.merchantId &&
            item.supplierType == this.params.merchantType
          ) {
            this.params.merchantId = item.supplierId;
            this.params.merchantName = item.supplierName;
            this.params.merchantType = item.supplierType;
            this.params.merchantIdIndex = index + 1;
          }
        });
      });
    },
  },
};
</script>
  
  <style lang="less" scoped>
.item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  span {
    margin-right: 10px;
    display: inline-block;
    width: 140px;
    text-align: right;
    i {
      color: red;
      margin-right: 5px;
    }
  }
}
</style>