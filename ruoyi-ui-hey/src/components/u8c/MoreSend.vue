<template>
  <div>
    <el-dialog title="推送合并凭证至用友U8C" :visible.sync="showDialog" width="1200px" append-to-body
      :before-close="handleCancel">
      <div style="display: flex;">
        <div>
          该界面的凭证为根据推送凭证规则生成的凭证。<br />您可以在本界面修改摘要。系统匹配不到银行简称时，需要您手动进行补充。
        </div>
        <div>
          <Button @click="alignSelect" style="
            height: 40px;
            font-weight: bold;
            font-size: 16px;
            margin-left: 40px;
          " color="blue">重新选择推送规则</Button>
          <p style="font-size: 12px;margin-left: 40px;color: red">如果系统自动匹配的规则不符合您的推送情况，请点击此处重新选择推送规则</p>
        </div>
      </div>
      <div style="margin-top: 15px; font-weight: bold">
        账套：{{ accountName }}
      </div>
      <Row class="margin-top" type="flex" :space-x="10">
        <Cell>
          <Select @change="loadCode" keyName="word" titleName="word" style="min-width: 70px" :deletable="false"
            :datas="voucherWords" v-model="form.word" placeholder="记" />
        </Cell>
        <Cell class="label">
          <NumberInput :min="1" v-model="form.code" v-width="90" style="display: inline-block" />
          号
        </Cell>
        <Cell class="label"> 日期： </Cell>
        <Cell class="label">
          <DatePicker :clearable="false" v-model="form.voucherDate" format="YYYY-MM-DD" />
        </Cell>
        <Cell class="label">
          <DropdownCustom :toggle-icon="false" class-name="h-text-dropdown">
            <span class="text-hover blue-color font-bold">备注</span>
            <div slot="content" v-width="200">
              <textarea placeholder="请输入备注内容" v-model="form.remark" v-autosize rows="3" style="width: 100%"></textarea>
            </div>
          </DropdownCustom>
        </Cell>
      </Row>
      <voucher-table ref="voucherTable" v-model="voucherTable" />
      <div class="padding-right-left padding-bottom">
        制单人：{{ userName }}
      </div>
      <div style="display: flex; align-items: center">
        <div>
          <div style="display: flex; align-items: center">
            <span style="margin-right: 12px">用友U8C凭证生成时间</span>
            <el-date-picker v-model="date" type="datetime" placeholder="选择日期时间">
            </el-date-picker>
          </div>
          <div style="margin-top: 8px; color: #999">
            请选择推送至用友U8C的凭证生成时间，默认为当前时间，您可以进行修改后再推送
          </div>
        </div>
        <div style="margin-left: 30px">
          <Button @click="send" style="height: 40px; font-weight: bold; font-size: 16px"
            color="blue">确认凭证无误，推送至用友U8C</Button>
          <Button style="height: 40px; font-size: 16px; margin-left: 20px" @click="handleCancel">取消</Button>
          <div style="margin-top: 8px; color: #999">
            请检查凭证合并是否正确，确认后点击推送按钮
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Cookies from "js-cookie";

import VoucherTable from "@/components/Financial/VoucherTable";
export default {
  components: { VoucherTable },
  props: {
    datas: Array,
  },
  data() {
    return {
      date: "",
      userName: "",
      accountName: "",
      voucherTable: { voucherItems: [] },
      showDialog: true,
      voucherWords: [],
      form: {
        remark: "",
        word: "",
        code: "",
        voucherDate: "",
        carryForward: false,
      },
      newData: null,
    };
  },
  computed: {
    currentAccountSets() {
      return this.$store.state.financial.currentAccountSets;
    },
  },
  mounted() {
    this.newData = JSON.parse(JSON.stringify(this.datas));
    this.date = new Date().getTime();
    this.userName = Cookies.get("userNickName");
    this.accountName = JSON.parse(sessionStorage.getItem("account")).title;
    console.log(this.accountName);
    this.loadVoucherWords();

    setTimeout(() => {
      this.newData[0].voucherDetails.forEach((item) => {
        item.subjectName = item.newSubjectName;
      });
      this.$refs.voucherTable.initValue(this.newData[0].voucherDetails);
      this.voucher = this.newData[0];
      this.form = {
        id: this.newData[0].id,
        auditMemberId: this.newData[0].auditMemberId,
        word: this.newData[0].word,
        remark: this.newData[0].remark,
        voucherDate: this.newData[0].voucherDate,
        carryForward: this.newData[0].carryForward,
        code: this.newData[0].code,
      };
    }, 1000);
  },
  methods: {
    alignSelect() {
      this.$emit("alignSelect");
      
    },
    send() {
      console.log(this.$refs.voucherTable.value);
      this.newData[0].voucherDetails =
        this.$refs.voucherTable.value.voucherItems;
      this.newData[0] = Object.assign(this.newData[0], this.form);
      this.newData[0].voucherDetails.forEach((item) => {
        this.datas[0].voucherDetails.forEach((item2) => {
          if (item.subjectId == item2.subjectId) {
            item.newSubject = item2.newSubject;
            item.newSubjectName = item2.newSubjectName;
            item.isGL = item2.isGL;
            item.subjectName = item2.subjectName;
            item.updateTime = item2.updateTime;
            item.abbreviation = item2.abbreviation ? item2.abbreviation : "";
            item.accountName = item2.accountName ? item2.accountName : "";
            item.accountNumber = item2.accountNumber ? item2.accountNumber : "";
            item.projectName = item2.projectName ? item2.projectName : "";
            item.projectId = item2.projectId ? item2.projectId : "";
          }
        });
      });
      console.log(this.newData);

      this.$emit(
        "submit",
        this.newData,
        this.$format(this.date, "yyyy-MM-dd HH:mm:ss")
      );
    },
    loadCode() {
      this.$api.voucher
        .loadCode({
          accountSetsId: this.currentAccountSets.id,
          word: this.form.word,
          currentAccountDate: this.form.voucherDate,
        })
        .then(({ data }) => {
          this.form.code = data;
        });
    },
    loadVoucherWords() {
      this.$api.setting.voucherWord
        .list({ account_sets_id: this.currentAccountSets.id })
        .then(({ data }) => {
          this.voucherWords = data || [];
        });
    },
    handleCancel() {
      this.$emit("close");
    },
    handleOk() { },
  },
};
</script>

<style lang="less" scoped></style>