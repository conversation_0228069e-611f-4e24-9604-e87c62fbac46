<template>
  <div>
    <el-dialog
      :title="'已选择'+table.length+'条'"
      :visible.sync="dialogVisible"
      append-to-body
      width="1200px"
      :before-close="handleClose"
    >
      <div class="h-panel-body" style="padding: 0">
        <table class="header" style="width: 1080px">
          <tr>
            <th style="width: 50px; opacity: 0">
              <input type="checkbox" />
            </th>
            <td style="width: 215px">摘要</td>
            <td>科目</td>
            <td align="right" style="width: 130px">借方金额</td>
            <td align="right" style="width: 130px">贷方金额</td>
          </tr>
        </table>
        <div
          v-for="(data, i) in table"
          :key="i"
          style="display: flex; align-items: center"
        >
          <table class="details">
            <tr class="details-header">
              <th style="width: 50px">
                <input
                  style="opacity: 0"
                  :class="{ display: data._checked }"
                  v-model="data._checked"
                  type="checkbox"
                />
              </th>
              <td colspan="2">
                日期：{{ data.voucherDate }} 凭证字号：{{ data.word }}-{{
                  data.code
                }}
                状态：<span v-if="!data.valid" style="color: red">已作废</span
                ><span v-if="data.valid">{{
                  data.auditMemberId ? "已审核" : "待审核"
                }}</span>
                <!-- <span
                v-if="data.mergeCount"
                style="font-weight: bold; margin-left: 20px"
                >已合并推送{{ data.mergeCount }}次</span
              >
              <span
                v-if="data.aloneCount"
                style="font-weight: bold; margin-left: 15px"
                >单独推送{{ data.aloneCount }}次</span
              >
              <span
                v-if="data.failureCount"
                style="font-weight: bold; margin-left: 15px; color: red"
                >推送失败{{ data.failureCount }}次</span
              >
              <span
                v-if="
                  !data.mergeCount && !data.aloneCount && !data.failureCount
                "
                style="font-weight: bold; margin-left: 20px"
                >该凭证尚未进行推送</span
              >
              <el-button
                type="primary"
                size="mini"
                style="margin-left: 20px"
                v-if="data.mergeCount || data.aloneCount || data.failureCount"
                @click="seeDetail(data)"
                >查看推送记录</el-button
              > -->
              </td>
              <td colspan="2" class="actions" align="right">
                <router-link
                  tag="span"
                  :to="{
                    name: 'VoucherForm',
                    params: { voucherId: data.id, showDetail: true },
                  }"
                  >查看</router-link
                >
              </td>
            </tr>
            <tr
              v-for="d in data.voucherDetails"
              :key="d.id"
              :class="{ 'un-valid': !data.valid }"
            >
              <th></th>
              <td style="width: 215px">
                {{ d.summary }}
                <template v-if="d.subject && d.num && d.price">
                  (数量:{{ d.num
                  }}<span class="dark4-color">{{ d.subject.unit }}</span
                  >，单价:{{ d.price }}<span class="dark4-color">元</span>)
                </template>
              </td>
              <td>{{ d.subjectName }}</td>
              <td align="right" style="width: 130px">
                {{ d.debitAmount | numFormat }}
              </td>
              <td align="right" style="width: 130px">
                {{ d.creditAmount | numFormat }}
              </td>
            </tr>
            <tr class="font-bold" :class="{ 'un-valid': !data.valid }">
              <td></td>
              <td>合计</td>
              <td>{{ data.debitAmount | dxMoney }}</td>
              <td align="right">{{ data.debitAmount | numFormat }}</td>
              <td align="right">{{ data.creditAmount | numFormat }}</td>
            </tr>
          </table>
          <el-button
            style="margin-left: 20px"
            type="primary"
            size="mini"
            @click="del(data.id)"
            >-删除</el-button
          >
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    datas: Array,
  },
  data() {
    return {
      dialogVisible: true,
      table: [],
    };
  },
  mounted() {
    this.table = JSON.parse(JSON.stringify(this.datas));
  },
  methods: {
    del(v) {
      this.table.forEach((item, index) => {
        if (item.id == v) {
          this.table.splice(index, 1);
        }
      });
    },
    handleClose() {
      this.$emit("close", this.table);
    },
  },
};
</script>

<style lang="less" scoped>
.h-panel-body {
  table {
    width: 100%;
    border-collapse: collapse;

    td {
      padding: 7px;
    }

    &.header {
      background-color: @primary-color;
      color: white;
    }
  }

  .details {
    font-size: 12px;
    margin: 15px 0;
    border: 1px solid @gray2-color;

    .actions {
      text-align: right;
      padding-right: 20px;

      span,
      a {
        display: none;
      }
    }

    input {
      &.display {
        display: inline-block;
      }
    }

    &-header {
      background-color: @gray3-color;
      color: @dark3-color;
    }

    td,
    th {
      border-bottom: 1px solid @gray2-color;
    }

    tr:hover:not(.details-header) {
      background-color: #dff7df;
      cursor: pointer;
    }

    &:hover {
      box-shadow: 0 0 10px 0 #dadada;
      border-color: #dadada;

      .actions {
        span,
        a {
          display: inline-block;
        }
      }

      input {
        display: inline-block;
      }
    }
  }
}
</style>