package com.ruoyi.nocode.monitor;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtilsForSpring;
import com.ruoyi.nocode.domain.ProcWorkflowFormdata;
import com.ruoyi.nocode.service.IProcWorkflowFormdataService;
import org.activiti.bpmn.model.BpmnModel;
import org.activiti.bpmn.model.Process;
import org.activiti.bpmn.model.UserTask;
import org.activiti.engine.HistoryService;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.impl.persistence.entity.VariableInstance;
import org.activiti.engine.runtime.ProcessInstance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.Map;

import static com.ruoyi.nocode.constant.FlowConstants.LOGINUSER;


@Component
public class TaskActOneListener implements TaskListener {
    private static final Logger logger = LoggerFactory.getLogger(TaskActOneListener.class);

    @Override
    public void notify(DelegateTask delegateTask) {
        logger.info("Activity工作流开始监听，获取流程实例为{}",delegateTask.getProcessInstanceId());
        if(StringUtils.isEmpty(delegateTask.getAssignee()) && !getCandidateUsers(delegateTask)) {
            String assignee = null;
            IProcWorkflowFormdataService procWorkflowFormdataService = BeanUtilsForSpring.getBean(IProcWorkflowFormdataService.class);
            RuntimeService runtimeService = BeanUtilsForSpring.getBean(RuntimeService.class);
            Map<String, VariableInstance> map = delegateTask.getVariableInstances();
            if (null != map) {
                assignee = map.get(LOGINUSER).getTextValue();
                logger.info("Activity工作流监听处理完成，通过流程实例Id获取流程发起人为{}", assignee);
            }else{
                String processInstanceId = delegateTask.getProcessInstanceId();
                ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
                String businessKey = processInstance.getBusinessKey();
                logger.info("Activity工作流监听处理中，通过流程实例Id获取业务主键businessKey,{}", businessKey);
                ProcWorkflowFormdata procWorkflowFormdata = new ProcWorkflowFormdata();
                procWorkflowFormdata.setBusinessKey(businessKey);
                List<ProcWorkflowFormdata> lastProcWorkflowFormDataList = procWorkflowFormdataService.selectProcWorkflowFormdataList(procWorkflowFormdata);
                if (lastProcWorkflowFormDataList.size() > 0) {
                    assignee = lastProcWorkflowFormDataList.get(0).getCreateBy();
                }
                logger.info("Activity工作流监听处理完成，获取流程发起人为{}", assignee);
            }
            delegateTask.setAssignee(assignee);
        }
    }

    /**
     * 判断是否存在候选者
     * @param delegateTask
     * @return
     */
    private boolean getCandidateUsers(DelegateTask delegateTask){
        boolean flag =false;
        HistoryService historyService = BeanUtilsForSpring.getBean(HistoryService.class);
        RepositoryService repositoryService = BeanUtilsForSpring.getBean(RepositoryService.class);
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(delegateTask.getProcessInstanceId()).singleResult();
        if(null ==historicProcessInstance){
            return false;
        }
        //获取bpmnModel对象
        BpmnModel bpmnModel = repositoryService.getBpmnModel(historicProcessInstance.getProcessDefinitionId());
        //因为我们这里只定义了一个Process 所以获取集合中的第一个即可
        Process process = bpmnModel.getProcesses().get(0);
        //获取所有的FlowElement信息
        List<UserTask> userTaskList = process.findFlowElementsOfType(UserTask.class);
        for (UserTask userTask:userTaskList) {
            if(userTask.getId().equalsIgnoreCase(delegateTask.getTaskDefinitionKey()) && userTask.getCandidateUsers().size()>0){
                flag = true;
            }
        }
        return flag;
    }
}
