package com.ruoyi.nocode.mapper;


import com.ruoyi.nocode.domain.ProcFormData;
import com.ruoyi.nocode.domain.bo.ActCopyTaskVo;
import com.ruoyi.nocode.domain.bo.ProcFormReadRecodeVo;
import com.ruoyi.nocode.domain.dto.FlowInfoDTO;
import com.ruoyi.nocode.domain.vo.ProcApprovalRecord;
import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.oasystem.domain.OaAccountingVoucherRules;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 单数据Mapper接口
 *
 * @date 2022-07-27
 */
public interface ProcFormDataMapper
{

    /**
     * 查询单数据
     *
     * @param procFormData 单数据
     * @return 结果
     */
    List<ProcFormData> selectProcFormDataList(ProcFormData procFormData);

    /**
     * 单数据统计
     *
     * @param procFormData 单数据
     * @return 结果
     */
    Long selectProcFormDataCount(ProcFormData procFormData);

    Long selectDataCount(ProcFormData procFormData);

    /**
     * 新增单数据
     *
     * @param procFormData 单数据
     * @return 结果
     */
    public int insertProcFormData(ProcFormData procFormData);

    /**
     * 修改单数据
     *
     * @param businessId 业务id
     * @param status 状态
     * @return 结果
     */
    public int updateProcFormData(@Param("businessId")String businessId, @Param("status")String status, @Param("dataJson")String dataJson, @Param("updateFormFlag")boolean updateFormFlag);

    /**
     * 根据业务id查询
     * @param businessKey
     * @return
     */
    public Map<String, String> selectByBusinessKey(@Param("businessKey") String businessKey,@Param("userName") String userName);

    List<Map<String, Object>> selectSPing(ProcFormData procFormData);
   Map<String, Object> selectSPingTotal(ProcFormData procFormData);

    int updateTimeByKey(@Param("businessKey") String businessKey, @Param("nowDate") Date nowDate);


    List<Map<String, Object>> shenpi(@Param("userName") String userName);

    long shenpiCount(@Param("userName") String userName);

    List<Map<String, Object>> yishenpi(@Param("theme") String theme,@Param("userName") String userName,@Param("comStatus")String comStatus, @Param("procFormData") ProcFormData procFormData);


    long yishenpiCount(@Param("theme") String theme,@Param("userName") String userName,@Param("comStatus")String comStatus, @Param("procFormData") ProcFormData procFormData);


    List<Map<String, Object>> yibohui(@Param("theme") String theme,@Param("userName") String userName, @Param("procFormData") ProcFormData procFormData);



    long yibohuiCount(@Param("theme") String theme,@Param("userName") String userName, @Param("procFormData") ProcFormData procFormData);


    int deleteProFormData(String oid);


    List<Map<String, Object>>   getDataByThisDept(@Param("theme") String theme,@Param("withProc") String withProc,@Param("status") long status,@Param("usersId") List<String> usersId);

    Map<String, Object>    getDataByThisDeptTotal(@Param("theme") String theme,@Param("withProc") String withProc,@Param("status") long status,@Param("usersId") List<String> usersId);


    /**
     * businessId查询发起的流程信息
     * @param procFormData 单数据
     * @return
     */
    ProcFormData selectProcFormDataByBusinessId(ProcFormData procFormData);

    /**
     * businessId查询发起的流程状态 - 审批通过和未通过
     * @param businessId 流程id
     * @return
     */
    String selectProcFormDataStatusByBusinessId(String businessId);

    /**
     * 找最大的流程编号
     * @return
     */
    Long selectMaxProcessNumber();

    /**
     * 修改单数据
     *
     * @param flowInfoDTO 流程信息业务对象
     * @return 结果
     */
    public int updateProcFormData1(FlowInfoDTO flowInfoDTO);

    List<Map<String, Object>> selectProcFormDataListNew(ProcFormData procFormData);

    List<String> getDeptUserIds(@Param("deptId") Long deptId);

    ProcFormData getDataByBusindess(@Param("businessKey") String businessKey);

    ProcFormData getDataByOid(@Param("oid") String oid);

    List<OaAccountingVoucherRules> getVoucherRulers(@Param("templateId") Long templateId);

    //根据发起流程人的id查找对应的部门负责人id
    Long selectDeptLeaderIdByFlowInitiatorId(String flowInitiatorId);

    //通过业务id查找该实例的发起人的用户名
    String selectInitiatorUserNameByBusinessId(String businessId);


    //插入节点流向信息
    void insertProcApprovalRecord(ProcApprovalRecord record);

    //修改节点流向信息
    void updateProcApprovalRecord(ProcApprovalRecord record);

    //查询节点流向信息
    List<ProcApprovalRecord> queryProcApprovalRecord(ProcApprovalRecord record);

    String selectBusinessId(String businessKey);

    /**
     * 新增抄送信息
     * @param vo
     * @param userList
     */
    void addCopyTask(@Param("vo") ActCopyTaskVo vo,@Param("list") List<String> userList);

    /**
     * 查询该用户下有多少条抄送给自己的信息
     * @param userName
     * @return
     */
    long copyToMeNum(@Param("userName") String userName);

    /**
     * 查询该用户下抄送给自己的详细业务数据
     * @param userName
     * @return
     */
    List<Map<String, Object>> copyToMe(@Param("userName") String userName);

    /**
     * 修改抄送为已读
     * @param copyTaskId
     * @param readFlag
     * @return
     */
    int updateReadFlag(@Param("id") Long copyTaskId,@Param("userName") String userName,@Param("readFlag") String readFlag);

    /**
     * 通过Id查询抄送的详细信息
     * @param copyTaskId
     * @return
     */
    ActCopyTaskVo queryCopyTaskVo(@Param("id") long copyTaskId);

    /**
     * 通过业务ID和流程编号查询处理人信息
     * @param processInstanceId
     * @param businessKey
     * @return
     */
    List<String> queryCopyTaskUserName(@Param("businessKey") String businessKey,@Param("processInstanceId")String processInstanceId);

    int updateCaiWuQuery(ProcFormData procFormData);

    /**
     * 新增表单未处理人信息
     * @param vo
     */
    void addProcReadRecode(ProcFormReadRecodeVo vo);

    /**
     * 通过businessKey更改表单未处理是否已读
     * @param vo
     * @return
     */
    int deleteReadFlagFormData(ProcFormReadRecodeVo vo);

    /**
     * 通过businessKey更改表单未处理是否已读
     * @param userName
     * @param businessIds
     * @return
     */
    int batchDeleteReadFlagFormData(@Param("userName") String userName,@Param("businessIds") List<String> businessIds);

 /**
  * 通过id批量更改抄送给我为已读
  * @param ids
  * @return
  */
 int batchUpdateCopyReadFlag(@Param("ids") List<Long> ids);


 int updateStatus(@Param("businessKey") String businessKey, @Param("statuss") String statuss);
}
