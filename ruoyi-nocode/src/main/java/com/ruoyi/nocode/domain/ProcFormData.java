package com.ruoyi.nocode.domain;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ProcFormData extends BaseEntity {


    private String id;
    private String oid;
    private String businessId;
    private String procKey;
    private String formId;
    private String formName;
    private JSONObject data;
    private String dataJson;
    private String instanceId;
    private String withProc;
    //0审批中 1完成  3废弃 4驳回 5草稿  10.审核不通过  11.已知悉
    private String status;
    private String createName;
    private Date beginTime;
    private Date endTime;
    /** 审核时间 */
    private Date checkTime;

    /** 金额 */
    private BigDecimal amount;

    /** 0已审核1未审核 */
    private String chunaCheck;

    /** 0已生成1未全部生成2未生成异常3未生成 */
    private String voucharGenerate;

    /** 凭证数量 */
    private String voucharNum;

    //主题
    private String theme;
    //流程编号
    private Long processNumber;
    //公司名
    private String companyName;
    //模板名
    private String templateName;
    //紧急程度
    private String urgency;


    private String nickName;
    //公司id
    private Long companyId;
    //流程关联分类id
    private Long classificationId;
    //流程关联模板id
    private Long templateId;

    //流程申请用户id
    private Long userId;

    private String templateType;

    private String userStatus;

    private String isFiling;

    private String newFromId;


    private String  flowFullId;

    private String oaModuleType;



    //以下是OA四期 我的流程-增加搜索 需要的参数（用于对库的查询操作）
    //发起人
    private List<Long> userIdList;

    //流程类别
    private List<Long> templateIdList;

    //发起开始时间
    private String initiatorStartTime;

    //发起结束时间
    private String initiatorEndTime;

    //父模板ID
    private Long parentId;

    //是否查询可阅览 1：是;其他-否
    private String onlyReadable;

    //阅览用户ID,只有当onlyReadable == 1 时赋值
    private long readableUserId;

    //阅览用户userName
    private String readableUserName;

    private List<String> statusList;
}
