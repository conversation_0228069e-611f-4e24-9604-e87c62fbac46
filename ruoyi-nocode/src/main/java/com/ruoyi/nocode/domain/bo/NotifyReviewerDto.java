package com.ruoyi.nocode.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotifyReviewerDto implements Serializable {

    //流程实例ID
    private String processInstanceId;

    //流程实例节点ID
    private String taskDefinitionKey;

    //模板ID
    private Long templateId;

    //主题
    private String theme;

    //流程发起人姓名
    private String startNickName;

    //流程发起时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
