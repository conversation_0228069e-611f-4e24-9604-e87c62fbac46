package com.ruoyi.nocode.service;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.nocode.domain.OaSystemUtil;
import com.ruoyi.nocode.domain.bo.*;
import com.ruoyi.nocode.domain.dto.FlowFileInfoDTO;
import com.ruoyi.nocode.domain.dto.FlowInfoDTO;
import com.ruoyi.nocode.domain.dto.FlowUserDTO;
import com.ruoyi.nocode.domain.vo.ApprovedNodesInfo;
import com.ruoyi.nocode.domain.vo.ProcessTableResultVo;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

public interface IProcFlowService {

    /**
     * 发起流程
     *
     * @param startBO
     * @return
     */
    public AjaxResult startFlow(StartBO startBO);

    /**
     * 处理流程节点
     *
     * @param execBO
     * @return
     */
    public AjaxResult execFlowNode(ExecBO execBO,LoginUser loginUser);

    /**
     * 根据businessid查询表单详情
     *
     * @param businessId
     * @return
     */
    public AjaxResult getDataByBusinessId(String businessId);

    /**
     * 根据formid查询表单数据集
     *
     * @param formId
     * @param request
     * @return
     */
    public TableDataInfo getDataListByFormId(String formId, HttpServletRequest request);


    /**
     * 查询我发起的任务
     *
     * @return
     */
    public TableDataInfo getDataListByUser();

    /**
     * 根据businessId获取流程审批记录
     *
     * @param businessId
     * @return
     */
    public AjaxResult getFlowhistoryByBusinessId(String businessId);

    /**
     * 获取所有用户
     *
     * @return
     */
    public AjaxResult getAllUsers(String userName);

    /**
     * 获取所有岗位
     *
     * @return
     */
    public AjaxResult getAllPosts(String postName,String postCode);

    TableDataInfo byCheckGetData(OaSystemUtil oaSystemUtil);

    TableDataInfo byCheckGetMonitoringData(OaSystemUtil oaSystemUtil);

    int deleteFlowByid(String oid);

    Map<String, Object> getTabsDataNums(OaSystemUtil oaSystemUtil);

    TableDataInfo myDeptByCheckGetData(OaSystemUtil oaSystemUtil, List<String> userIds);


    public List<String> getDeptUserList(LoginUser loginUser);
    /**
     * 根据businessId查询发起的流程信息、流程的审批记录
     *
     * @param businessId
     * @return 结果
     */
    AjaxResult getFlowInfoByBusinessId(String businessId);

    /**
     * 保存流程
     *
     * @param startBO 开始薄熙来
     * @return {@link AjaxResult}
     */
    public AjaxResult saveFlow(SaveDataBo startBO, LoginUser loginUser);

    /**
     * 发起流程
     *
     * @param saveDataBo
     * @return
     */
    AjaxResult startFlowNew(SaveDataBo saveDataBo, LoginUser loginUser);

    /**
     * 修改流程信息（如果是草稿状态，修改后发起流程）
     *
     * @param flowInfoDTO
     * @return
     */
    AjaxResult updateFlowInfo(FlowInfoDTO flowInfoDTO, LoginUser loginUser);

    /**
     * 根据businessId变更流程人员
     *
     * @param flowUserDTO
     * @return
     */
    AjaxResult changeFlowPeopleByBusinessId(FlowUserDTO flowUserDTO);

    /**
     * 根据formId修改表单定义中关联流程字段
     *
     * @param procFormBO
     * @return
     */
    AjaxResult updateProcFormDef(ProcFormBO procFormBO);

    /**
     * 根据businessId和页面的标识，查询按钮的权限
     *
     * @param buttonPermission
     * @return
     */
    AjaxResult getFlowButtonPermission(ButtonPermission buttonPermission, LoginUser loginUser);

    /**
     * 根据businessId和stepId，找所有的文件列表
     *
     * @param flowFileInfoDTO
     * @return
     */
    AjaxResult findFile(FlowFileInfoDTO flowFileInfoDTO);

    /**
     * 流程未发起前，准备发起的时候。根据模板id获取流程模板的第二个节点信息（第一个是发起，默认是通过的）
     *
     * @param flowFullId
     * @return
     */
    AjaxResult findFlowSecondNode(String flowFullId,String reqStr);

    /**
     * 通过模板部署的id，找流程图当前流程的下一个流程
     *
     * @param instanceId
     * @return
     */
    AjaxResult getNextFlowInfo(String instanceId, String taskId);


    /**
     * 根据businessId查询通过的审批记录
     * @param businessId
     * @return 结果
     */
    List<ApprovedNodesInfo>  getApproveNodesInfo(String businessId);

    /**
     * 根据businessId当前节点的按钮权限
     * @param businessId
     * @return 结果
     */
    Map<String,Object> getButtonPermissions(String businessId);

    /**
     * 根据businessId查询所有流程定义信息
     * @param businessId
     * @return
     */
    List<ProcessTableResultVo> getProcessTable(String businessId);

    /**
     * 根据businessId统计流程处理
     *
     * @param businessId
     * @return
     */
    Map<String,Object> statisticsData(String businessId);

    /**
     * 更改抄送是否已读状态
     * @param copyTaskId
     * @return
     */
    int updateReadFlag(String copyTaskId);

    /**
     * 新增抄送意见
     * @param copyTaskId
     * @param comment
     * @return
     */
    int addCopyCommon(long copyTaskId,String comment);

    /**
     * 通过businessKey更改表单未处理是否已读
     * @param vo
     * @return
     */
    int updateReadFlagFormData(ProcFormReadRecodeVo vo);

    /**
     * 终止流程
     */
    int stopFlowByBusinessId(String businessId, LoginUser loginUser,String status);

    /**
     * 通过businessId查询可阅览列表用户，新增未读标识
     * @param businessId
     * @return
     */
    int addReadFlagFormData(String businessId);

    /**
     * 通过businessIds批量更改表单未处理是否已读
     * @param businessIds
     * @return
     */
    int batchUpdateReadFlagFormData(List<String> businessIds);

    /**
     * 通过id批量更改抄送给我为已读
     * @param businessIds
     * @return
     */
    int batchUpdateCopyReadFlag(List<Long> businessIds);

    Map<String, Object> checkFeeFlow(SaveDataBo saveDataBo, LoginUser loginUser);

    /**
     * 已知悉流程
     * @param businessId
     * @return
     */
    public int knownFlowByBusinessId(String businessId);
}
